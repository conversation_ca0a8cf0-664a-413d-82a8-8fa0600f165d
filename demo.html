<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Pace Calculator</title>
    <meta name="description" content="This free pace calculator computes pace, time, and distance, given values for two of the variables. It can also be used for training purposes.">
    
    <style>
        @charset "utf-8";

        /* Exact replica of original CSS from calculator.net */
        body, p, td, div, span, input, th, li, textarea {
            font-family: arial, helvetica, sans-serif;
            font-size: 16px;
            color: rgb(0, 0, 0);
        }

        body {
            background: rgb(255, 255, 255);
            margin: 0px;
            padding: 0px;
            border: 0px;
            text-align: center;
        }

        p {
            margin: 5px 0px 8px;
        }

        img {
            border: 0px;
        }

        h1 {
            color: rgb(0, 51, 102);
            font-size: 26px;
            font-weight: bold;
            padding: 0px;
            margin: 12px 0px;
        }

        h2 {
            font-size: 22px;
            font-weight: bold;
            color: rgb(0, 51, 102);
            padding: 0px;
            margin-bottom: 2px;
        }

        h3 {
            font-size: 19px;
            font-weight: bold;
        }

        hr {
            border: 0px;
            color: rgb(170, 170, 170);
            background-color: rgb(170, 170, 170);
            height: 1px;
        }

        a {
            color: rgb(0, 102, 153);
            text-decoration: underline;
        }

        a:hover {
            text-decoration: none;
        }

        input {
            padding: 5px;
            color: rgb(0, 0, 0);
            box-sizing: border-box;
        }

        select {
            padding: 4px;
            color: rgb(0, 0, 0);
            box-sizing: border-box;
        }

        option {
            font-size: 16px;
        }

        input[type="text"], input[type="url"], input[type="tel"], input[type="number"], input[type="color"], input[type="date"], input[type="email"], select {
            border: 1px solid rgb(4, 66, 132);
            border-radius: 2px;
            box-shadow: rgb(102, 102, 102) 1px 1px 2px;
            font-size: 16px;
            background-color: rgb(255, 255, 255);
        }

        input[type="submit"] {
            border: 0px;
            color: rgb(255, 255, 255);
            padding: 11px 50px 11px 16px;
            font-size: 16px;
            font-weight: bold;
            background-color: rgb(76, 123, 37);
            background-image: url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"180px\" height=\"40px\"><circle cx=\"112\" cy=\"20\" r=\"11\" fill=\"darkseagreen\" /><path d=\"M110 12 L120 20 L110 28 Z\" fill=\"white\" /></svg>");
            background-repeat: no-repeat;
        }

        input[type="submit"]:hover {
            background-color: rgb(68, 68, 68);
        }

        input[type="reset"], input[type="button"] {
            border: 0px;
            color: rgb(255, 255, 255);
            padding: 11px 8px;
            font-size: 16px;
            background: rgb(171, 171, 171);
        }

        input[type="reset"]:hover, input[type="button"]:hover {
            background: rgb(68, 68, 68);
        }

        .innormal { width: 90px; }
        .in4char { width: 58px; }
        .in3char { width: 46px; }
        .in2char { width: 35px; }
        .inlong { width: 120px; }
        .inlonger { width: 170px; }
        .inlongest { width: 230px; }
        .inlongesthalf { width: 112px; }
        .infull { width: 226px; }
        .inhalf { width: 110px; }
        .infulltxarea { width: 600px; padding: 8px; }
        .inshortfull { width: 170px; }
        .inshorthalf { width: 82px; }

        .scaleimg { max-width: 100%; height: auto; }

        #headerout {
            background: rgb(0, 51, 102);
            text-align: center;
        }

        #header {
            width: 1100px;
            height: 60px;
            background: rgb(0, 51, 102);
            padding: 0px;
            margin-left: auto;
            margin-right: auto;
            text-align: left;
            overflow: hidden;
        }

        #logo {
            padding: 18px 0px;
            width: 270px;
            float: left;
        }

        #login {
            padding: 2px;
            float: right;
            color: rgb(204, 204, 204);
        }

        #login a {
            color: rgb(204, 204, 204);
            text-decoration: none;
        }

        #login a:hover {
            text-decoration: underline;
        }

        .topNavAbs {
            position: absolute;
            top: 21px;
            left: 50%;
            width: 520px;
            margin-left: -80px;
            text-align: left;
        }

        .topNavAbs a {
            color: white;
            padding: 10px 16px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            text-transform: uppercase;
            display: inline-block;
            text-decoration: none;
        }

        .topNavAbs a:hover {
            background-color: rgb(81, 132, 40);
        }

        .topNavOn {
            background-color: rgb(81, 132, 40);
        }

        #contentout {
            width: 1100px;
            padding-top: 5px;
            margin-left: auto;
            margin-right: auto;
            text-align: left;
            overflow: auto;
        }

        #content {
            padding: 0px 0px 15px;
            width: 728px;
            float: left;
        }

        #right {
            width: 336px;
            float: right;
            text-align: center;
        }

        #contentbig {
            padding: 0px 0px 15px;
            width: 843px;
            float: right;
        }

        #footer {
            background: rgb(225, 225, 225);
            padding: 25px 0px;
            font-size: 13px;
            color: rgb(85, 85, 85);
            text-align: center;
        }

        #footer a {
            color: rgb(68, 68, 68);
        }

        #footer a:hover {
            text-decoration: none;
        }

        #footerin {
            width: 1100px;
            margin-left: auto;
            margin-right: auto;
            text-align: left;
            overflow: auto;
            color: rgb(85, 85, 85);
        }

        #footernav {
            text-align: center;
        }

        #breadcrumbs, #breadcrumbs span {
            font-size: 13px;
        }

        #breadcrumbs a, #breadcrumbs a span {
            text-decoration: none;
            color: rgb(0, 102, 153);
        }

        #breadcrumbs a:hover, #breadcrumbs a span:hover {
            text-decoration: underline;
        }

        #othercalc {
            border: 1px solid rgb(51, 102, 153);
            margin: auto;
            text-align: left;
            width: 332px;
        }

        #octitle {
            background-color: rgb(51, 102, 153);
            padding: 6px;
            color: rgb(255, 255, 255);
            font-size: 18px;
            font-weight: bold;
        }

        #octitle a {
            color: rgb(255, 255, 255);
            text-decoration: none;
        }

        #octitle a:hover {
            text-decoration: underline;
        }

        #occontent {
            padding: 3px 6px;
            font-size: 14px;
        }

        #occontent a {
            display: inline-block;
            width: 158px;
            padding: 3px 0px;
        }

        #ocother {
            background-color: rgb(221, 221, 221);
            padding: 6px;
            text-align: center;
            font-size: 15px;
            color: rgb(187, 187, 187);
        }

        .smalltext {
            font-size: 13px;
        }

        .bigtext {
            font-size: 18px;
        }

        .verybigtext {
            font-size: 23px;
        }

        table.cinfoT {
            border-collapse: collapse;
            border-spacing: 0px;
            margin-top: 0px;
        }

        table.cinfoT th, table.cinfoT td.cinfoHd, table.cinfoT td.cinfoHdL {
            border-width: 1px;
            border-style: solid;
            border-color: rgb(17, 68, 119) rgb(17, 68, 119) rgb(51, 102, 153);
            background-color: rgb(51, 102, 153);
            font-weight: bold;
            color: rgb(255, 255, 255);
            padding: 5px 3px;
        }

        table.cinfoT td {
            border: 1px solid rgb(204, 204, 204);
            color: rgb(0, 0, 0);
            padding: 3px;
        }

        table.cinfoT tr:nth-child(2n+1) {
            background-color: rgb(238, 238, 238);
        }

        table.cinfoT tr:nth-child(2n) {
            background-color: rgb(255, 255, 255);
        }

        #topmenu ul {
            color: rgb(0, 0, 0);
            border-bottom: 1px solid rgb(187, 187, 187);
            margin: 12px 0px 0px;
            padding: 0px 0px 8px;
            font-size: 15px;
            font-weight: bold;
        }

        #topmenu ul li {
            display: inline;
            overflow: hidden;
            list-style-type: none;
            margin-left: 0px;
        }

        #topmenu ul li a, #topmenu ul li a:visited {
            color: rgb(255, 255, 255);
            background: rgb(51, 102, 153);
            border: 1px solid rgb(51, 102, 153);
            padding: 8px 5px;
            margin: 0px;
            text-decoration: none;
        }

        #topmenu ul li a:hover {
            background: rgb(238, 238, 238);
            color: rgb(0, 0, 0);
        }

        #topmenu ul #menuon a {
            color: rgb(0, 0, 0);
            background: rgb(238, 238, 238);
            border-width: 1px 1px 2px;
            border-style: solid;
            border-color: rgb(187, 187, 187) rgb(187, 187, 187) rgb(238, 238, 238);
            border-image: initial;
            padding: 8px 5px;
            margin: 0px;
            text-decoration: none;
        }

        #topmenu ul #menuon a:hover {
            background: rgb(238, 238, 238);
        }

        #insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}

        .panel {
            background: rgb(238, 238, 238);
            border: 1px solid rgb(187, 187, 187);
            padding: 5px;
        }

        .panel2 {
            background-color: rgb(238, 238, 238);
            padding: 5px;
            border-right: 1px solid rgb(187, 187, 187);
            border-bottom: 1px solid rgb(187, 187, 187);
            border-left: 1px solid rgb(187, 187, 187);
        }

        #searchbox {
            padding-top: 16px;
        }

        #bluebtn {
            border-radius: 1px;
            background: rgb(51, 102, 153);
            padding: 5px 8px;
            font-size: 18px;
            color: rgb(255, 255, 255);
            cursor: pointer;
        }

        #bluebtn:hover {
            background: rgb(68, 68, 68);
            color: rgb(255, 255, 255);
        }

        #calcSearchOut {
            padding: 5px;
        }

        #calcSearchOut div {
            padding: 5px;
            text-align: left;
        }

        #clear {
            margin-left: auto;
            margin-right: auto;
            clear: both;
            height: 0px;
        }

        @media (max-width: 1140px) {
            #header {
                width: 990px;
                padding-left: 8px;
            }
            #contentout {
                width: 1000px;
            }
            #content {
                width: 640px;
                float: left;
                padding-left: 10px;
            }
            #footerin {
                width: 990px;
            }
        }

        @media (max-width: 720px) {
            #header {
                width: auto;
                padding: 0px 8px;
            }
            #contentout {
                width: auto;
                padding: 8px;
            }
            #content {
                float: none;
                width: auto;
                padding: 0px;
            }
            #right {
                width: auto;
                float: none;
            }
            #footerin {
                width: auto;
            }
            .topNavAbs {
                display: none;
            }
        }

        @media (max-width: 650px) {
            img {
                max-width: 100%;
                height: auto;
            }
        }

        [style*="display: none"] {
            display: none !important;
        }

        [style*="display: block"] {
            display: block !important;
        }
    </style>
</head>
<body>

<div class="topNavAbs">
    <a href="#">Financial</a>
    <a href="#" class="topNavOn">Fitness &amp; Health</a>
    <a href="#">Math</a>
    <a href="#">Other</a>
</div>

<div id="headerout">
    <div id="header">
        <div id="logo">
            <a href="#">
                <svg width="208" height="22" viewBox="0 0 208 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <text x="0" y="16" font-family="Arial, sans-serif" font-size="18" fill="white" font-weight="bold">Calculator.net</text>
                </svg>
            </a>
        </div>
        <div id="login">
            <a href="#">sign in</a>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="contentout">
    <div id="content">
        <div id="breadcrumbs">
            <a href="#">home</a> / 
            <a href="#">fitness &amp; health</a> / 
            <a href="#">pace calculator</a>
        </div>

        <h1>Pace Calculator</h1>
        <p>Use the following calculator to estimate the pace for a variety of activities, including running, walking, and biking. The calculator can also be used to estimate the time taken or distance traveled with a given pace and time or distance.</p>

        <div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
        <div>
            <form name="calform" id="pace-calculator-form">
                <div id="topmenu">
                    <ul>
                        <li id="menuon"><a href="#" data-tab="pace">Pace</a></li>
                        <li><a href="#" data-tab="time">Time</a></li>
                        <li><a href="#" data-tab="distance">Distance</a></li>
                    </ul>
                </div>
                
                <div class="panel2" style="padding: 8px 5px;">
                    <table id="calinputtime" style="display: block;">
                        <tbody>
                            <tr>
                                <td width="75">Time</td>
                                <td><input type="text" name="ctime" id="ctime" value="00:50:25" class="inlong"></td>
                                <td>hh:mm:ss</td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <table id="calinputdistance" style="display: block;">
                        <tbody>
                            <tr>
                                <td width="75">Distance</td>
                                <td><input type="text" name="cdistance" id="cdistance" value="5" class="inlong"></td>
                                <td>
                                    <select name="cdistanceunit" id="cdistanceunit">
                                        <option value="Miles">Miles</option>
                                        <option value="Kilometers" selected="">Kilometers</option>
                                        <option value="Meters">Meters</option>
                                        <option value="Yards">Yards</option>
                                    </select>
                                    &nbsp; &nbsp;
                                    <select name="cpickevent" id="cpickevent">
                                        <option value="">-- Or pick an event --</option>
                                        <option value="M">Marathon</option>
                                        <option value="HM">Half-Marathon</option>
                                        <option value="1K">1K</option>
                                        <option value="5K">5K</option>
                                        <option value="10K">10K</option>
                                        <option value="1M">1 Miles</option>
                                        <option value="5M">5 Miles</option>
                                        <option value="10M">10 Miles</option>
                                        <option value="800m">800 meters</option>
                                        <option value="1500m">1500 meters</option>
                                    </select>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                    
                    <table width="310" style="padding-top: 10px;">
                        <tbody>
                            <tr>
                                <td align="center">
                                    <input type="submit" name="x" value="Calculate" id="calculate-btn">
                                    <input type="button" value="Clear" id="clear-btn">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <div class="smalltext">Note that placeholder zeros do not need to be entered in the "Time" or "Pace" field. For example, the time 5 minutes 3 seconds does not need to be entered as 00:05:03, and can be entered as 5:3.</div>
            </form>
        </div>

        <a name="multipoint"></a>
        <br><h2>Multipoint Pace Calculator</h2>
        <p>The following calculator can determine the pace of segments of a run (or other activity) for those with access to the time at intermittent points during the run.</p>

        <form id="multipoint-form" name="calform2">
            <table class="panel">
                <tbody>
                    <tr>
                        <td bgcolor="#E0F0FE">&nbsp;</td>
                        <td bgcolor="#E0F0FE" colspan="2">Distance</td>
                        <td bgcolor="#E0F0FE">Time (hh:mm:ss)</td>
                    </tr>
                    <tr><td>1.</td><td><input type="text" name="cmpdistance1" value="1" class="innormal"></td><td><select name="cmpdistanceunit1"><option value="Kilometers">Kilometers</option><option value="Miles">Miles</option></select></td><td><input type="text" name="cmptime1" value="" class="inlong"></td></tr>
                    <tr><td>2.</td><td><input type="text" name="cmpdistance2" value="2" class="innormal"></td><td><select name="cmpdistanceunit2"><option value="Kilometers">Kilometers</option><option value="Miles">Miles</option></select></td><td><input type="text" name="cmptime2" value="3:25" class="inlong"></td></tr>
                    <tr><td>3.</td><td><input type="text" name="cmpdistance3" value="3" class="innormal"></td><td><select name="cmpdistanceunit3"><option value="Kilometers">Kilometers</option><option value="Miles">Miles</option></select></td><td><input type="text" name="cmptime3" value="6:55" class="inlong"></td></tr>
                    <tr><td>4.</td><td><input type="text" name="cmpdistance4" value="4" class="innormal"></td><td><select name="cmpdistanceunit4"><option value="Kilometers">Kilometers</option><option value="Miles">Miles</option></select></td><td><input type="text" name="cmptime4" value="10:25" class="inlong"></td></tr>
                    <tr><td>5.</td><td><input type="text" name="cmpdistance5" value="5" class="innormal"></td><td><select name="cmpdistanceunit5"><option value="Kilometers">Kilometers</option><option value="Miles">Miles</option></select></td><td><input type="text" name="cmptime5" value="14:01" class="inlong"></td></tr>
                    <tr>
                        <td colspan="4" align="center">
                            <input type="submit" name="x" value="Calculate" id="multipoint-calculate">
                            <input type="button" value="Clear" id="multipoint-clear">
                        </td>
                    </tr>
                </tbody>
            </table>
        </form>

        <h3>Typical Races and World Record Paces</h3>
        <p>The following table shows the world record paces for various running distances. These represent the absolute fastest times ever recorded and serve as benchmarks for elite performance.</p>

        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Category</td>
                    <td class="cinfoHdL">Men's World Record Pace</td>
                    <td class="cinfoHdL">Women's World Record Pace</td>
                </tr>
                <tr><td>100 meters</td><td class="cinfoBodL">2:35/mile or 1:36/km</td><td class="cinfoBodL">2:49/mile or 1:45/km</td></tr>
                <tr><td>200 meters</td><td class="cinfoBodL">2:35/mile or 1:36/km</td><td class="cinfoBodL">2:52/mile or 1:47/km</td></tr>
                <tr><td>400 meters</td><td class="cinfoBodL">2:54/mile or 1:48/km</td><td class="cinfoBodL">3:12/mile or 1:59/km</td></tr>
                <tr><td>800 meters</td><td class="cinfoBodL">3:23/mile or 2:06/km</td><td class="cinfoBodL">3:48/mile or 2:21/km</td></tr>
                <tr><td>1,500 meters</td><td class="cinfoBodL">3:41/mile or 2:17/km</td><td class="cinfoBodL">4:07/mile or 2:34/km</td></tr>
                <tr><td>1 mile</td><td class="cinfoBodL">3:43/mile or 2:19/km</td><td class="cinfoBodL">4:13/mile or 2:37/km</td></tr>
                <tr><td>5K</td><td class="cinfoBodL">4:04/mile or 2:31/km</td><td class="cinfoBodL">4:34/mile or 2:50/km</td></tr>
                <tr><td>10K</td><td class="cinfoBodL">4:14/mile or 2:38/km</td><td class="cinfoBodL">4:45/mile or 2:57/km</td></tr>
                <tr><td>Half Marathon</td><td class="cinfoBodL">4:27/mile or 2:46/km</td><td class="cinfoBodL">4:58/mile or 3:05/km</td></tr>
                <tr><td>Marathon</td><td class="cinfoBodL">4:41/mile or 2:55/km</td><td class="cinfoBodL">5:10/mile or 3:13/km</td></tr>
            </tbody>
        </table>

        <h3>Pace Comparison Chart by Distance</h3>
        <p>The following chart illustrates how pace changes across different distances, showing the relationship between speed and endurance:</p>

        <div style="text-align:center; margin: 20px 0;">
            <svg width="600" height="400" viewBox="0 0 600 400" style="border: 1px solid #ccc; background: #f9f9f9;">
                <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#003366">World Record Pace by Distance</text>
                <line x1="60" y1="50" x2="60" y2="350" stroke="#333" stroke-width="2"/>
                <text x="25" y="200" text-anchor="middle" font-size="12" fill="#333" transform="rotate(-90 25 200)">Pace (min/mile)</text>
                <line x1="60" y1="350" x2="550" y2="350" stroke="#333" stroke-width="2"/>
                <text x="305" y="385" text-anchor="middle" font-size="12" fill="#333">Distance</text>
                <text x="55" y="55" text-anchor="end" font-size="10" fill="#666">2:30</text>
                <text x="55" y="105" text-anchor="end" font-size="10" fill="#666">3:00</text>
                <text x="55" y="155" text-anchor="end" font-size="10" fill="#666">3:30</text>
                <text x="55" y="205" text-anchor="end" font-size="10" fill="#666">4:00</text>
                <text x="55" y="255" text-anchor="end" font-size="10" fill="#666">4:30</text>
                <text x="55" y="305" text-anchor="end" font-size="10" fill="#666">5:00</text>
                <text x="100" y="365" text-anchor="middle" font-size="10" fill="#666">100m</text>
                <text x="150" y="365" text-anchor="middle" font-size="10" fill="#666">400m</text>
                <text x="200" y="365" text-anchor="middle" font-size="10" fill="#666">800m</text>
                <text x="250" y="365" text-anchor="middle" font-size="10" fill="#666">1500m</text>
                <text x="300" y="365" text-anchor="middle" font-size="10" fill="#666">5K</text>
                <text x="350" y="365" text-anchor="middle" font-size="10" fill="#666">10K</text>
                <text x="400" y="365" text-anchor="middle" font-size="10" fill="#666">Half</text>
                <text x="450" y="365" text-anchor="middle" font-size="10" fill="#666">Marathon</text>
                <polyline points="100,65 150,75 200,95 250,115 300,145 350,155 400,175 450,195" fill="none" stroke="#0066cc" stroke-width="3"/>
                <polyline points="100,85 150,105 200,125 250,145 300,175 350,185 400,205 450,225" fill="none" stroke="#cc0066" stroke-width="3"/>
                <rect x="450" y="60" width="120" height="50" fill="white" stroke="#ccc"/>
                <line x1="460" y1="75" x2="480" y2="75" stroke="#0066cc" stroke-width="3"/>
                <text x="485" y="79" font-size="12" fill="#333">Men's WR</text>
                <line x1="460" y1="95" x2="480" y2="95" stroke="#cc0066" stroke-width="3"/>
                <text x="485" y="99" font-size="12" fill="#333">Women's WR</text>
            </svg>
        </div>

        <br><h2>Training Through Pace and Heart Rate</h2>
        <p>Pace is a rate of activity or movement, while heart rate is measured as the number of times that a person's heart contracts over a minute. Pace and heart rate have a positive correlation; higher pace corresponds to higher heart rate.</p>

        <h3>Heart Rate Training Zones</h3>
        <p>Understanding different heart rate zones is crucial for effective training. Each zone targets different physiological adaptations and energy systems:</p>

        <table class="cinfoT" style="margin: 20px 0;">
            <tbody>
                <tr>
                    <td class="cinfoHd">Zone</td>
                    <td class="cinfoHd">% of Max HR</td>
                    <td class="cinfoHd">Purpose</td>
                    <td class="cinfoHd">Benefits</td>
                </tr>
                <tr><td><strong>Zone 1: Recovery</strong></td><td class="cinfoBodL">50-60%</td><td>Active recovery, warm-up</td><td>Promotes blood flow, aids recovery</td></tr>
                <tr><td><strong>Zone 2: Aerobic Base</strong></td><td class="cinfoBodL">60-70%</td><td>Fat burning, base building</td><td>Improves fat oxidation, builds aerobic capacity</td></tr>
                <tr><td><strong>Zone 3: Aerobic</strong></td><td class="cinfoBodL">70-80%</td><td>Aerobic development</td><td>Improves cardiovascular efficiency</td></tr>
                <tr><td><strong>Zone 4: Threshold</strong></td><td class="cinfoBodL">80-90%</td><td>Lactate threshold training</td><td>Increases lactate buffering capacity</td></tr>
                <tr><td><strong>Zone 5: VO2 Max</strong></td><td class="cinfoBodL">90-100%</td><td>Maximum oxygen uptake</td><td>Improves maximum aerobic power</td></tr>
            </tbody>
        </table>
    </div>
    
    <div id="right">
        <form name="calcSearchForm" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td><input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators..."></td>
                        <td><span id="bluebtn">Search</span></td>
                    </tr>
                    <tr>
                        <td colspan="2"><div id="calcSearchOut"></div></td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="#">Fitness and Health Calculators</a>
            </div>
            <div id="occontent">
                <a href="#">BMI</a>
                <a href="#">Calorie</a>
                <a href="#">Body Fat</a>
                <a href="#">BMR</a>
                <a href="#">Macro</a>
                <a href="#">Ideal Weight</a>
                <a href="#">Pregnancy</a>
                <a href="#">Pregnancy Weight Gain</a>
                <a href="#">Pregnancy Conception</a>
                <a href="#">Due Date</a>
                <a href="#">Pace</a>
                <a href="#">More Fitness and Health Calculators</a>
            </div>
            <div id="ocother">
                <a href="#">Financial</a> | 
                <a href="#">Fitness and Health</a> | 
                <a href="#">Math</a> | 
                <a href="#">Other</a>
            </div>
        </div>
    </div>
    
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="#">about us</a> | 
            <a href="#">sitemap</a> | 
            <a href="#">terms of use</a> | 
            <a href="#">privacy policy</a> 
            &nbsp; © 2024 <a href="#">calculator.net</a>
        </div>
    </div>
</div>

<script>
// Basic JavaScript for demo functionality
document.addEventListener('DOMContentLoaded', function() {

    // Tab switching functionality
    const tabLinks = document.querySelectorAll('#topmenu a');
    const timeInput = document.getElementById('calinputtime');
    const distanceInput = document.getElementById('calinputdistance');
    const paceInput = document.getElementById('calinputpace');

    // Create pace input table if it doesn't exist
    if (!paceInput) {
        const paceTable = document.createElement('table');
        paceTable.id = 'calinputpace';
        paceTable.style.display = 'none';
        paceTable.innerHTML = `
            <tbody>
                <tr valign="top">
                    <td width="75">Pace</td>
                    <td>
                        <input type="text" name="cpace" id="cpace" value="00:08:10" class="inlong">
                        <div id="cpaceunithint" style="text-align:right;">hh:mm:ss</div>
                    </td>
                    <td>
                        <select name="cpaceunit" id="cpaceunit">
                            <option value="tpm" selected="">Per Mile</option>
                            <option value="tpk">Per Kilometer</option>
                            <option value="mph">Miles Per Hour</option>
                            <option value="kph">Kilometers Per Hour</option>
                            <option value="mpm">Meters Per Minute</option>
                            <option value="mps">Meters Per Second</option>
                            <option value="ypm">Yards Per Minute</option>
                            <option value="yps">Yards Per Second</option>
                        </select>
                    </td>
                </tr>
            </tbody>
        `;
        distanceInput.parentNode.insertBefore(paceTable, distanceInput.nextSibling);
    }

    tabLinks.forEach(function(link) {
        link.addEventListener('click', function(e) {
            e.preventDefault();

            const tab = this.getAttribute('data-tab');

            // Update active tab
            document.querySelectorAll('#topmenu li').forEach(function(li) {
                li.removeAttribute('id');
            });
            this.parentElement.setAttribute('id', 'menuon');

            // Show/hide appropriate input sections
            const timeTable = document.getElementById('calinputtime');
            const distanceTable = document.getElementById('calinputdistance');
            const paceTable = document.getElementById('calinputpace');

            timeTable.style.display = 'none';
            distanceTable.style.display = 'none';
            paceTable.style.display = 'none';

            switch(tab) {
                case 'pace':
                    timeTable.style.display = 'block';
                    distanceTable.style.display = 'block';
                    break;
                case 'time':
                    paceTable.style.display = 'block';
                    distanceTable.style.display = 'block';
                    break;
                case 'distance':
                    paceTable.style.display = 'block';
                    timeTable.style.display = 'block';
                    break;
            }
        });
    });

    // Event picker functionality
    const eventPicker = document.getElementById('cpickevent');
    const distanceField = document.getElementById('cdistance');
    const distanceUnit = document.getElementById('cdistanceunit');

    if (eventPicker) {
        eventPicker.addEventListener('change', function() {
            const event = this.value;
            const distances = {
                'M': {value: 42.195, unit: 'Kilometers'},
                'HM': {value: 21.0975, unit: 'Kilometers'},
                '1K': {value: 1, unit: 'Kilometers'},
                '5K': {value: 5, unit: 'Kilometers'},
                '10K': {value: 10, unit: 'Kilometers'},
                '1M': {value: 1, unit: 'Miles'},
                '5M': {value: 5, unit: 'Miles'},
                '10M': {value: 10, unit: 'Miles'},
                '800m': {value: 800, unit: 'Meters'},
                '1500m': {value: 1500, unit: 'Meters'}
            };

            if (event && distances[event]) {
                distanceField.value = distances[event].value;
                distanceUnit.value = distances[event].unit;
            }
        });
    }

    // Calculate button functionality (demo)
    const calculateBtn = document.getElementById('calculate-btn');
    if (calculateBtn) {
        calculateBtn.addEventListener('click', function(e) {
            e.preventDefault();
            alert('Demo: Calculation functionality would be implemented here.\n\nThis would connect to the WordPress AJAX handler to perform actual calculations.');
        });
    }

    // Clear button functionality
    const clearBtn = document.getElementById('clear-btn');
    if (clearBtn) {
        clearBtn.addEventListener('click', function() {
            document.getElementById('pace-calculator-form').reset();
            document.getElementById('cpickevent').value = '';
        });
    }

    // Search functionality (demo)
    const searchBtn = document.getElementById('bluebtn');
    if (searchBtn) {
        searchBtn.addEventListener('click', function() {
            const searchTerm = document.getElementById('calcSearchTerm').value;
            if (searchTerm.trim()) {
                alert('Demo: Search functionality for "' + searchTerm + '" would be implemented here.');
            }
        });
    }

});
</script>

</body>
</html>
