<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.calibration-interface {
    background: linear-gradient(135deg, #795548 0%, #5d4037 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.calibration-input {
    background-color: #efebe9;
    border: 2px solid #795548;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.calibration-result {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.accuracy-meter {
    background-color: #e0e0e0;
    border-radius: 10px;
    height: 20px;
    margin: 10px 0;
    overflow: hidden;
}
.accuracy-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
}
.excellent { background: linear-gradient(90deg, #4caf50, #8bc34a); }
.good { background: linear-gradient(90deg, #8bc34a, #cddc39); }
.fair { background: linear-gradient(90deg, #ff9800, #ffc107); }
.poor { background: linear-gradient(90deg, #f44336, #ff5722); }
.calibration-history {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
    max-height: 300px;
    overflow-y: auto;
}
.calibration-entry {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 80px;
    gap: 10px;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    background-color: #f5f5f5;
    border-radius: 8px;
}
</style>

<h1>Dexcom G7 Calibration Calculator</h1>
<p>Calculate optimal calibration timing and accuracy for your Dexcom G7 sensor. While G7 is factory-calibrated, this calculator helps determine when user calibration might improve accuracy and tracks calibration history for better glucose monitoring.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="calibrationform" id="calibration-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Calibration Calculator</a></li>
                <li><a href="#" data-tab="history">Calibration History</a></li>
                <li><a href="#" data-tab="guidelines">Guidelines</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="calibration-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">🎯 G7 Calibration Calculator</h3>
                
                <div class="calibration-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">📊 Current Readings</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="dexcom-reading"><strong>Dexcom G7 Reading:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <input type="number" id="dexcom-reading" value="145" min="40" max="400" class="innormal" style="width: 100%;">
                                <span style="margin-left: 5px;">mg/dL</span>
                            </div>
                        </div>
                        
                        <div>
                            <label for="fingerstick-reading"><strong>Fingerstick Reading:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <input type="number" id="fingerstick-reading" value="155" min="40" max="400" class="innormal" style="width: 100%;">
                                <span style="margin-left: 5px;">mg/dL</span>
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="sensor-age"><strong>Sensor Age:</strong></label>
                            <select id="sensor-age" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="1">Day 1 (0-24 hours)</option>
                                <option value="2">Day 2 (24-48 hours)</option>
                                <option value="3" selected>Day 3 (48-72 hours)</option>
                                <option value="4">Day 4 (72-96 hours)</option>
                                <option value="5">Day 5 (96-120 hours)</option>
                                <option value="6">Day 6 (120-144 hours)</option>
                                <option value="7">Day 7 (144-168 hours)</option>
                                <option value="8">Day 8 (168-192 hours)</option>
                                <option value="9">Day 9 (192-216 hours)</option>
                                <option value="10">Day 10 (216-240 hours)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="glucose-trend"><strong>Current Glucose Trend:</strong></label>
                            <select id="glucose-trend" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="stable" selected>Stable (→)</option>
                                <option value="rising-slow">Rising slowly (↗)</option>
                                <option value="rising-fast">Rising fast (↗↗)</option>
                                <option value="falling-slow">Falling slowly (↘)</option>
                                <option value="falling-fast">Falling fast (↘↘)</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #efebe9; border: 2px solid #795548; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">⚙️ Calibration Settings</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="last-calibration"><strong>Last Calibration:</strong></label>
                            <select id="last-calibration" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="never" selected>Never calibrated</option>
                                <option value="1">1 hour ago</option>
                                <option value="6">6 hours ago</option>
                                <option value="12">12 hours ago</option>
                                <option value="24">24 hours ago</option>
                                <option value="48">48+ hours ago</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="calibration-reason"><strong>Calibration Reason:</strong></label>
                            <select id="calibration-reason" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="accuracy" selected>Improve accuracy</option>
                                <option value="symptoms">Symptoms don't match reading</option>
                                <option value="large-difference">Large difference detected</option>
                                <option value="routine">Routine calibration</option>
                                <option value="troubleshooting">Troubleshooting</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="fingerstick-quality"><strong>Fingerstick Quality:</strong></label>
                            <select id="fingerstick-quality" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="excellent" selected>Excellent (clean hands, good sample)</option>
                                <option value="good">Good (clean hands)</option>
                                <option value="fair">Fair (some concerns)</option>
                                <option value="poor">Poor (dirty hands, small sample)</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #f1f8e9; border: 2px solid #4caf50; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">📝 Calibration History</h4>
                    
                    <div style="margin-bottom: 15px;">
                        <div class="calibration-entry" style="background-color: #795548; color: white; font-weight: bold;">
                            <div>Dexcom Reading</div>
                            <div>Fingerstick Reading</div>
                            <div>Time</div>
                            <div>Action</div>
                        </div>
                        
                        <div id="calibrations-container">
                            <!-- Calibration entries will be added here -->
                        </div>
                        
                        <div style="text-align: center; margin-top: 15px;">
                            <button type="button" id="add-calibration-btn" style="padding: 10px 20px; background-color: #4caf50; color: white; border: none; border-radius: 8px; cursor: pointer;">
                                ➕ Add to History
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Calibration" id="calculate-calibration-btn" style="padding: 15px 30px; font-size: 18px; background-color: #795548; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Clear History" id="clear-calibration-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> G7 is factory-calibrated and typically doesn't require user calibration. Only calibrate when necessary and following proper procedures.
        </div>
        
        <div id="calibration-results" style="display: none; margin-top: 30px;">
            <div class="calibration-result">
                <h3 style="margin-bottom: 20px;">🎯 Calibration Analysis</h3>
                <div id="calibration-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-calibration-btn');
    const clearBtn = document.getElementById('clear-calibration-btn');
    const addCalibrationBtn = document.getElementById('add-calibration-btn');
    const resultsDiv = document.getElementById('calibration-results');
    const resultsContent = document.getElementById('calibration-results-content');
    const calibrationsContainer = document.getElementById('calibrations-container');
    
    let calibrationHistory = [];
    
    addCalibrationBtn.addEventListener('click', function() {
        addCalibrationToHistory();
    });
    
    calculateBtn.addEventListener('click', function() {
        const dexcomReading = parseFloat(document.getElementById('dexcom-reading').value) || 0;
        const fingerstickReading = parseFloat(document.getElementById('fingerstick-reading').value) || 0;
        const sensorAge = parseInt(document.getElementById('sensor-age').value) || 3;
        const glucoseTrend = document.getElementById('glucose-trend').value;
        const lastCalibration = document.getElementById('last-calibration').value;
        const calibrationReason = document.getElementById('calibration-reason').value;
        const fingerstickQuality = document.getElementById('fingerstick-quality').value;
        
        if (dexcomReading <= 0 || fingerstickReading <= 0) {
            alert('Please enter valid glucose readings for both Dexcom and fingerstick.');
            return;
        }
        
        const calibrationAnalysis = calculateCalibration(dexcomReading, fingerstickReading, sensorAge, 
            glucoseTrend, lastCalibration, calibrationReason, fingerstickQuality, calibrationHistory);
        
        displayCalibrationResults(calibrationAnalysis);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        calibrationHistory = [];
        updateCalibrationHistoryDisplay();
        resultsDiv.style.display = 'none';
    });
    
    function addCalibrationToHistory() {
        const dexcomReading = parseFloat(document.getElementById('dexcom-reading').value);
        const fingerstickReading = parseFloat(document.getElementById('fingerstick-reading').value);
        
        if (dexcomReading && fingerstickReading) {
            const now = new Date();
            const timeString = now.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'});
            
            calibrationHistory.push({
                dexcom: dexcomReading,
                fingerstick: fingerstickReading,
                time: timeString,
                difference: Math.abs(dexcomReading - fingerstickReading),
                percentDifference: Math.abs((dexcomReading - fingerstickReading) / fingerstickReading) * 100
            });
            
            updateCalibrationHistoryDisplay();
        } else {
            alert('Please enter both Dexcom and fingerstick readings before adding to history.');
        }
    }
    
    function updateCalibrationHistoryDisplay() {
        calibrationsContainer.innerHTML = '';
        
        calibrationHistory.forEach((cal, index) => {
            const entryDiv = document.createElement('div');
            entryDiv.className = 'calibration-entry';
            
            entryDiv.innerHTML = `
                <div>${cal.dexcom} mg/dL</div>
                <div>${cal.fingerstick} mg/dL</div>
                <div>${cal.time}</div>
                <button type="button" onclick="removeCalibration(${index})" style="background: #f44336; color: white; border: none; border-radius: 4px; padding: 5px;">❌</button>
            `;
            
            calibrationsContainer.appendChild(entryDiv);
        });
    }
    
    window.removeCalibration = function(index) {
        calibrationHistory.splice(index, 1);
        updateCalibrationHistoryDisplay();
    };
    
    function calculateCalibration(dexcomReading, fingerstickReading, sensorAge, glucoseTrend, 
                                lastCalibration, calibrationReason, fingerstickQuality, history) {
        
        // Calculate difference and percentage difference
        const difference = Math.abs(dexcomReading - fingerstickReading);
        const percentDifference = Math.abs((dexcomReading - fingerstickReading) / fingerstickReading) * 100;
        
        // Determine if calibration is recommended
        let calibrationRecommended = false;
        let recommendationReason = '';
        
        // G7 calibration criteria (more restrictive than G6)
        if (percentDifference > 20 && difference > 20) {
            calibrationRecommended = true;
            recommendationReason = 'Large difference detected (>20% and >20 mg/dL)';
        } else if (sensorAge <= 2 && percentDifference > 15) {
            calibrationRecommended = true;
            recommendationReason = 'Early sensor period with significant difference';
        } else if (calibrationReason === 'symptoms' && percentDifference > 15) {
            calibrationRecommended = true;
            recommendationReason = 'Symptoms don\'t match readings';
        }
        
        // Factors that discourage calibration
        const discouragingFactors = [];
        
        if (glucoseTrend !== 'stable') {
            discouragingFactors.push('Glucose is not stable');
        }
        
        if (fingerstickQuality === 'poor' || fingerstickQuality === 'fair') {
            discouragingFactors.push('Fingerstick quality concerns');
        }
        
        if (lastCalibration !== 'never' && parseInt(lastCalibration) < 12) {
            discouragingFactors.push('Recent calibration performed');
        }
        
        if (sensorAge >= 8) {
            discouragingFactors.push('Sensor nearing end of life');
        }
        
        // Override recommendation if too many discouraging factors
        if (discouragingFactors.length >= 2 && !calibrationRecommended) {
            calibrationRecommended = false;
        }
        
        // Calculate accuracy metrics from history
        let historicalMARD = 0;
        let accuracyTrend = 'stable';
        
        if (history.length > 0) {
            historicalMARD = history.reduce((sum, cal) => sum + cal.percentDifference, 0) / history.length;
            
            if (history.length >= 3) {
                const recent = history.slice(-3);
                const older = history.slice(0, -3);
                
                if (older.length > 0) {
                    const recentMARD = recent.reduce((sum, cal) => sum + cal.percentDifference, 0) / recent.length;
                    const olderMARD = older.reduce((sum, cal) => sum + cal.percentDifference, 0) / older.length;
                    
                    if (recentMARD < olderMARD - 2) {
                        accuracyTrend = 'improving';
                    } else if (recentMARD > olderMARD + 2) {
                        accuracyTrend = 'declining';
                    }
                }
            }
        }
        
        // Determine accuracy grade
        let accuracyGrade = 'Poor';
        let gradeClass = 'poor';
        
        if (percentDifference <= 10) {
            accuracyGrade = 'Excellent';
            gradeClass = 'excellent';
        } else if (percentDifference <= 15) {
            accuracyGrade = 'Good';
            gradeClass = 'good';
        } else if (percentDifference <= 20) {
            accuracyGrade = 'Fair';
            gradeClass = 'fair';
        }
        
        // Generate specific recommendations
        const recommendations = generateCalibrationRecommendations(calibrationRecommended, 
            discouragingFactors, percentDifference, sensorAge, glucoseTrend);
        
        return {
            dexcomReading: dexcomReading,
            fingerstickReading: fingerstickReading,
            difference: difference,
            percentDifference: percentDifference,
            calibrationRecommended: calibrationRecommended,
            recommendationReason: recommendationReason,
            discouragingFactors: discouragingFactors,
            accuracyGrade: accuracyGrade,
            gradeClass: gradeClass,
            historicalMARD: historicalMARD,
            accuracyTrend: accuracyTrend,
            recommendations: recommendations,
            settings: {
                sensorAge: sensorAge,
                glucoseTrend: glucoseTrend,
                lastCalibration: lastCalibration,
                calibrationReason: calibrationReason,
                fingerstickQuality: fingerstickQuality
            }
        };
    }
    
    function generateCalibrationRecommendations(recommended, discouragingFactors, percentDiff, 
                                              sensorAge, glucoseTrend) {
        const recommendations = [];
        
        if (recommended) {
            recommendations.push('Calibration is recommended based on current readings');
            recommendations.push('Ensure hands are clean and glucose is stable before calibrating');
            
            if (glucoseTrend !== 'stable') {
                recommendations.push('Wait for glucose to stabilize before calibrating');
            }
        } else {
            recommendations.push('Calibration is not recommended at this time');
            
            if (percentDiff <= 15) {
                recommendations.push('Current accuracy is acceptable for G7 sensor');
            }
        }
        
        if (discouragingFactors.length > 0) {
            recommendations.push('Consider addressing these factors before calibrating:');
            discouragingFactors.forEach(factor => {
                recommendations.push(`• ${factor}`);
            });
        }
        
        if (sensorAge <= 2) {
            recommendations.push('Early sensor period - accuracy typically improves after day 2');
        }
        
        if (sensorAge >= 8) {
            recommendations.push('Sensor is nearing end of life - consider replacement soon');
        }
        
        recommendations.push('Remember: G7 is factory-calibrated and rarely needs user calibration');
        
        return recommendations;
    }
    
    function displayCalibrationResults(data) {
        const trendNames = {
            stable: 'Stable',
            'rising-slow': 'Rising Slowly',
            'rising-fast': 'Rising Fast',
            'falling-slow': 'Falling Slowly',
            'falling-fast': 'Falling Fast'
        };
        
        let html = `
            <div style="margin-bottom: 25px;">
                <div style="font-size: 36px; font-weight: bold; margin-bottom: 10px;">
                    ${data.percentDifference.toFixed(1)}%
                </div>
                <div style="font-size: 18px; margin-bottom: 15px;">
                    Difference between readings
                </div>
                <div class="accuracy-meter">
                    <div class="accuracy-fill ${data.gradeClass}" style="width: ${Math.min(100, (30 - data.percentDifference) / 30 * 100)}%;"></div>
                </div>
                <div style="font-size: 16px; margin-top: 10px;">
                    Accuracy Grade: <strong>${data.accuracyGrade}</strong>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Dexcom Reading</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #795548;">
                        ${data.dexcomReading}
                    </div>
                    <div style="font-size: 12px; color: #666;">mg/dL</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Fingerstick Reading</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #ff9800;">
                        ${data.fingerstickReading}
                    </div>
                    <div style="font-size: 12px; color: #666;">mg/dL</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Absolute Difference</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #2196f3;">
                        ${data.difference.toFixed(1)}
                    </div>
                    <div style="font-size: 12px; color: #666;">mg/dL</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Calibration Status</h4>
                    <div style="font-size: 18px; font-weight: bold; color: ${data.calibrationRecommended ? '#4caf50' : '#f44336'};">
                        ${data.calibrationRecommended ? 'Recommended' : 'Not Recommended'}
                    </div>
                    <div style="font-size: 12px; color: #666;">Based on analysis</div>
                </div>
            </div>
            
            <div class="calibration-history">
                <h4 style="color: #795548; margin-bottom: 20px;">📊 Calibration Analysis</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <h5>Current Reading Analysis:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Sensor Age:</strong> Day ${data.settings.sensorAge}</li>
                            <li><strong>Glucose Trend:</strong> ${trendNames[data.settings.glucoseTrend]}</li>
                            <li><strong>Fingerstick Quality:</strong> ${data.settings.fingerstickQuality}</li>
                            <li><strong>Last Calibration:</strong> ${data.settings.lastCalibration === 'never' ? 'Never' : data.settings.lastCalibration + ' hours ago'}</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5>Historical Performance:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Historical MARD:</strong> ${data.historicalMARD > 0 ? data.historicalMARD.toFixed(1) + '%' : 'No history'}</li>
                            <li><strong>Accuracy Trend:</strong> ${data.accuracyTrend}</li>
                            <li><strong>Total Calibrations:</strong> ${calibrationHistory.length}</li>
                            <li><strong>Recommendation Reason:</strong> ${data.recommendationReason || 'Within acceptable range'}</li>
                        </ul>
                    </div>
                </div>
                
                <div style="background-color: ${data.calibrationRecommended ? '#e8f5e9' : '#fff3e0'}; border-radius: 8px; padding: 15px; margin: 15px 0;">
                    <h5 style="color: ${data.calibrationRecommended ? '#4caf50' : '#ff9800'};">
                        ${data.calibrationRecommended ? '✅ Calibration Recommended' : '⚠️ Calibration Not Recommended'}
                    </h5>
                    <ul style="margin: 10px 0; color: #2c3e50;">
                        ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
                
                ${data.discouragingFactors.length > 0 ? `
                <div style="background-color: #ffebee; border-radius: 8px; padding: 15px; margin: 15px 0;">
                    <h5 style="color: #f44336;">⚠️ Factors Discouraging Calibration:</h5>
                    <ul style="margin: 10px 0; color: #2c3e50;">
                        ${data.discouragingFactors.map(factor => `<li>${factor}</li>`).join('')}
                    </ul>
                </div>
                ` : ''}
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 G7 Calibration Guidelines:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Factory Calibrated:</strong> G7 typically doesn't need user calibration</li>
                    <li><strong>When to Calibrate:</strong> Only when readings differ significantly (>20%)</li>
                    <li><strong>Stable Glucose:</strong> Only calibrate when glucose is stable</li>
                    <li><strong>Clean Hands:</strong> Wash hands thoroughly before fingerstick</li>
                    <li><strong>Quality Sample:</strong> Ensure adequate blood sample for accurate reading</li>
                    <li><strong>Timing:</strong> Wait at least 12 hours between calibrations</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
