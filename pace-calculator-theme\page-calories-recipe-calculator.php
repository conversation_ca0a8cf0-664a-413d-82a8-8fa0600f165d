<?php
/*
Template Name: Calories Recipe Calculator
*/

get_header(); ?>

<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
</style>

<div style="text-align: center; margin: 20px 0;">
    <img src="https://d26tpo4cm8sb6k.cloudfront.net/img/svg/calculator-white.svg" alt="Calculator.net" width="208" height="22" style="max-width: 100%; height: auto;">
</div>

<h1>Calories Recipe Calculator</h1>
<p>Use the following calculator to determine the total calories and nutritional content of your recipes. This tool helps you track calories per serving, analyze nutritional breakdown, and make informed decisions about your meal planning and dietary goals.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="recipeform" id="recipe-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="recipe">Recipe Calculator</a></li>
                <li><a href="#" data-tab="ingredient">Ingredient Database</a></li>
                <li><a href="#" data-tab="nutrition">Nutrition Analysis</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 8px 5px;">
            <table id="recipe-input" style="display: block;">
                <tbody>
                    <tr>
                        <td width="120">Recipe Name:</td>
                        <td><input type="text" name="recipename" id="recipename" value="My Recipe" class="inlongest"></td>
                    </tr>
                    <tr>
                        <td width="120">Total Servings:</td>
                        <td><input type="number" name="servings" id="servings" value="4" class="innormal" min="1" max="50"></td>
                    </tr>
                </tbody>
            </table>
            
            <h3>Add Ingredients:</h3>
            <table id="ingredient-input" style="width: 100%; margin: 10px 0;">
                <tbody>
                    <tr style="background-color: #E0F0FE;">
                        <td width="30%"><strong>Ingredient</strong></td>
                        <td width="15%"><strong>Amount</strong></td>
                        <td width="15%"><strong>Unit</strong></td>
                        <td width="15%"><strong>Calories/Unit</strong></td>
                        <td width="15%"><strong>Total Calories</strong></td>
                        <td width="10%"><strong>Action</strong></td>
                    </tr>
                    <tr id="ingredient-row-1">
                        <td><input type="text" name="ingredient1" value="Chicken Breast" class="inlonger"></td>
                        <td><input type="number" name="amount1" value="200" class="innormal" step="0.1"></td>
                        <td>
                            <select name="unit1" class="innormal">
                                <option value="grams" selected>grams</option>
                                <option value="ounces">ounces</option>
                                <option value="cups">cups</option>
                                <option value="tablespoons">tablespoons</option>
                                <option value="teaspoons">teaspoons</option>
                                <option value="pieces">pieces</option>
                            </select>
                        </td>
                        <td><input type="number" name="calories1" value="1.65" class="innormal" step="0.01"></td>
                        <td><span id="total-calories-1" class="total-calories">330</span></td>
                        <td><input type="button" value="Remove" onclick="removeIngredient(1)" style="font-size: 12px; padding: 3px 6px;"></td>
                    </tr>
                    <tr id="ingredient-row-2">
                        <td><input type="text" name="ingredient2" value="Brown Rice" class="inlonger"></td>
                        <td><input type="number" name="amount2" value="100" class="innormal" step="0.1"></td>
                        <td>
                            <select name="unit2" class="innormal">
                                <option value="grams" selected>grams</option>
                                <option value="ounces">ounces</option>
                                <option value="cups">cups</option>
                                <option value="tablespoons">tablespoons</option>
                                <option value="teaspoons">teaspoons</option>
                                <option value="pieces">pieces</option>
                            </select>
                        </td>
                        <td><input type="number" name="calories2" value="1.11" class="innormal" step="0.01"></td>
                        <td><span id="total-calories-2" class="total-calories">111</span></td>
                        <td><input type="button" value="Remove" onclick="removeIngredient(2)" style="font-size: 12px; padding: 3px 6px;"></td>
                    </tr>
                    <tr id="ingredient-row-3">
                        <td><input type="text" name="ingredient3" value="Olive Oil" class="inlonger"></td>
                        <td><input type="number" name="amount3" value="1" class="innormal" step="0.1"></td>
                        <td>
                            <select name="unit3" class="innormal">
                                <option value="grams">grams</option>
                                <option value="ounces">ounces</option>
                                <option value="cups">cups</option>
                                <option value="tablespoons" selected>tablespoons</option>
                                <option value="teaspoons">teaspoons</option>
                                <option value="pieces">pieces</option>
                            </select>
                        </td>
                        <td><input type="number" name="calories3" value="119" class="innormal" step="0.01"></td>
                        <td><span id="total-calories-3" class="total-calories">119</span></td>
                        <td><input type="button" value="Remove" onclick="removeIngredient(3)" style="font-size: 12px; padding: 3px 6px;"></td>
                    </tr>
                </tbody>
            </table>
            
            <table width="100%" style="padding-top: 10px;">
                <tbody>
                    <tr>
                        <td align="left">
                            <input type="button" value="Add Ingredient" id="add-ingredient-btn">
                        </td>
                        <td align="center">
                            <input type="submit" name="x" value="Calculate Recipe" id="calculate-recipe-btn">
                            <input type="button" value="Clear All" id="clear-recipe-btn">
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="smalltext">Note: Calorie values are per unit specified. For example, if you select "grams" and enter 1.65 calories, this means 1.65 calories per gram. Adjust the calories per unit based on your ingredient's nutritional information.</div>
        
        <div id="recipe-results" style="display: none; margin-top: 20px; padding: 15px; background-color: #f0f8ff; border: 1px solid #ccc; border-radius: 5px;">
            <h3>Recipe Nutrition Summary:</h3>
            <div id="recipe-results-content"></div>
        </div>
    </form>
</div>

<script>
// WordPress-specific JavaScript for Calories Recipe Calculator
let ingredientCount = 3;

function addIngredient() {
    ingredientCount++;
    const table = document.getElementById('ingredient-input').getElementsByTagName('tbody')[0];
    const newRow = table.insertRow();
    newRow.id = 'ingredient-row-' + ingredientCount;
    
    newRow.innerHTML = `
        <td><input type="text" name="ingredient${ingredientCount}" class="inlonger" placeholder="Ingredient name"></td>
        <td><input type="number" name="amount${ingredientCount}" class="innormal" step="0.1" placeholder="Amount"></td>
        <td>
            <select name="unit${ingredientCount}" class="innormal">
                <option value="grams">grams</option>
                <option value="ounces">ounces</option>
                <option value="cups">cups</option>
                <option value="tablespoons">tablespoons</option>
                <option value="teaspoons">teaspoons</option>
                <option value="pieces">pieces</option>
            </select>
        </td>
        <td><input type="number" name="calories${ingredientCount}" class="innormal" step="0.01" placeholder="Cal/unit"></td>
        <td><span id="total-calories-${ingredientCount}" class="total-calories">0</span></td>
        <td><input type="button" value="Remove" onclick="removeIngredient(${ingredientCount})" style="font-size: 12px; padding: 3px 6px;"></td>
    `;
    
    // Add event listeners for automatic calculation
    const amountInput = newRow.querySelector(`input[name="amount${ingredientCount}"]`);
    const caloriesInput = newRow.querySelector(`input[name="calories${ingredientCount}"]`);
    
    amountInput.addEventListener('input', () => updateIngredientCalories(ingredientCount));
    caloriesInput.addEventListener('input', () => updateIngredientCalories(ingredientCount));
}

function removeIngredient(id) {
    const row = document.getElementById('ingredient-row-' + id);
    if (row) {
        row.remove();
    }
}

function updateIngredientCalories(id) {
    const amount = parseFloat(document.querySelector(`input[name="amount${id}"]`).value) || 0;
    const calories = parseFloat(document.querySelector(`input[name="calories${id}"]`).value) || 0;
    const total = amount * calories;
    
    document.getElementById(`total-calories-${id}`).textContent = total.toFixed(1);
}

// WordPress-specific initialization
jQuery(document).ready(function($) {
    // Add ingredient button
    $('#add-ingredient-btn').on('click', addIngredient);
    
    // Calculate recipe button
    $('#calculate-recipe-btn').on('click', function(e) {
        e.preventDefault();
        calculateRecipe();
    });
    
    // Clear recipe button
    $('#clear-recipe-btn').on('click', function() {
        $('#recipe-calculator-form')[0].reset();
        $('#recipe-results').hide();
    });
    
    // Auto-calculate ingredient totals for pre-filled values
    for (let i = 1; i <= 3; i++) {
        $(`input[name="amount${i}"]`).on('input', () => updateIngredientCalories(i));
        $(`input[name="calories${i}"]`).on('input', () => updateIngredientCalories(i));
        updateIngredientCalories(i); // Initial calculation
    }
});

function calculateRecipe() {
    const recipeName = document.getElementById('recipename').value;
    const servings = parseInt(document.getElementById('servings').value) || 1;
    
    let totalCalories = 0;
    
    // Calculate totals from all ingredients
    const totalSpans = document.querySelectorAll('.total-calories');
    totalSpans.forEach(span => {
        totalCalories += parseFloat(span.textContent) || 0;
    });
    
    const caloriesPerServing = totalCalories / servings;
    
    // Display results
    const resultsDiv = document.getElementById('recipe-results-content');
    if (resultsDiv) {
        resultsDiv.innerHTML = `
            <table class="cinfoT" style="margin-top: 10px;">
                <tbody>
                    <tr>
                        <td class="cinfoHd">Recipe: ${recipeName}</td>
                        <td class="cinfoHd">Total</td>
                        <td class="cinfoHd">Per Serving</td>
                    </tr>
                    <tr>
                        <td><strong>Calories</strong></td>
                        <td class="cinfoBodL">${totalCalories.toFixed(0)}</td>
                        <td class="cinfoBodL">${caloriesPerServing.toFixed(0)}</td>
                    </tr>
                    <tr>
                        <td><strong>Servings</strong></td>
                        <td class="cinfoBodL">${servings}</td>
                        <td class="cinfoBodL">1</td>
                    </tr>
                </tbody>
            </table>
            <div style="margin-top: 15px;">
                <p><strong>Nutrition Tips:</strong></p>
                <ul style="text-align: left; margin-left: 20px;">
                    <li>This recipe provides ${caloriesPerServing.toFixed(0)} calories per serving</li>
                    <li>For weight loss: Aim for 300-500 calories per meal</li>
                    <li>For weight maintenance: Aim for 400-600 calories per meal</li>
                    <li>For weight gain: Aim for 500-800 calories per meal</li>
                </ul>
            </div>
        `;
        
        document.getElementById('recipe-results').style.display = 'block';
    }
}
</script>

<?php get_footer(); ?>
