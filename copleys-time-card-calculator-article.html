<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.timecard-interface {
    background: linear-gradient(135deg, #607d8b 0%, #455a64 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.time-input {
    background-color: #eceff1;
    border: 2px solid #607d8b;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.timecard-result {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.day-row {
    display: grid;
    grid-template-columns: 100px 1fr 1fr 1fr 1fr 100px;
    gap: 10px;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    background-color: #f5f5f5;
    border-radius: 8px;
}
.copleys-brand {
    background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    color: white;
    text-align: center;
}
.overtime-breakdown {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.pay-summary {
    background-color: #e8f5e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
</style>

<h1>Copley's Time Card Calculator</h1>
<p>Calculate employee work hours and pay with Copley's professional time card calculator. Track daily hours, overtime, breaks, and generate accurate payroll calculations for weekly and bi-weekly pay periods.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="timecardform" id="timecard-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Time Card</a></li>
                <li><a href="#" data-tab="payroll">Payroll</a></li>
                <li><a href="#" data-tab="reports">Reports</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="copleys-brand">
                <h3 style="margin: 0;">⏰ Copley's Time & Attendance</h3>
                <div style="font-size: 14px; margin-top: 5px;">Professional Time Card Calculator</div>
            </div>
            
            <div class="timecard-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">🕐 Weekly Time Card Calculator</h3>
                
                <div class="pay-summary">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">💰 Employee Information</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="employee-name"><strong>Employee Name:</strong></label>
                            <input type="text" id="employee-name" value="John Doe" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                        
                        <div>
                            <label for="hourly-rate"><strong>Hourly Rate:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="hourly-rate" value="15.00" min="7.25" max="100" step="0.25" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="overtime-rate"><strong>Overtime Multiplier:</strong></label>
                            <select id="overtime-rate" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="1.5" selected>1.5x (Time and a Half)</option>
                                <option value="2.0">2.0x (Double Time)</option>
                                <option value="1.0">1.0x (No Overtime)</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="time-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">📅 Weekly Time Entry</h4>
                    
                    <div style="margin-bottom: 15px;">
                        <div class="day-row" style="background-color: #607d8b; color: white; font-weight: bold;">
                            <div>Day</div>
                            <div>Clock In</div>
                            <div>Lunch Out</div>
                            <div>Lunch In</div>
                            <div>Clock Out</div>
                            <div>Total Hours</div>
                        </div>
                        
                        <div class="day-row">
                            <div><strong>Monday</strong></div>
                            <div><input type="time" id="mon-in" value="08:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="mon-lunch-out" value="12:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="mon-lunch-in" value="13:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="mon-out" value="17:00" class="innormal" style="width: 100%;"></div>
                            <div id="mon-total" style="font-weight: bold;">8.00</div>
                        </div>
                        
                        <div class="day-row">
                            <div><strong>Tuesday</strong></div>
                            <div><input type="time" id="tue-in" value="08:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="tue-lunch-out" value="12:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="tue-lunch-in" value="13:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="tue-out" value="17:00" class="innormal" style="width: 100%;"></div>
                            <div id="tue-total" style="font-weight: bold;">8.00</div>
                        </div>
                        
                        <div class="day-row">
                            <div><strong>Wednesday</strong></div>
                            <div><input type="time" id="wed-in" value="08:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="wed-lunch-out" value="12:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="wed-lunch-in" value="13:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="wed-out" value="17:00" class="innormal" style="width: 100%;"></div>
                            <div id="wed-total" style="font-weight: bold;">8.00</div>
                        </div>
                        
                        <div class="day-row">
                            <div><strong>Thursday</strong></div>
                            <div><input type="time" id="thu-in" value="08:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="thu-lunch-out" value="12:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="thu-lunch-in" value="13:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="thu-out" value="17:00" class="innormal" style="width: 100%;"></div>
                            <div id="thu-total" style="font-weight: bold;">8.00</div>
                        </div>
                        
                        <div class="day-row">
                            <div><strong>Friday</strong></div>
                            <div><input type="time" id="fri-in" value="08:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="fri-lunch-out" value="12:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="fri-lunch-in" value="13:00" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="fri-out" value="17:00" class="innormal" style="width: 100%;"></div>
                            <div id="fri-total" style="font-weight: bold;">8.00</div>
                        </div>
                        
                        <div class="day-row">
                            <div><strong>Saturday</strong></div>
                            <div><input type="time" id="sat-in" value="" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="sat-lunch-out" value="" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="sat-lunch-in" value="" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="sat-out" value="" class="innormal" style="width: 100%;"></div>
                            <div id="sat-total" style="font-weight: bold;">0.00</div>
                        </div>
                        
                        <div class="day-row">
                            <div><strong>Sunday</strong></div>
                            <div><input type="time" id="sun-in" value="" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="sun-lunch-out" value="" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="sun-lunch-in" value="" class="innormal" style="width: 100%;"></div>
                            <div><input type="time" id="sun-out" value="" class="innormal" style="width: 100%;"></div>
                            <div id="sun-total" style="font-weight: bold;">0.00</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Time Card" id="calculate-timecard-btn" style="padding: 15px 30px; font-size: 18px; background-color: #607d8b; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Auto-Calculate" id="auto-calculate-btn" style="padding: 15px 30px; font-size: 18px; background-color: #4caf50; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Clear All" id="clear-timecard-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> This calculator follows standard labor laws. Consult with HR or payroll professionals for specific regulations.
        </div>
        
        <div id="timecard-results" style="display: none; margin-top: 30px;">
            <div class="timecard-result">
                <h3 style="margin-bottom: 20px;">💰 Weekly Payroll Summary</h3>
                <div id="timecard-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-timecard-btn');
    const autoCalculateBtn = document.getElementById('auto-calculate-btn');
    const clearBtn = document.getElementById('clear-timecard-btn');
    const resultsDiv = document.getElementById('timecard-results');
    const resultsContent = document.getElementById('timecard-results-content');
    
    const days = ['mon', 'tue', 'wed', 'thu', 'fri', 'sat', 'sun'];
    
    // Auto-calculate when time inputs change
    days.forEach(day => {
        ['in', 'lunch-out', 'lunch-in', 'out'].forEach(field => {
            const input = document.getElementById(`${day}-${field}`);
            if (input) {
                input.addEventListener('change', autoCalculateDay);
            }
        });
    });
    
    autoCalculateBtn.addEventListener('click', function() {
        days.forEach(day => {
            calculateDayHours(day);
        });
    });
    
    calculateBtn.addEventListener('click', function() {
        const employeeName = document.getElementById('employee-name').value || 'Employee';
        const hourlyRate = parseFloat(document.getElementById('hourly-rate').value) || 15.00;
        const overtimeRate = parseFloat(document.getElementById('overtime-rate').value) || 1.5;
        
        // Calculate all day hours first
        days.forEach(day => {
            calculateDayHours(day);
        });
        
        const timecardData = calculateWeeklyPay(employeeName, hourlyRate, overtimeRate);
        displayTimecardResults(timecardData);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('timecard-calculator-form').reset();
        days.forEach(day => {
            document.getElementById(`${day}-total`).textContent = '0.00';
        });
        resultsDiv.style.display = 'none';
    });
    
    function autoCalculateDay(event) {
        const input = event.target;
        const day = input.id.split('-')[0];
        calculateDayHours(day);
    }
    
    function calculateDayHours(day) {
        const clockIn = document.getElementById(`${day}-in`).value;
        const lunchOut = document.getElementById(`${day}-lunch-out`).value;
        const lunchIn = document.getElementById(`${day}-lunch-in`).value;
        const clockOut = document.getElementById(`${day}-out`).value;
        
        if (!clockIn || !clockOut) {
            document.getElementById(`${day}-total`).textContent = '0.00';
            return 0;
        }
        
        const startTime = timeToMinutes(clockIn);
        const endTime = timeToMinutes(clockOut);
        
        let totalMinutes = endTime - startTime;
        
        // Subtract lunch break if both lunch times are provided
        if (lunchOut && lunchIn) {
            const lunchStart = timeToMinutes(lunchOut);
            const lunchEnd = timeToMinutes(lunchIn);
            const lunchDuration = lunchEnd - lunchStart;
            totalMinutes -= lunchDuration;
        }
        
        const totalHours = Math.max(0, totalMinutes / 60);
        document.getElementById(`${day}-total`).textContent = totalHours.toFixed(2);
        
        return totalHours;
    }
    
    function timeToMinutes(timeString) {
        const [hours, minutes] = timeString.split(':').map(Number);
        return hours * 60 + minutes;
    }
    
    function calculateWeeklyPay(employeeName, hourlyRate, overtimeMultiplier) {
        let totalHours = 0;
        const dailyHours = {};
        
        days.forEach(day => {
            const hours = parseFloat(document.getElementById(`${day}-total`).textContent) || 0;
            dailyHours[day] = hours;
            totalHours += hours;
        });
        
        // Calculate regular and overtime hours
        const regularHours = Math.min(totalHours, 40);
        const overtimeHours = Math.max(0, totalHours - 40);
        
        // Calculate pay
        const regularPay = regularHours * hourlyRate;
        const overtimePay = overtimeHours * hourlyRate * overtimeMultiplier;
        const grossPay = regularPay + overtimePay;
        
        // Estimate deductions (simplified)
        const federalTax = grossPay * 0.12; // Approximate federal tax
        const stateTax = grossPay * 0.05; // Approximate state tax
        const socialSecurity = grossPay * 0.062;
        const medicare = grossPay * 0.0145;
        const totalDeductions = federalTax + stateTax + socialSecurity + medicare;
        const netPay = grossPay - totalDeductions;
        
        return {
            employeeName: employeeName,
            hourlyRate: hourlyRate,
            overtimeMultiplier: overtimeMultiplier,
            dailyHours: dailyHours,
            totalHours: totalHours,
            regularHours: regularHours,
            overtimeHours: overtimeHours,
            regularPay: regularPay,
            overtimePay: overtimePay,
            grossPay: grossPay,
            deductions: {
                federal: federalTax,
                state: stateTax,
                socialSecurity: socialSecurity,
                medicare: medicare,
                total: totalDeductions
            },
            netPay: netPay
        };
    }
    
    function displayTimecardResults(data) {
        const dayNames = {
            mon: 'Monday', tue: 'Tuesday', wed: 'Wednesday', thu: 'Thursday',
            fri: 'Friday', sat: 'Saturday', sun: 'Sunday'
        };
        
        let html = `
            <div style="font-size: 36px; font-weight: bold; margin-bottom: 20px;">
                $${data.netPay.toFixed(2)}
            </div>
            
            <div style="font-size: 18px; margin-bottom: 25px;">
                Net Pay for ${data.employeeName}
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Total Hours</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #607d8b;">
                        ${data.totalHours.toFixed(2)}
                    </div>
                    <div style="font-size: 12px; color: #666;">This week</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Regular Hours</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #4caf50;">
                        ${data.regularHours.toFixed(2)}
                    </div>
                    <div style="font-size: 12px; color: #666;">@ $${data.hourlyRate}/hr</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Overtime Hours</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #ff9800;">
                        ${data.overtimeHours.toFixed(2)}
                    </div>
                    <div style="font-size: 12px; color: #666;">@ $${(data.hourlyRate * data.overtimeMultiplier).toFixed(2)}/hr</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Gross Pay</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #2196f3;">
                        $${data.grossPay.toFixed(2)}
                    </div>
                    <div style="font-size: 12px; color: #666;">Before deductions</div>
                </div>
            </div>
            
            <div class="overtime-breakdown">
                <h4 style="color: #607d8b; margin-bottom: 20px;">📊 Daily Hours Breakdown</h4>
                
                <div style="display: grid; grid-template-columns: repeat(7, 1fr); gap: 10px; margin-bottom: 20px;">
        `;
        
        days.forEach(day => {
            const hours = data.dailyHours[day];
            html += `
                <div style="text-align: center; padding: 10px; background-color: #f5f5f5; border-radius: 8px;">
                    <div style="font-weight: bold; margin-bottom: 5px;">${dayNames[day]}</div>
                    <div style="font-size: 18px; color: ${hours > 8 ? '#ff9800' : '#4caf50'};">${hours.toFixed(2)}h</div>
                </div>
            `;
        });
        
        html += `
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h5>Pay Calculation:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Regular Pay:</strong> ${data.regularHours.toFixed(2)} × $${data.hourlyRate} = $${data.regularPay.toFixed(2)}</li>
                            <li><strong>Overtime Pay:</strong> ${data.overtimeHours.toFixed(2)} × $${(data.hourlyRate * data.overtimeMultiplier).toFixed(2)} = $${data.overtimePay.toFixed(2)}</li>
                            <li><strong>Gross Pay:</strong> $${data.grossPay.toFixed(2)}</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5>Deductions (Estimated):</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Federal Tax:</strong> $${data.deductions.federal.toFixed(2)}</li>
                            <li><strong>State Tax:</strong> $${data.deductions.state.toFixed(2)}</li>
                            <li><strong>Social Security:</strong> $${data.deductions.socialSecurity.toFixed(2)}</li>
                            <li><strong>Medicare:</strong> $${data.deductions.medicare.toFixed(2)}</li>
                            <li><strong>Total Deductions:</strong> $${data.deductions.total.toFixed(2)}</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="copleys-brand" style="margin-top: 20px;">
                <h4>⏰ Copley's Time & Attendance Solutions</h4>
                <p style="margin: 10px 0; font-size: 14px;">
                    Professional time tracking and payroll calculation services. 
                    Accurate, reliable, and compliant with labor regulations.
                </p>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
    
    // Initialize with auto-calculation
    autoCalculateBtn.click();
});
</script>
