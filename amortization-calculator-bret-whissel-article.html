<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.amortization-interface {
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.loan-input {
    background-color: #e8f5e9;
    border: 2px solid #2e7d32;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.payment-result {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.amortization-table {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
    max-height: 400px;
    overflow-y: auto;
}
.payment-breakdown {
    background-color: #f1f8e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.summary-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    color: #2c3e50;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.progress-bar {
    background-color: #e0e0e0;
    border-radius: 10px;
    height: 20px;
    margin: 10px 0;
    overflow: hidden;
}
.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #8bc34a);
    border-radius: 10px;
    transition: width 0.5s ease;
}
.bret-whissel-brand {
    background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    color: white;
    text-align: center;
}
</style>

<h1>Amortization Calculator - Bret Whissel</h1>
<p>Calculate loan amortization schedules with Bret Whissel's comprehensive mortgage calculator. Analyze monthly payments, interest costs, and principal reduction over time. Perfect for home buyers, real estate professionals, and financial planning.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="amortizationform" id="amortization-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Amortization</a></li>
                <li><a href="#" data-tab="schedule">Payment Schedule</a></li>
                <li><a href="#" data-tab="analysis">Loan Analysis</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="bret-whissel-brand">
                <h3 style="margin: 0;">🏠 Bret Whissel Real Estate Calculator</h3>
                <div style="font-size: 14px; margin-top: 5px;">Professional Mortgage & Amortization Analysis</div>
            </div>
            
            <div class="amortization-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">💰 Loan Amortization Calculator</h3>
                
                <div class="loan-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🏦 Loan Details</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="loan-amount"><strong>Loan Amount:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="loan-amount" value="400000" min="1000" max="10000000" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="interest-rate"><strong>Annual Interest Rate:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <input type="number" id="interest-rate" value="6.5" min="0.1" max="30" step="0.01" class="innormal" style="width: 100%;">
                                <span style="margin-left: 5px;">%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="loan-term"><strong>Loan Term:</strong></label>
                            <select id="loan-term" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="15">15 Years</option>
                                <option value="20">20 Years</option>
                                <option value="25">25 Years</option>
                                <option value="30" selected>30 Years</option>
                                <option value="custom">Custom</option>
                            </select>
                            <input type="number" id="custom-term" value="30" min="1" max="50" class="innormal" style="width: 100%; margin-top: 5px; display: none;" placeholder="Years">
                        </div>
                        
                        <div>
                            <label for="start-date"><strong>First Payment Date:</strong></label>
                            <input type="date" id="start-date" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                    </div>
                </div>
                
                <div class="payment-breakdown">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🏠 Additional Costs (Optional)</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="property-tax"><strong>Annual Property Tax:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="property-tax" value="0" min="0" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="home-insurance"><strong>Annual Home Insurance:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="home-insurance" value="0" min="0" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="pmi"><strong>Monthly PMI:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="pmi" value="0" min="0" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <label for="extra-payment"><strong>Extra Monthly Payment:</strong></label>
                        <div style="display: flex; align-items: center; margin-top: 5px; width: 200px;">
                            <span style="margin-right: 5px;">$</span>
                            <input type="number" id="extra-payment" value="0" min="0" class="innormal" style="width: 100%;">
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Amortization" id="calculate-amortization-btn" style="padding: 15px 30px; font-size: 18px; background-color: #2e7d32; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Calculator" id="clear-amortization-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Disclaimer:</strong> This calculator provides estimates for educational purposes. Consult with Bret Whissel or a qualified mortgage professional for actual loan terms.
        </div>
        
        <div id="amortization-results" style="display: none; margin-top: 30px;">
            <div class="payment-result">
                <h3 style="margin-bottom: 20px;">📊 Loan Payment Analysis</h3>
                <div id="amortization-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-amortization-btn');
    const clearBtn = document.getElementById('clear-amortization-btn');
    const resultsDiv = document.getElementById('amortization-results');
    const resultsContent = document.getElementById('amortization-results-content');
    const loanTermSelect = document.getElementById('loan-term');
    const customTermInput = document.getElementById('custom-term');
    
    // Set default start date to next month
    const today = new Date();
    const nextMonth = new Date(today.getFullYear(), today.getMonth() + 1, 1);
    document.getElementById('start-date').value = nextMonth.toISOString().split('T')[0];
    
    // Handle custom loan term
    loanTermSelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customTermInput.style.display = 'block';
        } else {
            customTermInput.style.display = 'none';
        }
    });
    
    calculateBtn.addEventListener('click', function() {
        const loanAmount = parseFloat(document.getElementById('loan-amount').value) || 0;
        const interestRate = parseFloat(document.getElementById('interest-rate').value) || 0;
        const loanTermValue = loanTermSelect.value;
        const loanTermYears = loanTermValue === 'custom' ? 
            (parseFloat(customTermInput.value) || 30) : 
            parseFloat(loanTermValue);
        const startDate = document.getElementById('start-date').value;
        const propertyTax = parseFloat(document.getElementById('property-tax').value) || 0;
        const homeInsurance = parseFloat(document.getElementById('home-insurance').value) || 0;
        const pmi = parseFloat(document.getElementById('pmi').value) || 0;
        const extraPayment = parseFloat(document.getElementById('extra-payment').value) || 0;
        
        if (loanAmount <= 0 || interestRate <= 0 || loanTermYears <= 0) {
            alert('Please enter valid loan amount, interest rate, and term.');
            return;
        }
        
        const amortizationData = calculateAmortization(loanAmount, interestRate, loanTermYears, 
            startDate, propertyTax, homeInsurance, pmi, extraPayment);
        
        displayAmortizationResults(amortizationData);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('amortization-calculator-form').reset();
        customTermInput.style.display = 'none';
        const nextMonth = new Date();
        nextMonth.setMonth(nextMonth.getMonth() + 1);
        nextMonth.setDate(1);
        document.getElementById('start-date').value = nextMonth.toISOString().split('T')[0];
        resultsDiv.style.display = 'none';
    });
    
    function calculateAmortization(principal, annualRate, years, startDate, propertyTax, homeInsurance, pmi, extraPayment) {
        const monthlyRate = annualRate / 100 / 12;
        const totalPayments = years * 12;
        
        // Calculate monthly principal and interest payment
        const monthlyPI = principal * (monthlyRate * Math.pow(1 + monthlyRate, totalPayments)) / 
                         (Math.pow(1 + monthlyRate, totalPayments) - 1);
        
        // Calculate additional monthly costs
        const monthlyPropertyTax = propertyTax / 12;
        const monthlyInsurance = homeInsurance / 12;
        const totalMonthlyPayment = monthlyPI + monthlyPropertyTax + monthlyInsurance + pmi;
        
        // Generate amortization schedule
        const schedule = [];
        let remainingBalance = principal;
        let totalInterestPaid = 0;
        let currentDate = new Date(startDate);
        
        for (let payment = 1; payment <= totalPayments && remainingBalance > 0.01; payment++) {
            const interestPayment = remainingBalance * monthlyRate;
            let principalPayment = monthlyPI - interestPayment + extraPayment;
            
            // Don't overpay on the last payment
            if (principalPayment > remainingBalance) {
                principalPayment = remainingBalance;
            }
            
            remainingBalance -= principalPayment;
            totalInterestPaid += interestPayment;
            
            schedule.push({
                payment: payment,
                date: new Date(currentDate),
                principalAndInterest: monthlyPI,
                principal: principalPayment,
                interest: interestPayment,
                extraPayment: extraPayment,
                balance: remainingBalance,
                totalPayment: monthlyPI + extraPayment + monthlyPropertyTax + monthlyInsurance + pmi
            });
            
            // Move to next month
            currentDate.setMonth(currentDate.getMonth() + 1);
            
            if (remainingBalance <= 0.01) break;
        }
        
        // Calculate savings from extra payments
        const standardTotalInterest = (monthlyPI * totalPayments) - principal;
        const interestSavings = standardTotalInterest - totalInterestPaid;
        const timeSavings = totalPayments - schedule.length;
        
        return {
            loanAmount: principal,
            interestRate: annualRate,
            loanTermYears: years,
            monthlyPI: monthlyPI,
            monthlyPropertyTax: monthlyPropertyTax,
            monthlyInsurance: monthlyInsurance,
            monthlyPMI: pmi,
            extraPayment: extraPayment,
            totalMonthlyPayment: totalMonthlyPayment,
            totalInterestPaid: totalInterestPaid,
            totalAmountPaid: principal + totalInterestPaid,
            schedule: schedule,
            payoffDate: schedule[schedule.length - 1]?.date,
            interestSavings: interestSavings,
            timeSavings: timeSavings
        };
    }
    
    function displayAmortizationResults(data) {
        let html = `
            <div style="font-size: 36px; font-weight: bold; margin-bottom: 20px;">
                $${data.monthlyPI.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
            </div>
            
            <div style="font-size: 18px; margin-bottom: 25px;">
                Monthly Principal & Interest Payment
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div class="summary-card">
                    <h4>Total Monthly Payment</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #2e7d32;">
                        $${data.totalMonthlyPayment.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    </div>
                    <div style="font-size: 12px; color: #666;">Including taxes & insurance</div>
                </div>
                
                <div class="summary-card">
                    <h4>Total Interest Paid</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #f44336;">
                        $${data.totalInterestPaid.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    </div>
                    <div style="font-size: 12px; color: #666;">Over life of loan</div>
                </div>
                
                <div class="summary-card">
                    <h4>Total Amount Paid</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #ff9800;">
                        $${data.totalAmountPaid.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    </div>
                    <div style="font-size: 12px; color: #666;">Principal + Interest</div>
                </div>
                
                <div class="summary-card">
                    <h4>Payoff Date</h4>
                    <div style="font-size: 20px; font-weight: bold; color: #1976d2;">
                        ${data.payoffDate ? data.payoffDate.toLocaleDateString() : 'N/A'}
                    </div>
                    <div style="font-size: 12px; color: #666;">${data.schedule.length} payments</div>
                </div>
            </div>
            
            <div style="background: white; border-radius: 10px; padding: 20px; margin: 20px 0; color: #2c3e50;">
                <h4 style="color: #2e7d32; margin-bottom: 15px;">💰 Payment Breakdown</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h5>Monthly Payment Components:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Principal & Interest:</strong> $${data.monthlyPI.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</li>
                            <li><strong>Property Tax:</strong> $${data.monthlyPropertyTax.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</li>
                            <li><strong>Home Insurance:</strong> $${data.monthlyInsurance.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</li>
                            <li><strong>PMI:</strong> $${data.monthlyPMI.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</li>
                            ${data.extraPayment > 0 ? `<li><strong>Extra Payment:</strong> $${data.extraPayment.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</li>` : ''}
                        </ul>
                    </div>
                    
                    <div>
                        <h5>Loan Summary:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Loan Amount:</strong> $${data.loanAmount.toLocaleString()}</li>
                            <li><strong>Interest Rate:</strong> ${data.interestRate}%</li>
                            <li><strong>Loan Term:</strong> ${data.loanTermYears} years</li>
                            <li><strong>Total Payments:</strong> ${data.schedule.length}</li>
                        </ul>
                    </div>
                </div>
            </div>
        `;
        
        // Add extra payment savings if applicable
        if (data.extraPayment > 0) {
            html += `
                <div style="background-color: rgba(76, 175, 80, 0.1); border-radius: 10px; padding: 20px; margin: 20px 0; color: #2c3e50;">
                    <h4 style="color: #4caf50;">💡 Extra Payment Benefits:</h4>
                    <ul style="margin: 10px 0;">
                        <li><strong>Interest Savings:</strong> $${data.interestSavings.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</li>
                        <li><strong>Time Savings:</strong> ${Math.floor(data.timeSavings / 12)} years, ${data.timeSavings % 12} months</li>
                        <li><strong>Early Payoff:</strong> ${data.payoffDate ? data.payoffDate.toLocaleDateString() : 'N/A'}</li>
                    </ul>
                </div>
            `;
        }
        
        // Add first few payments preview
        html += `
            <div class="amortization-table">
                <h4 style="color: #2e7d32; margin-bottom: 15px;">📅 Payment Schedule Preview (First 12 Payments)</h4>
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background-color: #f5f5f5;">
                            <th style="padding: 10px; border: 1px solid #ddd;">Payment #</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">Date</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">Principal</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">Interest</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">Balance</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        data.schedule.slice(0, 12).forEach(payment => {
            html += `
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">${payment.payment}</td>
                    <td style="padding: 8px; border: 1px solid #ddd;">${payment.date.toLocaleDateString()}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$${payment.principal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$${payment.interest.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$${payment.balance.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                </tr>
            `;
        });
        
        html += `
                    </tbody>
                </table>
            </div>
            
            <div class="bret-whissel-brand" style="margin-top: 20px;">
                <h4>🏠 Bret Whissel Real Estate Services</h4>
                <p style="margin: 10px 0; font-size: 14px;">
                    Professional real estate guidance and mortgage consultation. 
                    Contact Bret Whissel for personalized home buying assistance and loan options.
                </p>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
