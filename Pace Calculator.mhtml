

<!DOCTYPE html><html lang=3D"en"><head><meta http-equiv=3D"Content-Type" co=
ntent=3D"text/html; charset=3DUTF-8">
	<title>Pace Calculator</title>
	<meta name=3D"description" content=3D"This free pace calculator computes p=
ace, time, and distance, given values for two of the variables. It can also=
 be used for training purposes.">
	<link rel=3D"stylesheet" href=3D"https://www.calculator.net/style.css"><me=
ta name=3D"viewport" content=3D"width=3Ddevice-width, initial-scale=3D1.0">=
	<link rel=3D"apple-touch-icon" sizes=3D"180x180" href=3D"https://d26tpo4cm=
8sb6k.cloudfront.net/apple-touch-icon.png">
	<link rel=3D"icon" type=3D"image/png" sizes=3D"32x32" href=3D"https://d26t=
po4cm8sb6k.cloudfront.net/icon-32x32.png">
	<link rel=3D"icon" type=3D"image/png" sizes=3D"16x16" href=3D"https://d26t=
po4cm8sb6k.cloudfront.net/icon-16x16.png">
	<link rel=3D"manifest" href=3D"https://www.calculator.net/manifest.json"><=
meta http-equiv=3D"origin-trial" content=3D"AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6L=
YjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOi=
JodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0Z=
WRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVl=
fQ=3D=3D"><meta http-equiv=3D"origin-trial" content=3D"Amm8/NmvvQfhwCib6I7Z=
smUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJ=
vcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2=
ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU=
3ViZG9tYWluIjp0cnVlfQ=3D=3D"><meta http-equiv=3D"origin-trial" content=3D"A=
9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO=
8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF=
0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MD=
AsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"><meta http-equiv=3D=
"origin-trial" content=3D"A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkC=
QeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2ds=
ZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25=
TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUG=
FydHkiOnRydWV9"><link href=3D"https://securepubads.g.doubleclick.net/pagead=
/managed/dict/m202505290101/gpt" rel=3D"compression-dictionary"></head><bod=
y><div id=3D"headerout">
	<div id=3D"header">
		<div id=3D"logo"><a href=3D"https://www.calculator.net/"><img src=3D"http=
s://d26tpo4cm8sb6k.cloudfront.net/img/svg/calculator-white.svg" width=3D"20=
8" height=3D"22" alt=3D"Calculator.net"></a></div>
		<div id=3D"login"><a href=3D"https://www.calculator.net/my-account/sign-i=
n.php">sign in</a></div>	</div>
</div>
<div id=3D"clear"></div><div id=3D"contentout"><div id=3D"content"><div id=
=3D"breadcrumbs" itemscope=3D"" itemtype=3D"https://schema.org/BreadcrumbLi=
st"><span itemprop=3D"itemListElement" itemscope=3D"" itemtype=3D"https://s=
chema.org/ListItem"><a href=3D"https://www.calculator.net/" itemprop=3D"ite=
m"><span itemprop=3D"name">home</span></a><meta itemprop=3D"position" conte=
nt=3D"1"></span> / <span itemprop=3D"itemListElement" itemscope=3D"" itemty=
pe=3D"https://schema.org/ListItem"><a href=3D"https://www.calculator.net/fi=
tness-and-health-calculator.html" itemprop=3D"item"><span itemprop=3D"name"=
>fitness &amp; health</span></a><meta itemprop=3D"position" content=3D"2"><=
/span> / <span itemprop=3D"itemListElement" itemscope=3D"" itemtype=3D"http=
s://schema.org/ListItem"><a href=3D"https://www.calculator.net/pace-calcula=
tor.html" itemprop=3D"item"><span itemprop=3D"name">pace calculator</span><=
/a><meta itemprop=3D"position" content=3D"3"></span></div>		<h1>Pace Calcul=
ator</h1>
<p>Use the following calculator to estimate the pace for a variety of activ=
ities, including running, walking, and biking. The calculator can also be u=
sed to estimate the time taken or distance traveled with a given pace and t=
ime or distance.</p>
<br><div>
<form name=3D"calform" action=3D"https://www.calculator.net/pace-calculator=
.html">
<div id=3D"topmenu" class=3D"topmenucenter"><ul><li id=3D"menuon"><a href=
=3D"https://www.calculator.net/pace-calculator.html#">Pace</a></li> <li><a =
href=3D"https://www.calculator.net/pace-calculator.html#">Time</a></li> <li=
><a href=3D"https://www.calculator.net/pace-calculator.html#">Distance</a><=
/li></ul></div>
<div class=3D"panel2" style=3D"padding: 8px 5px;">
<table id=3D"calinputtime" style=3D"display: block;">
<tbody><tr>
	<td width=3D"75">Time</td>
	<td><input type=3D"text" name=3D"ctime" id=3D"ctime" value=3D"00:50:25" cl=
ass=3D"inlong"></td>
	<td>hh:mm:ss</td>
</tr>
</tbody></table>
<table id=3D"calinputdistance" style=3D"display: block;">
<tbody><tr>
	<td width=3D"75">Distance</td>
	<td><input type=3D"text" name=3D"cdistance" id=3D"cdistance" value=3D"5" c=
lass=3D"inlong"></td>
	<td>
		<select name=3D"cdistanceunit" id=3D"cdistanceunit">
			<option value=3D"Miles">Miles</option>
			<option value=3D"Kilometers" selected=3D"">Kilometers</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
		 &nbsp; &nbsp; 		<select name=3D"cpickevent">
			<option value=3D"">-- Or pick an event --</option>
			<option value=3D"M">Marathon</option>
			<option value=3D"HM">Half-Marathon</option>
			<option value=3D"1K">1K</option>
			<option value=3D"5K">5K</option>
			<option value=3D"10K">10K</option>
			<option value=3D"1M">1 Miles</option>
			<option value=3D"5M">5 Miles</option>
			<option value=3D"10M">10 Miles</option>
			<option value=3D"800m">800 meters</option>
			<option value=3D"1500m">1500 meters</option>
		</select>
	</td>
</tr>
</tbody></table>
<table id=3D"calinputpace" style=3D"display: none;">
<tbody><tr valign=3D"top">
	<td width=3D"75">Pace</td>
	<td><input type=3D"text" name=3D"cpace" id=3D"cpace" value=3D"00:08:10" cl=
ass=3D"inlong"><div id=3D"cpaceunithint" style=3D"text-align:right;">hh:mm:=
ss</div></td>
	<td>
		<select name=3D"cpaceunit">
			<option value=3D"tpm" selected=3D"">Per Mile</option>
			<option value=3D"tpk">Per Kilometer</option>
			<option value=3D"mph">Miles Per Hour</option>
			<option value=3D"kph">Kilometers Per Hour</option>
			<option value=3D"mpm">Meters Per Minute</option>
			<option value=3D"mps">Meters Per Second</option>
			<option value=3D"ypm">Yards Per Minute</option>
			<option value=3D"yps">Yards Per Second</option>
		</select>
	</td>
</tr>
</tbody></table>
<table width=3D"310" style=3D"padding-top: 10px;">
<tbody><tr>
	<td align=3D"center">
	=09
		<input type=3D"submit" name=3D"x" value=3D"Calculate">
		<input type=3D"button" value=3D"Clear">
	</td>
</tr>
</tbody></table>
</div>
<div class=3D"smalltext">Note that placeholder zeros do not need to be ente=
red in the "Time" or "Pace" field. For example, the time 5 minutes 3 second=
s does not need to be entered as 00:05:03, and can be entered as 5:3.</div>
</form>
</div>

<a name=3D"multipoint"></a>
<br><h2>Multipoint Pace Calculator</h2>
<p>The following calculator can determine the pace of segments of a run (or=
 other activity) for those with access to the time at intermittent points d=
uring the run. For example, if a person runs from point A to point B, then =
to point C, records the time at each point, and subsequently determines the=
 distance between those points (using many available websites, applications=
, or maps), the multipoint calculator can determine how fast the person tra=
veled between each pair of points, allowing use for training purposes; a pe=
rson can run the same route (or distance) repeatedly and track pace over th=
at given route, enabling comparison of times between each segment (or lap) =
to identify areas for potential improvement.</p><form action=3D"https://www=
.calculator.net/pace-calculator.html#multipoint" name=3D"calform2">
<table class=3D"panel">
<tbody><tr>
	<td bgcolor=3D"#E0F0FE">&nbsp;</td>
	<td bgcolor=3D"#E0F0FE" colspan=3D"2">Distance</td>
	<td bgcolor=3D"#E0F0FE">Time (hh:mm:ss)</td>
</tr>
<tr>
	<td>1.</td>
	<td>
		<input type=3D"text" name=3D"cmpdistance1" value=3D"1" class=3D"innormal"=
>
	</td><td>
		<select name=3D"cmpdistanceunit1">
			<option value=3D"Kilometers">Kilometers</option>
			<option value=3D"Miles">Miles</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
	<td><input type=3D"text" name=3D"cmptime1" value=3D"3:25" class=3D"inlong"=
></td>
</tr>
<tr>
	<td>2.</td>
	<td>
		<input type=3D"text" name=3D"cmpdistance2" value=3D"2" class=3D"innormal"=
>
	</td><td>
		<select name=3D"cmpdistanceunit2">
			<option value=3D"Kilometers">Kilometers</option>
			<option value=3D"Miles">Miles</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
	<td><input type=3D"text" name=3D"cmptime2" value=3D"6:55" class=3D"inlong"=
></td>
</tr>
<tr>
	<td>3.</td>
	<td>
		<input type=3D"text" name=3D"cmpdistance3" value=3D"3" class=3D"innormal"=
>
	</td><td>
		<select name=3D"cmpdistanceunit3">
			<option value=3D"Kilometers">Kilometers</option>
			<option value=3D"Miles">Miles</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
	<td><input type=3D"text" name=3D"cmptime3" value=3D"10:25" class=3D"inlong=
"></td>
</tr>
<tr>
	<td>4.</td>
	<td>
		<input type=3D"text" name=3D"cmpdistance4" value=3D"4" class=3D"innormal"=
>
	</td><td>
		<select name=3D"cmpdistanceunit4">
			<option value=3D"Kilometers">Kilometers</option>
			<option value=3D"Miles">Miles</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
	<td><input type=3D"text" name=3D"cmptime4" value=3D"14:01" class=3D"inlong=
"></td>
</tr>
<tr>
	<td>5.</td>
	<td>
		<input type=3D"text" name=3D"cmpdistance5" value=3D"5" class=3D"innormal"=
>
	</td><td>
		<select name=3D"cmpdistanceunit5">
			<option value=3D"Kilometers">Kilometers</option>
			<option value=3D"Miles">Miles</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
	<td><input type=3D"text" name=3D"cmptime5" value=3D"17:25" class=3D"inlong=
"></td>
</tr>
<tr>
	<td>6.</td>
	<td>
		<input type=3D"text" name=3D"cmpdistance6" value=3D"" class=3D"innormal">
	</td><td>
		<select name=3D"cmpdistanceunit6">
			<option value=3D"Kilometers">Kilometers</option>
			<option value=3D"Miles">Miles</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
	<td><input type=3D"text" name=3D"cmptime6" value=3D"" class=3D"inlong"></t=
d>
</tr>
<tr>
	<td>7.</td>
	<td>
		<input type=3D"text" name=3D"cmpdistance7" value=3D"" class=3D"innormal">
	</td><td>
		<select name=3D"cmpdistanceunit7">
			<option value=3D"Kilometers">Kilometers</option>
			<option value=3D"Miles">Miles</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
	<td><input type=3D"text" name=3D"cmptime7" value=3D"" class=3D"inlong"></t=
d>
</tr>
<tr>
	<td>8.</td>
	<td>
		<input type=3D"text" name=3D"cmpdistance8" value=3D"" class=3D"innormal">
	</td><td>
		<select name=3D"cmpdistanceunit8">
			<option value=3D"Kilometers">Kilometers</option>
			<option value=3D"Miles">Miles</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
	<td><input type=3D"text" name=3D"cmptime8" value=3D"" class=3D"inlong"></t=
d>
</tr>
<tr>
	<td>9.</td>
	<td>
		<input type=3D"text" name=3D"cmpdistance9" value=3D"" class=3D"innormal">
	</td><td>
		<select name=3D"cmpdistanceunit9">
			<option value=3D"Kilometers">Kilometers</option>
			<option value=3D"Miles">Miles</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
	<td><input type=3D"text" name=3D"cmptime9" value=3D"" class=3D"inlong"></t=
d>
</tr>
<tr>
	<td>10.</td>
	<td>
		<input type=3D"text" name=3D"cmpdistance10" value=3D"" class=3D"innormal"=
>
	</td><td>
		<select name=3D"cmpdistanceunit10">
			<option value=3D"Kilometers">Kilometers</option>
			<option value=3D"Miles">Miles</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
	<td><input type=3D"text" name=3D"cmptime10" value=3D"" class=3D"inlong"></=
td>
</tr>
<tr>
	<td>11.</td>
	<td>
		<input type=3D"text" name=3D"cmpdistance11" value=3D"" class=3D"innormal"=
>
	</td><td>
		<select name=3D"cmpdistanceunit11">
			<option value=3D"Kilometers">Kilometers</option>
			<option value=3D"Miles">Miles</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
	<td><input type=3D"text" name=3D"cmptime11" value=3D"" class=3D"inlong"></=
td>
</tr>
<tr>
	<td>12.</td>
	<td>
		<input type=3D"text" name=3D"cmpdistance12" value=3D"" class=3D"innormal"=
>
	</td><td>
		<select name=3D"cmpdistanceunit12">
			<option value=3D"Kilometers">Kilometers</option>
			<option value=3D"Miles">Miles</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
	<td><input type=3D"text" name=3D"cmptime12" value=3D"" class=3D"inlong"></=
td>
</tr>
<tr>
	<td colspan=3D"4" align=3D"center">
		Your Preferred Pace Unit
		<select name=3D"cmppaceunit">
			<option value=3D"tpm">Per Mile</option>
			<option value=3D"tpk">Per Kilometer</option>
			<option value=3D"mph">Miles Per Hour</option>
			<option value=3D"kph">Kilometers Per Hour</option>
			<option value=3D"mpm">Meters Per Minute</option>
			<option value=3D"mps">Meters Per Second</option>
			<option value=3D"ypm">Yards Per Minute</option>
			<option value=3D"yps">Yards Per Second</option>
		</select>
	</td>
</tr>
<tr>
	<td colspan=3D"4" align=3D"center" style=3D"padding-top:10px;">
	=09
		<input type=3D"submit" name=3D"x" value=3D"Calculate">
		<input type=3D"button" value=3D"Clear">
	</td>
</tr>
</tbody></table>
</form>


<a name=3D"paceconverter"></a>
<br><h2>Pace Converter</h2>
<form action=3D"https://www.calculator.net/pace-calculator.html#paceconvert=
er" name=3D"calform3">
<table class=3D"panel">
<tbody><tr>
	<td valign=3D"top" align=3D"center">
		<input type=3D"text" name=3D"ccfrom" value=3D"5:30" class=3D"inhalf"><br>=
<span id=3D"ccpaceunithint">hh:mm:ss</span>
	</td>
	<td valign=3D"top">
		<select name=3D"ccpaceunitfrom">
			<option value=3D"tpm" selected=3D"">Per Mile</option>
			<option value=3D"tpk">Per Kilometer</option>
			<option value=3D"mph">Miles Per Hour</option>
			<option value=3D"kph">Kilometers Per Hour</option>
			<option value=3D"mpm">Meters Per Minute</option>
			<option value=3D"mps">Meters Per Second</option>
			<option value=3D"ypm">Yards Per Minute</option>
			<option value=3D"yps">Yards Per Second</option>
		</select>
	</td>
	<td class=3D"verybigtext" valign=3D"top">&nbsp;<b>=3D</b></td><td valign=
=3D"top">	<b>?</b>
		<select name=3D"ccpaceunitto">
			<option value=3D"tpm">Per Mile</option>
			<option value=3D"tpk" selected=3D"">Per Kilometer</option>
			<option value=3D"mph">Miles Per Hour</option>
			<option value=3D"kph">Kilometers Per Hour</option>
			<option value=3D"mpm">Meters Per Minute</option>
			<option value=3D"mps">Meters Per Second</option>
			<option value=3D"ypm">Yards Per Minute</option>
			<option value=3D"yps">Yards Per Second</option>
		</select>
	</td>
</tr>
<tr>
	<td align=3D"center" colspan=3D"4" style=3D"padding-top:10px;">
	=09
		<input type=3D"submit" name=3D"x" value=3D"Calculate">
		<input type=3D"button" value=3D"Clear">
	</td>
</tr>
</tbody></table>
</form>


<a name=3D"finishtime"></a>
<br><h2>Finish Time Calculator</h2>
<p>The following calculator can be used to estimate a person's finish time =
based on the time and distance covered in a race at the point the calculato=
r is used.</p><form action=3D"https://www.calculator.net/pace-calculator.ht=
ml#finishtime" name=3D"calform4">
<table class=3D"panel">
<tbody><tr>
	<td>Current Distance Traveled</td><td>		<input type=3D"text" name=3D"cftdi=
stancenow" id=3D"cftdistancenow" value=3D"1" class=3D"inhalf">
		<select name=3D"cftdistancenowunit">
			<option value=3D"Miles">Miles</option>
			<option value=3D"Kilometers" selected=3D"">Kilometers</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
</tr>
<tr>
	<td>Elapsed Time</td><td>	<input type=3D"text" name=3D"cfttime" id=3D"cftt=
ime" value=3D"6:15" class=3D"inhalf"> hh:mm:ss</td>
</tr>
<tr>
	<td>Full Distance</td><td>		<input type=3D"text" name=3D"cftdistancefull" =
id=3D"cftdistancefull" value=3D"5" class=3D"inhalf">
		<select name=3D"cftdistancefullunit">
			<option value=3D"Miles">Miles</option>
			<option value=3D"Kilometers" selected=3D"">Kilometers</option>
			<option value=3D"Meters">Meters</option>
			<option value=3D"Yards">Yards</option>
		</select>
	</td>
</tr>
<tr>
	<td align=3D"center" colspan=3D"2" style=3D"padding-top:10px;">
	=09
		<input type=3D"submit" name=3D"x" value=3D"Calculate">
		<input type=3D"button" value=3D"Clear">
	</td>
</tr>
</tbody></table>
</form>





<br>


<h3>Typical Races and World Record Paces</h3>
<table class=3D"cinfoT">
	<tbody><tr><td class=3D"cinfoHd">Category</td><td class=3D"cinfoHdL">Men's=
 World Record Pace</td><td class=3D"cinfoHdL">Women's World Record Pace</td=
></tr>
	<tr><td>100 meters</td><td class=3D"cinfoBodL">2:35/mile or 1:36/km</td><t=
d class=3D"cinfoBodL">2:49/mile or 1:45/km</td></tr>
	<tr><td>200 meters</td><td class=3D"cinfoBodL">2:35/mile or 1:36/km</td><t=
d class=3D"cinfoBodL">2:52/mile or 1:47/km</td></tr>
	<tr><td>400 meters</td><td class=3D"cinfoBodL">2:54/mile or 1:48/km</td><t=
d class=3D"cinfoBodL">3:12/mile or 1:59/km</td></tr>
	<tr><td>800 meters</td><td class=3D"cinfoBodL">3:23/mile or 2:06/km</td><t=
d class=3D"cinfoBodL">3:48/mile or 2:21/km</td></tr>

	<tr><td>1,500 meters</td><td class=3D"cinfoBodL">3:41/mile or 2:17/km</td>=
<td class=3D"cinfoBodL">4:07/mile or 2:34/km</td></tr>
	<tr><td>1 mile</td><td class=3D"cinfoBodL">3:43/mile or 2:19/km</td><td cl=
ass=3D"cinfoBodL">4:13/mile or 2:37/km</td></tr>

	<tr><td>5K</td><td class=3D"cinfoBodL">4:04/mile or 2:31/km</td><td class=
=3D"cinfoBodL">4:34/mile or 2:50/km</td></tr>
	<tr><td>10K</td><td class=3D"cinfoBodL">4:14/mile or 2:38/km</td><td class=
=3D"cinfoBodL">4:45/mile or 2:57/km</td></tr>

	<tr><td>Half Marathon<br>(13.11 miles / 21.098 km)</td><td class=3D"cinfoB=
odL">4:27/mile or 2:46/km</td><td class=3D"cinfoBodL">4:58/mile or 3:05/km<=
/td></tr>
	<tr><td>Marathon<br>(26.22 miles / 42.195 km)</td><td class=3D"cinfoBodL">=
4:41/mile or 2:55/km</td><td class=3D"cinfoBodL">5:10/mile or 3:13/km</td><=
/tr>
</tbody></table>


<br><h2>Training Through Pace and Heart Rate</h2>

<p>Pace is a rate of activity or movement, while heart rate is measured as =
the number of times that a person's heart contracts over a minute. Pace and=
 heart rate have a positive correlation; higher pace corresponds to higher =
heart rate. The use of both in training can help a person improve performan=
ce, avoid over-training, as well as track progress and fitness over time. <=
/p>

<h3>Measuring and Estimating Heart Rate and Heart Rate Zones:</h3>

<p>Heart rate can be measured in different ways, from using devices such as=
 heart rate monitors, to simply looking at a watch while measuring pulse at=
 some peripheral point such as the wrist or neck. Some of the more notable =
measurements of heart rate include resting heart rate and maximum heart rat=
e, which are often used to estimate specific target heart rate zones to det=
ermine different levels of exercise. </p>

<p>Typical adult resting heart rates (RHR) are commonly cited to range from=
 60-100 beats per minute (bpm), though there is some argument that normal R=
HRs actually fall within the range of 50-90 bpm. Generally, a lower RHR ind=
icates more efficient heart function, though RHRs that are lower than 50 bp=
m can be a sign of an underlying heart condition or disease. The same is tr=
ue of RHRs above 90 bpm. </p>

<p>Maximum heart rate (MHR) is most accurately measured using a cardiac str=
ess test, which involves measuring a person's heart function (including hea=
rt rate) at periodically increasing levels of exercise. These tests typical=
ly range from ten to twenty minutes in duration, which can be inconvenient.=
 As such, there are many estimates for MHR based on age, which is strongly =
correlated with heart rate, though there is little consensus regarding whic=
h formula should be used. The most commonly cited formula for calculating M=
HR is:</p>

<p class=3D"bigtext" align=3D"center">MHR =3D 220 =E2=80=93 age</p>

<p>Although it is the most commonly cited formula, and is often used to det=
ermine heart rate training zones, it does not have a reference to any stand=
ard deviation, and is not considered a good predictor of MHR by reputable h=
ealth and fitness professionals. Furthermore, MHRs vary significantly betwe=
en individuals, even those with highly similar training and age within the =
same sport. Nevertheless, MHR determined using the above formula is often u=
sed to prescribe exercise training heart rate ranges, and can be beneficial=
 as a reference. Note that an exercise intensity level of 60-70% of maximum=
 heart rate is considered the ideal range for burning fat. Refer to the fig=
ure below for further detail. </p>

<div style=3D"text-align:center;"><b>Exercise intensity levels and typical =
heart rates associated with said levels based on age</b></div>
<div style=3D"text-align:center;"><img src=3D"https://d26tpo4cm8sb6k.cloudf=
ront.net/img/pace/heart-rate-vs-exercise.png" alt=3D"heart rate vs. exercis=
e" width=3D"426" height=3D"258" class=3D"scaleimg"></div>
<br>
<h3>Aerobic vs. Anaerobic Exercise:</h3>

<p>Aerobic and anaerobic exercise are often mentioned in the context of end=
urance training and running. These types of exercise mainly differ based on=
 the duration and the intensity of muscular contractions and the manner in =
which energy is generated within the muscle. Generally, anaerobic exercises=
 (~80-90% MHR) involve short, intense bursts of activity while aerobic exer=
cises (~70-80% MHR) involve light activity sustained over a long period of =
time. An exercise intensity level of 55-85% of MHR for 20-30 minutes is gen=
erally recommended to attain the best results from aerobic exercise.</p>

<p>In solely aerobic exercise, there is sufficient oxygen for a person's mu=
scles to produce all the necessary energy for the exercise. In contrast, in=
 anaerobic exercise, the cardiovascular system cannot supply muscles with o=
xygen quickly enough, and muscles break down sugar to supply the necessary =
energy, resulting in excess of lactate (a byproduct of glucose metabolism).=
 Excess lactate causes the burning sensation in muscles typical of anaerobi=
c exercises and eventually makes the continuation of exercise not possible =
if excess lactate is not allowed sufficient time to be removed from the blo=
odstream. Note that although lactate is also produced in aerobic conditions=
, it is used almost as quickly as it is formed at low levels of exercise, a=
nd only trace amounts leak into the bloodstream from the muscles. </p>

<p>Understanding aerobic exercise is particularly important when training f=
or a long-distance activity such as a marathon. Determining a pace that can=
 be maintained while using energy primarily derived through aerobic means, =
referred to as an "aerobic threshold pace," helps maintain a balance betwee=
n fat and carbohydrate utilization. This pace requires a relatively low lev=
el of intensity, and is usually maintainable for a few hours. Increasing ae=
robic threshold pace allows for a faster sustainable pace and is a large as=
pect of many marathon training programs. </p>

<p>An anaerobic threshold pace is defined by some as the threshold at which=
 glycogen, rather than oxygen, becomes the primary source of energy for the=
 body. Note that while anaerobic training will result in a person becoming =
more fit overall, it is not necessarily ideal training for a marathon, sinc=
e an anaerobic pace is not sustainable for long periods of time. This is no=
t to say that a person should not perform any anaerobic training, as traini=
ng at or slightly above their anaerobic threshold (the level of exercise in=
tensity at which lactic acid builds up more quickly than it can be removed =
from the bloodstream) can also be beneficial.</p>

<p>Similarly to heart rate, the most accurate way to determine these thresh=
olds is through testing within a lab setting. However, both aerobic and ana=
erobic thresholds can also be estimated using a number of different methods=
, some of which involve the use of a heart rate monitor. According to a 200=
5 study, the most accurate way to determine anaerobic threshold (outside of=
 blood work in a lab) is a 30-minute time trial in which heart rate is moni=
tored. In this time trial, a person must run at maximum effort, averaging t=
heir heart rate over the last 20 minutes of the run. The average heart rate=
 over the last 20 minutes is an estimation of the person's anaerobic thresh=
old heart rate, also known as lactate threshold heart rate (LTHR). It is im=
portant that the time trial be performed alone. If it is done in a group se=
tting, the duration must be increased to 60 minutes rather than 30 minutes.=
 Aerobic threshold heart rate can be estimated by subtracting 30 beats per =
minute from the anaerobic threshold heart rate.</p>

<p>Essentially, threshold training involves training to postpone the point =
at which lactate starts to build up in the bloodstream, which effectively p=
ostpones the point of fatigue, potentially allowing a person to run farther=
 and faster.</p>


</div>
	<div id=3D"right">
		<div style=3D"padding-top:10px;  min-height:280px; text-align:center;">


<!-- /1057446/Calculator-Top-Right-Desktop -->
<div id=3D"div-gpt-ad-1589567013929-0" data-google-query-id=3D"CPPDkNv3zo0D=
FVBGnQkdl98xgQ">
 =20
<div id=3D"google_ads_iframe_/1057446/Calculator-Top-Right-Desktop_0__conta=
iner__" style=3D"border: 0pt none; display: inline-block; width: 300px; hei=
ght: 250px;"><iframe frameborder=3D"0" src=3D"cid:frame-B5DA166788AF54017EF=
<EMAIL>" id=3D"google_ads_iframe_/1057446/Calculator-Top-=
Right-Desktop_0" title=3D"3rd party ad content" name=3D"" scrolling=3D"no" =
marginwidth=3D"0" marginheight=3D"0" width=3D"300" height=3D"250" data-is-s=
afeframe=3D"true" sandbox=3D"allow-forms allow-popups allow-popups-to-escap=
e-sandbox allow-same-origin allow-scripts allow-top-navigation-by-user-acti=
vation" allow=3D"private-state-token-redemption;attribution-reporting" aria=
-label=3D"Advertisement" tabindex=3D"0" data-google-container-id=3D"1" styl=
e=3D"border: 0px; vertical-align: bottom;" data-load-complete=3D"true"></if=
rame></div></div>
</div>
<form name=3D"calcSearchForm" autocomplete=3D"off"><table align=3D"center" =
id=3D"searchbox"><tbody><tr><td><input type=3D"text" name=3D"calcSearchTerm=
" id=3D"calcSearchTerm" class=3D"inlongest"></td><td><span id=3D"bluebtn">S=
earch</span></td></tr><tr><td colspan=3D"2"><div id=3D"calcSearchOut"></div=
></td></tr></tbody></table></form><div id=3D"othercalc"><div id=3D"octitle"=
><a href=3D"https://www.calculator.net/fitness-and-health-calculator.html">=
Fitness and Health Calculators</a></div><div id=3D"occontent"><a href=3D"ht=
tps://www.calculator.net/bmi-calculator.html">BMI</a><a href=3D"https://www=
.calculator.net/calorie-calculator.html">Calorie</a><a href=3D"https://www.=
calculator.net/body-fat-calculator.html">Body Fat</a><a href=3D"https://www=
.calculator.net/bmr-calculator.html">BMR</a><a href=3D"https://www.calculat=
or.net/macro-calculator.html">Macro</a><a href=3D"https://www.calculator.ne=
t/ideal-weight-calculator.html">Ideal Weight</a><a href=3D"https://www.calc=
ulator.net/pregnancy-calculator.html">Pregnancy</a><a href=3D"https://www.c=
alculator.net/pregnancy-weight-gain-calculator.html">Pregnancy Weight Gain<=
/a><a href=3D"https://www.calculator.net/pregnancy-conception-calculator.ht=
ml">Pregnancy Conception</a><a href=3D"https://www.calculator.net/due-date-=
calculator.html">Due Date</a><a href=3D"https://www.calculator.net/pace-cal=
culator.html">Pace</a><a href=3D"https://www.calculator.net/fitness-and-hea=
lth-calculator.html">More Fitness and Health Calculators</a></div><div id=
=3D"ocother"><a href=3D"https://www.calculator.net/financial-calculator.htm=
l">Financial</a> | <a href=3D"https://www.calculator.net/fitness-and-health=
-calculator.html">Fitness and Health</a> | <a href=3D"https://www.calculato=
r.net/math-calculator.html">Math</a> | <a href=3D"https://www.calculator.ne=
t/other-calculator.html">Other</a></div></div>	</div>
</div>
<div id=3D"clear"></div>
<div id=3D"footer"><div id=3D"footerin"><div id=3D"footernav"><a href=3D"ht=
tps://www.calculator.net/about-us.html">about us</a> | <a href=3D"https://w=
ww.calculator.net/sitemap.html">sitemap</a> | <a href=3D"https://www.calcul=
ator.net/about-us.html#terms">terms of use</a> | <a href=3D"https://www.cal=
culator.net/about-us.html#privacy">privacy policy</a> &nbsp;  =C2=A9 2008 -=
 2025 <a href=3D"https://www.calculator.net/">calculator.net</a></div></div=
></div>
<div class=3D"topNavAbs">
<a href=3D"https://www.calculator.net/financial-calculator.html">Financial<=
/a>
<a href=3D"https://www.calculator.net/fitness-and-health-calculator.html" c=
lass=3D"topNavOn">Fitness &amp; Health</a>
<a href=3D"https://www.calculator.net/math-calculator.html">Math</a>
<a href=3D"https://www.calculator.net/other-calculator.html">Other</a>
</div>


<iframe src=3D"cid:<EMAIL>" widt=
h=3D"0" height=3D"0" style=3D"display: none;"></iframe></body><iframe name=
=3D"goog_topics_frame" src=3D"cid:frame-A8371ECB6D8F0EF0BE13406549B5AAE7@mh=
tml.blink" style=3D"display: none;"></iframe></html>
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: image/png
Content-Transfer-Encoding: base64
Content-Location: https://d26tpo4cm8sb6k.cloudfront.net/img/pace/heart-rate-vs-exercise.png

iVBORw0KGgoAAAANSUhEUgAAAlgAAAFrCAMAAAA6vgdwAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJ
bWFnZVJlYWR5ccllPAAAAMBQTFRFW2wAWIkA1AcAkwYABmgB/2sAm5ubs7Oz/5wA9ccA/9QAdnZ1
7e3t9/f3kbJX//+e/6IAqcdmFBMPVFRSysrKK3ACoWEA0bQA19fX04oAwNZ0i28A//+jNTUxxmkA
gYoAsosAaisAAFsAOiQAUkAApqgAuEoAVAAAa2tC2dmC5cEA5OqJ2VcAKQEAO4cdZaI99viU1eaA
7PKO52IA6pQAij8A54oAMFMAcQAAAAAA/8wA/5kA//+Z/2YA/wAA////xXGRowAAUOhJREFUeNrs
XQlD4jwTRqhcLaItRRAKCnihInKsVt31//+rb46kTQtIqrir75fZ97UcmRyTJ89MpqXNvX1aHM81
YiQpdu7zwGqEnm3EiCqBuxNgld6MGFHFNsAyYoBlxADLiAGWAZYRAywjBlhGDLD+X4FV8oTY+Loo
hoPvHPmN1wIrURG7Fem1ija8b9TgK7vGSvYVfFKqpTRljbE0RHGooOSISfBEJSVSKSqdgqawZFTU
AOtnSDEU4sIMui5NuhPgcFryG3wTiEICeY4nvgoab66Ln9Rs8QmUcFRNgd8wlqJSQcAlAlnUpqOs
PQzoo1jNAOsHAavotEho+gOeXWSKVuiJb2o483Bs2KHbEsgLio1WoxjAdAcu1xMUoUTJJkgGsSZL
jd+3HBu+gxJcQckTgAoYQzGwSlyBwxU3RMUGWD8JWGrPPYRUieYegRV/EYS1GHJcjNVLDKxa4Mau
KqGZlJYbNtQKSi5B1XU9ZiQJrJbScsvEWD8eWA44w1rAH60DVosAUUoAJ3Dhq5pgHK5lI7CcgPDT
iEsXqUY3gI9qa4HlBiZ4//nAQrYSrLQWWCX6zk7oSMZqaDCWYCqlAsdFeoQ4zSbIrWOshgHWz4yx
Wg0UR049UwdFSvxNKwIWR/VvqtsTjAX1uHap5axqJhvzHFZpKVhrEbBg51BTYiyuoEYVX8mKDbB+
4K6wJNlGbr+iXaFHwPK8K88Nr8g7uW9pxoKKXNw1Ui7BUTUTdCgAxVgU9kdGwjqIslK7wlZccckA
64cBi68kkyknW+7PAFguf1N8i9INTGbJsEeixCnZHhSzU5qJwL30th5YCFWI75wIWOLyNiKqWlSx
AdZPjrG89TFWo9UqBvxV2hXGr2sl3PWtj7FqXpSKWnGFVAcGd6sxllBuuD/NxAZYyV1bw+GMwLrg
Hb5qrQnea4n67A27Qjv+1I6znVHwTpRVK24Altw3GGD9TGDZIo9V27ArLK5NNyQtYa9nrCJnx0Sh
oJZIN8gkq70ZWO8kxwywvjuwBKQ8mXlfAZbDXjCVIMU8Vksho3UYaLiK+8MKamo8z9sBB/OkaWDJ
7L39087pmHRDS57ScUTOiI8AD/6iUYuAJaZXnNIpxad0am5glxqNkoe85ISB0HQUH2u3WmpLUEEj
PqXjRltUmW6QRaOKXccA6yemG1z1RAvCI3USWiS3XJpfx45PQjNjufKDVur0tawxFremnITm7KdI
YNSCcOUkdBAmShpg/RApeZ6N/64g5pYXwOB1Kw3YxdniXyu+Nga+YS/VKtq2XeTLZuh9ifIDJYrx
rxRNYaCoMnmFTKMoi7/JOqg34rIZLlrEiovQuaikAZaR/3MxwDJigGXEAMuIAZYBlhEDLCMGWEYM
sD4NLJuvTDNihKXl7QZYRoykZEeMVTRiRBXPxFhGTPBuxADr/1XE2eIaigGWAdZnpcVXatmuG/Bv
D0P3p13zaYD1Le1Ilx4HQbFRxOtivKDVKLUMsAywPilOYLult1J0/bHn/d+vNAOsXUjRrQGW7IBu
fNV4e7sKirZdMsAywPqkePZbw3Vs4Cn+9SF4xsC1DbAMsD4bugdBEBaL9BMvjN6NKzTA2o0V8Sf1
geN6DoZbBlgGWDsK3THX0HIbrcB1Xfr1ReiadIMB1melxreebbTeao1Sg35HXQJpGGAZYBkxwDJi
gGXEAMsAy4gBlhEDLCMGWAZYRn4EsBri/qx8XlZXasUrOn1bvFKecrRVSrZXpDsMFb2i/gV2UJq6
1rA9L8uNzVqoVStxk/pSxFvf4sOcMsyPsEIrkw3leLLZ8M2xr0CrdJXFGA49vsrBGbCdvwOsWhB4
eIvNlhuo99ffan3Xowc4gHagbZQWtuXhhVCul+Hkrw1tuCW6eXKQ5ZQx3zsbmsr03IgG3gerEXpZ
zve4AUEjqw3FeAJXPm1My4gumdwOswCrBcPBe9RDk27g/B3GwnvXwTdoxwy2dBzsFZg0C4XUHOxF
660E1i+5rSxtYdfsbA8WKXpB8c2BDrbcDDFAzbMJWJlYTjSQ0YZyPJlsSA04tazGeKNrO/AMqaM2
98UxlgMTjc2V3GwO2iPGymIWx8FrC9AotQyzXXMaeJ4Pb/euP99OgCcHS3hrvixerWiXmLGyODUg
kZJASMmtZWAsGk8mGwIubGwMmNjOBP4GTHLLbfG0/R1g4dVvtOgabpZfFiDvwPrxMtzQFW/WWBJL
NcM6LYYhOtAi0Hmg3UO7iE3QWsnAIi2vRsBC79vKwHIUF5ANQ31gifFksmErBLcLRkRlL8t00fWN
SMQq130psJyA7mJeyrTaMMpCY9SyeahWwxaM9ZaBsZxWCRmrVsNrE7QXaLHk2k5WxoJox3ZLNRhX
LUM8J6yQkbFoPCXWdvWBxSsTlBuZHg2FnfurjOUEBPyM8QHgqsS2yWAULN2iGKsFE9/KMG3UNdJu
aPOpC/Roo8d1MpCjh2qug7fC1QdWTQALO3nlZYEjAgu1i/o2DASwshiD7NHiK4ccdVxfCawAPD1s
ShqwNclAIvggrSv7rRjYWWg8uGLyDwIIETLEcjY+0QtWwFUGVygWqe1eBZmUyHvC5jyDKwQr0CPH
stlQjiebDXE/jg93DTK6QiYN3Ij+rV0hPi8bV0HDy/LwKnz+N24x7ExaEBhwHssOMuSxYK98VeI2
s8WrmGmrwbxly2M1rtAYga2fWmpJK2S2IXYtow3RiKXYlLpEJ340UkymzEzm3ciXiAGWke8MrGLN
MWIklhoAq7XtVkel0pbv7dDFXxEYMRJJGOTwpJz33n+uu6VAYO5fZyQtXm7r5jfYtn8vheGlESOq
hO5OgHX3x4gRRfJ3BlhGvgJY2oxVq8UnGaKDUzPA0pTCZZ7+ghT+/HmCQ/679fCpgIiArj1Fhy9n
LMfma9pqEMkX+c51+PjkwHPw+bVrgJW/g7grT8e7+KAxuruQyj2BOuI+1MLs010olLM09nRHdecv
qasFvbaiDkIjBXp7qddWWBDACmHC7u50gAU9u/zDVsCuwVtsMksXn4S2FhTuuDEGVqgHLGFDmO0w
YXldxnKu+ISw7ZbwMcriEJS84lvLXsdYhad8Hjt6d5m/vBMHrQkokFGe7gqong/zeS2tp5AG85R/
uova1NTC4mBUmA6ttgpxB5+gd3dPNMzt0/ZHYiKPdgi1VhnSBh3zeXwBFnzSURQ2vMQu/tG2IY6J
jCiAocdMwoZhgdqILa8fY9FZcrr7xZX3hgfbq4Utz36zG5tcYYFmC9ookDG1Vpuw+R1a5A6NouvS
89QetqLfGFuPiYe1dYSqxomGhlApr8fFskc0fcAOBe25Fto0Rj3/ROwR5mPT6IFEMNZTnhlLD/xh
NGl/FGvox1gMLHy+ejGo4VUV8EFQDEqlYqtorwcWGEPMtThoQ+QPjgoJARj2SXt4uFDzYYbG8sKB
Pj3dsSssaCMfKOQJJpkYJcwELMbl06VWY0+X0Rrl3j5d6nYRhhVKV/iUAVh34N3y8qC9OClyeYot
r89YpQSwWvRBwyu2bMez3bXAQlJMAOtJn7HQWV/Cq0K+EBb04QjDy9IYGwX8IJmyoOdo2HJA+Rg1
EbD0VowoFRHI010GxiKCzAisUBhP14Y8GKWrGWwInhDQqwIrI2O5fC2YK68CBkfYCmvBOmBRTBV5
p4yMhXGF8E2X+sPDYITd0wdc4R9dLY6xaKbzT5dZXWE0e3rAuoyNQu1cagOLbC6Mp6f1lAaWvg3/
xNFEVsYCYNFtpp0GbAvh0KKLJ0v2W8t1gnW7wjtmnidYOuKQzwCsAuuQb9PWKjBNomIWGn+iWS7k
NbtIHbok55nH3mltFAqwwWNPQ5tD2BTqucK7yBNiaKwZMAkcFsh4ujZEDoYABLatOHXioGnDPPrO
S2pH9Fh7V+iCp8afWwb06wNxeHPwcjLbWwOsgtgii8gFdsB6dHwndtbCaYd00NUCV0g0qRkt3XHt
2DeYhEu9WASLUQzCxZ9CvdQGJbBwup/kdi+vM2liJOyUsE0d+r4UppeBj54N84ilPAWP+T/yoGnD
QhhZXnp8Tcaq0TPoMJHVaNXoQDeuc/Ajp1Fcw1gk/OJPfNg+vA+pxVp/PqSlNqmplHVgH8oyRj38
88FxfXUPZVv5tOXz5lyhkS8BnDlXaORLgGUYy4hhLCOGsYwYxjLAMmIYa8ved83rd3bbeZ1CSVk+
5jNp5h+XOx3icvT4GB/+YsP/14w1sqK86NIiu+ZHFsjjcu3kL+EbYQYopIeswWM2zbw12u0IQf78
ObO2d3hk5Q1j7Wo08Sw+klkLAIDRCGbjMb8eWML4S11gnXGxgrZmfnSx0wHSQPLrx5PEU95aGsba
lTzGVh0xnSwFHtbNxNIaWWes9/ioByxBVKg5yqa5I2Att2EmJqpHyzDWziIQaXF2io+Ra1yu80gF
a/koGGAUL/R8KkhTYyhZP2n+0dIUpzrWVJh4m1/9Yk3wuISFAK8LMJqVRsUbwHk+nzSGYazd+cKB
n4pv1vHK0iosCXqADYZH/uwRXNsoz9g8+5N2O6PIAaY1C6j4WPiT1szTawBi/hFjPVAdiAaWAvVE
LEsrv3wkj01R4Sif8KYWq/6xVFniN6LSqII/j/Td6AvCu//vPJbAz5KmU12yZ2uBdUHGBwQIyDxi
SPYooPRIcxezHjm+DZpLy3/EWV7+SWny7F5YWDN+P6LjI6lcxMA6iwo8WtiDkbpY+BsY0Yj6Rz0c
IQbh+DhiGC2pghGG9fD9MtFbw1g784UMExVM6/wCcgYiEb9jjaUCS5zRPHy+XEOIseYZa+Y59hG4
VjQlY9GbgthF0FcpxqJ3Sy6QV/g1z9/wR1GMdcajHPE3Z1EFic3gv9wX/tcYK0/RNf9NGLawBlj4
mQRVYhIeZYj+eJZwJxGwNmkuI1cpNVmlIEN9nn36cBnRG62BZYTI5DKQLwVDKsG7GCUfoyDyuwDr
v5Z5F45p+UeTsTAQwokRk7AcDSxOFf2JCWQ9YyU1KdyxpC+MNZlcLiIQ5RVgJWMsJU2mbvuA3Qr0
D9tKMFYh3qnkowoMY31l+P64su4ZZLCVU/PR9D34Ez6wj4Ko5fHRGkhiSsJRAVZKE/OkpLlMaUrG
WqpTLYCViLHyajojASwpjynGWn5rYP3XGAsijmj+pa8QH8Ofs+VyMEowFsU9fyJ4yLCdlfxU9jOq
MNLMC02BKAmgWDPNWH+0GOsiwVj55bJwUVimY6y8Yay/KWeYCYgsvow+XUbgyCfjLt6CqS40L1wh
oEXBJgdf+fWaotgo3hYKzc2MVZCNDd5lrKW6K30vxrpYQZNldoW7Dd8fB/GOaskBUBwrKcDiOcvn
o/koyG3WIEpHjRLR+1mcx0pqsvNdWnJTOsKyo3cZi+kRujZYZSxlq5G3fA74zzbtCsUec4WxRCnD
WDtLZcUGpazk6FGNwUePah4rlfqEkpgkstg7PUY5qXSQpUZvguswxLIeRfATa25mLIyeosY2Mxbl
sWgM+fQpnTx3V+Sx8vG24XG5KXX3bxirVks/FOEnAmupxkWUjrZG8TQtH/N/1nkZubejtPfIiuOt
5AlfQTmrmpw3X3JOVNFkDFxEwFLxSUn4ZIz1uBJjyfz6KP8nzVh8oiDKvEtWlSvLf/zzPRirEdIT
K0riUQZ22PqZF/rl33mfXMXrXsszd/Epwnwq9bVBc+WUn3i3WvtqY+u/UJvIr9VXXq1cipYIzv4p
YwlgBeKBenb0PJX/zhWkn98nPVr5nzHU/L8M3dcyViCeXvYfBBbi6pO4yC9/CrD+bUf/rxgrz4nG
H4KMny3/X4wV/WTdiGEsI4axjBgxjGXkLzKWG4C4YRDw0QDLyG4YKyUGWEZ2wVgtz7avbDv66zkG
WEZ2wFjm3g1GvoSxai3nXWCVPK/ID+gu4iOm6WnW/GDvtcAq8C3yC5fRUetOjE+X/OAHfuaMrhqU
e1LUCk9PekoF0eYT3q5W7/k2+YRW4UnvXp15+SydO7pJ59Ol3gMExEAuL/HxF3lNrYIslee7nur1
MNLi45Pmo3RYLbpz6Z1QW7Mr3ASsUujZbvGtFQQeHMBXwqfF4kbGEo9meQovn/g2teKpK1vk8pLv
f/zEd9i/DPUGiPdqV9TutNoq0LNmUPcJrPOEunktrYLQusv/AUPeaT7thwB19/RE/dN7gIAYFj4t
gm7trKUlhoXGZFto9TDSKtCtcC9B61LfhmQGvI/002VhUx5rE7ACeipTzQ5qeGtuz8YHCNjvucIn
eXP9fMh3ob7Tv7H2H74FdUH7cR3RE0vk0ya0lATNxbOvpRU98yTVYw0tvpU6aeg1VrhLtqB1x3ZR
qPAUPSiloGsMunky31M+f5ftsQNhQTF7BsbCv6XQ8fCm3K5zZReDN7vVam0GFrUIng0fMETTrX/z
ezlOoH7N51lEY+JGsgALn3iS4Y754ubYIXg1eefpDMAK757oiThglYL+sELx6J283mSLRzvdRU/g
0TIiF7qk51xleEaNfI4UPTYpvLvMZ2UscIVeEDpIXE7YanlBo1gqBV7jfcbCG4FTi380H9eReLYQ
eEI9TxNBSVheF1j0KBxAFvG53lMHhMFJi33ik74W+Oq7DI/Lk4x1Wbjkp5ho3bFdzDX4XPITd/wk
Oz1gFZ5omjI8o4Ybu6Qb0ucv5RN4MjAWBO920RWM1Xpzai37LSja3vuMBfEwLoGMj+uQFuUn5+Uz
ACt50GMs+cgTvf5JxsrLp13pahXilQM15PWC42ggkoWedOcalliBnqX4pEn7gr4Ld/HDtbK4Qrks
V5+l0wi9VkOVWgJYNdgFeh7EV84bxlmwL2y8uSV6os57MdYThz1IyplirCgkyQYsYcIsjHVZkBAp
ZIAI/qEVo/scUn620x96AkzWGOsPP+8kU4yFgT/SSF7XhDQSfGAV0CI9L0WPwLkxjrGorULGzHsr
8AK38ebg2R7cDBbpmYWUgdiwK+R9wiXtG/Co91SikAaYFw8Bu9OjcdjnhpeFyITi3VYlCgny2MWC
NhgLrFVgLWhI6wmfpIX0RuO5FE1unzUaCLhPtCUewrzusP5E6NcyYSHSIsTDVl7zQSmkdnlH0wuH
8HJNjPX+KZ0ipa7eHNsuIYGVMI9lUyJrLbBgh0xplMsnjnqe9IJVUCNfLwLjJz21P9gaAqugvttq
FNHFJ0r0FPRaSmphkuIyrzeuy3g8haenvP6w8gXOEulpyQ5iM/Ho9LXyWeZLqOG48up0JRnLc1qq
1Ezm3cgOMu/bgvf3gHVpTGkkGQntBliwmSgUzP/m/+j/HTGWESMp2Q2w3DMjRlTZEbCGv/eNGInl
1t3N9VjD30aMxLJ/qwTvTqkh/zUSl2YZYBnJDCx3Q4I0KNYMsIzshLFSmXfPAMvIThir1nKktEq2
GxYNsIzsgrGSUgwDAywju2CspNSCLDdeM8AyoslYb1cGWEa+gLGy3bvBAMuILmN5hrGMfAFjtcLA
McAysmPGqpWC+MyhAZaRzzAW/sZZ/AuCMCYsAywjn2KsZObda5nMu5HdMJbnXXn8v22XauYktJEv
2RUaYBn5il3hzwLWfP75Ojq5TW+STeU6iQa3ttzLNXc/4F6ul/6omZv/9W781xmrZ1mdT1fiD5SJ
8a1NxXJWj/7OZdOdrZ3rfMWIO+s7tnnt+f783zBWy3WDQPnP/TkJ0o5l+Z8HlqUYPreJseZW7vff
AVbHegcIPX+Ff5pbgNMcdP4NY33m5rb/GFi+lRv0dgqs3+8TVkwPXwesLQz0gQH+PcpKMJZjF+W/
oh38JGA1B7melfsrwAKH8jvJWFt5oDf4JsDqWM1/wliKNDw3DL0fc0oHp8ASyxFmvOlbPgeyvRy+
7EXhK7zrzJlE5jkLJ24elwBgdWSB3wI/+ImsLEE/acZSWgJnibU2ReW9RIkmh9mWbKdj9bC7+Apa
UuPrHD1RCpZL02r24M0c9eKREUiaVg+/4nY7MviLx889aMoefH7xfW5X2PLwovfSjwne5zgxcjmi
V/R9jrmaFr0U33To3QAB2LOwBLyCqB9fkuEfrZz16FtsfQ7e5/7Awhp6yqrv/V4TY6ktQXf8gQ/l
5ly5TyVyXCInIkLREfgYQZaDluhDZerxc98nYOW4s6IK6kGTKu3wV5aAFP8V459T/6kHOa5Wsu0/
YizHBrZyiz8oQUpGlsuRrTwnxmh2eA58LuX3aC582kXmhOGROJqkk7OYZagiBpbPxXpzZbolMeaE
MLCoXJOmcy4qhzJYeWeAJUTrvZ58Ce34v2WjVN+cPmyuuELonWA3OnSofz0BLPqKuyyBxaMYNH/L
LzoSr7nB/J8xVq2IsLKdn5RuYIuKEMnnqVFpv6N+T6V7YhPZFKXoyMyChDMXwOoNcquBmOqoUNQI
ilqaW5bwViIcwxJ+zHq5iPR6cuqj7s4TlNVLdDKB7YixIsqOgBWPfy57IIG166hNn7FKELS7XutH
5bF6kpE6SgjOMwQhko9+oUlG7pGgz+ylLE0z4KsTTgBaE+3GwGrOe715by6aTbQk0dqMCFUBjJxt
7rBwrR2rw71TdhAxsDpxkOgLxycYa9CMe9VRE2xzjs46CWj+TWAlGKuBwZXX+GEJUpiUOYjAly/p
Iod/BswqNN2RNJUQfB7jRaU0nqo1ExEXSsRYvbjuCEQyHMMSvTXAIs2cBFYka4DVlEgU0pNJqTiw
TGduBbCav1U+/EeM5dgYszd+XObdHygGTzAWR1BsX5jPDkuut7q3Y8aarzBWb9XtztftCnOJljIw
lgTWICd61/m9CVgYN87l5zLG2gKsTgpY/yTGatAPoEux/Ix0AzAVR9G+iLojxopDjGZqSyRTSwnL
i+hsTjXQVDVX9+dyKpN5rGRLQiuaTigxV1xc7HKbEXqaa5KoaWDF25NeHGP13gNWL+LOf7or/KGZ
93gjJaLuyLAchqMHaTIpMOQ6cTZqLhIEPs8xbxsH0a5wLrH2e1sei1ttJlyhQGqTkhmydYq75RYy
AQ1uqdl7B1g0HNhl6sZYotaeldwh/H3GchPyQ4AVnyUkiKkxFuYOMUMod/QW8ZqvnGVp0mdseEw+
0ffzKEjvWUSHikdczbzLfX/UUgQsUTntNuei9Zx8mUg+RS35CnM18ZOmEmPlBj43osRY7zIWQYp6
kPu93rP/nV2hk5IfEWMp7ko4NMWwlBRoypnB3DanqePTd038zBf7ScxiWzINL/ZhqKKeY+sM1p4r
VFqK4ymqvMNeC7aNA5GJnXesqJ2onjm31Jknc+9KCM5F/J5+jIXnqqklsWH+R+cKf+ZlM3NlFWL6
Ub7npOa8N5/jH1GW3v5Opjyjz+ak3UtVi0mFZJo/l2y2N/+daklRIOWohGyJOrLS/fjD34m2lY9F
I7/lh/Ne3HVRVXL8XGlH+OS/d0GWuYL0W5/K3dn2xporu1HDWN9T5p2fA6yen+t0csIB53q/DWMZ
2RVXYY6489cb3vS7Qv5nfmL/H4BWr9eb//4HwPpvXEFq5JtJ+neFSTGMZcTEWEa+LWMZYBkxjGXk
5zCWU0rJJ69u2E++2NftE/8FyaIWaYljFq1Y+yNamj38pDV+/85ujd8fssbn2pKVZNkVOi2nxsca
/aXXtc3AupVnLW6pvdu5Vk+pNCiDoNr8Vm+zzMWEFhy1GhNtQXl+p9eU1BLHqBKdDkprwN9s1uDx
aLYlGpsnDpptzW9l126zWEM0Iudr5Xos190ILNsNXfxFGByD1lsjwJ/xNOxNjLV/OwzPsIPzsxAf
4QRvw6UG/M/c8HYfimNf4O3FHajtbydeamz/ArSG+9TW3cX2xs6wCWoU2pxDy+GZhiWH4R1rDcM5
13K7HSPzIXRMqMOfJfRTQ2t/6YYX8XjIlNtBguWhsX0a0Hx/eYcHDWu44XyfDE5d219C0xpad6FQ
xunFDt+hWpqxgmKrFl/dkHh0b8ktOiXXBhCVWvCBZxeDt5rd2gys5RBNuX92dzGHJofD22WosQaW
FwJYt/M5Hs7mFxcawKLGAFhz0lre3p652225XJJR9i+G0CbYcX6hYcv5culKrblU3k4hy7O7fYLK
8G4O/by4HWqEpfvLCwIWj4dNqQMsagyU5xfuEg63FxqLE60BtruAtrBriE4dYAkbQlsIRGjo9wUD
K3HvBqQJu1FbG7yXXGCrwH7zPHxd8+ySSw+y3+gK989wrm/di/kt2OYO/gzPNBbpXALrlqqY6zk1
bgxASZNMFWjMwEXIJIJgXgLVzV0NW+5fELBuQQtP7ZKyDkbuyN5QnDoL1KBDWfs0uxA6gtocTanl
QUVjEPaAyVE0LQ+m3kdEIiWfXegYAw0tIywoPzwT85XcFbaKAT2fyW6s2xXaeJqnhth6c0Kn6Hl2
y3ZsewuwLkJyhTgJ9MF2pxG5wjPwNkNwPMt9fWBJbwZD/K0JrP2z5W9s8+xueKfjCiOtOY1pOc8A
LJgumAiaZz213wSs+dkQHMxtqOcKI2ABjQ+5x3c6C0asxf35cLm/vzz7rQcssTixzfk+mFC6wlQe
q0E3bXDBJa4Ai84fOgJYrbcioKpl2569DVhAjndLxHUGYP2eo19a7g/vbudnd3NNYP2+jbyZnhIZ
5XY4xzbnw+HZUA9YFPwNf+MKp4MesFy0xnD/g8ACn3YbLue3d2dajVEUAh5xeEuTvNTjHiQbNAQS
8e1+qMlYc2FLKA7tkfq6PFat5BG2Gu+6QsxEgCN0S3awxRUCjvfPzjK6wt+Cv3HtaM0AAwu1gOCA
fdxbzdUGnQvvIAa4gEW+P9dhR9DCyJi0boXyvhaJwJCwuDv/mCu8ddGUw/0MrhBtiE5Dy38SsBBX
c4zccVxLLZ77HYf6w4t9QejrMu/0Y+iwlAJWI8TgvYjHFn3QssEvFm1vE7DAfHi54/CMYkcO3jW2
T7cQRGMweEvB4N3FrRb5YGORFmzBLnQ25kBvEBHcLi+WQKtLl+PP7VpLF8YltPig00HUgtCYtMAg
WsG7sAYF7xD3oCm1AgNk+bmM2gFXc50bHiLZgzXu7nDbdAs9Dc90bYjb3CUmGpYuh/5rGKuGD5RL
/RpaSTfUZLoBjg384TT9vHXdrhDjHRgW7powWrrFYElj2UBp2M7DBih0l7SXDzXiA9kYll6Kdxpb
UJezE4IUQdld6rhqDOTosce4Pd/nmHf7upZayJK/l3fh8FbTGkBVlG7AHZBW4oAbg+2giFO56a1q
d2QNVMIJANGKsdiGpAxNUkLkdg1jtWwO3xMhlpIgFb+54Dwpv94YvN/OOVkpl4temg6UqKDM6mnu
g0RjYk8C77QY61a2kiVBik3N1QyiViLxNmprniHTiU3RuObkb3RvtSoaY8vP4w7rWH5leFo2jCwu
5ivBWLVi4PKmsLaLc4XRSZmMZwiEWrYTC0mt/XTbm7Wyn2bZT2npqe1/qK20ET+kpW+M/YTtsowr
0pK6K7+Etj/6E/t9I0ZiSWfe7VIxlgwnoe8ujBhRZBnu5tJkI0ZSogDLTUmGS5PDoREjqoTqrrCW
+pchxnopvxgxIqXc3tWlyQZYRgywjPwgYBkxogLrzjCWEcNYRkyMZcQAyzCWERNjGfnxjFWrGcYy
sntgNYKGYSwjuwYWPv2kYRjLyE5iLKfoBV6phrfKcsMw0x39DGMZ2chYDl2XHNpv9Gi5omN2hUZ2
Aiw7DD3bC1382YT6xELDWEY+AyzHpd98FUPlcdA/jLFUdJc3Q72cbRVw8R2tnHL535lErwe76GEi
xmqFDCAv9L5VHmux0B6Nfxp3I9ffXqwMlUP17/e9fOq3y8m6P9zFl5d+7q/CCvu+0oPFuxq50/Ju
GasR2gJYpW+UeS/3B1a7rGnFQT8uafmbyuVEheVFn5+u09/WAwAW1L25iG8tNCej7Ft/FVj9QRom
5dzg3WWw2Gy4TwLLDlvfiLEWvqXCRR9YuQ1aUSlYzQOLnqe1pQEC1juMVW5b2jQEwFrsGDs5/50a
MzPWOizuCFhXHwLWl3G5ldNd5ElW2RQqlHM8tVCz1Qc3WF6c+rnyNsZ6L/SAAv3yy79irPK7dFnO
HmPtgrISMVYjDGyUAPaGKFfON2AsgMFpvITAJgsZj2JotODoVHzGXCQLLOLodbGWXnzrVIblC7Wa
RN0LwVhRJWX+NuUJ24loOWpP1vMSN8PAWsjvRaHUSNRvXpLfqCaI/HCyotRQVjqyiP4m+iZLlHO6
sYc2Y33iyRRfFrnD8mkPBKNwUOSf0jr18U0OfBQ+GzJHnALAauNTJgmHvs9Kp/SUv4jdodBpWVCh
Yj6gLXwUJa19WfdhGT/MtTnGsvpRdX5ftXw5ucTjAsiN2GGu9aWP/VwgsMp9MXW+j0CnTsMHpzQS
6t3C2lvgu76Ypj3sXT8yQe5UxRVKv4yVQn/9F6pHFIEPoT7xlZWTvaKmsSa/z5Dkl2wyHOpOgdUK
UvINMu9MQiIugThn4PscEpXxSaO+3wcr0Gdkv1ML7c9vIHjn6YQ4yvdjEEXrkUyuRLRQCqon08u6
+1ybL4L3sqzOGqiRSzK0Yx0qUM5RPfA/dn+PNH0ai+RgC4E1oE630TVHI2mLslRzNMZFwgTRVgTV
4H1/gCq5qB7R39O4IwN/EUV58MEAK8ohWHPcNzYZLJTcyy6B9S2vx0IYRDGMD0ERhtHow3xkLgiQ
YCKAEE5xyaNJc/wG/pKVYNKx2MsiBoLPsa4MtWSYBLUAGxAAZd1tqButznVj/CaqW8AcJrx1W+FD
v/2Ck5zj+aQKYHLpi/IL9N2KojbJWDCPZVwWfdQ8RRjisGAk5egNktWiTVuZyASiyUOMsZiJsLtl
WQ/hpyyBJUbSjxnLwiTKgmwJvUWzAdLKqol2FWN9w+uxcOXKv8wMIIdt9IA+L3qJOTzi5JHX6eNq
5eXn86SXV9IQ6e0ZzQ0bPVk3zmvEWDme0HJOobuEJ6RZhj7SguB62OmyBmL/ZYWxRPBNnSTnhSNe
EFgQgXKM5aQJUvvMeAsR1yMZa49HQloRY52WY4/AkYQg+R3sXL87Y9GCK5eZE+Bv/5QEDCAGH7EF
zi5YaU/G5wJYa3Y40nq5pPUgMEFngFyn1B0xEjMWRCEr+0+Yjz11IbSpi9Rx0QS4lsMoreavMhZ8
y8p97sIpEnGuHI0/nueECdLAAujG9aCnw4hBMlabIze/HDNWNBDRGH/9BcCqtRIncvg5Af84xoJ1
tWi3F7QYgQUGQvxo8iMbINhksLNQgLWSSZDYSMRYGBpxQNUup+uOEqTl2PRtJchR8j7o12QfY0Rg
J5KTlmQsmVbDZ1Zi2EOMlSvLupUMRcIEq4zF5NwW9VCQJRlLAZavAgtaj+nvi4Al8lhFuybypP98
V0jzhP8G5BFhuQo5fVnDWHF2vM0UUY4XYWp3zmt1L7GxO13I6sRMRgkvhbGsVcZaKNOAjCX72H7Z
zlgCWPIb3KLRrrW8GOxtYqzIBKuMxZXmZD39l+2M1V7DWHLt7S6PRcAKQucjwPoSxtqzcnv0T8SY
pyKVE7NKnzc/HHycIpXJaCMRY60LtaMAGGN7EU75qivk9uDDiLEi5b04tdZWSRF37GXRxReVscR5
JFwqEclR7BgxFi0NCquQsQYxY0X8Vn5JmGADY71wPSIy28ZYOCwedRRjZTmRkIWxgvDbMNZChMLl
Q6IX3MJQgrDflv4BJwr3QHzAXDrv5tpyV0gbHmCAhRISRa4H/CzW1871KVgWG8CXuG7cOL2kdoW4
BTsdRPum5BkQ2d7Labz/IsYSXyx4V0grAN8ojLXANVBGn8wx1mHEWLw7pfylagJ1pZQV0HM94Fh1
GQv7Vi5TJqO8kj7ZJbC+C2OpI8RlRhkaoC9L8Q+Ua8KP+rRn8qM3MmDYGyDrKSdtZADO6SFiQyif
G9BZQ5WxKGnFH8Z5rD36aKDEZ6mABBO0WGfMDcxY3IJFwAKlAfbTVxkLIUWtpWMsOUZMAqgmiLEM
XxKht8tqr1cZ62VtjFVGuHLf/LKyafkPM5bc50QrD1PUA0q9x4HHqS8+IiDSieW+EoliwntgWf01
vpCy2BjoQkzS3sNa2mqMxbpQdeLqBrogIqeG66ntAdVJpwIixhog/Sz2cHvQprqRG2H6F74aYy36
dIpAxFgxY8kxkoNSTBCtFIzo++V4XFRPXy/G4lJI1VauzSZbs+H5r8VYi7byksKeMu4Rqa3oO4yQ
FiIT1F5QgTK/loE5fp88WRjBYhHVRy2Uuda4blLlt6K+uAcbTkBze2WlnrJQVVqgTgMxtZWOYgEa
wEJVicYohlVOdSD+JLZWXA/bLfpKfKSaJ7Lr4lCc5Epul/+TjJUwX+rcvJLyjCPZ8oYCK+e1y2vO
9cfXiZZTXyevIE1cHVDO+e3VywfKyYY3di+ufVNr61pdvTwhfY2rUm850ZHVMYoT9mWRhV9DwTsB
Vq1GjFWD43fYFX4NWhf90xcjiU1SLteHyJE2g3hGaNfAojuPhqG4Bel/93eF5ocfK8EsxXCL8suO
LspPxVifuWzGTNYPFgj2TtuL8i4rVH+lU0yJ819lLCNrwsLybpFq7o9l5Eso0Nwfy8iXACt1PZby
iHHDWEZ2xVh4LxA3KNYMYxnZJbDoMfZ0V5BdMJaSJiwn3m4NI+MDnYEuZ9V6yayURUvpWmqcGRor
Z9Z6+dDAXvS7uGqNDB1cMUoCWF7oFksl5cZYCWC1UBy6IBAVag167WxirPKi/Ut0DV8ob7ekmNq/
8PgLDmX4C6KhJ2sHrTK1pdfYr7Y4YGloua3j0GVboo3yL622xBkWKI2N4EFvd8/WED3FgWl0UdhQ
mrKs1UUxLGkFzWFFPePiOF2LlRjLcfkJ43aassRT7AWZtYLQhYONj7R/s0sbGKvcHoYjuuT2sBk2
Dw97w9BdatgE1IaHL+UlkOdocUiPnV0e6mhBY+URatG70G1ub2wUunTAx2qDYUfY5Fatw6YL4ykv
hi4OqIw9dduaWnQIh21x0JhssFpZHl4Oe65OF8u/2IZ8gImBgQ23oQTrXrISNnG41DMh2hCvB4EB
ub3DF3R5vcOXlcy7l7g5SApYENaXsKgXOKWw5LitoPhWsjfFWOV282xITfwaudDj4fBXk9vcApHm
CI3S7C16oLb49dLTmDbZGGrB/LWbv34t3a0TsGg2Qzwu29S1pdtuuxog7jWHSwAWaC3DNq6al+b2
cyDlZtNt4riav9ru2WEPDsPh9rZ+NZcILHF4GY5AbWsXoTjZELUQWEu399LcBiwxLCj8qwc9hf8X
TQ3Lv5ANAUbLxQgsDlhcrLpC+RN7Jww2xFh2gLwGJOV5TuhAlC/uorUuxiofjhhYI+xx220flkej
w+0zcLikZXZ4WKaBwlsdB8qNHR7CokFiAIZ0NcinR8CC0gtYbkPgn6ZGY4eHOLflwzJqQScXvcWh
jhZ2DIMQHCAMj8epwXRleSj/AiAfNodlbRs2AVjlhQso3u5AaVhlNB9qgyZ80Nzew8N2iPw2LB/+
Amu4TXkNRiZg1dwi8plDCAvsoIERWWnTrvBwdIZz3Rtij3EK+YOtPSWjgP2GSPo0Ug1kicaWQ3dJ
vmCow+OIPjQCakFDvUOBtG1qTBqE+YULjnSoc9a2LAYCa4wig+FQ53fsMbDQpwHtt4duWUfrUB7K
7ZC6WNYZVnkRtgmPQPxA40sNIgCTMZBxgHiiWfyi4y4DsEquEwOrZZcaNrAW/l567a6QSATCgx4i
n3A9GmYA1miIcQHMvVYMyXWXhdbL6EwPWMxYy9Fw9MLAcrWBVUbfebgIR78AxmVtYEEAOMJrU0ZD
WODZGAsm0cUHjpY1bSiA9StcLqDRQ01g9RBYFGONXB1gCcZiYPXAGKM1MZYb0L8w5GP6J/ae9xa5
QnxlOw23hm83MlYZ5g6vl1j+Qlc4XGoCq4yukD2OFhhFY+RAYXio3At/abhClzZA6Aqb2Nyhjiss
0wxgNNJD14HeSYNEyugKy8hXZ2Uenq4rPCwzsPDw8gvWTDmDFnQUTY++SmtYL8MmF4bwtu32DrUW
J0LxBV0hjEoYI9NNQQhSGLy3inxPyeJbw23hTnIdYy1+jYa/FuVFr9lzR+0yMD9EyBprFNV+QfDe
xs0kLLeeDrC4sReh1V622yONGAvIHjbJC9BaQjPQvV64feNaxkX5C7dDELosDs+G7R5y0HYtd/kL
oTxst38dNpuopTGyXxBBwwrhAwTY2NPtRmxDCI5adChjF7eST3mBw1oADfdw/4OmHOpEt2xDHBxQ
KfSv6a4ylmMXk/9SVzcUA/rAwXQX4tCmcN5eH2MBkkGYPWAVYAqAluz2WJASDLiJX9Ka00n3yMZG
qHVIW+bhVvtDVAtKQBuo1SzTrny0PSuITiIM2wtaer1DSlJokOOItFh5SOkGDS1YVyCjQ3HAlICr
s7NmG4oDZV+2JilgXUXjoSyKRooChW1ITrpH/TtbrMRY205COwJoNX7BT0Shn0uvjbF+gfDe8xfd
remXXkaQ1MqwgOhOF7o390QtYi76gRy81Ul1yh7+Yq0yN7kVj9zWL9GkZltCa8Fquj2UXRQHaPsj
Wi/IW5odpHmi3xRkaop7Frf0lVc3xKcfspwiUM55RGeDPqCle+YjcbYpUw/L5UxdLCta5WzWKK/2
9Cu0UuPJ1tRLqqXdAQtdnhEjUn7tCFhu04gRRZY7ApYRIynZEWONjBhRZUfAGlaqRozEcuzuDFjP
RoxI2SGwjDGNbAVWybbthgGWkR0Bq1QkNNXoWtGwaIBlZCfAEs8rxOdhukEQxs8AM8Ay8hlgFfl6
rJaLz8F07PgCZQMsI58B1hVzVJGvl3Fc1zHAMrIDYIlb+dlhUONf5bQMsIzsAlhujfF19YE7+hlg
GdnMWOwCxX7QMJaR3QCLf6halJckr17zboBl5CPAaoSh3Si54jc6LRFqGWAZ+SSwMIGFUnpL/dDe
AMvIp4D1ZgdhGJQS2VIDLCOfB9ab05LPkuNbyvwzYK1eK1H92ssn0tVve//lHfhok3jNSsaGsk2E
VmUJYKWeV/iFJ6Er47E8jhMfRN3yz9MTe+6P3x0h1jV+/uj0V/uTRPXjvT7+lVKp7PW/GNj9vUq2
Ea+ak1739/b23i295eukjFcmItny3l5FJ3i3/xKwxpbPprMGe/xikJg3eL8CrL3BcfxRurXqmJ+K
s9d/by7e6SNUP05MM3YAPhRyXBn41fdr0AHAe8DyB5X3Rvye6vlgLyq4N7C4qxub9f0sHk2peu1y
GKwH3j8CVmXPGnO3LL9CNrSOU4x1/A6wYLjJ4UD5gQUrFcC12Q5r0LoJWNVjC+uBD/dYxs/IWPDx
J3iryoPOwFh7Y82KI9MgxsZJ9odVnLSJ5WehgL13Gata8f3KNwKWBPqe5TOifKuyxXcngJUiuOdj
ywKqqlYrx3u7AVbfOq6KNqsctND/W9bv1vkfV78ixoLJHatjrKbIPNXpLIy1tRObjPqvgHVOSx88
Ir1AgmATVSocJVXGoj785Bn/p0mmdyAArOex0uKeHB1EWvQXpCrCj2dF65y1og+rUYtJYI15HQL+
IzBTHQAsUUNFdO35Odlxbvs5fpno5XEl1h4n2hcNJCqucI/GFSoWdwReqoNPwL2PY3xO1Bx1OmKs
uD7RBDcuzTJWB8eaK31AS1cEH34jxkJIIZ4GfVpOzEDVY3BlFlGvXAcVfDjg3hjXGALrHN8Bh9CD
wa2Y5FJEUsFyfn9cZffDdZDftSD+YMcLRfyoxTSwsGPVJEuiP4FeUbvjasWajEHV71eiAA/WCJWj
xxnS6wq/7EdBFOpST4Fh9nx4xR3dozaqExoPfEVPvaxwN9n9jvewSoL6Mb08Vhk7jnOg39QGqJ3j
wxHRlqLTSrhGrWPfq9HKqdB0sK0mFbDWnug4WwKn43gih1ill8diijY4+BSwvEZJ/feFl81MLJrq
c/LRZEKM5P0JRElsDex1BSLRCT7glIE1seD7gT+uIir2/CgmSUWQqOVPoLKx1PJVLbIFvPT3MDKj
FvtpYPWt8whY7ArHg0kVysNk+bB9rOBD4vF583siwJPVVOAlvvYpjsT+Rk6+iu/3fJwnGhJst/ZQ
D6JDcrtcEL+acMUMa5hanz9iaqemfDXWA81xzLTYxjGiSdiSOz1RNpjcutKEYDFhlj4NLmpQLHyf
bG/R3EAfsB0BLGGrLad0Pv6QpmzAIiwQnmhosIiqY8s6B+4GOyDIBLz2xvgJ08wAvwfU9KvEcLHz
56WtVA1TXx3vCcvBBKLWuYg/qlFVEHgOcNke+9QXFViiQqwKQ/fJeZUYn8yMNVRgWo5pzo4RTNZ5
pVodwwF7XEEYHlPZMbysHCdiLOo1DAgB+9w/xxLnDE8BrMHkmOo6rkpgWVTnuQU2AlD0wUbnlsJY
URyhxFjV8z7WfAw6HGOpkRK3Pt7jUauMRbaCoVLPIW4dx8Cy0FI4umccJ8xD3xLASse7/xhYGFth
JEMIG1tABwS1apW+EYzl81o+F4xFTlL4JHWVJOMjmGdygqgdaREipFZ1j2whd+l4TDGWoBlEIGUb
9qrIWEo0Y3Eb1GfqME3IHi0FylES0xzzy5Vu+mKR85fsTCLGOpahQAQsvyI3ErxZlQNYEz5L8hY1
k6dbCd4tNolF1k8Ai3THBGFWjoDF7aJpGUjVqmSs9TvldIzlOC3l3xeehEYsYVRJtiIC2rPOWSLk
QBmGigBWZIPUKkkmfGQ0KUiRJ3OcAhbNXkTiCPANwDqnvGvlOWasZzkR8fz3Rcf9amUCPmjvnLqD
Hnmvf7wWWOKjc4zxfEsBlkzwkUWEK9yLQHK+jiVUvo5KQPRIMd35OmBV5DHNWPRmHDWIjlTGWHIJ
SCxGEF6/U/5XwTvZkbqLY6K14ctkZISByCJ+GljJPFZyzcqByll/B1gCj1U28Xgl46TsCpOMlQSW
JTvuU+IbnylJwfs5BipWfw2wfBnKYbTyAWCpwftYzUuJ6QYWtTC09AdrgCXKk2feSwXvzwqwzhlY
ezGwnhFYQmVtX74DsGCd8VKDvxTJ+5Kx+iIc0masxK4wSttoMNZgE2OJfG1yV7iVsc5pe1mtHMO0
UgMQYJ37EehXGGtMWz0JIOEKfXVRaDBW0hayBBgT83rng3cYa2KlgTVIAKu/AizCYpqx1iey/h2w
wB40QuwgAogck5DnKMYaqzGWCqzEYCpxHut5LGIsNNzx87vAOhcRC0a/yTgtNqQGY50P4o5zTkzs
Par8sl9dz1hireMeYoWxjtcz1roYKxEHiOkmI8m4dEOMNbZEhMvx1iqwztcAK4qxoohM5JIzAcv5
wnQDjoWCIYQNhX/wwTHuqI4xh84z06ftydi3Vl1hcqOTyLyzVqUvdoUqsPpyV8hVAZtUcG+3siuU
k5lmLPxbrSaBJbZJcIR46pi7gUaHlzicvqUA61zsCi3RygQ7ukf8puUKscepXeE4cU5F5mloVyl2
bpgzTO0Kx3JXyLvYsT/YzFj9FGPh7p3GJZbpykmTLcCqNWz3S39MATEVb0I4PUJxgU+n+yJgQSgM
n1grwEKdPfW8Op0rnIhzhZjHgpe8KVONBSahM/9RmIBZIahfRWCURKysibEQihM8cZgAlqiGskaY
P6K2K5is5Jfx6RZsn5jYl2RDemsYa4Mr5KbUPFYqdpYU1CcTcK6pMsGOJPNY2LFJhfNubGFdxqLc
w0TWnUx2bLweK35OoVMMvjTdwPnCYwF5ESofU9yLiXA5kvEefnCcDt7xihL4opK4umESXd3AVzrs
ceZdMRZoDQZKVdTigPPJqXOF3H6asRDAEKlj5l0BFifwueMVynj7e5ggwJcwbUq1fZl5t+QZXmye
Y810jLWWsagDwBgJ/7p2H9P3cWD8jjqtZt5p2Lwu2cJj/RjrmY0v697gCdffFKRW8vAmy4H9pT9Y
rYzFCazobHz1mXb2BJ9j3u9X4ROxKOLi8uqr5LnS+Hosei3Oa8lSfBRasirRYlUtGDmYZ7Wg/L4a
1cBj4G4+V+O24xrpZeK0MxdT2yI1fqv+5TOJY3FiU5wypHdYaUXJdlf85BkV2eMqdaMS6SgnC7E+
pWNjfr9iK1YWJzePxykjVEQSWW6wNIDVwuuTQ9crOf/+piAYsHA08LevrMUT1t9Q8OyvSIrLGGDv
7/ehSucL/JWtwzvAcop0pxk3Gcb/M2D5k70+xkB//4L68fl3BBZuZPt7E+vvr7R0H0RgXNlkpiSw
IF4nsmqUvgmwKPfoa17vtmP7fcc7FHKMJ64y+GfAoj5M5FkpDWDhuUI3wAedNL4JsJ7Hx8fHY3MT
SjUyBYv829tyQvgGk7Ll1wUrwPKcNRmtf/jzr2rVwOrbGUSjDwlgtdgRlmrfCFhGfij+k3kskWco
Fg2wjOwQWMhalBkFl1gzwDKyQ2AhbV2RS1Rum2yAZeTzwIpO6ITB5zLvIsQTp/23//R7RU3Vzqal
HePKYml1PZVq9g4+yx+TfaVWbOxqpli/mrWZtCG2AYtPQa8+utcOPPio4eFTVR3ba+HfjcCqtkfD
4+pzpTMc5vAM0/Fo2KnoQH00PK2KQ/V4OHSHw+15rHFn2Kmysji/jJVoNdbGcqej0TMfKhoqx89y
PFVQGY6ONdIEneGIOgRHGBcYxe1UtfqH40Ht4yqacqSVfKmegtErpKdlB5lHyFFxbOZYH1hQvINN
jvCwDViIraKXBJYTuEWvCHtHz3btN9uz4VO7tBlYuVEIJjwfdjruCDrtjuCoYUtSe6526DDudMBG
W/M2aEisuzoiLZBR2NEBFjf2PB6OXJx9OGzFvlTh8ZyGo87Q3fpTeAD6aIgd7LidERTPuTAwDWPw
eKoj1Bofoyl1Yo7qKXQODFIFK3RcfWRBMzn3eAzNDF3d7GG1E+ZgUACkUUdZK1keeVLkW5TaAT4d
uubZJfetZL/jCivPPKYqWH8Ma7QC069zM4IqqVX4APw61IRINVaudjpaWiCij6cuaZ9vBxYPi8fj
VkYwfdVQByMdAFYVJo0GlBvp+kIXeRv+VHBA8DI81sJjFZo6xp7CCtWFCNZNxUE51KUsHA2wx3Nu
iDw8rq7PY9HNRxuCqOzUrSIDYCpwf+gHW27LtotezXa48PrgvSKB1RmKsYb6wIoOx66OKbH+CChg
1LEmsKpJYLXDih4WaTwV9/h42D/N6fgnBhbP3RCWOfo2TeBX225FDBDxrLPKAPXH4ekzUAhwpC5h
jXGBILEea4QEMbBGyByIexX16y70E3flXrlrcuDaxSAgYDlhy/G8RrEEH5Q2AyuUk3YsJ+I0O7By
Q63xJYCV69BCys5Yp24mYMEAR6Gr53UFsMb06hQodajlbRBYyKM0QOifXujoukN0oojfoavtCnNQ
3IVuDsNQ239ClyAMYWCNPwgspKrQ8Zix3mpvDfstKGGktSndINxSKCdCi3xSwHp2tagnAaxjdPua
UWvKFWZirLF7DOz/fKrFqQwsKCr8oJZTE+OBiIcaPA01HTzGptjUae4U29UVKI7NPFe0hiTkuAOB
XGWU436+C6zaWmDZXg3iLKcIxW28723Nbr0LLIg9YGjPGD3SWjtGH6yze2WFqtTT2wfhbJEyXvaI
61QLkKIVDK74djLhVp5jFTEe8ByopBljVXF7AHDsPOOc64Qx3Bj6dYyzyJQ61qhA/SNwh3iVpC4W
ESLsQEG5rR1jPUMbOKoOzNNouH5XuIWxYFcI3vCthgf0f3YRweYVN+4K3dAdAnuEQ2T9CpCs3gSg
2lgenkcjTe530erUJj3sVS/kF61UhnDMyYOWyjOPB51OuD0gAayDWgfxOASjjMF9hB2d5cLjOQ2H
sANlU+pQyXgIC+uYW9OOlnBowzAHMAbtUYZM6FCYcKj46SyM9eYUKRvvFOnpc7US5SSKG9MNEEd0
OkSUnQ4wVeW0c6zls7F8hQ+gdqqlRK0cS2WqRsvrivIVPMDOiw56Kjiec275dPtlLNUxt0Dl8X4R
WtmGqDHQgkbG0pRa7gmJHvp4muHumVi8SsqnGZLsp2SA6hjmt/ohxvpA5p1cWjVDWns1/ZtBKZE0
ztRYlpxzajzVauaWPqpV/VDmPdOJmEydW3/24gOMZc4VGsngIVVgBR6IG3okQSZgmee2G1FlvKPb
GLkdI0ZUCZXMe+AlJcNDmowYSYn2ucL3GevKiBFVdgSsYFo3YiSS7rW7K2C9GjESSX09sGqtRqtm
gGVkV8Byih4m10t4ZbLXMMAyshtgOXSpe6nhclTfMsAyshNg2aEbBGHghXajBazlGWAZ2QWw+KGq
xZDP7LRc1zHAMrIDYLWIowBe/N4LWwZYRnYArM9c3WCAZWQ7sGoGWEYMYxkxjGXk/xRYbgAShgGJ
a4BlZEfA+vj1WAZYRjYCy7FTYvJYRnYBLHN1gxEDLCMGWJul2+3KjnS79Q2FpjvBbD1qSrMNnWa5
zPujmP7dJffuMHcjOiP6t8CaHR0diX6cHB3crEdW9956qOsg5/1CJwcn6ypnpak1Sc1H/WRw0EWk
vNfiiXUA+tPEKFJ9rT9Y990VjH/hpMu+fCEZHVgn9W8NrPrDYMCgqc/8weCouwFYAy1gnTy8a3B/
YK0id3pyQx2ZDdLA6jKwZiezd+qc+LP6a/1mMBiQpevTycooYIwrwHq30k9O+sng/suBdWNNXr85
sCxrgGv+tXs0sD4JLCCd+rvkYlkH3TXT0OV1nl6D9RlyT/dgU6+wyBGT2g1UfV/n6lZHMTtK9/7d
Sj/NJl9Xd2IA9W/OWJOJj4r1e+teWKRe78ZODWOWCFj1KIKBAxZTg5o6erPozUqoU78fHCjIE1V1
EVgUlNCfutTCFxSTAeCPqEC9u+rG6hMLCAsZC0YxY6d9L0we96AeVS0/I2ApX9fVNuur0VryC3pd
3xTSTScRL4vRyEbra2pFUyu1p6pTOpbsBFJW95sD655mAqhrIpZad3ZycjITA60/4GsBLPENfnH0
AG+ObsC+UOBhWifOsfyTkyOa6Xr0acw/wN6TiPmoqgdkpcE9vJqCS0TGejgSLgpe1JFp6jf3VAD8
7En8lRI9EbZvBvfUebT3EY2iO8Ue8PRPkbGgtpvuDX+GlR6cHFHE14WGT3AcSgn+/IE6KCrA/op3
NGTuzLpxPkSrp35zdENulw5THIRiLvyAbDg7molm69jmrK6ECezfb9CoshM8pvr2sPefAwv+Y4/D
U/J6AMHWwDqYkq3v8TFTRwSsuvyGI6Ij+OagezTBp4NNThCaA3xJ8/twLz+NFx96rSN2u2BBqgrC
qiNWuuEYC9yIcJYAQYqxANP0+LEufsXz4ceMIHqMwHpAfWzghBjrYIJdnWBJjrHwqwMczP30NaqU
Rssf1pUSWLeo4EFUgKMd3FN1JzRkjJ4BoKvjjD0hVMgNo4Hr9CY2F9sQ49qo2foDtulHtcm4k6qU
nbDuZyJyOfruwKoTd4NToWkCj2XdH4DB0LzT+4F/cDCBOAyBBW/gGx8mGOjHAoY7eOhOoPDBPW0A
ZgcWFIa4qP7gDyb0qWLxOrIVsBZ3kho5uJ8ABLEooHiG3I6MQ4A+gWVPwIJpBDY6OMKvME4XsyW9
q9h4ALCgJ9AyDAVHgb4RtCaDCYb212J+fUt2X1YK+KGOTHC0iRIzy6fPaW8ASwZH6xNATiwa3ARb
4XFayXHGVDJD8kKD+gTmk3pd1GoRhbINb6Jm732078CP1s3MSgBLdkJErPffHVhdpIkTYg/qv/8A
Mc3NBO0An87ArYPpYNKOBpMpfDNDy80w5EeHDzQOckImlzEWRlP45ZEaUj0II9NiPeJ6X6dxjMVG
FAW6GO7xrjCOsTgErMfulEIs9jnQexwFNELMCPE6dpVD3IixYP6hUsQ2fHGCleIAb6AjMxytWqIe
VXDAwMI+3BBXTgZHGDVNpzTO1zXjTICsO/N9PHDoxbUeYf5D2pCaRRsCYNF895K008Di4tRV/u7b
AwunAyaUI1oRwSMx1HGacS6nPk4m0EOXUXTQnTF9YEQ5ezh5ePB9Dt7rMm9Ech+nF3ivVCcEosFP
ulFOgQhIGJEdRh0mY1YXwJKuBfcZqYnrYrOSsYDsqBERY9VvHh4eqPIIWNRQF+N9WSksmRPqKXVZ
lGC4igosrmBSZ7g/0BpSAmgxzhjrR4N450v9QRfGmH+VtfIiEzbsCvdPKIwNsoaxJjOx26HGfgCw
wDGdwEwyN9wPGAy4ImCsU7GhA8sBYbOAisw6iSgDIoN6lG5A9uOCCrmAq8XFKjjGn0xT6QZpRIig
ZnURnMSMJbeVJ3UJfP7Et7oRYyGjQe0cfIhAaECBWxRjrQJrEo1p0pUtUYnVCghYJ0pso44zjovu
49c8OGjqBtwWNS+CzwEtEQmbo6gv02TWLQ2s+2hifgiw0DPhiiXG4g08dxzGGuex6hRukwCwxG53
OiHjHlhoFAmso6hgZGQMmg5QJmTF2ChpYIlOICJTjMWcc+P78TC7EvjIWPD1hA/sN2jSrW3Ainqa
AhaGiQJwSWDFsTl0fXWcQEPqTtj3kYhwVRHgZhPRLT81Yj1gdZVs4U9wha+YdH+oS8Y6Ef5s0q3L
lAxNNFhIyCzar8BcTzFOIATGjHU/EyWn8UKOUDlF+prV1wOL2uUde5qxUO1G9TRq8A7KmHSnSIn2
kgeY9LnZzljxmNQSsoKZtcpYBzGwVsapODKB/BNgZ1RE2KC5sFZw4d0PMNYkmpg40/K9gVU/oE04
9R9pATNxGLbS7h+pA3eFFHBhOq+7stqggD+NTvfBOsUoH0tOlaV7dHSC/2jzdk/14rlFaUdZI87F
g6w1yVgUgE3U9I1MchNj4SgmU54naOCmS4N4H1giUSpytQpjUTiVrEAACzenaAGQ1XG+iqg0zj1Y
4AvqGN4jDuq4Z4Baj/QZa4ppngmTsCW6dFD/GemGbpxpOqIN0v3N6wy24RBowGAOptMTn3aF+OZm
On04mESMhbuqk+nsyB9YzCgnIpUzOZlNZyf392pGh4WwKqs6ogU4SwCL0DNTGet+GsHTGqjrVMYd
zFh8FpwsDhC4f8D8xyZgiUqxxoMH7MjkIclYB1DBNFHBq8gY3OOQpzdHD/XVcUKI6M/qqTNmmJfA
A/VtArUeWQMO3rcwFmZl7mevD/cD6d1PsEvkRerbz7J9H2CJjRugBOxgcV4AXsCARMxD38DbaXTK
GEIH/OTeR6PAgoQ3uGLBFPhx5DW6UXjLp08xGYQFMOSfwCs/giqfDL+vRyeh8b0ldoIws4PkGcWJ
P60rwIqXBw+CY6zrJLAGsGJuqFIY04MYE8xWzFhQ4mbCFawCayoGh2UPxDjvFe+YPN0w4bQUp67Q
W3O3/FT6cz2whJn8e+EKUXtgHYlsw/c+CX3jK4xFoKjPDiaTycEDT/PJ/WRyPz0g6/A390czNBir
1af40dH0noaJbxiQqDa5f4ioZnIfde4es9n1G66KstfwalafTsSU1A98TmUDxOkM88P9xOdAFY07
XXMyGXAUj+LEonM799TTCWULsJ46fY7ooKQpVnrP53F4TOTxybvUoYRSAXbRZ0we+HQC4gh7fHAT
j/Nk47lhDDLqNCZmSGGuCUJK2FCYHZtlYPlKvuIBewEUB4AGwE3xHdlG5yz6v70eq66e9BQzy/Ia
venKd3Tyld+o32OoFKuu1pG8Ukuc64oKyOqjInElXflenvk+GCQvj6hPfc7Ur4yCaq1HjcWjU9tP
jSlRIjGwaFTqmFfHCQsofWFQPWpONWe3qxol0bFX1WyyfnliqittNpv402/NWD9J8FSRNUvOW3T2
8Xv0cHZ/8GV1q7GX1qU5BlialyBBILcGRQ8P36qT3b8CLK1RG2DpAsvyD9bx2Ov/hSQvsNYZtQGW
vi/8PwHRjsxlgGXEAMuIAZYRAywDLCM/C1jp3Jt+nPyhPVcqTZpJK2Nbqexitvg/awc/pPX6Ea04
WZu1h/W05bMAq+aA0F1pnBq95b+bgFV/veFLd2c3r/XooNPP2Q0ltKc3N1P9AUJxyl/egEyzauHh
tf4RrQwzAOOa1aNDRi0wxk2Gn7kK42Vs64aNl9HysxvuGx4+BKwWPlQAjkU3xAfOFQMPkFUsbQBW
/ToIPezdFRSH0eHhRqu3UDLAS96D0A2udcdHxW14gQ8/0Nc6GobuwbR74Iaud6M9a1j8oI4jdDOk
3m3QuhIHbehPwRowrvoRDCvQxxUbD00Jw9P2XnRTtOvuESrf6A6se4VaV91ZwvLZgNVwWgiiYgM+
cNxGUHxr2G8bgXXljfCKJvfgxL3qHoR40OgsKlxfBa/ToXd9EgR13XUNxY+GMM3uwexa25Q37tX1
gXvUPTi4PoJOaocPqHX9GgTXtqsLYhoX/H+Eh/Cgm0UrhB66N9fayH+dumS816ujazs80YXI9Pr6
Biw/g+GdBJ72irm5vj5xD7rT8Ogmtnw2YLXQ73nwpuQ6TugAsOzWxhir3r0Kuq/14KrbPRh2PTpo
8HLX87qA/utXmG9U1F5tN9CgR8Ca6tNcOO12r666eGGl571qTzaU9+xrANU0uNKGyBDaGgKw4BDY
2nQQdLtTmLYj9+amngFYYDx7iCecZ67+b+7r3Sn07AaVrzLQYx0sMu3OwqOZ8hv2TMByQ7v2FgBJ
OWHrLSgGjWKxgQ91Wh+8E7DAlN3udTjlg45trtzr2UF4AhPheYHuakPumV4H0KALjlR3CmD09vTE
9br1I88baXMPrM8pENyJC288XWCBUwuuAm9KhyvtcGnmYfFp9yh00Y9qY394BcYDvwvqGcJwxG+9
fhBc6Vue7Bhc1esztPzsI67QKTlFtxgBq+GVGrYT2EFrI7DAFdZhtQGdTAOIZPSANQMP7yH5D4Ng
qL3aMO4JglF3enCt53JZy0YtD0KYYJghnruCGGRIwAImzhAteUNvCgtnFIym+sAaQlMwTwc3tpvF
GmA8CFSvRm4mYF1BB6VyltQClJ4mwoms6QZAFbrCBqnU7FbDfXNL7zEWu0L3FQ9Hrs4Wpf56DT28
JkQCm2vb5OZkBgsH7yJiD7XJH9q6CcgVvoIL1m7r+vo6OEBX+JrBFYK7mLIrfA1sXa2roA7e7AB/
vIYWyeAK6+wKb7K4whs0unCF9QxwRL+Jl37jjGcHVrFBjFUKS474AIJ3F4L49cCCtQkhOMD/5HoI
wbt7BAedzs4g+h56YJurm2tP29PXj06AEmBHc3Rzoh2Gw+K8nl0B+x8dzU6G2ksbtbzh7DXwwGdf
60dmR7MjCt6nR9pbNdKi7cV1FsaSxgMtjCr0PaiLV067V7NrIH99ohvaOAFHN0exr8gCLNwn23xE
9/fWgoCrZlPSYQ2wurhBht3PFHajEPTgtlkv9pmiK4TxHcH2VZ+Qp+AKYVOIiQPdtIZIAQyP6pQC
yLbBxoTI0RAGqN0UGiK8mlL+wNOOsW5AC7MTHsRYtn6KmY33Gricg9GV4KrOUUWoHxhgYHZdpwlQ
xpXJFbZalBOFY43zpPinUdsQvMPmFfxMHb0N5R6vrzV3a6DIP2K5vp5lcfTX9Muom+vrLGeXWAu3
zBlSgq/cM+jhTT1TW9fs6bP0cEpalArIkgyf0cAyar1ez6RyFhvOrtmPqlpfeUondX4g8ymdeubz
EX/thMmP0Kp/ROv1A229rrtefnfAqhsxEkl3Z8C6NmJEkSMEVrH0rhSD4P0CJRsiTCNGVAmDnB1s
E297AWNJIwkJGv8TYADrE1SmY0VugQAAAABJRU5ErkJggg==

------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: image/svg+xml
Content-Transfer-Encoding: quoted-printable
Content-Location: https://d26tpo4cm8sb6k.cloudfront.net/img/svg/calculator-white.svg

<?xml version=3D"1.0" encoding=3D"UTF-8"?>
<svg width=3D"208" height=3D"22" viewBox=3D"0 0 208 22" xmlns=3D"http://www=
.w3.org/2000/svg"><defs><style>.cls-1{fill:#ffffff;}.cls-2{fill:#ccff99;}</=
style></defs><title>Calculator.net</title><g transform=3D"matrix(2.0833 0 0=
 2.082 -2.3542 -19.565)"><path class=3D"cls-1" d=3D"m10.2 19.8v-1.8h-4.02a1=
.9 1.9 0 0 1-2-1.64 7.39 7.39 0 0 1 0.09-3.47 2.06 2.06 0 0 1 2.17-1.75h3.7=
6v-1.74h-4.41a3.83 3.83 0 0 0-3.85 2.85c-0.15 0.51-0.22 1-0.33 1.56v1.74c0 =
0.25 0.09 0.5 0.13 0.75a4 4 0 0 0 3.83 3.47h4.63z"/><path class=3D"cls-1" d=
=3D"m18.71 14.5a2.24 2.24 0 0 0-1.52-2.12 3.54 3.54 0 0 0-1.19-0.23h-4.57v1=
.6h3.92a1.13 1.13 0 0 1 1.29 1.34q0 1.22 0 2.44a0.59 0.59 0 0 1-0.67 0.69h-=
2.27a0.6 0.6 0 0 1-0.7-0.75 0.66 0.66 0 0 1 0.62-0.73 3.12 3.12 0 0 1 0.56-=
0.05h2v-1.52c-0.65 0-2.32-0.06-3.45 0.07a1.88 1.88 0 0 0-1.82 1.92 2 2 0 0 =
0 2 2.6 28.12 28.12 0 0 0 3.94 0 1.88 1.88 0 0 0 1.92-2.06c0-1.06-0.01-2.13=
-0.06-3.2z"/><path class=3D"cls-1" d=3D"m26.69 13.89a6.34 6.34 0 0 1 1-0.09=
h2.25v-1.68h-3.59a3 3 0 0 0-2.62 1.6 4.38 4.38 0 0 0-0.2 4 3 3 0 0 0 2.64 2=
c1.19 0.09 2.38 0.05 3.57 0.06h0.18v-1.63h-2.75a1.69 1.69 0 0 1-1.8-1.45 5.=
3 5.3 0 0 1 0-1.52 1.44 1.44 0 0 1 1.32-1.29z"/><path class=3D"cls-1" d=3D"=
m35.85 12.14v6h-1.56c-1.15 0-1.4-0.25-1.4-1.39q0-2.1 0-4.21v-0.41h-2v5.17a2=
.28 2.28 0 0 0 2.38 2.5c1.24 0.08 2.49 0 3.74 0.06h0.87v-7.72h-2z"/><path c=
lass=3D"cls-1" d=3D"m54.51 18.24a1 1 0 0 1-1-1 3.75 3.75 0 0 1 0-0.43q0-1.3=
9 0-2.77v-0.35h2.1v-1.58h-2.16v-2.69h-2.06v0.38q0 1.77 0 3.54v4.24a2.09 2.0=
9 0 0 0 2.31 2.24h1.86v-1.57c-0.36 0-0.71 0.02-1.05-0.01z"/><path class=3D"=
cls-1" d=3D"m48.5 12.29a3.46 3.46 0 0 0-0.92-0.15h-4.58v1.6h3.92a1.13 1.13 =
0 0 1 1.29 1.31v2.47a0.59 0.59 0 0 1-0.67 0.69h-2.3a0.56 0.56 0 0 1-0.64-0.=
58 0.64 0.64 0 0 1 0.56-0.81 4.56 4.56 0 0 1 0.84-0.05h1.8v-1.58c-0.8 0-2.2=
6-0.05-3.37 0a1.77 1.77 0 0 0-1.7 1.14 4.19 4.19 0 0 0-0.24 1.67 1.78 1.78 =
0 0 0 1.77 1.75 34.06 34.06 0 0 0 4 0 1.86 1.86 0 0 0 2-2 27 27 0 0 0 0-3.3=
7 2.24 2.24 0 0 0-1.76-2.09z"/><path class=3D"cls-1" d=3D"m20.13 19.78h2.09=
v-10.37h-2.09z"/><path class=3D"cls-1" d=3D"m39.33 19.79h2.08v-10.38h-2.08z=
"/><path class=3D"cls-1" d=3D"m71.21 12.15c-1.34 0-2.67-0.08-4 0a2.39 2.39 =
0 0 0-2.41 2.55c-0.05 1.63 0 3.27 0 4.91v0.16h2.1v-6.07h4.31z"/><path class=
=3D"cls-1" d=3D"m61.49 12.19a4.93 4.93 0 0 0-2.18-0.19 3.62 3.62 0 0 0-3.07=
 2.15 4.6 4.6 0 0 0-0.32 2.41 3.61 3.61 0 0 0 2.52 3.14c0.19 0.06 0.34 0.11=
 0.53 0.15a4.45 4.45 0 0 0 2 0 2.85 2.85 0 0 0 0.33-0.07 3.47 3.47 0 0 0 2.=
63-2.66 5.63 5.63 0 0 0 0.07-1.9 3.47 3.47 0 0 0-2.51-3.03zm0.37 4.41a1.84 =
1.84 0 0 1-1.86 1.68 1.77 1.77 0 0 1-1.91-1.62 4.25 4.25 0 0 1 0-1.45 1.76 =
1.76 0 0 1 2-1.54 1.83 1.83 0 0 1 1.79 1.73v0.61c0 0.19 0.02 0.4-0.02 0.59z=
"/></g><g transform=3D"matrix(2.0996 0 0 2.1134 -3.605 -20.098)" fill=3D"#c=
f9"><path class=3D"cls-2" d=3D"m91.63 12.63a4.13 4.13 0 0 0-0.89-0.11h-4a2 =
2 0 0 0-2.06 1.89 16.29 16.29 0 0 0 0 3.16 2.4 2.4 0 0 0 2 2.1l0.56 0.12a3.=
37 3.37 0 0 0 0.65 0.06h4.75v-1.74h-3.88a4.94 4.94 0 0 1-0.89-0.11 1.06 1.0=
6 0 0 1-1-1.11v-2a0.55 0.55 0 0 1 0.55-0.64c0.9-0.05 1.8-0.06 2.7-0.05a0.54=
 0.54 0 0 1 0.52 0.55 0.57 0.57 0 0 1-0.5 0.58 1.79 1.79 0 0 1-0.4 0h-2.35v=
1.64a23.3 23.3 0 0 0 4.19-0.16 1.79 1.79 0 0 0 1.42-1.41 4.41 4.41 0 0 0 0-=
1.29 1.67 1.67 0 0 0-1.37-1.48z"/><path class=3D"cls-2" d=3D"m97.2 18.15a1 =
1 0 0 1-1.1-1.1v-2.84h2.27v-1.7h-2.3v-3h-2.27v7.47a5.54 5.54 0 0 0 0.07 0.9=
3 2.09 2.09 0 0 0 1.53 1.79 3.33 3.33 0 0 0 1 0.14h2v-1.69c-0.4 0-0.8 0.03-=
1.2 0z"/><path class=3D"cls-2" d=3D"m70.3 19.89h2.07v-2h-2.07z"/><path clas=
s=3D"cls-2" d=3D"m83.59 19.76v-4.68a2.31 2.31 0 0 0-1.88-2.39 4.11 4.11 0 0=
 0-1-0.14h-4.86v7.37h2.25v-5.5h2a1 1 0 0 1 1.21 1.22v4.26h2.29s-0.01-0.1-0.=
01-0.14z"/></g></svg>

------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.calculator.net/style.css

@charset "utf-8";

body, p, td, div, span, input, th, li, textarea { font-family: arial, helve=
tica, sans-serif; font-size: 16px; color: rgb(0, 0, 0); }

body { background: rgb(255, 255, 255); margin: 0px; padding: 0px; border: 0=
px; text-align: center; }

p { margin: 5px 0px 8px; }

img { border: 0px; }

h1 { color: rgb(0, 51, 102); font-size: 26px; font-weight: bold; padding: 0=
px; margin: 12px 0px; }

h2 { font-size: 22px; font-weight: bold; color: rgb(0, 51, 102); padding: 0=
px; margin-bottom: 2px; }

h3 { font-size: 19px; font-weight: bold; }

hr { border: 0px; color: rgb(170, 170, 170); background-color: rgb(170, 170=
, 170); height: 1px; }

a { color: rgb(0, 102, 153); text-decoration: underline; }

a:hover { text-decoration: none; }

input { padding: 5px; color: rgb(0, 0, 0); box-sizing: border-box; }

select { padding: 4px; color: rgb(0, 0, 0); box-sizing: border-box; }

option { font-size: 16px; }

input[type=3D"text"], input[type=3D"url"], input[type=3D"tel"], input[type=
=3D"number"], input[type=3D"color"], input[type=3D"date"], input[type=3D"em=
ail"], select { border: 1px solid rgb(4, 66, 132); border-radius: 2px; box-=
shadow: rgb(102, 102, 102) 1px 1px 2px; font-size: 16px; background-color: =
rgb(255, 255, 255); }

input[type=3D"submit"] { border: 0px; color: rgb(255, 255, 255); padding: 1=
1px 50px 11px 16px; font-size: 16px; font-weight: bold; background-color: r=
gb(76, 123, 37); background-image: url("data:image/svg+xml;utf8,<svg xmlns=
=3D\"http://www.w3.org/2000/svg\" width=3D\"180px\" height=3D\"40px\"><circ=
le cx=3D\"112\" cy=3D\"20\" r=3D\"11\" fill=3D\"darkseagreen\" /><path d=3D=
\"M110 12 L120 20 L110 28 Z\" fill=3D\"white\" /></svg>"); background-repea=
t: no-repeat; }

input[type=3D"submit"]:hover { background-color: rgb(68, 68, 68); }

input[type=3D"reset"], input[type=3D"button"] { border: 0px; color: rgb(255=
, 255, 255); padding: 11px 8px; font-size: 16px; background: rgb(171, 171, =
171); }

input[type=3D"reset"]:hover, input[type=3D"button"]:hover { background: rgb=
(68, 68, 68); }

input[type=3D"image"], input[type=3D"image"]:hover { background: rgb(81, 13=
2, 40); color: rgb(255, 255, 255); padding: 0px; margin: 0px; }

.clearbtn { cursor: pointer; }

.inputErrMsg { position: absolute; padding: 4px 8px; color: rgb(0, 0, 0); b=
ackground-color: rgb(255, 204, 204); border: 1px solid rgb(255, 170, 170); =
white-space: nowrap; display: inline-block; }

.cbcontainer { display: inline-block; position: relative; padding-left: 28p=
x; padding-top: 1px; margin: 5px 0px; cursor: pointer; font-size: 16px; use=
r-select: none; }

.cbcontainer input { position: absolute; opacity: 0; cursor: pointer; heigh=
t: 0px; width: 0px; }

.cbmark { position: absolute; top: 0px; left: 0px; height: 16px; width: 16p=
x; background-color: rgb(255, 255, 255); border: 2px solid rgb(51, 102, 153=
); }

.rbmark { position: absolute; top: 0px; left: 0px; height: 16px; width: 16p=
x; background-color: rgb(255, 255, 255); border: 2px solid rgb(51, 102, 153=
); border-radius: 50%; }

.cbcontainer:hover input ~ .cbmark, .cbcontainer:hover input ~ .rbmark { ba=
ckground-color: rgb(204, 204, 204); }

.cbcontainer input:checked ~ .cbmark, .cbcontainer input:checked ~ .rbmark =
{ background-color: rgb(51, 102, 153); }

.cbmark::after, .rbmark::after { content: ""; position: absolute; display: =
none; }

.cbcontainer input:checked ~ .cbmark::after, .cbcontainer input:checked ~ .=
rbmark::after { display: block; }

.cbcontainer .cbmark::after { left: 4px; top: 0px; width: 5px; height: 10px=
; border-style: solid; border-color: white; border-image: initial; border-w=
idth: 0px 3px 3px 0px; transform: rotate(45deg); }

.cbcontainer .rbmark::after { top: 4px; left: 4px; width: 8px; height: 8px;=
 border-radius: 50%; background: white; }

.indollar { background-image: url("data:image/svg+xml;utf8,<svg xmlns=3D\"h=
ttp://www.w3.org/2000/svg\" width=3D\"15px\" height=3D\"20px\"><text x=3D\"=
2\" y=3D\"15\" style=3D\"font: normal 16px arial;\">$</text></svg>"); backg=
round-position: left center; background-repeat: no-repeat; padding-left: 11=
px !important; }

.inpct { background-image: url("data:image/svg+xml;utf8,<svg xmlns=3D\"http=
://www.w3.org/2000/svg\" width=3D\"17px\" height=3D\"20px\"><text x=3D\"1\"=
 y=3D\"15\" style=3D\"font: normal 16px arial;\">%</text></svg>"); backgrou=
nd-position: right center; background-repeat: no-repeat; padding-right: 18p=
x !important; }

.innormal { width: 90px; }

.in4char { width: 58px; }

.in3char { width: 46px; }

.in2char { width: 35px; }

.inlong { width: 120px; }

.inlonger { width: 170px; }

.inlongest { width: 230px; }

.inlongesthalf { width: 112px; }

.infull { width: 226px; }

.inhalf { width: 110px; }

.infulltxarea { width: 600px; padding: 8px; }

.inshortfull { width: 170px; }

.inshorthalf { width: 82px; }

.inuiyear { padding-right: 50px; }

.inuiyearspan { margin-left: -45px; color: rgb(136, 136, 136); }

.inuipound { padding-right: 62px; }

.inuipoundspan { margin-left: -58px; color: rgb(136, 136, 136); }

.inuifoot { padding-right: 38px; }

.inuifootspan { margin-left: -34px; color: rgb(136, 136, 136); }

.inuiinch { padding-right: 57px; }

.inuiinchspan { margin-left: -53px; color: rgb(136, 136, 136); }

.inuick { padding-right: 32px; }

.inuickspan { margin-left: -27px; color: rgb(136, 136, 136); }

.inui1c { padding-right: 16px; }

.inui1cspan { margin-left: -11px; color: rgb(136, 136, 136); }

.scaleimg { max-width: 100%; height: auto; }

#tt { position: absolute; display: block; background-color: rgb(71, 71, 71)=
; color: rgb(255, 255, 255); padding: 8px; border: 1px solid rgb(0, 0, 0); =
text-align: left; }

.ttimg { opacity: 0.4; vertical-align: top; }

.ttimg:hover { opacity: 1; }

#headerout { background: rgb(0, 51, 102); text-align: center; }

#header { width: 1100px; height: 60px; background: rgb(0, 51, 102); padding=
: 0px; margin-left: auto; margin-right: auto; text-align: left; overflow: h=
idden; }

#logo { padding: 18px 0px; width: 270px; float: left; }

#login { padding: 2px; float: right; color: rgb(204, 204, 204); }

#login a { color: rgb(204, 204, 204); text-decoration: none; }

#login a:hover { text-decoration: underline; }

.topNavAbs { position: absolute; top: 21px; left: 50%; width: 520px; margin=
-left: -80px; text-align: left; }

.topNavAbs a { color: white; padding: 10px 16px; border: none; cursor: poin=
ter; font-size: 16px; text-transform: uppercase; display: inline-block; tex=
t-decoration: none; }

.topNavAbs a:hover { background-color: rgb(81, 132, 40); }

.topNavOn { background-color: rgb(81, 132, 40); }

#contentout { width: 1100px; padding-top: 5px; margin-left: auto; margin-ri=
ght: auto; text-align: left; overflow: auto; }

#content { padding: 0px 0px 15px; width: 728px; float: left; }

#right { width: 336px; float: right; text-align: center; }

#contentbig { padding: 0px 0px 15px; width: 843px; float: right; }

#footer { background: rgb(225, 225, 225); padding: 25px 0px; font-size: 13p=
x; color: rgb(85, 85, 85); text-align: center; }

#footer a { color: rgb(68, 68, 68); }

#footer a:hover { text-decoration: none; }

#footerin { width: 1100px; margin-left: auto; margin-right: auto; text-alig=
n: left; overflow: auto; color: rgb(85, 85, 85); }

#footernav { text-align: center; }

#homecaldiv { background: rgb(209, 221, 233); padding: 10px 0px; }

#homelistdiv { background: rgb(255, 255, 255); padding: 20px 0px; }

#homecaldiv td { overflow: hidden; }

#homelistwrap { display: grid; row-gap: 30px; justify-content: center; grid=
-template-columns: 280px 320px 260px 220px; }

#breadcrumbs, #breadcrumbs span { font-size: 13px; }

#breadcrumbs a, #breadcrumbs a span { text-decoration: none; color: rgb(0, =
102, 153); }

#breadcrumbs a:hover, #breadcrumbs a span:hover { text-decoration: underlin=
e; }

#othercalc { border: 1px solid rgb(51, 102, 153); margin: auto; text-align:=
 left; width: 332px; }

#octitle { background-color: rgb(51, 102, 153); padding: 6px; color: rgb(25=
5, 255, 255); font-size: 18px; font-weight: bold; }

#octitle a { color: rgb(255, 255, 255); text-decoration: none; }

#octitle a:hover { text-decoration: underline; }

#occontent { padding: 3px 6px; font-size: 14px; }

#occontent a { display: inline-block; width: 158px; padding: 3px 0px; }

#ocother { background-color: rgb(221, 221, 221); padding: 6px; text-align: =
center; font-size: 15px; color: rgb(187, 187, 187); }

#sectitle { background-color: rgb(51, 102, 153); padding: 6px; color: rgb(2=
55, 255, 255); font-size: 18px; font-weight: bold; }

.hicon { padding: 20px 0px 20px 10px; }

.hl { list-style-type: none; margin: 0px; padding: 5px 0px 5px 8px; backgro=
und-color: rgb(255, 255, 255); font-size: 16px; }

.hl li { padding: 0px 0px 8px; }

.hl li a { text-decoration: none; }

.hl li a:hover { text-decoration: underline; }

.hh { color: rgb(35, 131, 43); padding: 8px 5px; font-size: 22px; }

.hh a { color: rgb(35, 131, 43); text-decoration: none; }

.hh a:hover { text-decoration: underline; }

.smtb a { text-decoration: underline; }

.smtb a:hover { text-decoration: none; }

.smtbtop a { text-decoration: none; }

.smtbtop a:hover { text-decoration: underline; }

.smalltext { font-size: 13px; }

.bigtext { font-size: 18px; }

.verybigtext { font-size: 23px; }

.morelinespace { line-height: 125%; }

.inlinetable { display: inline; }

table.cinfoT { border-collapse: collapse; border-spacing: 0px; margin-top: =
0px; }

table.cinfoT th, table.cinfoT td.cinfoHd, table.cinfoT td.cinfoHdL { border=
-width: 1px; border-style: solid; border-color: rgb(17, 68, 119) rgb(17, 68=
, 119) rgb(51, 102, 153); background-color: rgb(51, 102, 153); font-weight:=
 bold; color: rgb(255, 255, 255); padding: 5px 3px; }

table.cinfoT td { border: 1px solid rgb(204, 204, 204); color: rgb(0, 0, 0)=
; padding: 3px; }

table.cinfoT tr:nth-child(2n+1) { background-color: rgb(238, 238, 238); }

table.cinfoT tr:nth-child(2n) { background-color: rgb(255, 255, 255); }

table.cinfoTS td.cinfoHd { font-size: 13px; }

table.cinfoTS td.cinfoHdL { font-size: 13px; }

table.cinfoTS td { font-size: 13px; padding: 3px 1px; }

.frac { display: inline-block; text-align: center; vertical-align: middle; =
}

.fracnum { display: block; }

.fracden { display: block; border-top: 1px solid rgb(0, 0, 0); padding: 0px=
 3px; }

#topmenu ul { color: rgb(0, 0, 0); border-bottom: 1px solid rgb(187, 187, 1=
87); margin: 12px 0px 0px; padding: 0px 0px 8px; font-size: 15px; font-weig=
ht: bold; }

#topmenu ul li { display: inline; overflow: hidden; list-style-type: none; =
margin-left: 0px; }

#topmenu ul li a, #topmenu ul li a:visited { color: rgb(255, 255, 255); bac=
kground: rgb(51, 102, 153); border: 1px solid rgb(51, 102, 153); padding: 8=
px 5px; margin: 0px; text-decoration: none; }

#topmenu ul li a:hover { background: rgb(238, 238, 238); color: rgb(0, 0, 0=
); }

#topmenu ul #menuon a { color: rgb(0, 0, 0); background: rgb(238, 238, 238)=
; border-width: 1px 1px 2px; border-style: solid; border-color: rgb(187, 18=
7, 187) rgb(187, 187, 187) rgb(238, 238, 238); border-image: initial; paddi=
ng: 8px 5px; margin: 0px; text-decoration: none; }

#topmenu ul #menuon a:hover { background: rgb(238, 238, 238); }

.topmenucenter { }

#insmd { background-color: rgb(51, 102, 153); margin-bottom: 3px; }

#insmdc { background-color: rgb(51, 102, 153); margin-bottom: 3px; text-ali=
gn: center; }

fieldset { margin-top: 10px; padding: 0px 10px 5px; border: 0px solid rgb(1=
89, 210, 218); background: rgb(238, 238, 238); color: rgb(238, 238, 238); }

legend { font-size: 18px; font-weight: bold; padding: 5px 15px; background:=
 rgb(238, 238, 238); color: rgb(0, 0, 0); }

fieldset a { display: inline-block; white-space: nowrap; padding: 6px; font=
-size: 16px; background: rgb(51, 102, 153); color: rgb(255, 255, 255); marg=
in-bottom: 5px; text-decoration: none; }

fieldset a:hover { background: rgb(65, 117, 22); color: rgb(255, 255, 255);=
 }

fieldset div { display: inline-block; white-space: nowrap; padding: 10px; f=
ont-size: 18px; background: rgb(4, 66, 132); color: rgb(255, 255, 255); mar=
gin-bottom: 5px; border-radius: 3px; text-decoration: none; }

fieldset div:hover { background: rgb(196, 119, 81); color: rgb(255, 255, 25=
5); }

.arrow_box { position: relative; background: rgb(238, 238, 238); border: 1p=
x solid rgb(170, 170, 170); padding: 3px 8px; text-align: center; }

.arrow_box::after, .arrow_box::before { left: 100%; top: 50%; border: solid=
 transparent; content: " "; height: 0px; width: 0px; position: absolute; po=
inter-events: none; }

.arrow_box::after { border-color: rgba(221, 221, 221, 0) rgba(221, 221, 221=
, 0) rgba(221, 221, 221, 0) rgb(238, 238, 238); border-width: 12px; margin-=
top: -12px; }

.arrow_box::before { border-color: rgba(238, 238, 238, 0) rgba(238, 238, 23=
8, 0) rgba(238, 238, 238, 0) rgb(170, 170, 170); border-width: 13px; margin=
-top: -13px; }

.result_box { background: rgb(227, 237, 218); border: 1px solid rgb(141, 18=
0, 109); padding: 3px 8px; text-align: center; }

.panel { background: rgb(238, 238, 238); border: 1px solid rgb(187, 187, 18=
7); padding: 5px; }

.panel2 { background-color: rgb(238, 238, 238); padding: 5px; border-right:=
 1px solid rgb(187, 187, 187); border-bottom: 1px solid rgb(187, 187, 187);=
 border-left: 1px solid rgb(187, 187, 187); }

.reference { font-size: 13px; padding-left: 1.8em; }

.reference li { font-size: 13px; overflow-wrap: break-word; }

#printit { width: 80px; float: right; text-align: right; }

.h2result { background: rgb(81, 132, 40); color: rgb(255, 255, 255); border=
: 1px solid rgb(81, 132, 40); padding: 5px; margin-top: 3px; font-size: 22p=
x; font-weight: normal; }

.h3head { margin-bottom: 2px; }

.sectionlists { }

.sectionlists div { padding-bottom: 5px; }

#searchbox { padding-top: 16px; }

#bluebtn { border-radius: 1px; background: rgb(51, 102, 153); padding: 5px =
8px; font-size: 18px; color: rgb(255, 255, 255); }

#bluebtn:hover { background: rgb(68, 68, 68); color: rgb(255, 255, 255); }

#calcSearchOut { padding: 5px; }

#calcSearchOut div { padding: 5px; text-align: left; }

.leftinput { width: 325px; float: left; }

.rightresult { width: 375px; float: right; }

.clefthalf { width: 350px; float: left; }

.crighthalf { width: 350px; float: right; }

.espaceforM { display: none; }

#clear { margin-left: auto; margin-right: auto; clear: both; height: 0px; }

.leftchart { padding-top: 10px; width: 500px; float: left; }

.rightpie { padding-top: 10px; width: 165px; float: right; }

@media (max-width: 1140px) {
  #header { width: 990px; padding-left: 8px; }
  #contentout { width: 1000px; }
  #content { width: 640px; float: left; padding-left: 10px; }
  #footerin { width: 990px; }
  #homelistwrap { display: grid; row-gap: 30px; grid-template-columns: 270p=
x 300px 220px 210px; }
  .leftinput, .clefthalf, .crighthalf { width: 310px; }
  .rightresult { width: 320px; }
  .leftchart { width: 445px; }
  .rightpie { width: 155px; }
}

@media (max-width: 720px) {
  #header { width: auto; padding: 0px 8px; }
  #contentout { width: auto; padding: 8px; }
  #content { float: none; width: auto; padding: 0px; }
  #homelistwrap { grid-template-columns: 320px 320px; }
  #right { width: auto; float: none; }
  #footerin { width: auto; }
  .topNavAbs { display: none; }
  .espaceforM { display: block; }
}

@media (max-width: 650px) {
  #homelistwrap { grid-template-columns: 250px 250px; }
  .leftinput, .rightresult, .clefthalf, .crighthalf { width: auto; float: n=
one; }
  img { max-width: 100%; height: auto; }
}

@media (max-width: 490px) {
  #homelistwrap { grid-template-columns: auto; }
}
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://securepubads.g.doubleclick.net/static/topics/topics_frame.html

<!DOCTYPE html><html><head><meta http-equiv=3D"Content-Type" content=3D"tex=
t/html; charset=3DUTF-8">
   =20
    <title>Topics Frame</title>
    <meta http-equiv=3D"origin-trial" content=3D"Avh5Ny0XEFCyQ7+oNieXskUrqY=
8edUzL5/XrwKlGjARQHW4TFRK+jVd5HnDIpY20n5OLHfgU4ku7x48N3uhG/A0AAABxeyJvcmlna=
W4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiUHJpdmFjeVNhbmRi=
b3hBZHNBUElzIiwiZXhwaXJ5IjoxNjk1MTY3OTk5LCJpc1N1YmRvbWFpbiI6dHJ1ZX0=3D">
   =20
  </head>
  <body>

<fencedframe id=3D"ps_caff" style=3D"border: 0px;"></fencedframe></body></h=
tml>
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.google.com/recaptcha/api2/aframe

<!DOCTYPE html><html><head><meta http-equiv=3D"Content-Type" content=3D"tex=
t/html; charset=3DUTF-8"></head><body><img src=3D"https://pagead2.googlesyn=
dication.com/pagead/sodar?id=3Dsodar2&amp;v=3D237&amp;li=3Dgpt_m20250521010=
1&amp;jk=3D4763275072110855&amp;rc=3D"></body></html>
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://f8e231356770a0289ddfbea26eab4725.safeframe.googlesyndication.com/safeframe/1-0-45/html/container.html

<!DOCTYPE html><html><head><meta http-equiv=3D"Content-Type" content=3D"tex=
t/html; charset=3DUTF-8"><link rel=3D"stylesheet" type=3D"text/css" href=3D=
"cid:<EMAIL>" /><link rel=3D"s=
tylesheet" type=3D"text/css" href=3D"cid:css-4ff38303-28c6-48c0-9870-db8cf4=
<EMAIL>" />
   =20
    <title>SafeFrame Container</title>
    <meta http-equiv=3D"origin-trial" content=3D"A3vKT9yxRPjmXN3DpIiz58f5Jy=
kcWHjUo/W7hvmtjgh9jPpQgem9VbADiNovG8NkO6mRmk70Kex8/KUqAYWVWAEAAACLeyJvcmlna=
W4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiUHJpdmFj=
eVNhbmRib3hBZHNBUElzIiwiZXhwaXJ5IjoxNjk1MTY3OTk5LCJpc1N1YmRvbWFpbiI6dHJ1ZSw=
iaXNUaGlyZFBhcnR5Ijp0cnVlfQ=3D=3D"><meta http-equiv=3D"origin-trial" conten=
t=3D"AxjhRadLCARYRJawRjMjq4U8V8okQvSnrBIJWdMajuEkN3/DfVAcLcFhMVrUWnOXagwlI8=
dQD84FwJDGj9ohqAYAAABveyJvcmlnaW4iOiJodHRwczovL2dvb2dsZWFkc2VydmljZXMuY29tO=
jQ0MyIsImZlYXR1cmUiOiJGZXRjaExhdGVyQVBJIiwiZXhwaXJ5IjoxNzI1NDA3OTk5LCJpc1Ro=
aXJkUGFydHkiOnRydWV9"><meta http-equiv=3D"origin-trial" content=3D"AxjhRadL=
CARYRJawRjMjq4U8V8okQvSnrBIJWdMajuEkN3/DfVAcLcFhMVrUWnOXagwlI8dQD84FwJDGj9o=
hqAYAAABveyJvcmlnaW4iOiJodHRwczovL2dvb2dsZWFkc2VydmljZXMuY29tOjQ0MyIsImZlYX=
R1cmUiOiJGZXRjaExhdGVyQVBJIiwiZXhwaXJ5IjoxNzI1NDA3OTk5LCJpc1RoaXJkUGFydHkiO=
nRydWV9"><meta http-equiv=3D"origin-trial" content=3D"AxjhRadLCARYRJawRjMjq=
4U8V8okQvSnrBIJWdMajuEkN3/DfVAcLcFhMVrUWnOXagwlI8dQD84FwJDGj9ohqAYAAABveyJv=
cmlnaW4iOiJodHRwczovL2dvb2dsZWFkc2VydmljZXMuY29tOjQ0MyIsImZlYXR1cmUiOiJGZXR=
jaExhdGVyQVBJIiwiZXhwaXJ5IjoxNzI1NDA3OTk5LCJpc1RoaXJkUGFydHkiOnRydWV9"><met=
a http-equiv=3D"origin-trial" content=3D"AxjhRadLCARYRJawRjMjq4U8V8okQvSnrB=
IJWdMajuEkN3/DfVAcLcFhMVrUWnOXagwlI8dQD84FwJDGj9ohqAYAAABveyJvcmlnaW4iOiJod=
HRwczovL2dvb2dsZWFkc2VydmljZXMuY29tOjQ0MyIsImZlYXR1cmUiOiJGZXRjaExhdGVyQVBJ=
IiwiZXhwaXJ5IjoxNzI1NDA3OTk5LCJpc1RoaXJkUGFydHkiOnRydWV9"><a href=3D"https:=
//f8e231356770a0289ddfbea26eab4725.safeframe.googlesyndication.com/safefram=
e/1-0-45/html/container.html?_pmch=3D1748736661466" rel=3D"nofollow" target=
=3D"_blank" style=3D"width: 0px; height: 0px; font-size: 0px; line-height: =
0;">click</a></head><body leftmargin=3D"0" topmargin=3D"0" marginwidth=3D"0=
" marginheight=3D"0" class=3D"jar"><div class=3D"GoogleActiveViewInnerConta=
iner" id=3D"avic_CPPDkNv3zo0DFVBGnQkdl98xgQ" style=3D"left:0px;top:0px;widt=
h:100%;height:100%;position:fixed;pointer-events:none;z-index:-9999;"></div=
><div style=3D"display:inline" class=3D"GoogleActiveViewElement" data-googl=
e-av-cxn=3D"https://pagead2.googlesyndication.com/pcs/activeview?xai=3DAKAO=
jsvZhsAvIZC-ura7VNWOqyPJiWhzZSWdx3wByskPo4cPelRgtEVhyf-BqqmSPy8hV8yKKl8NGf3=
xjdBK04g4aSCFBvLNdpDA8YeFPnfboi_HpJvESuFScE4_NlxunL9EBikQT-xhgQiqQu9WlW83YZ=
k-sZtQLrIXuvRZ4iDPeU0&amp;sig=3DCg0ArKJSzFZZwm3f90yWEAE" data-google-av-adk=
=3D"3640173279" data-google-av-metadata=3D"la=3D0&amp;xdi=3D0&amp;" data-go=
ogle-av-ufs-integrator-metadata=3D"CoUBCjltb2RlbF9wZXJzb25fY291bnRyeV9jb2Rl=
X1NHX3BlcnNvbl9yZWdpb25fY29kZV81MzQ3Lmpzb24SGkNQUERrTnYzem8wREZWQkduUWtkbDk=
4eGdRGAEiEQiOFSCOFSgCMAI4AV3NzMw-KP3xjun5_____wEw_fGO6QE4AUABSABQARKRAgqEAm=
h0dHBzOi8vcGFnZWFkMi5nb29nbGVzeW5kaWNhdGlvbi5jb20vcGNzL2FjdGl2ZXZpZXc_eGFpP=
UFLQU9qc3ZaaHNBdklaQy11cmE3Vk5XT3F5UEppV2h6WlNXZHgzd0J5c2tQbzRjUGVsUmd0RVZo=
eWYtQnFxbVNQeThoVjh5S0tsOE5HZjN4amRCSzA0ZzRhU0NGQnZMTmRwREE4WWVGUG5mYm9pX0h=
wSnZFU3VGU2NFNF9ObHh1bkw5RUJpa1FULXhoZ1FpcVF1OVdsVzgzWVprLXNadFFMcklYdXZSWj=
RpRFBlVTAmc2lnPUNnMEFyS0pTekZaWndtM2Y5MHlXRUFFEgAaACABKAAwBBoeChpDUFBEa052M=
3pvMERGVkJHblFrZGw5OHhnURAF" data-google-av-override=3D"-1" data-google-av-=
dm=3D"2" data-google-av-aid=3D"0" data-google-av-naid=3D"1" data-google-av-=
slift=3D"" data-google-av-cpmav=3D"" data-google-av-btr=3D"" data-google-av=
-itpl=3D"20" data-google-av-rs=3D"4" data-google-av-flags=3D"[&quot;x%27844=
0'9efotm(&amp;753374%2bejvf/%27844&gt;'9wuvb$&amp;56533&gt;!=3D|vqc)!273794=
&amp;&lt;qqvb/%&lt;1735020!=3Dnehu`/!364=3D5051!9abk{a($160210:3&amp;&lt;cb=
otf+*0150034:%2bejvf/%72;17613!=3Defdwa*'76463;21$?ebkpb$&amp;0366717&gt;*&=
gt;bgipf+!3=3D712363%9aihwc)!7202&lt;217'9efotm(&amp;20061;48&amp;&gt;`dopb=
/%&lt;1707200!=3D8(&amp;2005575?&amp;&gt;`dopb/%&lt;170642?!=3D|vqc)!7201;=
=3D50'9wuvb$&amp;03641654*&gt;bgipf+!3=3D731103%9aihwc)!7200?073'9efotm(&am=
p;2004?51;&amp;&gt;`dopb/%&lt;17&gt;474&gt;!=3Dnehu`/!36406412!9abk{a($1677=
45;=3D&amp;&lt;cbotf+*01254133%2pvs`/!36383624!9abk{a($167574&gt;7&amp;&lt;=
qqvb/%&lt;104=3D460!=3Dnehu`/!363;42&gt;7!9abk{a($1656;3?&lt;&amp;&lt;cbotf=
+*01011776%2bejvf/%72&gt;17266!=3Defdwa*'7616?=3D&lt;=3D$?ebkpb$&amp;033522=
5&gt;*&gt;bgipfz&quot;]" data-creative-load-listener=3D""><div id=3D"mnet-v=
tgt-5300021a2fdaa4de81ac6222f36a5b5c"> <div style=3D"position: absolute; le=
ft: 0px; top: 0px; visibility: hidden;"><img src=3D"https://pagead2.googles=
yndication.com/pagead/gen_204?id=3Dxbid&amp;dbm_b=3DAKAmf-B59g69rRNGlEh9D1e=
D7nkecCqVTBNXLS0tyCtW8Fpf6RL0005vAUQhabToLPraKnOiXFAw17gT3C2rEC7PaU2kVri0na=
Qcwka9gBrt4WFy3LQA__Q" border=3D"0" width=3D"1" height=3D"1" alt=3D"" style=
=3D"display:none"></div><iframe title=3D"Blank" src=3D"cid:frame-AC5B0B5C63=
<EMAIL>" style=3D"display:none" aria-hidden=3D"t=
rue"></iframe><div><div style=3D"position:absolute; display:block;"><div cl=
ass=3D"GoogleActiveViewClass" id=3D"DfaVisibilityIdentifier_4114279737" act=
ive_view_class_name=3D"GoogleActiveViewElement" data-google-av-cxn=3D"https=
://pagead2.googlesyndication.com/pcs/activeview?xai=3DAKAOjss0vSWKSHGtS_GC7=
sxdgxxnMmQKzMRJUGd5mibT3_NTmLpmTeo7UWjCbqau8RUn4cwWebkP2Pt0WapA2prk5IxSl_qo=
ACpsuJKmbFIqjxUGlvjV-ItdR10Wa-XH_bAyV7Ox4Qq1Mlf4RuppaXIpqheI_g&amp;sig=3DCg=
0ArKJSzHEPu2RbFm8rEAE" data-google-av-adk=3D"1952848662" data-google-av-met=
adata=3D"la=3D0&amp;" data-google-av-ufs-integrator-metadata=3D"Cm0KOW1vZGV=
sX3BlcnNvbl9jb3VudHJ5X2NvZGVfU0dfcGVyc29uX3JlZ2lvbl9jb2RlXzUzNDcuanNvbhIaQ0=
5qUHlOdjN6bzBERmVxSlpnSWQ1cDBRS2cYASIKCI4VII4VMAI4ATgBQAFIAFABEoACCvMBaHR0c=
HM6Ly9wYWdlYWQyLmdvb2dsZXN5bmRpY2F0aW9uLmNvbS9wY3MvYWN0aXZldmlldz94YWk9QUtB=
T2pzczB2U1dLU0hHdFNfR0M3c3hkZ3h4bk1tUUt6TVJKVUdkNW1pYlQzX05UbUxwbVRlbzdVV2p=
DYnFhdThSVW40Y3dXZWJrUDJQdDBXYXBBMnByazVJeFNsX3FvQUNwc3VKS21iRklxanhVR2x2al=
YtSXRkUjEwV2EtWEhfYkF5VjdPeDRRcTFNbGY0UnVwcGFYSXBxaGVJX2cmc2lnPUNnMEFyS0pTe=
khFUHUyUmJGbThyRUFFEgAaACABKAAwBBoeChpDTmpQeU52M3pvMERGZXFKWmdJZDVwMFFLZxAF=
" data-google-av-override=3D"-1" data-google-av-immediate=3D"true" data-goo=
gle-av-aid=3D"0" data-google-av-naid=3D"1" data-google-av-slift=3D"" data-g=
oogle-av-cpmav=3D"" data-google-av-btr=3D"" data-google-av-itpl=3D"32" data=
-google-av-rs=3D"6" data-google-av-dm=3D"2" data-google-av-flags=3D"[&quot;=
x%278440'9efotm(&amp;753374%2bejvf/%27844&gt;'9wuvb$&amp;56533&gt;!=3D|vqc)=
!273794&amp;&lt;qqvb/%&lt;1735020!=3Dnehu`/!364=3D5051!9abk{a($160210:3&amp=
;&lt;cbotf+*0150034:%2bejvf/%72;17613!=3Defdwa*'76463;21$?ebkpb$&amp;036671=
7&gt;*&gt;bgipf+!3=3D712363%9aihwc)!7202&lt;217'9efotm(&amp;20061;48&amp;&g=
t;`dopb/%&lt;1707200!=3D8(&amp;2005575?&amp;&gt;`dopb/%&lt;170642?!=3D|vqc)=
!7201;=3D50'9wuvb$&amp;03641654*&gt;bgipf+!3=3D731103%9aihwc)!7200?073'9efo=
tm(&amp;2004?51;&amp;&gt;`dopb/%&lt;17&gt;474&gt;!=3Dnehu`/!36406412!9abk{a=
($167745;=3D&amp;&lt;cbotf+*01254133%2pvs`/!36383624!9abk{a($167574&gt;7&am=
p;&lt;qqvb/%&lt;104=3D460!=3Dnehu`/!363;42&gt;7!9abk{a($1656;3?&lt;&amp;&lt=
;cbotf+*01011776%2bejvf/%72&gt;17266!=3Defdwa*'7616?=3D&lt;=3D$?ebkpb$&amp;=
0335225&gt;*&gt;bgipfz&quot;]" data-creative-load-listener=3D""><div id=3D"=
ad_unit"><div id=3D"abgcp" class=3D"abgcp"><div id=3D"abgc" class=3D"abgc" =
dir=3D"ltr"><div id=3D"abgb" class=3D"abgb"><div class=3D"il-wrap"><div cla=
ss=3D"il-icon"><svg xmlns=3D"http://www.w3.org/2000/svg" xmlns:xlink=3D"htt=
p://www.w3.org/1999/xlink" viewBox=3D"0 0 15 15"><path d=3D"M7.5,1.5a6,6,0,=
1,0,0,12a6,6,0,1,0,0,-12m0,1a5,5,0,1,1,0,10a5,5,0,1,1,0,-10ZM6.625,11l1.75,=
0l0,-4.5l-1.75,0ZM7.5,3.75a1,1,0,1,0,0,2a1,1,0,1,0,0,-2Z"></path></svg></di=
v></div></div><div id=3D"abgs" class=3D"abgs"><a id=3D"abgl" class=3D"abgl"=
 href=3D"https://adssettings.google.com/whythisad?source=3Ddisplay&amp;reas=
ons=3DASp5-QCZ4EuTvgWnBg4Gb6mQrcfYi7gQ9iwhKwtYBlu9U8LnAvi62izIgASRUSWSqm0X0=
VFvMFsvivm43sgb8z7jOKyiuGx3w9jBkZzsldFeiZRSsjIsZG_aqXQb7xjtX3vMOyVQcE7duCEh=
ToQT0xxp0IIwz0yBXSkd2V7Q_yY1PImaPnrD_Whtow7yL_KvZzphVL7AjsmXng1d0xj9E29pAkS=
KtegalFcXmV4Us5UJRjPuZSvzRRjvwgd9T_U4H2gYBgeSQtdlDfkCaQ_JxVSAh2Wbt9vUR3Nuwa=
EUaWBw7Zf-3fB2z-bh4S8csD9FfKefRal5ovQxeor9T0YWQbFPwmxHCfgSShhoPQf9ydf3RWhVc=
NbgrQGBKYrQ1blKgGMLyBr8aYOl--umHS_EFyxC1gYlVtiLQO16MBWOLrZRdJDgr_VcSkORNzkU=
f2QBq2fGvA-qhb9RkfgHms9ya6ml6UHTOHYXCyjgXu7VIMu5nwqYzhUgJ4qc9Hv4fNzm3Sk2WT2=
kqdiFDK3zF75stmD0_JWpKZEeQwf162hLXhTSbN6cH4cB_KjE-UNENW0r-iYZH5zjvXCHKy90GJ=
aW8q_tJF0-IPdmPvOZYSLWTyibN5zR599v5YF_-rpy-47sY-csZ5tFVIrYzMRoHktdY5ooPmEsc=
NBfP9oN_Ydk1a13-Lhmn9EWROZ2J96REvaeihaVizyp9af68lFWAx997yVhjsG-T3EOLe_iVW3H=
uVqAUrjYfgB95G5U9s0AqV_pAnvCE49z8crNBt_Pa93KnePyx4MmMGXZoNfJpXCjFUAhv_vj31p=
-3D2_xtlBeA2lwduCPzCU2p6jPAvI4ALMdPWZCyGM229tVw7kCZ4SbtZYHTU6bjhfkLcHJqqMT0=
BJyHNYDQZ3TpjgiUzSAHWiISLq43JDzTPIyCH2V6x8-LPAjzF_9f9kcLIo0yUBJoLwdiKbKWMW8=
MiKbeAINPFnD3snv9dP1zS1aManM4X_8PcQKTE4dTplAraPwYMej8b3WNu20wD-2x_ZiOZvisrS=
btUcFk8Ltqaks9e_Dv2wQ-V1-Y5VyquNlJua-cbNWJmPvlDrpj-k76CYRlv6ujIlAvtlUAOrZT7=
_m-6JPry8wOgiFmn0puwivuSC9tV0D9dGK4d277P1oJ39bXyzkq8em2sLZgdIgvktJ2SptEU4JP=
Nl7bUrRP-Dx2ZBGKREUKDUwhtxSdehCP-_-bNOH5hFwfdCtpYiEqY1AxYcIcIbLqXhPwXGA9PY8=
Ap4xqa506SMvNizEjmfAUjHJoIL64FQhG39yqwaEKgInMFac6x6zlj1n1vviWCNNuYVr_bRBIv4=
0bNOQWEcsQcNTF1rUgu_fMbUzahyy9gFIryJs_cqYySwkGqibOoJVwTVTDu4S_P5xghclYcscTm=
JCkKlWLI5zzg0slkomHhEbuZGbuEQGIu0mymLvxuc6r3eMQ5WkcVG-bs7wzf97vKoN7OdLHfvbC=
B2VshKjcHOD8uFUbxT80c-eP5TA5n3V51LgHVKD2Kqmw58bgLrhZcyddUVtHEdXJXrFnMpvepwQ=
x60Pb2ZWf6QOIQN6AfBHJOApK8wsHGArm-8PWc1vkLfe4-vhoSe4ApqZd9xyyW6nA_vnEk9aweP=
Oue365soIjNlh6PFUPaZUMi81wjDVGdSorrIiUvG5tKrlkC9hXj2-NnjFZ1yBw1xzTxsnyqH93X=
vB7qjP3eel4DqteoNcHN-SGlFUL2uCIdcgKsfSIzSbThJ6RJsozQW5s3zwJjKFV0yznvXBpEYeQ=
3pFQWMrQbE4pkuMXWtyWIfFwIOOM6WnVww&amp;opi=3D122715837" target=3D"_blank"><=
/a></div><div id=3D"cbb" class=3D"cbb" tabindex=3D"0" role=3D"button"><svg =
xmlns=3D"http://www.w3.org/2000/svg" xmlns:xlink=3D"http://www.w3.org/1999/=
xlink" viewBox=3D"0 0 15 15"><path d=3D"M3.25,3.25l8.5,8.5M11.75,3.25l-8.5,=
8.5"></path></svg></div></div></div><div id=3D"mute_panel" class=3D"mute_pa=
nel" aria-hidden=3D"true"><div id=3D"abgac" class=3D"abgac" aria-hidden=3D"=
true"><div id=3D"mlsc" class=3D"mlsc"><svg class=3D"mls" viewBox=3D"50 50 1=
00 100"><circle class=3D"mlsd" cx=3D"100" cy=3D"100" r=3D"30" fill=3D"none"=
 stroke=3D"#9E9E9E" stroke-width=3D"3"></circle></svg></div></div></div></d=
iv><div class=3D"ft_async_tag">
   =20
    <img src=3D"data:image/png,ft" style=3D"display: none"><div id=3D"ftdiv=
9126976" style=3D"position: relative; width: 300px; height: 250px;"><a href=
=3D"https://googleads.g.doubleclick.net/dbm/clk?sa=3DL&amp;ai=3DCjE05k5o7aN=
DMHoqT9fwPspbsyQGkl6K8f9n8vKW4FKGm-PPQHRABIMTiln9gvwWgAYviiqAByAEJqAMByAObB=
KoEhwJP0FFgLqoHPJpqHORZLYrt8XiHKC_69dJf_-1uJzTvwy6cjj8waJciFsMx9phZ2Txg3Xi9=
00JRVkskGG_z90Fv5syxn2MeH840kkssaBzd4Y5lwWd_L47kx_8ezqlMuOsrWVX2u-pSNt6al6A=
2vJZGC7EPRKYGaFVMaCKDeYNwxP4LgFZsXc5C3fAOVdSzYzpfGfUQxUtrhJZoXgPDBj3gF5ZZWP=
ErIBAmWP_6OyP0I6yUcIMCr0AZTjtSNM7cdWk4sgjMoIuenYTLuJx05U_hkVoyc_3W_LgWqsR6D=
xDnheX_SfNkfT9ThG50rwQc1rYYh_SCyafZKs4l4HB1JnyTZKJPoVDaMcAEj6Du6KsF4AQDiAW0=
2J2QVJAGAaAGTYAH3Z313wKoB9XJG6gH2baxAqgHpr4bqAfz0RuoB5bYG6gHqpuxAqgH4L2xAqg=
Hjs4bqAeT2BuoB_DgG6gH7paxAqgH_p6xAqgHr76xAqgHmgaoB_-esQKoB9-fsQKoB_jCsQKoB_=
vCsQLYBwDSCCgIgGEQARhdMgKKAjoNgECAwICAgICogAKoA0i9_cE6WLjElNv3zo0D8ggQYmlkZ=
GVyLThDVTlSMjY4RIAKBJgLAcgLAYAMAaoNAlNH8A0BsBOi7fgc0BMA2BMN2BQB0BUB-BYBgBcB=
shcEGAIqALIYCRIC6FgYTSIBAA&amp;ae=3D1&amp;num=3D1&amp;cid=3DCAQSKQDZpuyzzPh=
y-9r0oFVO8QeVKIpbgpvCjeCU-3C4khup-NvvxM5mpVJRGAE&amp;sig=3DAOD64_0OLKeoIBXt=
urZ8ouRXtCHFAwjNpA&amp;client=3Dca-pub-2240701429486168&amp;dbm_c=3DAKAmf-B=
9OCQJ-BRQ8PGAYLM9tRWltgEOWMPnW_Qxc4zLjs6_9BhWcMZZOWFFcmC_60Ne84Dqzl9LYhm7jl=
7wG6-vzwH1v6C3qrz5op58hZ8WLB7XHvf39d01o1544gkRBejcvNLXzivnrGvrYflVIy1PKIrQB=
GM6pD8dW_WA3Kj_TY8vABs8wmbzWWsR67RmSYXspNeMowK9dKukRTsAs2hiqkiPeHmmHKERvxoL=
B4forcAjVgvvUeWxh_FszdiXkBIRSFlrKZRnIBC_RqXiO_X_xsv3jS1C_pMnE-kM2OmVM6AmfBC=
5D0o&amp;cry=3D1&amp;dbm_d=3DAKAmf-AFuj17PgTS7pkPEUpX6swtHO2W1JDXklNKmUHcMU=
1hmGl3FCseTt_yYWNmAtUdtKM1kVYYXEzbjWghFoifkmn6C-izKt9Bb5KyR_DLMkXv4wm_uzTQE=
LEeSSUntYeDWvtYTmKSRFSpFvzH-fdNjD6RitXqTjeChcjAamHXHiMUU2dzs2p_VmtFGSnypysg=
ZDO4_mkLX8nWwFEa-8eDAWMLMr3GoHtTpedNJtzAb1i3e4R1n63ObKvOrBvp1JR_0MZc4YXwgBc=
AdE-BcZ_BbRiI4_2egCKKurCTFf7-v6tLIQ5Zn3lK8bl6bLNgV3odxf4mymJKgP1DjZYl_LQ3P8=
iaEPfOWuwN1VRyRZSy31o3f73rfz7yzqwzeF6hKG5qzyx9YDmSWPGzf1H9VZDnR6tRt_ALSnlHd=
3GykufovsgUi-MR5UfVQ9sbPa9tscDAvE9UVqq8juxXRg7NBMP3fpEIRRVehr7EzQ6P-S-Ea010=
K_mV92SrNqKkYwJs3avEqmHabLvL9XmvNwa_7J-Np10wOTeAmuNbtY76VsXtb0TsdgR0EoU32ka=
8Jp4LOxfWe1_IL0NPaxM9NVwAU-bng6aglGlzo1uKBmeQISSsSZd6TW2J5KsMKligg0UxpRTSsi=
RBO5EwubRAzVSatPCvz4g6EdsO7Q&amp;adurl=3Dhttps://servedby.flashtalking.com/=
click/7/258567;9126976;4794602;210;0/?gdpr=3D0&amp;ft_partnerimpid=3DABAjH0=
iFKXfp2ny_6Bu25WcGP14V;ABAjH0iFKXfp2ny_6Bu25WcGP14V&amp;ft_impID=3DCB83A3B0=
-7364-8978-6953-9E51CEE90043&amp;ft_section=3DDV360CampID:22582619188&amp;g=
=3D6355524836A521&amp;random=3D611258.7775327358&amp;ft_width=3D300&amp;ft_=
height=3D250&amp;url=3Dhttps://www.adobe.com/sg/products/illustrator.html?s=
did=3DWPHDJ44J&amp;mv=3Ddisplay&amp;mv2=3Ddisplay" title=3D"Click to learn =
more" id=3D"ftalt9126976" target=3D"_blank" style=3D"display: inline-block;=
"><img src=3D"https://cdn.flashtalking.com/190737/4794602/FY24Q3_CC_Individ=
ual_Illustrator_xy_en_AIJune2024Release-GenShapeFill_ST_300x250_V2.jpg?1995=
79228" id=3D"ftalt9126976" alt=3D"Click Here" style=3D"border: 0px; width: =
300px; height: 250px;"></a><div id=3D"ftAdChoices_9126976" style=3D"positio=
n: absolute; top: 1px; right: 2px; z-index: 999999;"><a target=3D"_blank" h=
ref=3D"https://www.flashtalking.com/consumer-privacy"><img src=3D"https://c=
dn.flashtalking.com/oba/icon/iconc.png?EDAA_icon=3Dy" id=3D"button" style=
=3D"border: 0px; width: 19px; height: 15px;"><img id=3D"expButton" style=3D=
"border: 0px; display: none; width: 72px; height: 15px;"></a></div></div>
</div></div></div></div><iframe width=3D"0" height=3D"0" style=3D"display:n=
one" src=3D"cid:<EMAIL>"></ifram=
e></div> </div><img width=3D"1" height=3D"1" attributionsrc=3D"" src=3D"htt=
ps://ad.doubleclick.net/ddm/activity/attribution_src_register;crd=3DcXVlcnl=
fZXZlbnRfaWQgewogIHRpbWVfdXNlYzogMTc0ODczNjY2MDM1MDkzNgogIHNlcnZlcl9pcDogND=
AyNzQ0MTAKICBwcm9jZXNzX2lkOiA3MDU3MzIwNzAKfQpmbG9vZGxpZ2h0X2NvbmZpZ19pZDogO=
TIxMjI1MgphZHZlcnRpc2VyX2RvbWFpbjogImh0dHBzOi8vYWRvYmUuY29tIgp4ZmFfYXR0cmli=
dXRpb25faW50ZXJhY3Rpb25fdHlwZTogVklFVwppbXByZXNzaW9uX3ByaW9yaXR5OiAwCmltcHJ=
lc3Npb25fZXhwaXJ5X2luX2RheXM6IDMwCmV2ZW50X2ltcHJlc3Npb25faWQ6IDE1MDA4MjY0NT=
g2MDI1NjE2NzMKZGVidWdfa2V5OiAyNzYxMTQ0NTMwOTMxOTI4NDI3CmltcHJlc3Npb25fbWVhc=
3VyZW1lbnRfZGltZW5zaW9uc19kYXRhIHsKICBtZWFzdXJlbWVudF9kaW1lbnNpb246IElNUFJF=
U1NJT05fRElNRU5TSU9OX1BST0RVQ1RfVFlQRQogIG1lYXN1cmVtZW50X2RpbWVuc2lvbl92YWx=
1ZSB7CiAgICBpbnQ2NF92YWx1ZTogMgogIH0KfQppbXByZXNzaW9uX21lYXN1cmVtZW50X2RpbW=
Vuc2lvbnNfZGF0YSB7CiAgbWVhc3VyZW1lbnRfZGltZW5zaW9uOiBJTVBSRVNTSU9OX0RJTUVOU=
0lPTl9JTlRFUkFDVElPTl9UWVBFCiAgbWVhc3VyZW1lbnRfZGltZW5zaW9uX3ZhbHVlIHsKICAg=
IGludDY0X3ZhbHVlOiAzCiAgfQp9CmltcHJlc3Npb25fbWVhc3VyZW1lbnRfZGltZW5zaW9uc19=
kYXRhIHsKICBtZWFzdXJlbWVudF9kaW1lbnNpb246IElNUFJFU1NJT05fRElNRU5TSU9OX0lOVE=
VSQUNUSU9OX0RBVEUKICBtZWFzdXJlbWVudF9kaW1lbnNpb25fdmFsdWUgewogICAgc3RyaW5nX=
3ZhbHVlOiAiMjAyNS0wNi0wMSIKICB9Cn0KaW1wcmVzc2lvbl9tZWFzdXJlbWVudF9kaW1lbnNp=
b25zX2RhdGEgewogIG1lYXN1cmVtZW50X2RpbWVuc2lvbjogSU1QUkVTU0lPTl9ESU1FTlNJT05=
fRkxPT0RMSUdIVF9DT05GSUdfSUQKICBtZWFzdXJlbWVudF9kaW1lbnNpb25fdmFsdWUgewogIC=
AgaW50NjRfdmFsdWU6IDkyMTIyNTIKICB9Cn0KaW1wcmVzc2lvbl9tZWFzdXJlbWVudF9kaW1lb=
nNpb25zX2RhdGEgewogIG1lYXN1cmVtZW50X2RpbWVuc2lvbjogSU1QUkVTU0lPTl9ESU1FTlNJ=
T05fQ09SRV9QTEFURk9STV9TRVJWSUNFCiAgbWVhc3VyZW1lbnRfZGltZW5zaW9uX3ZhbHVlIHs=
KICAgIGludDY0X3ZhbHVlOiAwCiAgfQp9CmltcHJlc3Npb25fbWVhc3VyZW1lbnRfZGltZW5zaW=
9uc19kYXRhIHsKICBtZWFzdXJlbWVudF9kaW1lbnNpb246IElNUFJFU1NJT05fRElNRU5TSU9OX=
01PQklMRV9CUk9XU0VSX0NMQVNTCiAgbWVhc3VyZW1lbnRfZGltZW5zaW9uX3ZhbHVlIHsKICAg=
IGludDY0X3ZhbHVlOiAzCiAgfQp9CmltcHJlc3Npb25fbWVhc3VyZW1lbnRfZGltZW5zaW9uc19=
kYXRhIHsKICBtZWFzdXJlbWVudF9kaW1lbnNpb246IElNUFJFU1NJT05fRElNRU5TSU9OX1FVRV=
JZX0NPVU5UUlkKICBtZWFzdXJlbWVudF9kaW1lbnNpb25fdmFsdWUgewogICAgc3RyaW5nX3Zhb=
HVlOiAiU0ciCiAgfQp9CmltcHJlc3Npb25fbWVhc3VyZW1lbnRfZGltZW5zaW9uc19kYXRhIHsK=
ICBtZWFzdXJlbWVudF9kaW1lbnNpb246IElNUFJFU1NJT05fRElNRU5TSU9OX1BMQUNFTUVOVF9=
JRAogIG1lYXN1cmVtZW50X2RpbWVuc2lvbl92YWx1ZSB7CiAgICBpbnQ2NF92YWx1ZTogNDEyMT=
MxOTE2CiAgfQp9CmltcHJlc3Npb25fbWVhc3VyZW1lbnRfZGltZW5zaW9uc19kYXRhIHsKICBtZ=
WFzdXJlbWVudF9kaW1lbnNpb246IElNUFJFU1NJT05fRElNRU5TSU9OX0NPT0tJRV9DT05TRU5U=
CiAgbWVhc3VyZW1lbnRfZGltZW5zaW9uX3ZhbHVlIHsKICAgIGludDY0X3ZhbHVlOiAwCiAgfQp=
9CmltcHJlc3Npb25fbWVhc3VyZW1lbnRfZGltZW5zaW9uc19kYXRhIHsKICBtZWFzdXJlbWVudF=
9kaW1lbnNpb246IElNUFJFU1NJT05fRElNRU5TSU9OX0RWM19BRFZFUlRJU0VSX0lECiAgbWVhc=
3VyZW1lbnRfZGltZW5zaW9uX3ZhbHVlIHsKICAgIGludDY0X3ZhbHVlOiA3NDAwMzI1MjMKICB9=
Cn0KaW1wcmVzc2lvbl9tZWFzdXJlbWVudF9kaW1lbnNpb25zX2RhdGEgewogIG1lYXN1cmVtZW5=
0X2RpbWVuc2lvbjogSU1QUkVTU0lPTl9ESU1FTlNJT05fRFYzX0xJTkVfSVRFTV9JRAogIG1lYX=
N1cmVtZW50X2RpbWVuc2lvbl92YWx1ZSB7CiAgICBpbnQ2NF92YWx1ZTogMjI1ODI2MTkxODgKI=
CB9Cn0KaW1wcmVzc2lvbl9tZWFzdXJlbWVudF9kaW1lbnNpb25zX2RhdGEgewogIG1lYXN1cmVt=
ZW50X2RpbWVuc2lvbjogSU1QUkVTU0lPTl9ESU1FTlNJT05fRFYzX0NSRUFUSVZFX0lECiAgbWV=
hc3VyZW1lbnRfZGltZW5zaW9uX3ZhbHVlIHsKICAgIGludDY0X3ZhbHVlOiA2Mjk1NzY0ODkKIC=
B9Cn0KYXJjaGV0eXBlX2lkOiAxMgphcmNoZXR5cGVfaWQ6IDEzCmFyY2hldHlwZV9pZDogMTQKY=
XJjaGV0eXBlX2lkOiAxNQpmbG9vZGxpZ2h0X2FjdGl2aXRpZXNfZm9yX2JpZGRpbmc6IDEyMzQx=
MjEzCmZsb29kbGlnaHRfYWN0aXZpdGllc19mb3JfYmlkZGluZzogMTIzNjU3ODUKYWR2ZXJ0aXN=
lcl9jb252ZXJzaW9uX2RvbWFpbnM6ICJodHRwczovL2Fkb2JlLmNvbSIKYWR2ZXJ0aXNlcl9jb2=
52ZXJzaW9uX2RvbWFpbnM6ICJodHRwczovL2ZsYXNodGFsa2luZy5jb20iCmltcHJlc3Npb25fZ=
XZlbnRfcmVwb3J0aW5nX3dpbmRvd19kYXlzOiA0CmJyb3dzZXJfYXR0cmlidXRpb25fYXBpX3Jl=
cXVlc3RfcHJvY2Vzc2luZ19iaXRzOiA3ODA0NzYwODgzMgpkbWFfcHJvZHVjdF9pZDogMTIyNzE=
1ODM3CnhmYV9hdHRyaWJ1dGlvbl9hcGlfdHlwZTogWEZBX0FUVFJJQlVUSU9OX0FQSV9UWVBFX1=
dFQgplY2hvX3NlcnZlcl9hY3Rpb246IEVDSE9fU0VSVkVSX0FDVElPTl9VU0VfQkVTVF9BVkFJT=
EFCTEVfQVJBCmV2ZW50X3JlcG9ydGluZ193aW5kb3dzIHsKICBlbmRfdGltZXNfc2Vjb25kczog=
ODY0MDAKICBlbmRfdGltZXNfc2Vjb25kczogMzQ1NjAwCn0KbWF4X2V2ZW50X2xldmVsX3JlcG9=
ydHM6IDIK" style=3D"display: none;"><iframe title=3D"Blank" scrolling=3D"no=
" frameborder=3D"0" height=3D"0" width=3D"0" src=3D"cid:frame-6DB0BCB1B76D0=
<EMAIL>" style=3D"position:absolute" aria-hidden=3D=
"true"></iframe><div style=3D"display: none; position: absolute; z-index: 2=
147483647; width: 100%; height: 100%; top: 0px; left: 0px;"></div><img src=
=3D"https://www.google.com/ads/measurement/l?ebcid=3DALh7CaTi9Xa2HpsG7LPMdi=
L8XmdTdErIOs9vxurT-4kO-F2gi1IhkzAjo1XYcqb6BghcIQTGAs0iBOSLyT6y7oRt7HxiB0ZZu=
g" style=3D"display:none;" alt=3D""><div style=3D"bottom:0;right:0;width:30=
0px;height:250px;background:initial !important;position:absolute !important=
;max-width:100% !important;max-height:100% !important;pointer-events:none !=
important;image-rendering:pixelated !important;z-index:2147483647;backgroun=
d-image:url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACsAAAAWBAMAAACr=
l3iAAAAABlBMVEUAAAD+AciWmZzWAAAAAnRSTlMAApidrBQAAAB9SURBVBjTbVDbEcAwCMIN3H/=
aWsFXr3xEvSAhAP8wAva2PQGet3F6oKdkwKZyM27IIrWmzcJho86t3U60IycN949viEvz1E5FH2=
G2EoRWUIWm84/s+IiSGLJqGaF5L7KcNsvmf+IyEZwUOpibIN3jslc0R9vpXW90ghNL4AFuAwXNM=
liRIAAAAABJRU5ErkJggg=3D=3D') !important;"></div><iframe src=3D"cid:frame-4=
<EMAIL>" style=3D"display: none;"></ifr=
ame><iframe src=3D"cid:<EMAIL>" =
style=3D"display: none;"></iframe>
 =20
 =20
 =20

<iframe src=3D"cid:<EMAIL>" widt=
h=3D"0" height=3D"0" style=3D"display: none;"></iframe><div id=3D"__pm_dom"=
 style=3D"position: absolute; left:-10000px; top:-10000px; overflow: hidden=
;"></div><iframe pm_ifr=3D"true" style=3D"position: absolute; width: 100%; =
border: none; height: 0px; top: -1000px;"></iframe></body></html>
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: image/png
Content-Transfer-Encoding: base64
Content-Location: https://cdn.flashtalking.com/oba/icon/iconc.png?EDAA_icon=y

iVBORw0KGgoAAAANSUhEUgAAABMAAAAPCAYAAAAGRPQsAAAAGXRFWHRTb2Z0d2FyZQBBZG9iZSBJ
bWFnZVJlYWR5ccllPAAAAyBpVFh0WE1MOmNvbS5hZG9iZS54bXAAAAAAADw/eHBhY2tldCBiZWdp
bj0i77u/IiBpZD0iVzVNME1wQ2VoaUh6cmVTek5UY3prYzlkIj8+IDx4OnhtcG1ldGEgeG1sbnM6
eD0iYWRvYmU6bnM6bWV0YS8iIHg6eG1wdGs9IkFkb2JlIFhNUCBDb3JlIDUuMC1jMDYwIDYxLjEz
NDc3NywgMjAxMC8wMi8xMi0xNzozMjowMCAgICAgICAgIj4gPHJkZjpSREYgeG1sbnM6cmRmPSJo
dHRwOi8vd3d3LnczLm9yZy8xOTk5LzAyLzIyLXJkZi1zeW50YXgtbnMjIj4gPHJkZjpEZXNjcmlw
dGlvbiByZGY6YWJvdXQ9IiIgeG1sbnM6eG1wPSJodHRwOi8vbnMuYWRvYmUuY29tL3hhcC8xLjAv
IiB4bWxuczp4bXBNTT0iaHR0cDovL25zLmFkb2JlLmNvbS94YXAvMS4wL21tLyIgeG1sbnM6c3RS
ZWY9Imh0dHA6Ly9ucy5hZG9iZS5jb20veGFwLzEuMC9zVHlwZS9SZXNvdXJjZVJlZiMiIHhtcDpD
cmVhdG9yVG9vbD0iQWRvYmUgUGhvdG9zaG9wIENTNSBXaW5kb3dzIiB4bXBNTTpJbnN0YW5jZUlE
PSJ4bXAuaWlkOjc3NDlERTJEQTNGRjExRTE4RDI4QzdBNDRBRTA3RTFGIiB4bXBNTTpEb2N1bWVu
dElEPSJ4bXAuZGlkOjc3NDlERTJFQTNGRjExRTE4RDI4QzdBNDRBRTA3RTFGIj4gPHhtcE1NOkRl
cml2ZWRGcm9tIHN0UmVmOmluc3RhbmNlSUQ9InhtcC5paWQ6Nzc0OURFMkJBM0ZGMTFFMThEMjhD
N0E0NEFFMDdFMUYiIHN0UmVmOmRvY3VtZW50SUQ9InhtcC5kaWQ6Nzc0OURFMkNBM0ZGMTFFMThE
MjhDN0E0NEFFMDdFMUYiLz4gPC9yZGY6RGVzY3JpcHRpb24+IDwvcmRmOlJERj4gPC94OnhtcG1l
dGE+IDw/eHBhY2tldCBlbmQ9InIiPz7GFD4wAAABkklEQVR42qyUwUrDQBCG/yYW26pFEako6AYR
EaxIvenB9qAXe/EJjEefQn0K8WQ9+wCKF1voQVDEU0AUswoVDx5aqoJQo/+2KaikSS0uTCY7JF/+
mdlJyLbtPfzT6vIKpiVW74H4EnBt0tICbwx/BsE0ryBBo3Q9BWB2A1jblpiSQKQjmFrjwMMWcEL/
fAAsZqg2J2H4QVvCBFAxBR7zAsfrQJEhfQdYNiUyhI5IjxJpPqp1dclLDPDFKFUWWcMLlqCf0CxT
XyA0Ib8xtKA68OFu2jBvazmBy33giFCL9ZwgdIXQVNswLocpv9KceqcFyoQqlRa3UUJTf1HWxxem
6XvdfYRqkoV6WeGwQTe+5+xXI5Sidxcyyc7OcD9EyFMaOGeTSu3APlxfo4UJmVdnj1Zmd095mEui
cZh/TIAKxDzSi0uJGH0YDXU6IWdUc8e6vbQaJ34Umx5TMJYBEiz0LTt4qGCEVPzGSjMM44p+11XY
XFWVJutSYjoWIVXVxaD5DDVv+PdQqWZpc7TBTv4aXwIMAOLRdM1/O7HYAAAAAElFTkSuQmCC

------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: image/jpeg
Content-Transfer-Encoding: base64
Content-Location: https://cdn.flashtalking.com/190737/4794602/FY24Q3_CC_Individual_Illustrator_xy_en_AIJune2024Release-GenShapeFill_ST_300x250_V2.jpg?199579228

/9j/4AAQSkZJRgABAgEASABIAAD/2wBDAAEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEB
AQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/2wBDAQEBAQEBAQEBAQEBAQEBAQEBAQEB
AQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQEBAQH/wAARCAD6ASwDAREA
AhEBAxEB/8QAHwAAAQUBAQEBAQEAAAAAAAAAAAECAwQFBgcICQoL/8QAtRAAAgEDAwIEAwUFBAQA
AAF9AQIDAAQRBRIhMUEGE1FhByJxFDKBkaEII0KxwRVS0fAkM2JyggkKFhcYGRolJicoKSo0NTY3
ODk6Q0RFRkdISUpTVFVWV1hZWmNkZWZnaGlqc3R1dnd4eXqDhIWGh4iJipKTlJWWl5iZmqKjpKWm
p6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uHi4+Tl5ufo6erx8vP09fb3+Pn6/8QAHwEA
AwEBAQEBAQEBAQAAAAAAAAECAwQFBgcICQoL/8QAtREAAgECBAQDBAcFBAQAAQJ3AAECAxEEBSEx
BhJBUQdhcRMiMoEIFEKRobHBCSMzUvAVYnLRChYkNOEl8RcYGRomJygpKjU2Nzg5OkNERUZHSElK
U1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6goOEhYaHiImKkpOUlZaXmJmaoqOkpaanqKmqsrO0tba3
uLm6wsPExcbHyMnK0tPU1dbX2Nna4uPk5ebn6Onq8vP09fb3+Pn6/9oADAMBAAIRAxEAPwD8fK/y
XP8At2CgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKA
CgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAK
ACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgD03wh8FfjJ8QtCvvFHgH4S/E3xx4a0u7urDU/E
XhDwH4p8S6Fp19ZWdtqF7ZX2raLpV7YWl3aWF7Z311bXFxHNb2d3bXMqJDPE7+pg8kzrMaFTFZfl
GaY7DUpyp1cTg8Bi8VQp1IQjUnCpWoUp04ThTnCpKMpKUYTjJpRkm/j8+8Q+AOFcyw2TcT8c8H8O
ZxjaFHFYPKs+4myXJ8yxeGxFethcPiMNgcwxuHxVehXxWHxGGo1aVKdOrXoVqMJSqUpxj5lXln2A
UAFABQAUAFABQB6dZ/BT4x6h8NNQ+M9h8KfiNe/CHSb9dL1T4oWngvxHcfD/AE3UXuYrJbO+8Xw6
c+gWs/224trIpPfoVvbm2s2xc3EMUnqQyPOamWVM6p5TmU8no1FSq5rDBYmWX06jkoclTGKm8PCX
PKMLSqK05Rh8Uop/HV/ETgDC8X4Xw+xPG3CeH47x2FeNwXBlfiHKaXFGLwsaU8Q6+GyGeLjmden9
XpVcQpU8NK+Ho1q6vRpVJx8xryz7EKACgAoA9D+Gnwj+Kvxn1+Twp8Ifht47+KPiaGwuNVm8P/D7
wnrvjDWYdMtNgudQm03QLG/u4rOF5IonuZIlhE00MIcyzRI/pZZk+bZ1iHhMnyzH5rio05VZYfLs
JXxlaNKFuapKnh6dSagm0nJpLmcY3vJJ/K8X8dcFeH2Vwzvjvi7hrgzJ6mJpYKnmnFOeZbkOX1MZ
X5nSwtPF5picLQniJxjOcaMJufs4VKnLyU5yjf8ACnwN+NfjufXbXwP8H/il4yufC2oHSPE1v4U+
H/izxFP4c1UPPGdM12HSNIvJNI1ASWt0hstQW3uQ9tOpi3QyBdMJkWeY+WIjgcmzXGywtT2OKjhM
uxeJlhq15L2WIVGjN0al4yXJU5ZXjJW0ZzZ34keHfDNLLK3EfHvBfD9HOsKsdk9XO+Kcjyqlm2Cc
aU1jMsqY/HUI4/CuNajJYjCurR5atJ89qkG2+E/gj8aPHum6prPgX4RfE/xppGh31zpmtar4T8A+
K/Eem6RqVnBFdXen6pfaPpN5a6ffWtrPDc3NpdyxTwQTRTSxrHIjFYTI87zClVrYDJ80xtGhUlSr
1cJl+LxNKjVhFTnTq1KNGcadSEZRlKE2pRi02kmmPPPEbw94YxeDy/iXjvg3h7H5jhqWMy/BZ5xP
kmU4vH4TEVZ0KGKweGx+Ow9bFYatWp1KNKvQhOlUq0504SlOMkvL68o+yPUPiB8EvjL8JtO8K6v8
UfhR8R/hzpPjrTjq3gvU/HPgrxH4UsPFmmrFazve+HbzXNNsbfWLdLe+sbiSSwknEdvfWNw+2G8t
nl9XMcjzrKKeErZrlOZZbSx9P22Cq4/A4nCU8XSSjJzw069OnGtFRnTk3TcrRqQk/dnFv43hbxF8
P+OMXneA4L434S4tx3DeKWB4hwfDfEWU53iskxjnWpxw+a0MtxeJq4CrKrh8TSjHExpuVXDYmlG9
TD1ow8vryj7IKACgAoAKACgAoAKACgAoA9Qu/gl8ZbD4ZWHxpvvhR8R7L4P6rqH9k6Z8UrrwV4jt
/h7qGpfaJ7MWVl4xl01PD91cNeWt3ZJHDfuXvLS7tE3XFrPFH6s8jzqnldPO6mU5lDJqtT2NLNZ4
HExy6pV5pQ5IYx01h5Sc4TglGo7zhOC96MkvjaHiL4f4rjDFeHmG434SxHHuCwv17GcF0OIspq8V
YXB+ypV/rGIyCGLlmlGksPXoYiUqmFio4evQrytSrUpz8vryj7IKACgAoAKACgAoA/XP/gin+yz8
I/2r/wBs6Pwn8a9Os/EfgrwB8NPE3xR/4QnULqS3sPG2taLrvhTw9pWi6lDBLBcahpdlL4pfxFf6
bHMsOoR6ItlqUV1o9xqNpP8AsHgjwpk/FvGqwmd04YnA5flmKzX6jUm408dXoV8Jh6VCrGLjKpSg
8U8TUpKXLUVBQqqdGVSEv4V/aH+NPHXgh9H2Wd+HeLxGU8Q8UcX5PwZ/rFhaMauJ4dy/MMtzvNcd
mGEqVIVKWFxuIhkscpwuMnTdTCzzF4jBzo4+lha9P+gr9rX9uv4GfsUfEHVfg78Uv+CXWrRfs+6X
LZ6Lo3xQ0L4dfDI/DjxVp9zYWshTwvoM3he28Fzwo1w9kNEvfGOnanH5Wy/03T5XNsn9DcXceZFw
RmNXJs18LKy4dpOFCjmmHy3K/wCzcXTlTi7YXDywsMFJJydP2E8bTqq1qlKm3yr/AC18Dfo1eJP0
iOFsFx9wX9M7Az8U8ZTr5hmHBuZcV8Yri3JcVRxVaHNnOZ086rcQ05yVKOIeY4fIMXg58/NhsXio
RVaWN/wQes7Tx1+wf+1hYeELW30yx8Y/tT/HWz8L2VxDHp1pp9p4h+Dfwfh0W1nt7FbiGwt7SG7t
opIbRJo7WONkt1kVEDYeAkIY/gLi2ng4RpU8bxXn0MLCUVShThiMmyeNCEo01KNOMIzinGCkoJWi
mkj0P2mFevw39JjwQxWe1quMxOQeC3hrXznEUqk8XXxVfK+P+PKmY1qdXEOlUxNWvUoVpwqV5U51
pyUqrg5Sa/If9pX/AIN//wBpP9n/AOB3ij4z6N8Tvh58V5PAGgXfirxx4K8OaZ4k0nWrbw/pNobz
XtQ8K3OpW8sHiY6Lax3Wo3NndxeHby50qyuZrCG71L7PpM/4/wATfR84m4eyLFZ1RzTLs2eX4eeL
x2Bw1LE0a0cPRhz4iphJVIuOK9hBSqShOOGnKlCUqcZ1eWjL+7vCH9qJ4ReKPiPk3h9mHB3FXBEO
KM0oZLw5xDm2MyjHZfWzTHV1h8swudUcJVp1cnWY1pUcJRr0J5rQpY3EUqeKqUMJ7XHU/K/2DP8A
gjB8X/26Pg+fjjpHxe+HXw48E3PiHWvDOlWuqab4j8ReJ7vUPD8kMWpT3emWVtpml2OnmW4RLKZN
evbudo5zNY2sawvceTwD4KZxx3k39u0c4y3LcDPEV8LRhVpYnE4qdTDtRqynShGlSp07yShJYic5
WlzU4LlcvtfpNftBuA/o28e/8Q4x3AnFfFvEVHKsuzjG1sFi8pynJqGFzWFSeEp0MZiKuMxuJxSh
SlLEU5ZZh6FJSpqnia0nUjS8t/bo/wCCT37QX7C+peA7rxZrfhHx98PPiT4mg8G+G/iL4WbVLLTt
P8U3Zkey0Hxhpmq2aXWgajf2MF1qli1pNrWnXVlYaj5OoPdWNxap5XHfhJxDwJVy+WLr4PMMuzPF
RwWGzLCurTpU8VO7hh8ZSqwU8PUqU4zqwcJV6c6dOpy1HOEoL7T6Nn03/C36SeD4mo5Hl2e8McVc
IZPU4gzfhPOVgsRi8VktBRjiMzyHGYLESoZphMNialDBYhV6eXYujiMThPaYWNHE0q0v0H8Kf8G1
H7RF7pFtceOP2ifgz4U1y6RCmjaHpHjDxbbJO0U07Wkmq3lp4TL3EMUeZhaafdw5S5aGaaCBZpv0
PCfRm4jnRjLH8R5LhK80rUKFHGYyKk05ODqzhhLyil73JTmtJNOUYqUv5Yzv9r14VYfHVqXDfhR4
gZ3ltGUubMMyx+Q5FVlTU6dONeGCoV885aVSc7U/b4qhU96iqkKdSpKnT/Kr9sH/AIJu/tNfsY/E
/wAGfDPx94btvGT/ABR1IaT8J/E/w4/tLxDovxD1aTULPTYvD2jW0unWOtweLRdanpUE3hq80yO9
ebUrQ6a+p2k0N5L+UcY+GvE/BWaYLLMww0ca81q+xyjFZb7XEUMxrOpCksNRi6dOvHGc9WlGWFnS
U26sPZOrCSm/7Y8Bfpb+D30guDeIOMOGM3rZBHgzCPHccZNxb9UyrMOFcDHC4jFzzXMK0MXicuq5
G6ODxtWnnGHxk8PGnhK6xkcHXp1MPD9L/Bv/AAbkftH6x4S0m68bfHj4KfD74l69pM2q6b8Mbo69
r9xB5NvbzT2Gq6/p0EUf2vTJLmK212fw3pPijSrCYq1pqWqQyQyyfp2C+jdxLWwlGeOz/JMuzPEU
ZVaWVy+sYiUbRjKVOtiKcUuek5KNeWGo4qlTesKtWLTf8f8AEH7WbwlwGeY6jw74Z+IfFPCGWY6n
gsZxjR/szLKVT2lWrTp4nA5Xi6k5+wxkKM62W083x2TY3FU01XwmDqQnCP4f/tB/AL4m/swfF7xj
8EPi/okeheO/BN5b2+p29rdR6hpl7aahY22qaRrOjajEFj1DSNY0q8tNQsLkJFMIZxBeW1pfwXVp
B+GcRcP5pwtnGNyPOKCoY/AzjGrGE1UpThUhGrRrUai0qUa1KcKlOVlK0uWcYVIzhH/Rzws8T+Dv
GTgTIPEbgPMZ5lw1xFh6tXB1a1GWFxmGr4XEVsFj8vzDCTbnhcfl+Nw9fC4qlzTpupS9rh61fDVK
Ner/AEdfAjS/2vL7/gg5rtr4e1n9nq2+BmqeB/jI8UmqaR45k+LFj4PT4xeNbHxho4jtGHg271rU
/EFrrc2ja5OQdP0XUbaKa2m1O0ivIv6SyClxhU8A68MNW4djkVXA5006tHHvN6eCWc46njaNof7F
OvVxEa7o15fw6FSKlGVWCnH/ACW8TMb4EYb9pjltfNcv8VK3iTguI+AIzhgsfw3HgfE5/LgHh7E5
Bj3Ounn9DL8HldbLqeYZbSTWKzHCVZ061PB1p0Kn6P6F/wAEdfh9of8AwTu1H9l+Dwl8AH/aV1Tw
lqukXn7Rsvw40+41qfXNR8e3fieLUX8X3GgyePxbWWg3CeHLSNbpTaWcS2VrFHYKI6/SqHg1l1Dw
5qcLRwfDz4mq4OtRnxI8tpyryr1MfPFKo8ZLDvMOWGHksNBKXuQShBKnofyVmX0+eKcy+lZhPGWr
nnigvCHBZ5gsdh/CaHFuKpZfTy3C8MUMnnhI5DSzOPC7rYjM6Us2rzlRar4ibxFapPFNzP4dviZ4
Hvvhj8SPiB8NdUvLTUdT+HvjfxX4H1HULATLY3194T16/wBAu7yyFwkVwLS6uNPknthPHHMIXQSo
j7lH8LZngamV5lmOWVZwqVcux2LwNSpT5vZ1KmExFTDznDmSlySlTco8yUuVq6TP+jzg/iTDcZcJ
cLcX4PD18Jg+KuHMk4kwmFxTpvE4bDZ5lmFzOhh8Q6Up0nXo0sVGnVdOcqbqRlySlGzfv37BGkaV
r/7b37I2ia7pmn61o2q/tH/Bqw1TSNWsrbUdM1KxuvH+gxXNlf2F5FNa3lpcRM0c9tcRSQyxsySI
ykivoeAKNHEcc8IUK9KnXoVeJMlp1aNaEalKrTlmFBShUpzUoThJNqUZJxa0asfl/wBJzHY7K/o5
+OuY5bjMVl2YYLwl4/xWCx2BxFbCYzCYmjwxmU6OIwuKw86dfD16U0p06tKcKkJJSjJNJn9kX7ff
7Uf7GX7AXiL4c+HPGX7Evhr4kT/EfRde1uxuvA3wq+FS22lxaDfWFjNb351XTLdzPcPfpJCIVZRH
G245IFf2b4gcU8FeH2Jy3DY3gfDZlLMqGIr054DKcp5aSw9SnTcantaUXzSdRONrqydz/AX6MPgz
9IL6UGU8WZtw/wDSKzfhKnwlmOWZdiaPEvG3GzrY2eZ4bFYmnVwv1LGVY+zpRwsoVPaNPmnHlVrn
xH+xF+0ZrP7Vv7efxW+LH7B/wh+FPwI0XSP2YfBfgr4l/Dz41+G5tDtdW1S3+IfiXVLHxL4Xf4Q7
V0u8a3v/AA/a3y6nZ3EepWen3KSxCc6Xfaf8PwNxJW4s4+zbN+AsnynIaFHhfA4HM8uzvDSoRrVo
5jiqtPE4V5PpSny1MPGoqsJKrCnJNc3sqlP+ivpF+E+X+CX0ZeCeB/pMcecbeJeYY/xk4h4h4Q4q
8O83p5lWwOCq8K5RgsTlGcx47beNw8auGzWthng69KeDxGKoyhN01jcNivqD/gjWnjaL40/8FSYv
iVe+G9S+IMX7XV5F40vvB9hdaX4UuvE0eoePE1mXw3YX7y6jbaI18s39mJqc1xqjWYifU7q6v2uL
iT6nwXWOWdeKizOeGq5iuMJrG1MHTnSwksUqmPVZ4anUbqRoe05vZKrKVXks6s51HKT/ABr9oBLh
yfh59C+fCGHzfCcLT8CqE+HsNn+Jo4zO6OTzwvDMsBDN8ThowwlXMVhnT+uSwdOlg1X544OjRwyp
Uoed/wDBCj/kzb9s7/s5n4y/+qs8A15vgN/yRnGv/ZT51/6qsvPrP2lX/J/vo+f9me4A/wDW04nP
4srK3kury0tYTGJrm6t7eIzDMQkmlSNDKNkmYwzAuPLfK5Gxvun+J4Rc5whG15TjFX2vJpK+j0u9
dHp0Z/0K4mrChh69aopunRo1as1TdpuFOEpyUHzQtPlT5XzRs7e8t1/Zz/wUX/ZV/bO/a98Q/sU/
AH43+Nv2Z9IsfEfxG8YAax8PdG+JIm1TX/Cnwu1/xlr+p6smvtKNN02/8N+GNV0/TdG0kS41nVIp
r2/+xQQi1/tPxH4T414wxHBHD+eY7hijTxOZYz99l1HM71cRhMqxGNxFWssRf2VKphsLVp0qNG/7
+qpTqckY8n/Pt9E/xs+j54EZV9IfxP8ADnh3xgx+JynhPIW8BxVmHCLp4LK874zyzh/LMHgZZYof
W8Xhs3znBYrF5hjnD/hPwU6eHw31irUdfQ/4LKf8E0fh3r/7MUPxF/Z58B/Aj4E6T+zpovxD+Jnj
zT/Cfw50fwbqnjjRrLQLGWz0i0uPBug2kd5eW7WOozI2vSmEXF4JVmV5bhzr40eGWXYjhdZjw7gM
gyGjw5QzHM8fTwmW0cFVx1Gnh4OFGEsFh4Kc4uFSS+sPl5p35k3JnL+z/wDpf8V5X4x1OFPFXibx
L8Ssd4sZjwpwfwzis84sx+f4PhvMMRmmJhiMdXpZ/mdeWHw9VYnCU5RyyHtHSw7g6bjClFfxWV/E
h/0Mn78f8G7ngLwN8QP2uvi9pfj3wZ4T8b6ZZ/s4a9f2mneLvDuj+JLG1vl+Jvwut1vba01mzvbe
C7FvPPALmKNZhDNLEH2SOp/oL6OeX4DMeMM4pZhgsJjqUOG8RUhTxmGo4mnCp/amVR54wrQnGM+W
Uo8ySlyyavZs/wAwf2rPE/EnC3gVwJjOGOIc84cxmI8WsswtfF5Dm2PyjE18M+DuM6rw9avl+Iw9
WpQdWlTqOlOUqbqU4T5eaEWvg7/gqvoOh+F/+ChX7UWgeGtF0nw7oWmePbK303RdC06z0nSdPgPh
Pw5KYbHTrCG3s7SEySPIY4IY03u77dzEn4HxYw9DC+InFWHwtCjhqFLH040qFCnCjRpx+p4Z8tOl
TjGEFdt2jFK7b6n9MfQmzPMs5+iv4M5pnGYY7NcyxnDOJq4vMcyxdfHY7FVVnubQVTE4vFVKuIrz
5Ixjz1akpcsYxvZJHZ/sE/8ABKb9ov8Ab5s9Y8XeC7nw78O/hRoOpPot98SvHH9pGx1TW4Yo5rrR
fCWj6Xa3F/4hv9PiuLaTU5nk0zR7FZ1gl1f+0Nti3bwB4TcSeIEK2MwUsNl2U4eq6E8zx3tfZ1a6
SlOhg6NKEqmJqU1KLqybpUafMout7T92fP8A0m/pteE/0YsRgMi4ho5txXxvmeEjmGG4R4c+qfWc
Fl05zp0Mwz3H4yvSw2VYXFTpVoYOnGGMx+JdKVSGB+q3xK9p/bO/4IiftHfsi/Da/wDjTa+MvBfx
l+FPh6Gyu/Ges+DLbVdO8TeEtLu7q1tW8QXnhXU1lj1Pw9BJeQme/wBE13ULi0g82/1Kw0/S4J9Q
i9vjXwN4k4PyypncMbgs6ynDqE8bWwUKtPFYSlOcYPETwlW6q4eLnHmqUK9SUI3qVadOlGVRfnv0
ff2jHhL47cXYbw8rcP8AEPh/xvms8RQ4fy/iCtgcXk+eY2hRrV1leHzvBuE8HmtWFCoqWFzHLcLS
r1eTDYTE4rG1KWFnwP7bP/BJL4r/ALFnwJ8H/tC6v8VPh38VfAXjHxT4d8N2k/gK08TJLaW/ivw7
rPiPQfEVw+sabaW50S+t9G+yJcCRXF7qWmReWTdDb5/HHhDm3BOQ4PiKtmuXZtl+MxeGw0JZfDFJ
wji8NXxOHxMnWpQj7CpGjyKV789WlG3vn0/0dvpzcEfSG8Ss+8K8BwVxXwVxPkGS5tm9elxNXyeU
K9XJM2y/KczyqlHAYyvV/tHDVcw9vKk4OLw+ExlTmSou9/42/wDBHb4+/Bj4afs6eLH8YeDPGvxF
/aa8ReBvCXw9+CHhu21uHxz/AG/4w8Ky+Kb631G81e1sNAtLDwdDGln4o1m4v4NK02W4gurm6gsT
JcR6Z54NcQZJlnDmLeMwWOzLifE4HB5dkeGjXWO+sYzCPFVI1J1oU8PCngopQxVeVSNKk5RnKUad
5Lm8Ovp8eGHiFxf4sZHHIeIOHeE/B7KeI884q8Rs3rZdPhv+y8hzqGS4arhKGArYrM6+Kz+pKWIy
bL6WFqY3FwpVKNKjUxKjSn9hW3/Bt5+0VL4aUXH7Q/wKtPijJoX9tx/Dtk8VTWariKJo7jxPFpxv
ktYr+Q6dNqtt4RvLBbkII5ZVkBH2Mfo1cRvCrm4jyGGauh7dZa1i3BbJqWKVP2igqj9nKrHBzp89
rNpn4JW/a3eFEM4bpeFXiVX4LhmX9nT4rUskp4hu85qdLJp4pYaVaeGgsXTwVbPcPinRcnOEHBp/
gJ8T/hp43+DfxB8X/Cz4k6DdeGPHfgTXb7w54n0K7eCWXT9UsJNkqJcWss9peWsyFLmyv7Oeeyv7
Ka3vLOea2nilf+fs0yzHZLmOMyrM8PLC4/AV54bFUJuLdOrTdmlKLlCcZK0oVISlCpCUZwlKMk3/
AKgcG8YcOcf8LZDxpwjmdHOeGuJctw2bZNmVCNSEMVgsVDmhKVKtCnXoVqcuajiMNiKdPEYbEU6u
Hr06danOEf6nvFvwy/bI+KH/AARu/Zy+EVz4n/Z20/4SfE//AIZR8FWmrxaZ4+b4ijw38RPjF4B0
f4e6fqcEkU3hyxu/DniTXPCl14m1jS5JptT0XQdRt7KzS51R6/qvGZXxnmngxw3k8sVw5TyjNP8A
VLAwrKlmDzL6tmOc5fRy6nVi08NTnhsTXwk8VWpOUqtDD1IwgpVWf4sZFxh4AcG/T+8WeO6OTeK2
K464N/4jdxFXwE8ZwwuE3m/CvAPFGP4qxWDqQnDN8TQzbKcuzujk+X42NOng8wzPC1cRiJUcFE+5
fi7/AMEdvAfin9gPwl+zJ8M/Cn7Pvgj4+WXhr4Taf4y+PR+G2m2mr+JNf8INpF5421Z/FOleH/8A
hN7keJtRsb0QPe3BmlsbryL8KHlSvu848GsBivD7CcL5ZhOHsDxBDC5RTxuf/wBmUoVsTiMG6M8d
WeLpYf69L61Upz5XOXM6c+Wpu0fzdwJ9PjibJfpQZ54xcYZ54pcR+GGIzfjjFcP+GS4uxlfA5Rle
fLH4fh3AxyXG5r/q5SeT4TE4d1Y4el7OGJoe0wzbjCR/CDX8En/S6FABQAUAFABQB+gH/BOH4S/t
f/Er476j4j/Yg1/TdC+OXwd8E6h8SLBb/W9J0WXWtDOtaD4M1bQ7MeJbO58H6ub4eLYPt+jeL57D
Q73So77zLl7yOztbj9C8Nso4xzPP6mJ4GxFLD57k2BqZlT9pXpUHXoe3w+CrUIfWYSwdb2n1uPtK
OMlToTpKpeTmoRl/Lv0teOfAbhDw0wmU/SNyzF5l4b8fcRYXhLEvDZdjswhl+ZLLsz4gwOZV3lGI
o59gVhnkdT6tmGRU8TmWHxs8Ny0o4edetS/tN/Yv+I37aXx88JeNfht/wUF/Y88IfDmwt/DNtpt1
4l/4SLwl4k8H/FhtQnuLHVNC1D4Yrq/i9rBP7OSO81G6l1i+0C/lllhtrDTo5Le0X+2eCsy424gw
mOyzxD4NweW044WNKWJ+s4PE4LN3UlKFWhUyv22MdNeySnUk61TD1G5KMKacYL/np+kHwn9Hvwwz
zh3i76LXj3nvFmKq5xWxlHKP7Jz3KM+4IjhadLE4LMsLxi8DkKxUvrcpYfCUYYDDZnhoQp1K2JxU
o1a8vnX/AIJreEfh98Nv2d/+Clngn4PzRr8OvBf7an7VWg+CfsN9LfW9hpGjfCH4V2tpYWGqNPcT
XtrpEkLafZag11PLdQWkNy1xM8hlb5vwzweXZZw54m4HJpJZbguNuLMPgfZ1HUjTo0cnymMKdOrz
SlONFxdOnUc5OcYKTk27n6x9LzPuKeLvFX6IXEfHlOb4r4h+jz4J5nxF9Zw0MNVxWPx/HfGtavic
Vg1TpU8PWx0aixWIwqo0oUatedFUqcYqC8G/4IZ3t7rX/BLH4/22sXd1q1vb/Ev486dBBqdxLfww
afcfCPwFfXFjDFdPKkdnPe6jqF5NaoqwS3V9eXDo0tzO7+D4E1J1/CriCNac60Y5nn9OMasnUjGn
LKMBUlTiptpQlOpUnKK91zqTk1eUm/0z9pFh8Pl/00/C+rl9CjgatXhDwzxdSrg6UMNUqYqlx1xN
hqWJnOjGEp4ilh8JhaFOtJupCjhsPSjJQo04x4j9lb9iD9nH4R/8Eyvhp+0l4l/Zt8eft5fEjxF4
R8P/ABHt/hZoureI/FUVrc+PdbtYptB8B/Dizvr7w9pv/CH2Oo+d4r1Gx8La94svNX0rWb+Vktkt
rDRuDhTgbhvJ/DDLOJcVw1mHH2ZYnB4fMo5VQq4nFqEswrwTw+Ay2E6mHpfU6dTmxdWnhcRi51qV
ao2oqNOj9H41/SN8WuO/ph8YeEeUeLnDP0ZuEsqz3NOEqvGmY4HKcknWo8MZdWnTzPibi2vhsNmu
L/t7E4T2eSYTE51lmSYfAY3L8NBSrSrYnMPoL/gsBfjXP+CbX7P2tTeCNS+HUuofFn9l3Vl+Huvx
3H9ufD6a/wBGvJX8HaoL63tb2LV/C6XMvh/UTc2trdi4tLmO4ghdpIh9F4xVPb+GnD1eWBq5a6mb
8LVll2IUvb5dKpRm3g6vtIwmq2FU3h6nNGE+aElKKbaPy76BmGeW/S58Usvp8R4TiyGF4G8ZsC+K
sslS/s3imnhcww8I5/gvq1Wth54DOZUYZphFRrVqHsq9GVKpUioTc3/BcH9mj9pT9pTTv2RtH/Zm
8H+JPEHi/wANfFXxTfXXiLQb5dFtvAbahpnh6HSvEus+I3ubRfD9ja3NrNP/AGkJhLEbV/s6S3Hl
RSPxz4Y4m4mpcIUeGMHicRjMNm2KqSxOHqewjgHUpYeNLFVsS5Q+rwhKMpe1vdcr5U5WTy/ZyeMH
hF4Q4rx1x/jDn2U5XkWb8FZLhqOVZnhnmFbiZYXGZrUxuUZflMaNd5pia1GtTp/VHDkn7aPtZQpc
9SH0D+3p8Wvg54M/aF/4JjeAvilrGgy+O7/9qSHxTZyv9lgubGyh+FHxC8AWOrzQSmUaNo2sfFrx
t8PTaSXU8KXFxotx9ie5k0W8lsvoePs3ybBcReF+X5rWw7x9TipYqDfLGVOEcozHL6daUXf2NGtm
+Oy7kc5RUpUZcjk6E3D8u+jLwNx9xB4V/TG4m4LwGZ0+GsN4L1MlxFOPtqlHE4ipxvwrxRicDTqw
5HmGYYDgbh3ipV4UKVSVOlmFL6zGjDMMPDEdj+13GngH48/C345aF/wT48aftUfETw34eGl+E/jD
8PfFnhqy8R/Dx7e818v4Zn0bW9X0+7srGa31/UrxNThtbrSb5dZ1CyvZopo/Jl7eL0svz/Ks9oeH
mN4rzHDYf2WEznLsXhYYnLnGeIvhZUa9anOEJRxFWaqxhOjUVapCbUlyvwPAmcuJ/DPjTw3zL6U3
D3grwpm+avGZ5wDxVkeb4jKeKo1cPlfLnFPMMuwOKoYjE06uWYTDywdSvRx2Gll+FxGHpzpy9pD+
Qr/gsn8etY+P37aGp6t4n+Dmu/Avxd4C+HPgv4deK/AviXW/DPiLXI9Ws5Na8WwalqGq+Ebm90Wa
S50XxjpFtHFFfXc9rBZRW1y8U0TWtv8Ax740Z/W4g41q1sVk1fIcZl+W4LLcXgMVXwuJrqtB1sZG
rUq4OdShJyoY2jFJVJyhGCjNqScI/wC730APDLAeF/0fMHgcm4/y3xJyLifiziHivJOJcny7OMqy
2eBxEMvyKphMLgs9o4fMKcKOYZBjqs5zw1CnWqYidajGdOarVf3T/Z//AOVbLXv+yJftL/8Aq/vi
tX7vw9/yjRiP+xHxP/60GbH+bHil/wArdMs/7OL4P/8ArsOCT0/4a+CvHvxi/wCDfjQ/BPww0jVv
G3xA8S/Aq+s9A0bQpluda1fU9N+LF7dXtnYs1xG0+oQQ6deqbZZvtUk9u9pFG90Uhb1cswWYZz9H
qhgcro1sdmGKyCpDD0aEuavWq0s2nKcKb5k5VIxpzXKnzuUXBJytE+N4v4i4Y4B/akZlxFxljsDw
7wtlHiVhq+aZhmVN0suwODxnA+Ho4eviYqlJU8LUqYvDv2zp+xjTqxrzlGipVF/O98PP+CPf7Xfx
P/Zc8cftX2a+CNI0LwknxEvn8A+J9c1ex+JXiC1+F2r6zo3ju6sbMaFc6DBcafq3h7xDaWtprPiL
T73VbjR7vyYwtxpj6h/OeXeDnGGacK47i2H1GjQwizGo8vxVetDM8RHKq1ahj504ewlh4yp1sPiY
QhWxNOdWVGfKvepOp/qxxV9PXwJ4N8Z+HPBDEPiLHZlnkuFMNHifJstwGJ4QyutxngcBmHDNDE4j
+0qWZ1KWKwOa5VXrV8vynFYfBUsfQ9pNuljI4X5r/wCCeX/J+P7G/wD2cz8FP/VhaBXzPh1/yX3B
n/ZT5J/6scOfrv0qv+UZ/H7/ALM94h/+stmZ/ZV/wU+/4Ksah/wTq8W/CfwxZ/A+z+La/E3w74k1
57y6+Ic/gltGbw/qenaeLZYIvBfioX4uxqHmmUy2ZgMWzy5fM3J/Z/il4sVPDjF5RhYZFDN1mmGx
OIc5ZjLA+w+r1aVPlUVgsX7Tn9pe94ctrWd7r/AD6G30JsL9K/I+OM4xHiPiOBpcHZtlGWRw9HhW
nxEswWaYPF4p1pVJ8Q5I8K6DwvJyKGI9p7Tm54ctpfH/APwSO/a7uf24f2//ANrv9oK68BQfDabX
vgR8J9CPhW38SSeLI7VfDGsLpK3I1qTQ/DrTG9EPnmL+y4hb7vKEk2PMPx/hBxhLjrxB4w4hnl8c
seIyHKMP9UjiXi1BYWt7Lm9u6GGcue3Nb2S5b2vLc/evp0+BNH6OP0XvAnwtocTVOLqeWeJfHGZL
O6uURyOdZ5zgHjnR/s+GZZrGmsP7T2Sn9dn7W3PyU78q+lP+CUTKf2nf+CuqBhuX9trxExXI3BW8
QfEUKxHUBijhSRglWA+6cfTeE3/JUeL66/68YnT1xGY2/J/cfkP03E/+IN/QTdnZ/R2ypJ20bWV8
JtpPZtKSbXS6vujiP+CKfw58ceBv2bv22vhv4v8ADWpeHvHOn/tU/FbTL7w1qaR2+o2t9qnwl+G1
zp9vMpkMSG7t76znhcy+W0VxHJvCNmuDwRy3HYDhrjjLMZhquGx9PivNqU8LVSjUjUq5RlkqcXrZ
c8akJRd7NSTvY+j/AGh3FnDfEni39HXi7Ic3wma8N4rwV4JxmGzfBynVwlbDYPjji6jiqtNqCnJU
KuGxFOpHk51OlOHLzKx/FXqHhHxL4B+I1z4I8Z6Lf+G/F3hHxgfDnibw/qkJt9T0TXdH1cWGqaVq
FuSTBeWF5BNbXMJOY5o3Q8g1/EtTB4rL8ylgcbQqYbGYPGfVsVh6seWrQr0a3s6tKpH7M6c4uMl0
aaP+hrC57k/E/CVLiPh/McLm+RZ7kH9rZPmmCqKrg8xy3H4B4nBY3C1VZVMPicPUp1qU1pOE4yWj
P7Nv+DgG5az8DfsV3K3BtfJ/ar0VnuBKYPKiOh3KzM0oZdkZhLrKSwXyy4f5Sa/tP6Qc+TA8ET5u
Tl4soNyvy2XsJ813dWVr31ta99D/AJ+P2XdFYjiX6Q1F0lX9p4J5io0nBVOeazKg6aUGpc0vaKLg
km+dRcdUjkv+DjD4P/F34i/Bz4F+MPhz4P8AEfiXwx8MNW+KOp/EzUdAjeaDw1oWsaP4RWy1DW4o
pVl/syd9KvoGl8qWFZljhkw9xCsnH9I/Js4zLJshxmW4PE4rC5XWzWrmdTDpyjhaFajg1CpXSafs
pOjUi3ZpNKL1lFP3P2TvHvAnCnH3iVkHFufZRk+c8ZYHgzB8H4TNJRp1M4zPAY/PXiMLl05wcPrl
KOOw1RQ54VHTcqkLxpVHH+f39sL/AIJLftQfsT/Bfwb8cfite/DnVfC/ibV9J8O6zpng3XtY1LX/
AAN4g1zTbzU9O07xLbap4d0axmjlSwu7CW/0DUNZs4NVgNrJIIJ7C8vP574y8IuKeCMkwWe5tPLa
uFxVajhq1LBYitVxGAxFelOrTp4qNXDUack1TnTdTD1K0I1Y8jfLKnOf+ovgJ9OTwa+kT4hcQeG/
BGH4swWc5PgMdm2X4zP8swGEyviTK8uxeHweLxeUVsHmuYYmnKEsVQxMMLmeFy+vVwVRVoRdSlia
GH+5f+Da7/k8n4z/APZs2v8A/q0/hTX3f0Z/+Szzr/smMR/6tcpP5u/a7/8AJgfD7/s8OWf+sXxs
aH/BRf8A4JM/8FAvjv8AtsftCfFz4U/AH/hKvh5448ZWuq+FvEH/AAtT4KaH/alhH4b0Owef+yfE
nxH0fXLLbd2dzF5Wo6baTHy/MEZieN208SPCPxCz/jfiLOMp4f8AreXY7Gwq4XEf2tklD2tNYahT
cvY4nMqNeHvwkrVKUJaXtZpvl+if9OL6Lnhp9Hbws4F428T/AOxeKuG8gr4LOsq/1K8RMy+pYqeb
5lio0vr2UcJY/LsRehiKM+fCYyvTXPyuanGUY/qx+zz8NviL4l/4IgeOf2d/gpajTv2gPBPhX49f
BLxb4T8P+IdBbVLL4s+Gvit4tg+I/g0eIdG1O50C41LxNps97HpmoWetTabq2meJNJu7fVPsV9Dd
D9Y4dy3McT4G4/hzJIez4hwOEz/I8ZhMPiMO6sM3w2bYuOZYL6xRqyw8quKpymqVSFeVKtSxNGca
vJUUj+JvFXi7hPKP2jXDfit4iV3i/C7iLO/DLxFyLPM0yvM1gsRwPm/BOR1OEs/eVZhg6OaUsJk+
Lp4eeMwlfL6eLwOMyjHUKuC+sYapRdb9hf4d/EX9lP8A4I5fHbRf2x9K1Hwfp1n4Z/aH1S38E+MH
iOp6F8OvEXhQ6NY+E7nTZpZktrvxT4pbxBcaRoTfvb2TxVYRrb+dqKxGeA8uzLhPwZz6hxnRqYOl
DC8RVY4HGNe1oZbicJ7GGEnSk2ozxeK+sSo0HrN4umlG9RI2+klxXwn42fT78NMx8Acdhc/xeIzn
wqwdXiLIYzWDzLizKc7WYYnPKOLpwputQyXJVldLH5kvcw8clxU3V9nhXNc5+w9YaV/wUt/4JA+B
PgV4ols9S8QfC7x38N/hR4mkupYjJp+i/CL4m+B/E2lXMcVy0jbZvgdLaaCTIJV1GYarbWql3+zw
83AtOj4m+DuAyHFOFXEZVj8synFOTV6dDJ80wOKpSSk2/eyJwoO91UftYw192Pq/SOxWN+iD9PHi
bxKyaGIwmVcacNcXcbZPGjCahisx474P4kyfG0ZTpKKvT8R4V8zXK4SwkPqVas+WPtanXfFX9p3w
b4g/4Lsfs0fA7VNUsY9J+Cnwc+IPh7RhdzQwWmmfG34xeB7vXrm3t5GMcX27U/h3Z+E/D+nx75JZ
dQ1eXSreJbm5KSdmbcUYLEePPDORVatNUckyXMcNR55KMKWeZzgZ4iUYt2XtKuWwwmHpq7bqVnRi
uaVn4fBPg5n+V/s1fF/xHweCxEsd4icfcLZrmDoU6lWvjPDrgHiShllGrVilOf1bB8V188zTFS5Y
U4YXAwx1WbpUeaH1J+0Lq2ofBr9qW2+NHgf/AIJufEf4/fEe88LWejad+0L8NvGfg+C7bTJ9NOk3
3hLVdI1jX7W90e306FvLKalpa6NeLJDqtldS3sV0bL6riKtUyXiqOdYHw1zLiDMp4SFGnxFlmNwU
ZulKn7Gpg6tKtiIVKMaa0tVpexneNWE3NT5PxfwrwOF8QPBet4fcSfS44S8L+EqGdYjMMX4V8XcP
59VoLGU8Wsdhs9wWOwGWVsPj6uLqLnUsHjHmGHcamCxFCGHnRWI/jM/4Ki/GK5+Ov7dPx58e6n8N
NQ+EfiCLW9F8EeKvAusa5ofiPV9I8VfDTwvovw919NS1nw1PdeH764/tHw1NEk+j3l7YvaxW7RXl
yWaVv4t8U85ln3Hmf4+rllTJ8Qq9DA4vAVq9DE1qOLyzC0MuxCq1sLKeHqS9phpJSozqU3FRtOWr
f/QL9DLgGj4a/Rs8M+GcHxhheO8rnl2Y8RZJxLgMuzHKcDj8l4vznMeKcrlhMvzinRzTDUvqmb05
yp4/D4fExrTqqeHopKC/ot/aqZk/4NzPh2ysVZfgn+ycyspIZWHxF+GJDKRgggjII5B5Ff0fxZp9
G/LWtGsj4Ss/+6llZ/lB4KJS/aycWRkk4vxE8cE01dNPhTjFNNPRprRp7nqn7UPwp+Lnx0/4IY/s
4eC/gt4W8SeP/H958BP2M9Vi0Lws32jX72z0zwd4Dm1ie3i+0Qz3j2qFp7uKFpbgRpNM0ZSGVk9X
inKc4z7wJ4bwWSYTE5hmE+H+C6qoYV82InClgsBKtKK5lKbirymotysm7WTa+J8GeNuBfDb9pJ4t
cQ+IWdZRwvwvQ8TvpA4KpmWdL2WV4evjM/4mp4ClVn7KdKhGtLlp0J1FClzypwjNSnBS/nCuf+CS
f7T1r+xLH+3PLqPw4Pw9k8J23xC/4QhNe1g/ENPh9d3UdvD4na1Ph5fDHNpLHrx0pfEzX66A4mKf
2sG0Vf5sl4RcUw4HXHbqZb/Zzwkcx+orEVv7RWXTmoxxTj9X+q/A1iPZLFOp9XfNb216B/rVR+nN
4N1/pFS+jbDCcWriqOeVuFf9Y5ZZgFwpLimhRnVqZMqyzV5zpXhPLFjXk6wrzOPs+b6i1mD/AC8r
8sP7MCgAoAKACgD3f9nj9pr45/so+PJPiX+z/wDEHUvh34yuNIuPD99qVlY6Lq9rqehXV5YahcaR
q2i+ItN1jRNUsJb7TNPu/Iv9OuBHcWkE8JjljVx7/DnFGfcJY95nw9mNXLsbKjLD1KsKdCtCrQnO
nUlRrUMTSrUKtN1KVOfLUpytKEZKzVz808VfB3w28buGY8IeKPC2E4ryClj6WaYbCYjE5hgK2DzK
jh8ThaWOwOYZTjMBmOCxMMNjMVQ9phsXSc6VepTqc8JOJ9n/ABO/4LN/8FG/ir4X1Dwdrf7Ql94f
0TVrea01M+AfCHgjwJrV1bTxvDLCnijw14esPE+no8Ujo/8AZOsWBkVsSl8Lj7XNPGnxIzbC1MFX
4inh6FaLhV/s/B4HAV5xkmmlisLh6eKppptP2Nanfrc/nzg79n39EzgnOcLn+XeFeGzTMcDVhXwf
+s+e8R8S5fQq05xnCpLJs4zXFZNipRnCMo/XsBiuRr3OW7v4L8Bf+Cg37Xv7Mfwy8VfBz4H/ABdP
gn4ceN9d1zxL4o8PN4D+GXidtU1vxJ4f0fwtrd82s+MPBniDX7Q32haBpNiYLDVbW1gNp9qtYIL2
4urmbwMg8Q+MOF8rxeTZFnH1HLcdXr4rFYb6hleK9rXxOHo4WvU9tjMFiMRDnoYejT5adWEY8nPC
MZylKX6b4m/Ra8B/GPjHJOPvEfgRcRcWcO5bluUZNmq4m4xyZYLLsozTH51l2GWX5BxBleV11hsy
zTHYlVcTgq1ar7f2FapUw9KjSpyfAf8A4KF/tgfszfC3xD8Fvgj8Xv8AhCfhn4r1bXdc1/w1/wAI
B8L/ABJ9v1TxLoum+Htbuv7Z8XeCte1+1+26PpGnWnkWWq29vbfZ/tFpDBdSzzyvIPETjHhjKsRk
mR5x9RyzF1q9fEYb+z8rxPtKuJo0sPXn7bGYHEYiHPRo04csKsYx5eaCjJyk58TPoreAvjDxplXi
F4jcCf6xcYZJgcty3LM3/wBaOMso+rYLKMwxeaZdR/s/IuIssyut9Xx+Pxdf2mIwVWrV9r7KvUq0
YU6cOo/Zs/4Kc/ts/sm+D0+HnwX+NF5pPw/t57y6sPBniLw34T8aaFo9zqF097eS6FH4q0TVb3QY
7m9mub25stHvbLTrm9u7u+ubOa8nec9XDPihxxwjg1l2SZ3Ojl8ZTnTwWJw2ExtCjKpJzm6CxdCr
OgpTcpyhRnCnKc5zlBzk5HjeLv0Ofo7eOOfS4r8QvD3D47iirTw9HFcQZVm+ecPZlj6OFoxw+Hhm
UskzHA4fM50cPTo4eliMfh8Ri6OHoUMPSxEKFKNNRfFr/goX+19+1fr3gjRP2hPjnrvizwdpvjXw
vrA8Lw2HhjwX4Niu9O1OP7Pqd/oPg7RvDuj391YRT3LQahq9veXNqskjpcJ1CzfxF4x4txGBocRZ
9XxeCpY7C1vqsaeFwWCU6dVctWph8FRw1GpKmpScalaM5Qu2pIvgb6K3gP4IZZxHmPhZ4bZbkef4
vh3OsA86qYrOOIc/nQxeDn7XB4bM8/zDNcfhqOKnSoqphcDVoUazhCMqUtj+lr/gsJ/wVS1b4J+C
/hLbfsO/tP8Awe1bxfr3iLxXZfET/hAtY+E/xf1fRNHtdK0qbRJrywuF8XWvh8zXlxeC3nurGFrs
qypvEXy/014x+K9XI8DlEeBeKcmrYzEYnFwzH+z62U5xWoUYUaToSnTksZDD805T5ZTppztpex/k
F9Ar6FOB8ROIOOa30j/Bvj3A5DlmVZJiOFP9Z8BxvwHgcxx9fHY6GY08PiaTyKtmip0KWH9rSoYm
caCacuVz1/j0+JvxZ+Jvxn8Z6l8RPix478U/ELxxqzo194n8V6ze6xqrxwu721pBcXcr/YtOst7J
p+mWK2+n6dDiCxtreFVjH8b5pm+aZ1jauY5tj8XmOOrNc+Kxdadaq0m3GEZTb5KcLtU6UOWnTXu0
4xikj/e3g7gfg/w+4ewnCnBHDOS8K8OYGMlhsnyTL8PgMFGVSMY1q9WlQhH6xisRyqWKxmJdXFYu
pepia1Wo3J/oB8Pf+CzP/BSH4a+EbPwVoX7R+qarpOmWsVnpd7428HfD7x94jsYItoRW8U+MfCus
+INVIRREra9qOrGOP5YjHgEfoWXeNPiVlmDhgqHEtWrRpRUKU8dgsuzDEwirWvisZhK+Iq6KyeIq
VrLRWP5d4q/Z+/RI4vz3EcQ5l4S4LBY7GVp18bh+Hc/4p4YynE1J3cnHJcgzrL8rwScpObWWYTAq
c9Z813f4A+LHxX+IXxy+Inin4sfFXxLdeMPiD41v49T8T+JLy106xn1O8hs7bT4ZDZaRZ6fplpHB
Y2draQW1hZWttBBBFHFCiqBX59m2bZjnuY4vNs2xUsZmOOqKrisTONOEqs1CNOL5KMKdKCjThCEY
04RjGMUlFJH9Q8EcEcLeG/CmS8EcE5RRyHhbh7DSweT5Rh62LxFLB4epXrYqpFYjHYjFYyvOpicR
Wr1a2JxFatUq1ZznUk3c+iPD/wDwUE/a78Lfs2XH7IWg/Fv7D+zvdaJ4m8OT/D3/AIQL4Y3W/RvG
Ov6r4o8R2f8Awll54LuPHC/2jrut6pffaE8TLdWn2r7NYz2tnDb28X0WH8Q+MMJwzLg/D5v7PhyV
DFYaWXf2flcr0cZiK2KxMPrc8FLHL2levVqcyxXPDm5acowjGK/Kc0+i34E514u0vHfM+BfrPitR
zHJ82p8Vf6z8Y0eXMMgyvA5NlOI/sPD8Q0uG39Uy3LsFhvZSyd0K/sfbYmnWxFSrVn2v7Nv/AAVC
/ba/ZN+HjfCj4KfGA6J8P47/AFDVNK8N674R8G+MrTw7f6tM91qcnh2bxVoOrXekWt/fSS6lc6Tb
3A0Z9VuL3VBpwv8AUtSuLvu4a8UuOOEcueU5JnPsMvVSpVpYbEYPBY2GGqVm51Xhni8PWnRjUqN1
ZUYy9i6sp1fZ+0qVZT+e8XPoafR18cOKlxv4h8BLMeKZ4bC4LG5vlme8QZBXzXDYGnGjg4ZrTyXM
8DQx1bDYaEMHRx1Wl/aEcFSw+D+tvDYTCUqGXoX/AAUs/bV8N/BPx3+z3o/xs1Oz+GPxH1DxtqPi
fS08P+Ezqrt8SdSv9Y8fWWj+IzoX9u+HtM8Varquq6hqWnaFf6fapc6rqj2MdmNTv1uMqHibxvhs
jx/DtHPKsMrzKpjqmKpLD4T2z/tOrUrZhCjifYe3w1LF1atWpVp0KlOClVqumoe1qc3ZmX0Qfo8Z
t4icNeKeP8O8HX4y4SwvDuEyfGSzTPFgYrhHB4bL+GMRj8pWZf2bmuMyTBYLA4XB4vMsNiq0qOBw
UcTKu8HhnS+Rfh54/wDF3wp8eeDfib4B1b+wfHHw/wDE2ieMfCOt/YNM1T+yPEfh3UbfVdG1H+zd
astR0i/+x39rBP8AY9TsL2wuPL8q6tZ4WeNvj8uzDGZTj8FmmX1vq+Oy/FUMbg6/s6VX2OJw1SNW
jU9lXhUo1OSpCMuSrTnTla04yi2n+7cV8L5FxvwzxBwdxPgf7T4c4oyfMcgz3LvrOMwX17Kc2wtX
BZhhPreX4jCY/DfWMLWqUvb4PFYfE0ubno1qdRRmvbf2mv2y/wBpP9sbVvCmuftH/Ef/AIWLqngj
TtS0rwvdf8If4C8I/wBmWGr3Ntd6hB5PgTwv4Yt737RcWlvJ5uoQ3c0Xl7IJIkd1b3OKONOJuM6u
Er8S5l/aVXA06tLCy+p5fg/ZU60ozqR5cBhcLGfNKEXeopyVrRaTaf514O/R+8IvALA53l3hLwl/
qnguI8VhMbnNH+3uJs9+uYrAUa1DC1facS51nNXD+ypV6sOTCzoU58/NUhOUYtRfszfti/tHfsd6
74n8S/s5fEX/AIV1rfjHSbTQ/El7/wAIj4E8Xf2jpdheG/tLX7N468MeJ7Sz8q7Jl8+wgtbh8+XJ
M8XyUuGOM+JODa+KxPDeZf2bXxtGFDEz+p4DGe0pU5+0hHlx+FxUIWm781OMJPZtrQvxh8A/CXx8
y3J8o8WeE/8AWvLsgx1fMspw/wDbvEuRfVMbicOsLXre14aznJq9fnoJQ9niatalH4oU4z949B+D
n/BRX9sH4DfFr4qfGz4bfFqXSfHvxw1u98SfFq4ufCvg3UNC8ca7fahq+rNqmo+FbnQG8OWF3a6p
ruq3mnSaFpmk/wBnfbrm0sxDp1xPZy+jk3iPxjkGb5tnmWZu6OYZ7Xnic3lLCYKph8diJ1K1b2tT
CSw7w1OcKterOm6FKj7P2koQtTlKD+W4++ih4C+JnA3BXh3xdwNTx3DPhzl2HyjgalRzriDC5nw5
lmHwuBwKwWEzujmizbE0K+Dy3A4fFxzLGY7639Wo18Q6mLpUsRDs/hz/AMFU/wBuX4VfE34ufFrw
Z8ZBZeLvjlqtnrvxKW68FeBdQ8Pa3rem2cWm6ZrFt4Xu/Dsmg6LqVhpkMWnJd6NY2Mt3ZxpHqTXx
RWHblvivx3lOaZxm+Czrkxme1YV8zU8DgKmHr16UFSpVo4WeGeHoVadKKpqdGnTc4JKr7SyZ8/xZ
9Cn6N3G3B/AvA3EHh+8RkXhvgsRlvCLocRcS4XNcuy7F4ieMxmArZzQzaGZ5hhMTjKk8XKhmGJxM
KFecp4NYZSkn8U+PviP4y+J3xF8W/Fjxtq41jx7458W6x458T64un6Zpy6j4o17VJ9a1TUl0vS7O
y0iyFzqVxNcCysLC2sIQ3kwWscCrGPiMwzLG5pmWMzfHVvbZhj8ZWx+Kr+zpU/aYrEVZV6tX2VKE
KMOarJy5KdONON7Rio6H9D8McJcP8HcJ5HwPw7gXgOGOG8iwHDeTZa8VjMW8Jk2WYKnl+CwjxuNx
GIx+IdLCUoUniMTiq2JqW9pUrTqNzf0n+0v/AMFBP2u/2wvDfh3wj+0Z8W/+FieHvCety+I/D+n/
APCBfDHwj9g1mewm0yW8+1+BfBfhi+ut1jcTQfZ725ubVd/mLAJlWRfpeJ/EPjDjLDYbB8SZv/aO
Hwld4nD0/wCz8rwfs60qcqTnz4DBYWpO9OUo8s5ShrflvZn5F4P/AEW/AnwEzfNs98J+Bf8AVTNc
8y6GU5piv9Z+Mc9+tZfTxNPGQw/sOJeIc4w1C2JpU6ntcPRo13y8jqOm5Rfp3jL/AIKxft6fEH4H
ah+zz4z+OM/iL4dax4ah8Ha3/afg3wLc+LNb8NQRRwLpureNZPDbeJryeSKGBLvXJNS/4SO/MRlv
tYuZri7kuPUxvi3x/mORVOHcbnssTltbDLBV/a4LASxdfCxSiqVXGvDfWpyajFTrur9ZqWvUrSlK
bl8dkH0H/oy8LeJGF8VeH/DinlXFmAzepn+XfU8/4lo5Jl2cVJzqPF4Lh6ObrJ8PThOpUlQy6OE/
snDKahhsBSp0qEKXnH7RH/BQr9rz9qv4d+DPhV8dfi7f+NPA/ga6s9R0rSDoXhnRX1DWNO02bSLD
W/E1/oOj6bf+J9XtNOubq3hvdbubx1e7vbwhr+9u7qbzeI/ETjDizLsFlOfZxUxuBwEoVKVH2GFo
OpWp0pUadfFVMPRpVMVWhTlOMZ15Td5zn/EnOcvrfCn6K/gR4J8V8QcbeGvAmG4e4k4ko4jCY3Hf
2lnGYxwuAxeLp4/E5dk+FzPH4vC5PgK+Lo0atTD5dRw8XGhh8OmsLh6FCn5t+zX+1h8f/wBkHxlr
XxA/Z28ff8K88XeIfDNx4O1jV/8AhFvBfi37Z4cutV0nW59O+weOfDnibTLfzNT0PS7n7Za2UF+v
2XyUult57iKbzeGeLeIODsbXzDhzMP7OxmIwssFWrfVMFi+fDTq0q8qfs8fhsVSjerQpS54QjUXL
yqSjKSf13i94IeF/jxw/l/C/ivwx/rVkWVZxSz/AYH+2uIcj9hm1HBY7LqeL+s8N5tk+Mq8uDzHG
0fYVsRUwz9t7SVF1adKcPtb/AIfg/wDBUT/o57/zC37PP/zpq+2/4jn4p/8ARUf+YTh3/wCdB/PH
/FOP6GX/AEZv/wA6H4q//RwfL/wI/b1/a1/Zr8eeMfiL8HfjL4g8Ma/8RNav/EXj+zns9F1zwr4y
1vU7y4v73Vtd8Ga5puoeFp9Ve4vLs22q2+k22qabFd3MGl3llDNIjfLZDx/xdwzj8bmWTZ1iMLiM
yr1MTmEJQoV8Jja9Wcqk61fBV6VTCSquU58tWNGNWkpyjSnCMmj9l8S/oyeBvi9wzkHCfH3h/lec
ZZwpl+GyrhfEU6+YZdnfD+XYPD0sNh8DlvEGXYzC51TwUaWHoe2wVXHVsHjJ0KVTG4fEVKcZLd/a
b/4KL/tj/tfaLaeFvjv8ZtW8SeDbG7hv4fBWjaP4c8GeFJr63Yva3uqaN4Q0jRYNfu7ORmksLnxA
dUm092ZrF7Ysc9HFHiRxnxjQhhM+zqticFCaqRwNGjhsFhJVI6xnVo4OjQjiJwetOWI9rKm2/ZuN
zzfB36J/gD4D5hXzrw08PsDlHEGJoTwtTiHMMfm3EGd08NVSjWw+CzDPsdmNXK6GIilHE0sr+pU8
VFJYmNWytgfsyft5/tZfscab4t0f9nH4t3Hw70rxzfaVqXifT28H/D7xjaahqGiwXtrp95DD498J
+KU0q4S2v54LqXSBYNqMaWiaibsafYfZufhfj7i7gyli6PDebyy6lj6lKriqbweXY2FSpQjOFOcY
5hhMUqUlGpKM3R9m6iUFU5/Z0+X0/GL6Mvgf4+4vI8f4tcDUuK8bw3hsbg8mxSz7inIK+FwuYVcP
WxWHqVOGM8yWWNpSq4alUowx7xKwk5V5YRUHisV7bwXx58WPiL8TPid4j+M3jXxXqWrfE/xZ4ruf
G+t+MIBbaLqUvim5vhqJ1awj0G30yy0SS2u1jk0230S106z0lIbeDS7azt7a3ij8DH5vmWaZpic6
x2Lq1s0xeLljq+Mjy0KrxUp+09tTWHjShQcZ2dKNCFOFFRjGlGEYxS/TeGeB+E+D+Dsp8PuHskwm
B4NyPJKXDmXZBVdbMMJDJaOG+qLA4meZ1cZiMxhWoOUMXVzGvi8RjpVKtXG1a9WtVnP9FPDf/BbH
/gpd4X8LWvhSz/aOuNSt7C0jsbHWvEnw8+F/ifxTDbRQCFPtXiPXfBl/qet3fHnSap4hm1fVbicl
7i+l4A/RsN43+JuFwkMJDiSVWNOCp06+Jy7KsVi1FR5Vz4mvgqlWvPq6uIlWqylrKoz+Uc3/AGdv
0QM5zqtneI8JaWEq4mvPE4nL8o4q4zyfJaladR1JexynLeIMNg8uof8ALuGDyqngcFSppRpYaG5+
ZvjPxj4n+Ifi7xP498a61eeI/GHjTX9X8U+Kdf1Fke+1nxBr1/Pqer6ndmNI4vPvb65nuJFijjhR
pCkUccaqi/mONxuKzHGYrMMdXnicZjcRWxeKxFRp1K2IxFSVWtVnZJc06kpSaSUVeySVkf2Dw/kG
TcK5Fk3DPDuX4fKch4eyvAZLkuV4VSjhsvyvLMNSweBwdBTlOfs8PhqNOlFznOpJR5pzlNuT+pfF
P/BQL9rvxr+zjp/7JPif4uHVP2fNK0Xwr4esPADeBPhnaG30bwTqem6x4Xsv+EssfBlr43kGl6jo
+nTrNN4lkubtbYW99NdW0k0Mn1WL8QuMMdw3T4QxWb+14dpUMJhqeX/UMshy0cDVpVsLD63TwUcc
/ZVKNOXNLEuU+XlqSnFyT/F8l+i54E8O+LWK8c8m4FWD8U8bmGd5riuKFxLxhX9rj+IsHi8BnOI/
sTE8QVuHIPG4XH4uk6dPKIUaDrOrhqdGrCnUh6d8Ev8AgrD+3p+zz8K9O+DPws+OMukeAtCt57Tw
xYav4K8A+K7/AMLWlxLLPLZ6FrHijwxq+pQWKyzO1pYXVzeWWm526Zb2allPqZH4t8f8O5TTyXKs
9dHL8PGUMLTrYHL8XUwkJNycKFbFYWtVjBOTcKc5ThS2pRgtD47xF+hB9GXxV41xXiBxp4cQx/E2
ZVadfOcVgeIeJ8kw2dV6UIU4YjMsBk2cYHB1MS4U4xr4mjRoYjF74yriJWa86b/goZ+1+37Mg/ZA
b4wagfgMNP8A7D/4RY6D4XOqnwwL5NSTwmfGB0U+LR4bjvUymmrrQAsSdDMh8PhNKTzn4i8Yvhf/
AFOec1P7A9n7D6r9XwvtfqvOqiwn1z2H1z6sprSl7f8Ah/uL/V7Ul9Wvoq+A0fGJ+PK4Cwq8TXiv
7S/tpZnnSwSzl4eWElnn9grMVkX9rTw8rSxby9t4lf2koLNHLGy+LK+IP6FCgAoAKACgAoAKACgA
oAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACg
AoAKAPqr9l/9lHWP2pb7xdp+kfGz9nH4Ny+EYdBkaX9oX4qwfDCDxJJr82pwwWvhF5tI1dtcu7Ft
LdtVgjSE2aXunEmQ3ahfrOFuEq3FVTGU6Od8N5K8GsO2+Is2jlccS8RKrGMMG3Rre3nT9k/axSXI
p09+fT8U8ZfG7AeC+GyLFY/w78WuP4Z7PM4Rh4V8E1OMqmURyung6lStnsaePwKy2hiVjYrBVJyq
LESw+LXuewbf3lP/AMEWfin4W+MPgn4N/Ej9qD9knRvFninx34W8D6p4N8EfEy88c/F/w5J4us01
HTdXl+Euo6J4J1m7sI9Mns9YuDPqel40W7g1SKSW3kiMv3z8E81wuc4HJcy4p4QoYvFY/C4GrgsD
mc8fnGGeLgqtKs8oqUMDWnTVKUK0uarS/cTjVTcWr/zNT/aF8FZ1wFxFx/wj4N+OeYZHkvDWdcSY
LP8AiLg+hw3wJm0MixEsJi8BDjnCZjxFl9DFSxlLEYCl7LB4y+YUKmDnGFWE+T8mfiV4Ob4d/Ebx
/wDD9tQGrt4F8a+KvBzaqtqbFdTbwxrt/oh1BbI3F2bMXpsftItTdXJtxL5RuJtnmN+R5ngv7NzL
MMudT2zwGOxeC9tyez9r9Vr1KHtOTmnyc/JzcnPLlvbmla7/ALj4Qz9cV8J8L8UxwrwMeJeHclz9
YJ1liXg1nGW4bMVhXiFSoKu8OsT7F1lRoqq4c/sqfNyLia4T6EKACgAoAKACgD7a/Zm/YF+OX7T3
hTX/AIm6LqXwx+EnwU8Lak2ieIfjt8ffHun/AAx+E+m6+sEN1/YY168t9Q1HUtSEFxBJcJo+i6jb
6eJrddTuLJ7q1Wb7jhjw/wA94pwmIzShVyvKMkwlV0MRn3EGYU8rymliFGMvYfWJxqVKtXllFyVG
hUjT5oqrKDlFS/nbxg+k94b+Ded5ZwfmOE4x458RM6wizHKvDXww4YxXGXG+Lyx1alH+0nlmHq4X
CYTCOrSqQpSx+YYSrinTqvB0sRGjWdPzH9pL9nK9/Zt8UaB4bn+MfwB+OFp4k8PDxFp/jH9nX4lR
fE/wXFGup3+lzaTf6ymlaNNYa9azWBnudLubFJYrS6spt584onl8S8Nz4axWHw0s54fz2GJw/wBZ
p43hzM1mmCS9rUpSo1KypUXTxEHT5pUpU01CcJX96y+x8I/FnD+LmTZpm9LgHxQ8Oa+UZr/ZWKyD
xX4QnwbxDObweGxtPHYXL5Y3MKeJyytTxKpUcbSxMoTr0MRTsvZqUvnOvmz9YCgAoAKACgD6wuf2
RPHXhTxP+zJpfxd8S+CPhH4T/al8PeGPHHg34heJ9etp/Dnh74e+J9QSxg8YeL2snL6LaWqGS6uL
S8kgmjhj33D20Tecv10uD8fhMVwvSzjE4HJ8JxVh8LjsFmOKxEZYbD5diqihHGYzkd6EIK8pQm4y
UVeTiveX4jS8duGs7ybxixvAmUcR8dZ54L5rnPDef8LZNllalm2a8U5NhZYmpkOQrERUcxr1pctG
lXoRqU51JctKNaa9m/n74g+GLHwT498b+DNM8U6D4503wj4v8S+GNP8AG3hWd7rwx4xsdB1m90q0
8U+HLmRVkuNB8QwWkeraPO6q82n3dvIygsRXzuY4Wngcwx2CpYvD4+lg8ZicLTx2Ek5YXG08PWnS
hi8NJ6yw+IjBVqMnq6c4tn6jwtnGJ4i4Z4c4gxmS5nw3i89yLKM4xXDudUo0c5yDE5nl+HxtfJc2
oxbjSzPKqteWBx9KLcaeKoVYptK5yFcZ7oUAfpfo/wDwS3+MT/Dfwz4/8f8Axw/ZG+CGs+OPAll8
S/A3wf8AjR8edN8G/Gfxj4K1eybUNB1jRPBUWi6rHnX7VX/srTtS1bT9UkuY5rC5srXULe4tYv06
j4V5y8twuYZhnvCGRV8dgIZngMmzvP6WCzrGYKtD2mHrUMCqFVf7RFP2VOrWp1XJSpyhCpGUF/IG
P+mfwDHi7OOF+F/Djx18Rsv4c4lxHCHEnHvh74Z4ziDw+yDiHA4hYXM8BmPEM8xwU7ZZWcfruLwm
BxeCjRlTxNHEVsLVpVp+CfGX9li5+EH7M/7In7Rk3jaDXof2rrX43XVv4Ri8PyadJ4GX4NeNdG8H
us2uNrF6viI+Iv7YXUVKaToo0ryGtD/aPmC5T5/OuFJZPwxwfxJLHRxC4thnko4NYd03gf7FxtDB
tSrutNYn6z7ZVFajQ9ly8n7y/Mv07w/8aqPHnjB47eE1Ph2pllTwRreHVGrns80ji4cSPxA4ezDP
oyp5bHAYeWVLKvqDwr5sdmH132qrr6pyujL5Lr5E/cQoAKACgD7Z/ZI/YT+JX7Yfh34xeL/B3xF+
B/ww8J/AtPAUnj7xR8c/Hl98PvDtnH8R7vxNYeG3h1uLw3rumxo994WvLK5bVLnTALu+0q2tDeTX
jJD9xwhwHmfGWGznGYLMsiyvCZCsveYYrPsfPLsNBZlPFU8M411hq9JJ1MLOEnVlS9+pSjDnlO0f
538c/pK8IeAma8A5Dn/CfiPxlnniTLiePDGTeG3DOG4pzXES4SoZPis3jUy6ebZbjJyjhs6oYiks
FRxj9hhsdWr+wp0FKp0fjP8AYSsfhxr/AI08P+Mf2wf2OrqTw18DPFPxk0HVfh78XH+IWheN9X8N
6iNPg+EOi6jpmiWXlfE3XcG70nQp4WlvLOW2ns0vI/7QbTunG8BU8txGNw+N4x4Mk8NkWLzrD1cu
zd5jQx1bDVPZxyehUpUIWzOv8dGhKLc4OMoKa9o6XkcP/SWxHFuV8PZpkHgN4+UYZv4k5LwBmeB4
p4FjwrmXDmBzbCfWqvHeY4TGZjiOfg7Lbqhjsyp1FChiIVqWIlQn9VWL9j+Hn/BLq+8ffGn9hL4P
x/Gq00t/22/gTq3xstfED/D+a7T4bx6T4I8Y+M38N3Glr4ytj4reYeEW05dWj1Dw8EN+t2dNYWxt
p/Zy7wsqZhnfAWTLO4UnxxkNbPIYh5fKay1UcDjMa8NKl9dj9bcvqjpqsqmGt7Tn9l7vLL4Hir6Z
uG4Y8PfpK8ez8PK+Mj9HXxLwPh1WyuPFFOhLi2WN4jyDh+Ob0sa8gqrJI03nqxbwM8LmrksM6Cxa
9sq1P8tdd0ttE1vWNFaYXDaRquoaW1wqGNZ2sLua0MyxlnMYlMW8IXYqG2lmxk/ldel7CvWoOXM6
NWpScrW5vZzcOa13a9r2u7dz+0MtxizHLsBmEabpRx+CwuMVJy53TWKoU66puaUVJwU+VyUY81r2
V7GVWJ2hQAUAFABQAUAFABQBraD/AMh3Rf8AsLad/wClkNbYf+PQ/wCv1P8A9LicOaf8izMf+wHF
/wDqPUP6UPjP/wArLGj/APZWPgL/AOsy/Dqv6Yzr/lJqj/2Nsg/9ZfLj/Irw+/5VCY//ALIfxN/9
fDxYN+JXx38fftZfsU/8FafDvx+1Hw/4l0b9l34+fCA/Ay6s/Avg/Q734W6d4g+PuteFNbt/D7+H
NE0uSaOfw1ox06aW9N1ql5BqesrdajPJqDPGZnn2YcXcEeLuG4gqYfE0OFuIMn/sGUMBg6E8qp4j
iCvhK8cO8NQpOSlhqPs2589WcatZSqSdS6OEfDXhjwP+kP8AQbzbwvwmaZRmHjN4Ycd/8RJo4jiT
Psyw/GmLyvwwy/O8uq5pHNsxxsKc6eb5gsXThh/Y4LD1MHl8qOFpxwqjL7X/AGofHXwg/Zsm8X/A
a7+Dn7XvxC/YsX9mhdM8L/Db4R/svfBvxp+y5eeG5fhxba2/xrg+NieMtM8cXfjHwxqNveeLvFnj
zV30zVdNvrO71qV57JbDxLqP2/FOPybhmWMyCeS8Y5jwT/qyqWFyzJ+FslxvCs8M8tjXedxztY2l
jp43C1IzxmLzCs6VWlUhOu3KHs8TU/njwZ4a488XaeQ+JtDj7wI4V+kK/F+WMzri7jrxm4/4e8Z8
Pm8OLa2XR8O6nh3Lh/GcOUMgzjCVcPkWR8M4GOMwWLw2IoZdCNLEvFZRhPxN/wCCG0VpL+29c/bt
O03VbeL4BfG6d9P1exttT0y78nwuj+RfWF3HLbXdrKAY57eaNo5oXeJwUdgfxDwKUHxzPnp0qsVw
/nknTrU41aU7YVPlnTmnGcXtKMk1JNp6M/0S/aQTrw+jnR+rYvF4GrPxP8OaccVgMTWweMoe0zqU
faYfFUJQrUK0LqdKrTlGdOpGM4tSimfXGk/FGb9uj9g/9mP4k/trax4f8WXHhj/grR8OPgLqfjwe
GfDHgi70b4A+J/h34c8R+NPCT3HgnSfDtrp+hXMmr3U91d29vFNaQ6dpVyWlfSLfP11HNXx3wDwv
mfG9bD4uWF8XMtyCrj/quFwM6PD+Ky7DYnG4NywNHDRp0JOtOU5xipQjTpSu3RifheP4Mp/Rt+kx
4xcI/R4wGaZJSzn6DfFvibg+GXnGc8RUMf4oZNxXm2U8PZ7GlxFjs2rYrMqUMBRpUaFWrOnXqYvG
0VGCx9W30p/wUO+Mvh3wX4J/bL+A3xE+Dv7ZmveBdE0Uad8BbOf9lL4Q+Ef2Zv2dtd8O6xYaf8L/
AIgfCz4u+EvFkPiHT/BV/d3Ok6RPrN1ZHUNY/tcabdaTYatcXOg19N4i51hsFgeNMgzHJuNMRgKF
D2eQQlwnk+D4Y4cr4atTp5XmGVZxg8XHE08DUnKjRlWnD2lb23spUadWUsOfkX0VfD/NuIeIvo/+
JvCnH30fss4kzHMHivE3EU/G3jvPfGDxXy3NsBicVxlwvxpwJnmR1MqxXEWFoUcdj6WX0cR9VwH1
F4ujjsVgaVLMz2vwb4z+Ffhjx58If+Cj3i/QvDWoWH7enwr/AGIf2Xb9L210t9HtviR8RPFXiTQf
2j2urcCOCOw8I+FfhL4XtLq78iFd0kqPHGtubeT3MFjcpwuYZP4k4zD4apT4/wAq4G4WmpxpOjHM
8xxeJw/EnNHSKp4PCZRhITnyx1bTS5XF/nfEHD/Guc8M8d/RLyHM84wuK+jJxr9I3xmwssPWxscf
W4S4UyXKMz8JFRqtzqTxWe51xznVejQdSo+WEJRlN1VVh+J//BUPwjafst/DH9k79g2wWJtU+FOk
/Fj4x/EjU1SJb3XfE3xX+JviKw8GJq7xwQiS70DwF4WsxZON4bS9ftT5kqCKQ/iHing4cK5XwjwD
Ts6uU0c2znMqtkp4jFZtmmJp4JVrRV54fAYWHs2r3pYiOrVmf6I/Qzz6v40cY+OH0mcU6iwXG+O4
H4B4RwcpTeHy3J+COD8qxPEEsApVajjQzTifOsQ8TF8rWNyut7sJc8F7L4u+Hvif9o3/AIJif8E7
dW+CPgXxJ8a/h5+zH8Rfi3o/7VfwR+F0uoXnjqDXPFPj7T/EOn6zf6N4ftL7Wo013wkniJrXxRBp
F4/ha28ZRXe+a2l1KS09rGZdiuJPC7w5rZHgMTneW8L5jm9HizI8qdSePjXxWYU8RTrVKOHhUrpV
8GsS44qNGbwscap3lGVVw/P8i4qybwm+mP8AStwPiLxJlPh3xX4x8KcDY/wT8RuNIYWhw1Vy3JeG
MVlWKy/DZhmlfDZfKWW57LKVWyarjqEc5rZBOhy061PBwr/YXwe/Yq/Y/wDBf7ZX7AOt2HwP8U/D
X/hc37Pfxg+Mdl8EPi5r19411hfjD4Ps7C98E+G77TvG8Xh+1vdZ0Oyk1fX7TR9Wi0XTrzVtIsHv
fLjhxJ9lk3BHBuC408Pq9PI8Vln9tcO5xnVPI83xFTHVv7ZwcKc8DhqlPHLDwnWoU3WxEKNZUKc6
1Gm52S1/BOPfpD+PXEP0f/pQZdivEbJeL/8AiH3inwHwDiPEbgXLMNw7gHwFn2IxWH4izbDYvhye
aVsPl+ZYiGAyyvj8DPMcXh8Dj8THD80qnuZPj349aR8TPij+wv4S+J/w1/bU1n47+Gf+Cj3wGXQv
jr+1V+zF8LvgfDpngjXPGGi2PjH4Hwaj4B8QXtlqdrLd/ZPEum6FLoxmtYrd5ILu0sLC3inxzDP6
OaZrwHg80yzjetn2F8Scg9hn3FnC+VZHGlga+MoU8bkUamX4icKsHPkxNKg6HNFRbjOFOnFS7uGf
DLH8H8GfSTz3g3i/6POX+GmcfRK8THmXhr4KeMfGfiNUxnEeW5DmGJyDxHq4TifK8NicHWhQ9tlG
LzKGYezrTqxhUoV8Tiqs6Vz4nfHDx7+0H4Z/4LhfAL4rP4W8QfCv9m9/F2v/AAP8N2vgTwXoH/Cu
tX8M/FfxZHBqOjajoGhaZqNxqOqzaTZX2u6pq91qWqalfm9uXvY21TVBeXmmeZhxFhfHTh/NnhMR
lXDTxeIyLDRwGCw/9m1sNm2MUalGph8PSqSqVZUYVK9WtOpVq1OeTmva1efn4O8OeGPCzOP2cfif
wTHOsq418W45FlfiPm9biXiHM/8AWvAZxwTkcqmEzDCZpmeMwlLC4Knj8RhstweBo4TBYPDfV6Uc
PJYLBvD/AEX4r8a/CT9l3wx+z58ONM+E37XXxX/Y28S/sweHr/Vvhh8CP2Wfg78UfgF8bYfF3gu/
n8S+NfHfxN1DxhY+N3+JUd2bTxXrPmS2H9i2+naXdWKf2ZqVzf3v0mLxuT8K4Xh7LaWUcYZvwZie
F8PUq5XkHCuTZrw/nkcZgqksTjsfmlTGU8c8zU+TF1run7CNOlOC9lUlUn+UZJw9x14zZz4pcW4z
jjwJ4I8f8o8ZM0w2B4y8S/Gjj7gzxP8ADqpkXEOFp5Rw7w1wfhchxPDkeEJ0FXyTL+SGJ/tCri8Z
RxEvrmEpYbD/AJ1fFP41+OPhT/wS6/4Jz/D/AOC6+B/DjftQaR+2D8E/Ffjfxb4O8L6l4vg+Gnif
4uT+FtQ8MQeKtWtNSk8J6Rr2m+LC3iy80hlv1fSdKubDU7T+zt0/5xmud47KfCzw3y/JPqOGfFNH
jHI8XjsZgsLVxkcsxWbywtTCxxdaFV4SjiKWLvi50X7S9GlKnVh7O8v6v4K8POHONvpnfSx4o8QX
xHm0fBrH+AviJknDmR5/nODyGpxhk3A1POsLnFXJMDXwkM8x2WYvI7ZJh8cnhnHHY2licHX+t2p/
oZ8cdE8RRfs4ft4/sx/HDx78afjvrn7OP7NVp8Q/DniXxz+zB8Ivg98AvCvjDw3Zadr3hXU/gF4l
8J2cXi2/vLCRYtOuYNVe70+902w1aKPULkHU0u/0XPaGJXDXH3C+e5hnefV+G+GYZjhsVj+Fsnyb
h/CYzDQp4jCVeH8ThILF1J03y05RrOdOdKnVSqS/eqf8reHGY5VPxa+jP4xeHHDPh54aZb4teL1b
hTNsn4b8ZOO+PfE/OsgzfEYvLM6wfiflGeYieR4bD4mLni6VXAxoYrD4zFYGcsLRtg5UPN/i38dP
2kfip8Yv+CIK+MdQ1DxF8Ifido37KnxY+IOsRfDzwpZeGNX+Pt5f6tZ6nNL4n0XwpZJouoSaHq11
v8B6TqmmeHUt5hdR+Gle3imh8zN8+4lzbOfA367UqYnJ80o8J5tmNZZdhIYWtxBOpWhVk8VQwkFQ
qOhWnfAUatLDKL5lhk4qUfreBvDbwj4K4B/aMvIMLhcq474OzDxs4I4WwE+Ks7xGc4Hwww+FwOIw
lOGTZjneIlmGFhmWBo8vE2OweMzaVWm6M83cas6dTxjwn8Krb9v7Sf23P2JUTTYfHnwI/wCCmmr/
ABk8D3ubO01i0+C/xM+OXiD4c/GG20koq3Mmi+BbTUtY8dapblXhlvNT0qFRJdx6TbjxcHlMPEGj
xxwOlTWPyDxPrZzgZ+5CtDJM0z3EZbnMaNlzOhgIVa2Pqxs051aMVeaoxP0HPONq30Xsd9HT6RUp
Yupwz4mfQ9wPAHEeHtiK+X1/ELg/w3yvizgKtjlKTpQzHiWvhMBw1gqqcZww+DxtSThQljqr9e+B
vxd+H/xc1/8A4KY/tUfCfTviV/wsX4b+PPhp8Gvg1ffs1fCzwT8WPjN8NP2YfDcbeDdG134UfDv4
garpukWtj44j8O3+r+NNTtBPqulWS6jqFtFGLaSSD2MizjLs4xHidxXlNPM/7Sy3MMsyXJZ8M5Vg
c3zrLOF8MvqVGvlOXZhVpUYQxyw1StjasOarSgqlSKXK3H4TxI4E4o4Fyz6H3grxxi+EP9U+LeGe
L/EDj/DeL3GvEXBHh/xh4x5tNZ/mGW8b8V8L4LGY+vieHJ5rhcDw9g67p4LG4h4XC1pz9tCNT8V/
+Cp/xS8B/GD43fDzxj4Y+Fvxr+GvjGT4KeENO+K+pfHX4T+Gvgz4x+LPjPT9Q12Ffi7L4O8Ka5rm
kF/FmkfYLe91azexs7280mSKzsUitDLN+JeK2a4DOc8y7G4XKs7yzGPJMHTzapn2U4XJcbm+Np1M
RFZw8FhK9ejfF0fZxqVoOnCc6LUKaULv/Qz6FfBfE3AfhzxXkGccaeHnF+QQ8RM+xfBGE8NeOM48
QMg4H4fxWFy2o+BIZ/neW5bjlHI8f9Zq4bA4iOJxGHw+OjUxGJlOvyU/05+CPw01X/goVpXwx/Zx
/br/AGLfjD8NPiJ4N+CCaJ8LP2/fB3h7xF4X8PaX8PvBvhHUPEXgP/hay6vpC/DrxB4SvLGH7Jae
JYNZvxrGsajb6Zo1t4ZvNZ1LXoP1DI8sreIlLK+G+POCc5yzMsFkaoZV4g4LD4nC4ell+CwdTE4D
+1vbUVluIwc6ceSGKjXqe2rVI0qEcLOtVxEf458RuL8F9FfG8Y+LP0a/pC8BcYcK8QeI0sx41+i/
n+a5VnWa43iniDPcLlXE3+pLwOOfFmV57h8TU9vXyirl+FeAwGEq4zMK2cUMvwmWVLHgz9qf41fs
v/sof8ETPD/wf8UaFp2jfFX4h/tF+DviKt14R8K+I08YeErb9prwbpM2gx6tr2jXuu6JoWsWfiTU
n1GPwzqeh3GpOulXN1cPcaRpklpeC4qzvhbhPwPw2T4qhTo5tmPEeCzJSweExKxmDjxPgqMsOq2I
ozr0KFaGJquosLVoSqv2UpycqNJwx4g8FvDzxl8bv2iea8eZNmWLzDgnhXwoz/hOVHPc7ymWQ57W
8HuIMdTzOeBy3MMPluY5lgK+UYSOEnnGDzKlhIvG0aFKNLH4yNf0jwd4I8TfsxfHr/gpr8e/hJ8R
fid8KfhZ4f8A2oPDHw9v/hV+yr8Afhl8aPjBq2pXGn6p4yt5rPTfiPavoPw7+F1jc+OdQEkOkC0t
tYmj/stJtJg8M6TZaj6WDwGK4Xz/AMT+IMozHNMpyrD8U4XLqmU8KcP5Xnec1qsqVXGxlClmUHh8
uyqEsfUvGjyRrSXslKjHC0YVPk8/4jyfxj8MvoeeGPHPCnB3G3GmaeDOccVYXjbxr8UOMPD3gLA4
OlisHw/Vp4jF8JV45nxXxniaXDeFcKmO9vWwFOf12VPHVc4x2Iwnp+u6BD8Lf2+/+CjPxF+AXguP
wv8AEtf+Caf/AAu7wFb6X4G0W51/SfjLrGl+HbiLxBpHg06brOm2vjDVNUiin1XTbOxvV1LW7zU1
uUv5NRujcepiMPHKvEDxIzLh/BLC5n/xDL+3MBGlgKEsRSzqtSw8liKOC9nWpQxlWrFSq0oU5qrX
nV5lUdSfN8blmaVONPow/RO4U8T+IJZzwe/pe/8AEOuJquM4kzCjlmO8P8BjM2pTyvH8QLF5fjK+
Q4LBTnTwWLxGJw7wmXYfBulLCxwlD2XnGl+G9N+Pc3/BJzxP/wAFIPDPhfSv2m/iD+0F8QdL1q28
Y+EvD3gvxz8T/g1pWgeI774ZD4s+DLTR9Iiez1T4m6d4E8J6LZa34fhj1fSdbkkQpN4h1ia682lh
qXEEvCTFeJOFwtLijMeIcxpV443CYfBY/NMlpYfE1Mr/ALXwUKNGLhVzSngMJQhXw8VWpV21aWJr
Sl9ZjM3xfhjT+nDk30Ss4znG+DvC3hbwtjMvrZDnmacQ8N8G+IGNzPKcNxh/qPxBXx+PnGvguDsX
xLnmYYjLs0qSwGOy+EJqVPKsvp0fFP8AgpX8bbHXv2dv2ifhN8Zvhl+2v4s+IGifFnwpefBb4j/G
r9l34M/DD4S/A/ULHxp9i8SeFfBfxI+HvivUb3UPBXjbwLDq+n+GbALrK6rc6VpWoqxEuranF4ni
bnlPEcOcR5RnWV8b4vMKGb4SeSZlnfC2S5XlGRVKeN9nicJgcyy7F1KlTA47AKtTwtP9/wC1lSpV
L61qq/RPoheHWJyzxW8KOOPD/jD6O+R8L5jwPndDxC4S8PPGbxB4y448R8LieHvrGUZ3xDwlxVkm
Ew+F4i4d4lqYDFZxib5e8DRxuNwjS5MDg5/NX/BMHxB8K/C37Cv/AAVa1/42/D7Vvip8L9P0/wDY
yfxV4A0PxZd+B9U8RRTfE/4kWulw2viqxhuLvSPsWuT6Zqk8kELtc29lNZEBblmHzHhbiMpwnAfi
ziM8y6tm2V06fBTxeX0MXPA1cSpZpmcaUYYunGU6PJXlSqycYtyjBw2lc/Xvpk5XxrnX0lPoSZZ4
d8U4HgrjLFYv6QUcl4ozLI6HEmCyqdPg3hGtjKlbJcTUpUMd9Yy6njMHTjUqRVGriYYhXdFJ97+x
f4r/AGePiC//AAU01j9nX4I6z8EPANl/wSr+PlvP4P8AFHxB1H4pahceLrWDULi88Tw+INZs7W4s
0udPu9KsINNhi8u0bTJbqOXzL6UL38FYvhzMX4n1uHMjrZHl8PCjiCMsHisxqZrUljIRqSnio4iv
CEoKVOdKnGnFWg6Tmneoz5n6QmSeKvC0foeYDxX8Rsv8RuJ8R9Nbwxq08+ybhbCcF4WlkVerhaWH
yapleX161LESo4qhjcVVxdSfPXWMhRlDkw0G/t79nXStUX9r7/ggn4ibTb8eH5f2IvG2hR66bO4G
jSa3B8CfjBcTaOmpmP7E+qw29xbzy6es5u44Z4ZXhCSozfdcOUqv+uHgDifZVPq74Hx1BV+SXsXX
jkGcSdFVbcjqqMoydPm51GSbVmj+cvFfG4J+A/7TfKVi8K80h9Izh7Mp5asRSeYRy6p4l8BUqePl
g+f6xHBVKtKrShinTVCVSnUhGo5Qkl/M38XPgh8YfCkPib4qeJ/hl438PfDnUfi741+Htn4y1nw5
qem6DL440i+1K81Hwqb27t4Ui1u2tra9kNhN5c0n9m6qkKySaVqSWv8AMWcZFnOEjis2xWV47D5b
VzjHZdDG18NVpYd46jUqzqYTnnFJV4xjN+zlaT9lWUbujVUP9g+BfEbgLO6mT8FZNxhw5mvFmE4E
4e4qxGQZfm2DxmZw4cx+GwmHwmdLD0KtSU8urVa2HgsTT56cfreClUcY43CSreA188fp4UAFABQA
UAFABQAUAPjkkikSWJ3jljdZI5I2KSRyIQyOjqQyOjAMrKQVIBBBFNNppptNNNNOzTWqaa1TT2Yp
RjOMoTjGcJxcZwklKMoyVpRlF3UoyTaaaaadnoelXvxq+MmpfEtPjRqPxa+Jt/8AGKO7sL+P4sXv
jzxVdfEuO+0rTLbRNLvU8dz6rJ4pW703RrO00iwuV1UTWemWttYW7x2sEUS+nPO86qZms7qZvmlT
OVOnUWbzx+LnmaqUqUaFKax8qrxSnToQhRpy9reFKEacWoRSXyGH8POAMJwhLw9wnA3B+F4BnQxW
FnwPh+Gclo8ISw2NxlXMcbh5cNU8FHJZUMXmGIr47FUngnTxGMrVcVVjOvUnOVGD4r/FK20bx94d
tviV4/t/D/xWvrHU/ijoUHjHxFFo3xJ1LS9Tn1vTdQ8faZHqK2XjG+07Wbm51axu/EUGoz2mp3E9
/byR3UskrRHNs1jRzDDRzPMI4fNqlOrmtCONxKo5nVpVZV6VTMKSqcmNqU60pVqc8TGpKFWUqkWp
ts6anBHBdbMOGM2rcIcL1c04Iw2JwfBmZVMgymeYcI4TGYOnl2LwvDGMlhHiMgw2Ky+jRwOJoZVU
wlKvg6VPDVYyoQjBdxYftSftK6V8Nbr4NaZ+0B8Z7D4S3ulXmhXXw0tPiZ4xg8DTaFqCGLUNDfwx
HrC6P/Yt/E0kV7pItBp93FNPHcW0iXE6yd1Piriallk8lpcQ51TyidKdCeWQzPGxwEqFRWqUHhVW
VH2FRNqdHk9nNSkpRalK/wA3ivBjwhxvF9DxAxnhd4fYrjnDY2hmVDi+vwfkFXiSnmWFkp4XMo5z
PAPH/wBoYaajPD4513iqE6dKdKrCVKm4+deBviJ8QPhhrbeJvhp468Y/DzxI+n32kN4g8DeJ9b8J
a22lapF9n1PTG1bQL7T786fqMH7m+sjcG2u4v3dxFIny15uAzLMcrrvFZZj8bl2JdOpReIwGKr4O
u6NVctWk6uHqU6ns6kfdqQ5uWa0kmj6ziThThbjLLlk/F/DWQcVZRHFYbHRyviTJ8uzzLljsFP2u
DxiwOaYbFYZYrCVf3mGxCpe2oT9+lOEtRi/EHx6ngST4XJ438Xp8MpfFI8cy/DpfEusr4Ek8bDS0
0MeMJPCIvR4ffxSNFjj0ceIG086sNLjTT/tf2RFhCWY5gsA8qWOxiyt4v6+8tWJrfUHjvZKh9ceD
5/q7xfsEqP1h0/beySp8/IrFPhbhiXEseNJcOZFLjGGS/wCrcOLHlGXviWHDrxssyeQxz14f+1I5
K8xnPH/2WsUsD9dlLFew9vJzfpfiX9qf9pnxl8PU+Evi79oT41eKPhekGnWo+H3iD4neM9Y8HNaa
RcRXek2cvh7UNZuNLms9Kure2udNtJrZ7awntLOW1iie0tmi9TE8V8T43LllGM4izvFZUo04f2di
M0xtbBOFGSnRg8PUrSpShSnGMqUHFxpyhBwScI2+PyjwV8HuH+Kpcc5F4WeHuTcZyqYus+Kcr4O4
fwGfqvj6U6GOrwzXC5fSxlPEY2hVrUcZXhWjWxNOvXhXnONesp8Pc/Fv4rXnhHwl8P7v4m/EK68B
+AdWn17wJ4JufGniSfwj4K126urm+uda8JeG5dSbRvDmrXF7e3l5PqOj2VneTXV3c3EkzTTyu/BL
N82ng8Hl880zGeAy+tLEYDAyxuJlg8DXlOVSVfB4Z1XRw1aU5znKpRhCbnOUnK8m39JR4F4Jw+e5
5xTQ4O4VocTcT4GnlnEvEVHh7Kaee8Q5bRo0cNRy/PM3hhI5hm2BpYfD4fD08Jj8RiKEKNCjSjTU
KUIxz/HnxE+IHxU8TXnjT4n+OvGPxH8Y6jDZ2+oeLPHnifW/GHia+g0+1isrCG813xDfajql1DY2
UENpZxz3TpbWsMVvCqRRogzzDMsxzbFTxuaY/G5ljaihGpi8wxVfGYqpGnFQpxniMROpVkqcIxhB
Sm1GKUY2SSOrhnhThbgrJ8Pw9wbw1kHCWQYSderhcj4ZyfLshyfDVcVWniMVUw+WZVhsJgqM8TiK
lSvXnToRlWrTnVqOU5Sk9j4Y/Gb4vfBTWLnxD8Hfij8QvhXrt7brZ3urfDzxj4g8HahfWaszLZ31
14f1DT5b20VnZhbXTSwBmLeXkk1tledZxklaWIybNcxyqvOPJOtl2NxGCqVILVQqSw9Sm5wu2+Wb
cb62ODjHw/4E8RMvpZVx9wZwtxrluHquvh8DxVkGV5/hcNXaUXXw1HNMLioYeu4xUXWoqFSyS5rK
xpeOfj98dfid4r0Xx38RvjN8VPHfjXw2Ym8N+LPF3j/xV4h8ReHDBOt1B/wj+sarqt1f6KIbpFuY
l0ye1WO4UTIFlAatcdxDn2aYuhj8yzrNcfjsNZ4bF4zMMXiMThuWSnH6vWq1ZVKHLNKS9lKFpe8t
dTj4b8L/AA14OyTMOGuE/D/grhrh3N1NZvkeRcL5JlWU5t7Sk6NT+1MBgsFRwuYupRk6M3jKVZzp
N05NwdjovG37Vv7T3xJ1LwdrPxA/aI+N3jTV/h5rdn4n8Bar4m+KPjXWtS8F+JtPngurDxJ4Wvr/
AFqe60HxDZXVrbXNprmmS22qW09vDNDdJJEjDpx3FnFOZ1cFWzDiPPMbWy6vDFZfVxWa46tVwWKp
yjOnicLUqV5Sw+IhKMZQr0nGrGUYtTTSZ5XDvgl4N8I4TP8AL+FvCnw64ewHFeXYjJ+J8Fk/BnD2
X4TiHJ8VSqUMVlGdYbDZfTo5nlWJo1q1Gvl2MhWwVWlVqU6lCUJyT4tPjN8YYrj4jXcfxX+JUd18
YUuo/i3cp468UJcfFKO+vLjUL1PiNMuqCTxul5f3d1fXS+Jm1MXF5c3FzMHmmkduFZ1nKlmU1m2Z
qecqazeSx+KUs1VScqk1mUva3xynUnOc1inV5pylJ3lJt/Qy8PuAp0uE6E+COEJ0eApUZcC0ZcNZ
NKlwXLDUKWFw8uE6bwThw5LD4ahQw1F5OsG6VCjSow5adOEV1nhb9qL9pXwN4DvPhb4L/aB+NHhL
4bX8V9b3fgLw38TvGeieEZrfU1Kapbt4f03WbbS1t9TQtHqUCWyxahE7x3iTI7q3ZhOKuJsDgJ5V
guIc7wmWVFOM8vw2aY2hg5Rq6VY/V6daNJRqq6qxUUqibU1JNnh514M+EPEnE1DjTiHwu8Pc84uw
08PVocTZvwdw/mOe06uDfNg6qzTF5fVxjq4OSU8HVlWdTCzjGdCVOUYted618RPiB4k8KeEfAniL
x14x17wP8P8A+2f+ED8G614n1vVPCngn/hIrxNQ8Qf8ACI+Hr6+n0jw3/bt/HHfaz/Y1nZf2neRp
c3vnzKrjza+ZZjicJg8BicfjcRgcv9t9QwVfFV6uEwP1maqYj6nhqk5UcN7eolUrexhD2s0pT5pK
59Xl/CnC2UZ3nvEuVcNZBlnEfFH9n/6zcQZfk2XYLO+Iv7JoSwuV/wBu5rhsNTx+b/2bhZyw2X/2
hXxH1OhKVHD+zptxfp/iH9rH9qPxd4IX4aeKv2jvjn4k+Hq2gsD4I1z4reOdV8LSWCxvClhcaHe6
5Pp1zYxwyPBFZ3FvLbwwMYYo0iwlepieLeKsZgVlmL4kz3E5coez+o182x9XCOnZpU5UJ15U5QUW
4qEouKi+VJLQ+NyrwP8ABjIuI3xfkvhN4bZTxU6/1pcR5bwTw3gs6hinKNSWJpZlh8tp4ujiZ1IR
qTr0qsKtSovaTnKfvGZaftL/ALRdh4W8D+CLD48/GOw8H/DLXrHxT8OPDFj8SvGNpofgHxLpbXz6
Xr/g3TLfWI7TwzrOmPqmpvpupaNFZ3dg+o37WksLXlwZM4cT8SU8LgcDTz/OaeDyvEU8XluFp5nj
YUMvxVJ1HSxGCpRrKGFrUnVqulVoqE6bqVHBx55X7K/hB4T4rOuI+I8V4Z8A4nP+McsxOS8W5zie
EMgr5lxPk+MWGjjMr4gxlXATr5xgMZHB4OOLwmYTxFDFRwmGjXhUVCkoYPhn43fGjwV4t8T+P/Bv
xd+J/hLx342i1m38Z+NfDPj7xXoPi3xdB4j1GPV/EMPifxJpWrWms6/Fr2rQw6prMeq3t2mqajFH
e3yz3KLKOfC55neBxmKzDBZxmmDx+OVaONx2FzDF4fGYyOJqKtiI4rE0q0K2IWIrRVWsqs5qrUSn
Pmkkz0848OfD3iLIsm4X4g4E4Nz3hrh2pl9Xh/h3OOGMkzPIsiq5ThJ4DKqmTZTjcDXwGWVMswNS
pg8vngsPQlgsJOeHwzp0ZODofDb4sfFH4N+Ih4u+EnxG8cfDHxSLWax/4SLwD4q1vwjrLWNwALiy
k1HQb2xupbOcAedaySvby4HmRtgYzyzN81yXE/XMnzLHZXiuR0/rOX4uvg6zpy+KDqYedObhL7UG
3F9UdXF3A/BnH+VPIuOuE+HOMcldanif7K4nyXLs9y+OJpa0sRDCZnh8TRhiKV37OvCEasLvlmrs
g+IvxP8AiR8XvE1x40+K3j7xj8SfF91b29nceJ/HXiTWPFWuy2doHFpZtqut3l7e/ZLUSSC2thMI
IN7+VGu9szmWaZlnGKljc2zDG5njJxjCWKx+JrYvEOEL8kHVrznPkjd8sebljd2SuzThTg3hHgTJ
6XD3BPDGQcI5FRq1a9LJ+G8owGSZZCvXcXXrrBZdQw+H9vWcY+1rOm6lTljzylyq3o+oftY/tR6r
8Pk+E+qftHfHPUfhimmW+hr8Pr34reObrwcNDs4Vt7TQj4dm1x9LbRLWCOOK20drY6bBHFCsVqgi
j2+lU4t4qq5csoq8SZ7UytUo0Fl082x88H7CEVGFD6tKu6XsIRSUaLj7KKSSirK3yWF8D/BjBcUy
44wfhN4bYXjKWMq5i+KcPwTw3Rz/APtKvUdWvmSzWnlscasxr1JTnWx6rLF1ZTqOdaTnPm86uPip
8T7rSvh/oV18R/Hlzofwmu9Rv/hXo1x4v8QzaV8M77WNVtNd1e9+H+nSai1p4Mu9U1uwsdY1G58O
Q6bNfarZ2moXLy3dvDMnnSzbNJ0suoSzLMJUMonUqZTRljMQ6WV1K1WFetPLqbqOGCnVr06dapLD
KlKdWEKkm5xTX1dLgrg2hjeKcyo8JcM0cx45oYTC8a5hSyHKqeN4ww2AwVfLcDh+KcVDCKvxBQwW
XYnE4DCUs2qYunhsFiK+FoxhQq1Kcu98K/tTftMeB/GHiv4g+D/2hPjX4a8d+O3jk8ceMtG+KHjW
w8T+M5YTmGXxZrlvrSal4jlgb5oJdYubySFuYmU8134TivifA4zF5jg+Is7w2Px7Tx2No5rjqeKx
rj8LxdeNdVcS4/ZdaU3Ho0fM514LeD/EmQ5Jwtn/AIWeHmb8NcNRnDhzIMw4N4exOTcPwqK1SGR5
bVy+WEymnUWlSGApYeFRaTUkfUn7KP8AwUB8Z/AbW/2rfiD428T/ABa8bfF/48/s5+K/hH4P+J8X
jS/uvGfhPxtq1zpVz4d8Z6h4r1nVW19YvDkmlwyWj6bdSahbNFbizESxKV+r4S8QsbkFfizMcdis
3x2cZ/w3i8oweaLG1J43CY6tKlLDY2pi69b6wlhnSi4OlN1ItR5LWR+L+N30XeH/ABMy7wS4W4dy
bgbh3gPwz8WMk46z/g2pw9hqHD+ecO4GjjaOa8P4XJMvwSytzzaONqRrxxdGGFrKdV4jnc3f4Z8d
/Ev4jfFHxRceN/iV488Y/EDxldeUJ/FXjPxLrPibxDIsEjy28Z1jWby8vxDbSSyPbQrOIrcu3kJH
mvhMfmeZZripY7M8wxuYY2dubF43FVsViGotuK9tWnOpaLbcYqVo391I/pLhrg/hPgzJaXDnCHDO
QcL8P0ef2WScP5Rl+T5VB1YRhVksBl+Hw+Fc60IRjWqOk51VFe0lKx33xE/ag/aT+L3hew8E/Fb4
/wDxn+JXg/S7q3vrDwv47+JvjLxZoFtfWcL29neppOu6zfWTXllbyzQWd08LXFpDNPFbyRxzSq/f
mXFPE2cYWngc24hzrM8HSlGdPC4/NMbi8PGcE4wmqNetUhzwi3GE3FyhGUlFpSd/mOFPBrwi4Ezn
FcRcE+F/h9whn+No1cNis54a4P4fyPM62Gr1I1cRh5Y7Lcvw2IWHxFWFOriKEakaVepTpzqwnKnB
x850X4g+PfDXhjxh4J8OeN/F+geDPiGmhx+P/COi+JdZ0rwx45j8MXs+peGk8YaBY3sGleJk8Paj
c3OoaGutWl6NJvbie6sBbzyySN5tDMcww2FxmBw2OxmHwWYqgswwdDE1qWFx6ws5VcMsZh6c40sU
sPUlKpQVeE/YzlKdPlk2z6zMeFuGM3znIOIs24cyLNOIOFZZlLhfPcxyjL8bnPDcs4w9PCZvLIMz
xOHq43J5ZrhKNHC5k8ur4d47D0qdDFe1pwjFP8JfET4geAYPFNr4F8deMfBdt458M6l4K8a2/hLx
PrfhyDxh4N1lBHq/hLxTDo99Zx+IPDOqxgR6loOrLd6VfIAt1aSqMU8JmWY5fHFQwGPxuCjjsLVw
OOjhMVXw0cZgqytWweKVGcFiMLVWlXD1lOlUWkoMnPOFOFuJ6uS1uJeGsg4hrcN5xhOIuHaueZPl
2bVch4gy+TngM8yWpj8NiJZXnGCk3LCZngXQxuGk26NeD1Oysf2ifj7pmk/DrQtN+NnxW0/RvhDq
k2t/CnTLL4geKrWy+GusXJma41LwJbwaokfhO8ma4uDLPoQsXc3E+4nzpN3bT4j4gpUctw9LPM2p
0cnqyr5TShmGLhDLK0ubmq4CMaqWEnLmleVD2bfNL+Z3+fxPhR4YYzHcV5li/DvgnFZhx5gqeXcb
YzEcL5LWxHF+AoqmqWE4mq1MFKWeYemqVJQp5k8TGKpUrL93C1Dxj8dPjV8RNCu/C/j34tfEfxp4
av8AxvqfxKvfD/inxp4h17Rbr4ha1DcW+reNp9M1PULmzk8U6jBeXcN5rjQnUZ47u6R7grczh88b
n2d5jQnhcwzfMsbhqmOq5nPD4rG4jEUJZjXUo1sdKlVqSg8VUjOanX5faSU5py96V+rIPDXw84Uz
KhnPDHA3CXD2b4bhzB8IYfNMl4eyrLMwo8K5fUpVcDw7TxmDwtHERyXCVKFCpQy1VPqlOdChKNJO
jT5fKq8k+1CgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAKACgAoAUAk4H
JPAA6k00nJqMU3JtJJK7beiSS1bb0SW4pSjGLlJqMYpylKTSjGKV223oklq29EtWPlilgkaKeKSG
VMb45UaORcgMNyOAy5UgjIGQQehrqxuBxuW4qrgcxweKy/G0HFV8HjcPWwuKoucI1IKrh68KdWm5
U5wqRU4JyhKMleMk3x5dmeXZxgqGZZRmGCzTLsUpyw2Py7FUMdgsRGnUnRqOhisNUq0KqhVp1KU3
TqSUakJwdpRaUdch2hQAUAFABQAUAFABQAUAFABQAUAFABQAUAFABQAUASxQSzH92hbHU9FH1JwP
w6+1fuvgr9Grxu+kJmNTA+FHAOccSYfC140Myz6UaWWcL5TUlFVPZ5lxHmdTC5Rh8T7FutDARxVT
Mq9JOWFwdd6P+XPpKfTR+jL9EbKKOaePXivw9wZjMdhqmLybhSEsRnfHGf0YTdL2uS8HZJRx/EGL
wbxCWGqZrLA0cmwteUY47McKm5K3/Z0/96If8Cb+iGv7twf7HH6UeJw9KvW4u8D8vq1IqU8HjOKu
NZ4ii2vgqyy/w5x2Ecls3RxVWF1pJqzP8ssx/wBIv+g1gsZXw2G8P/pO5vRpTcaeY5dwJ4aUsHiU
m0qlCGbeMWV5hGEt0sTgcPUs1eCd0j+zZ/78X/fT/wDxuur/AIo0/Se/6LvwG/8AEn8Qv/pXHD/x
cc/Qh/6NZ9Kr/wAQjwj/APp4h/Zs/wDfi/76f/43R/xRp+k9/wBF34Df+JP4hf8A0rg/4uOfoQ/9
Gs+lV/4hHhH/APTxD+zZ/wC/F/30/wD8bo/4o0/Se/6LvwG/8SfxC/8ApXB/xcc/Qh/6NZ9Kr/xC
PCP/AOniH9mz/wB+L/vp/wD43R/xRp+k9/0XfgN/4k/iF/8ASuD/AIuOfoQ/9Gs+lV/4hHhH/wDT
xD+zZ/78X/fT/wDxuj/ijT9J7/ou/Ab/AMSfxC/+lcH/ABcc/Qh/6NZ9Kr/xCPCP/wCniB06f+9E
fYM39UArlxn7HH6UeGw9WvR4u8D8xq04uUMHg+KuNYYis0vgpSzDw5wOEUnsnWxVKF3rJK7O7Lv9
Iv8AoNY3GUcNifD/AOk7lFGrNRqZhmPAnhpVweGTaTqVoZT4xZpmEoLdrD4HEVLJ2g3ZOpLBLCcS
IVz0PVT9COPw6+1fwp42fRo8bvo85jRwPixwDm3DmHxlWVLLM+pvD5twvm01F1FTy7iPKq2Myiti
vZL21TLamKpZphqUoyxeBw7dj/Ur6NH01PoyfS8yjEZp4B+K+QcZYzLqEcRnXClVYvIeOMgpOcaT
rZvwdn2Hy7iDD4D6xL6tSzmlga+R4yvGUMvzPFpcxFX4Qf1MFABQAUAFABQAUAFAHb/DbwHrfxR8
feEfh34bi8/XfGWvafoGlxAAl7rUJ1hTapZA7AEsiF0DsAm9N24fpvg54aY7xg8S+FPDvA4+jlH9
v4zFzzHOcRSeIo5Jw/kuWY3iDifPJ4WNSjLF/wBi8OZVmuaLBxrUZYyWEWGjWoyqqpH8R+kh435J
9HDwP8RPGrP8vr5vg+B8lpYnB5JhqywtbPuIc4zPAcOcJ8PwxkqWIjgVn3FOcZPk88wlh8THAQxs
sZLDYiNB0Z/0zx/8EyP+Cdv7IkPhDw1+1j8ePFWmfG3xL4eTxFoOpW/hnxLL4E0/UEkmtVuYLy38
FeINF1VdA1qJd6X+teG57u3ht7m+0ewg1C2ev9OPD/J/B3wvzvJM38MfBHI+Ja/CedZfmWH408Tc
2zbiDjXMsZlWMpYzCZnRwOCzfKuEOF8TKdL6xgqeV8M42vldb2EnmmZYnBQxU/8Akv8ApNftKvHb
xA/tvhDxh+kJjfDDIPEHIM0y/wD4hx4a8K/2HwXT4fzijiMrzHKcVxJ/q/nXE+f0K1CU8Hmsc64w
owxlCpWtlmW4HH/Vj7g/4di/sKftkyat8W9Q8d6J8QvEviG4hOq6/wDs8Dwb8O/BOnR2trbWWn6b
pPgSztvGw8OPDaW0T6hHqWq3uoX+rT6hqNy0c12YIfe8Q+NuEON8ywNbivwP4G4peW5Vh8nwmZ+J
FfibibjfE4OjiMVjIxzfjbJc34NzDNVSxONxEMAvq2HjgMujhctoJ4fDRcvivAX6Qvi74ecNYnBe
Bv0lOKcg4dxub4nOq2TeHuK4bjwbhMzxeHwuGxFXBcKZzlvFOAyqtiKODw88dTfvYrGqrja8PrNa
cj8oPin/AMEv/wBj7RP2svE37Jsnxb8e/BnxQLXwhd/DrxT4zstO8R+FfG1x4p8N6Zqv9h3epR3+
iHwzrA1K8u9O0hp7C+0/V7i1+wLLa6lLaWl3+eZh4QfRp4gy9Y7GeCWdcL+2Vb6xU8NPFDiLB1sF
K84qvRwHiFhPEbD1oR92pKgq1CEG5cvLRiqUf1/hz9s19MPw+8VsV4WcVeKXB3FeKowyapkOO8Uf
C/hpZbn6x+Cw2LeXYnH+G1bw1xWAxjq1amBw1eq66xkqXI68cfOCr+afGv8A4IG/tbfD2C/1T4d6
h4S+LWl2olmjttB1FrPXPsyAMC+m6vBpdzdXbZ2Cy0i11ORmUlGdSMfkGcfRF8Ls/bl4beNuI4fx
tVS+r8P+MnCtfK8L7S9SUKMeOeCa3EuAm5xVOmsRmnDHD+EjU/eVq1GnNql/pj4cftn81wsqGE8c
Po94l0IulHFcU+C/FWGzzSSpQqV3wJxrT4dx2HpUpurWlTwXGOfYt0rUqWHrVaalX/Hz4nfBT4r/
AAZ1u68O/FDwD4o8E6vZOEuLTX9IvdPeJmd403/aYY/LMjxyLGsojdyjhVO1sfzT4mfR48XvCXCw
zfi/hHEvhavVpUcFxzw9isFxVwJjqteMJ0KOH4v4dxGZZFDF1oVIOOXYrGYbMo3camDpzjOMf9R/
Aj6Y/wBHD6SL+peFPidkuacTU6FbE4vgLOo4rhTxDwNHDSnHFV8RwTxJQyziGrgsPKDVTNMDgcXl
LThOnj6kKlOUvLq/FD+nAoAKACgAoAKACgAoAKACgDtPA3w78cfEvXLPw34C8La34r1u/nS2tdP0
XT7m+nlncFliVII3JkZVZkiXMsgVvLRyMV+peGHgt4m+MeY18B4e8J4/O6WB5JZxndWWHyvhfh6h
NpLFcRcUZrWweQZHh7O8JZlmGHnXtyYaFaq405fhnjn9JXwP+jdkEOIPGTxDyPg+li4VXk+TValb
MuK+JKtJPmw3DHCOVUsbxHxBXUkoVP7Ly3EUsLzKpjKuGoqVWP61eBv+CFH7cXi/w3Ya/qWg+G/B
02oIJo9E8Ra9pVvq8Nu8cckcl5Z/2il1YSsXaKSy1C3tL6CWKQS26oY3k/qLA/Q04MwuFpUeLvpG
cN5fn8U/r2A4P4C4n42ybBTu0sNDiWpiuGsNmWIppfv6uV4PF5Vdx+pZpjoXnH/LPif9tPkFDOcX
R4D+jTxxxNwzTkoYDPOKuO+GuBM0zHlup4hcM0ss4srYDCVHaWFWYZjh8ynBt4zK8BUXsn+MNfwW
f7fhQBPbw+fKqdF+85HZR1/E8Ae5r+n/AKIH0dcd9J7xx4b8NY1sVgeHKdLEcR8dZxg1D6xlPB+U
Tw6zGphp1KdanSx2ZYvFYDIcsrVKFejQzLNsJiMRQq4ajWifxF+0M+mDlf0IvowcZ+M88PgM04wq
18Jwb4XcPZi6n1PP/ETiGli5ZRRxtOjWw9atleS4DA5txVneHo4rCYjFZNkGPweExVDG4jDTOiVV
RQqgKqjAA/z+Z6k8mv8Ar84D4D4R8MuEsj4F4EyLA8N8K8O4OGByrKcvpezo0KScqlWrVm3KrisZ
i686mKx2OxM6uLx2MrVsXiq1XEVqlSX/AJ5vip4qeIPjb4gcT+KPilxTmnGXHfGGY1Mzz7P83r+2
xOJryjGlQw9GnFRoYHLsBhadHAZXleCpUMvyvLsPhsBgMPh8Jh6NGGrpmia1rTTLo+kapqzW4Rp1
0ywu79oFkLCNphaxSmMOUcIX2hirBc7Tj6bEYzCYNReLxWGwqm2oPEV6VBTcbcyi6ko8zV1e17XV
9z4ahhcTiXJYbD18Q4Wc1QpVKrinezl7OMuW9na9r2dthNS0XWNFeKLWNJ1PSZJ0aSGPUrC6sXmR
TtZ4kuoomkRW+VmQEA8E5ow+LwmLjKWExWHxUYNRnLD1qdaMZNXSk6cpKLa1Sdm0KthsRhnGOIw9
bDykrxValUpOSTs3FVIxbSel1pcza6TEKACgAoAKAGuiupRwCpGCD/nr6HtXyHHnAfCPibwlnnAv
HeRYHiThXiLBzwObZTmFL2lGvSbjOlVpTTjVwuMwleFLFYDHYadLF4HGUaGLwlajiKNOpH9B8K/F
TxB8EvEDhjxR8LeKc04N474PzGnmeQ5/lFf2WJw1aMZUq+Hr05KVDHZdj8LUrYDNMsxtKvl+aZdi
MTgMfh8RhMRWpT524hMErJ1H3lPqp6fiOQfcV/yA/S9+jxjPox+OXE/ho6+Kx/DvJhuIeCM3xij9
ZzXhDOHVll08TOnSoUquOy3EUMbkWZ1qVChRr5llWLxGHo0sPVoxX/oZ/s9vpe5d9Nv6L/BHjSsL
gMq4v9pjOEfE3h/LpTeDyLxB4dWHhnFLBU6tbE1qGV5zg8VlnFGS4bEYnFYnC5Nn2AwmLxNfF0MR
UcFfzEf22FABQAUAFABQAUAenfBf4g3fwp+LHw9+I9iUW78GeKtI16B5Vd443srlHMzRxsjyCFSZ
fLR0aTZsV0JDD9z+jV4h5T4W+OXh7xnxCprhnC5pjcj4qqUoudbD8JcZ5LmfBnFWMw9NQm6uJwXD
3EGZYvD0eV+2r0adLTnuv5c+mt4MZr9IL6LHjP4UcPcj4n4g4Xo5rwjRqTjSo4vjPgrOsq464Oy/
EVpyhHD4XMeJ+GsqwGKxEnbD4fE1a/LL2fK/7qf2sfgD4L/4KS/so+DPGfw5v9Oi8WHSIfH/AMJd
fuZkaGO8v7IQ674I1u6txKsNtfz250jVWRX/ALM8Q6PZ3J3RWdzDP/oXmWX4/wAPOLs44dzOVKvL
LcZPB16+EnGthMfhXy1svzfLq0W4YjAZjgqtDMcuxEJOnisBi6NWnJxqxkf8TvjV4XYfxr4DoQwt
GWV8U5VKtjcl/tKlVwuJwOY01LDZrw7m1GpBVsJN4ijLA4+jVpKrgsywVN1Ir2FWnL+XbwR48/aL
/Yd+Njajo7eJvhZ8TPCF/wDZdc8PaxazwWOs2UUzCXS9f0efGn+JPDeporPbTj7TZ3ETw6rot6k6
Wd/H9RKODzLDp3p4ijNXhOLT5W7O8XvCa0utGmrSW6P8vcnzrxA8F+MXWwrzLhjiPKa/ssbgMXTn
Cji6MZvmw2Ows/3GYZdiYpunNe0o1IOGKwdZTVGvH9Hf+ChkUX7aPwp+GH/BQ34N6e7ponh2x+GX
7QnhLTJZbvXfhd4q0PUby90bWdQSDM3/AAj9y2qvBaa6VjCWM/hyW4ZJLi4iseDLv9iq1curv4pO
rh5te7VhJJSiv72mse/Nbz/f/HiMfF7hjhzx54SoNxweAo8Ocd5XhpSq43hrM8FiK1bCYyuoe99R
qPEuFLGtRtRnl85tSnUjQ908c/t9/G34afAH/gnN+0P9s1HXdL1L/hYvhn4xaXNN5cPxCh8MX+je
FpU1GQgxLrl5p2l6prWkalIjtba2pvHSW3e7tZ/Do5NhMRjM8wXLGEo+wqYWVv4DqRnU93ryKUox
lFWvDTR2a++zvx14z4c4E+j5x/7bEY3DYj/WDLeLsNOfLDP4ZbXwmWTjiJW5Fja2Hw2JxmExMk3S
xq9s4zpurTn9of8ABQP9pL4C6F8BfgT8RfGPws8I/Hb4X/GfxXodpLa63YqNZtvhxr3hXUde1XWv
COqI8Go6F4mtNujGNYLu0d3Nxp1xNaPJ9pt+rgnOeLOHcwzKnw/nuOyWr7CpTx2EpyhXyrNOWXsv
qWdZRioV8rzjAVIyqRqYPM8Hi8LOL96jJaP938XvGPIuDOGfDvjrCZfTzvA5/nuWV8FjsNWr5fnW
X5TXy2vmU81yDM8LVw+OyvOcJOODnQqUMRQqwqc9GVSjNqrT/Kv9rD/ghz4a8b+D7b45/sLeJh4h
8OeJNBsvGGk/D/Vb2J5dV0bV7WHUrWbwjqjW9rDfK9ncieHTNRaw1OG3iNsj69rDx20v55xt4NeD
Pi/LEy+qYHwL8TJylTo5vkeDqvwjz/GqU248T8MUI4rMuCsVi6ihR/tvhN4rIKFSbq4rhXC4V1MZ
hv8AYn6LX7Unxt8JsFw/hePMZi/pGeDGOwuExWHx+YYuC8YsgyjFUKM8NiMg4lxNTDZdxvgsPQl7
eWS8XwoZ/iElSocWxqU44PEfzX+NvA3i34c+JdU8IeOPD+qeGfEmjXdxZalpGsWdxY3trc2s0lvc
RS29zHFLHJDPFJDLHIiSwzRyQTJHNHJGv+f/AIl+FvHHhFxLU4V48yWplOYvD0swy7E061DH5NxB
k2JlNYHP+G86wU62W59kWYKnOWDzTLcTXw1RwqUZSp4ijXo0/wDot8EvHbws+kRwLgvETwk4rwXF
PDuJqywWMjTjVwmccP5zRpUquN4d4oyTFwo5nw9xBl8a9J4vKszw2HxEadWhiqSrYPE4bE1uUr89
P10KACgAoAKACgB8ccksiRRI8ksjrHHHGpeSSRyFRERQWd3YhVVQSxIABJrowuFxWOxWGwWCw1fG
Y3GV6OFwmEwtGpiMVisViKkaOHw2Gw9GM6tevXqzhSo0aUJVKtSUYQjKUknzY3G4LLMFi8yzLF4X
L8uy/C4jG4/H43EUsJgsFgsJSnXxWLxeKrzp0MNhcNQp1K2IxFapClRpQnUqTjCLa/bb/gn/AP8A
BGn4rftPx6V8Sfi3Jd/C74OTtHc2dxd2sh8R+LrcFtyaDprtbsbVmQQSapcTW9hbvIzQS6nd2V7p
A/0F8Pvos8K+HFPC8Q/SIo4jOuK50qOLy3wLybMp5fXy9VqcalGr4t8R4NvFcPT9nONdcEZBN8TT
vCnnmZ8LzU8JX/wh+ll+1jx+Z18z8PPofVcFLC0auJy/OvpB5zl9LMcq9rRqOlWp+EXD2MjLB8TP
2kJ0lxxxDRlwylGdTIcn4mw9bDZtQ/rb+A37LH7Pn7JvhB9M+EvgHR/DMOn6ZM2reJ5bW3vfFmq2
ttGbm5fU9deBLp4WMbTnTrMWekxS7nttPgLHP7PxHx3nmf5fg8ml9QyHhLJ03kvBHC2AocP8G5HC
MOVyy7h/L1TwaxUoL9/mWKWKzbGyvUx2PxNVub/xczPEY7Ns6zbjLirO874u4vzbnxGe8Z8W5rjO
IeKc15eao1jM5zGpWxTw1JXWGwFCVDL8HTUaODwmHoxjBfz9fHP9sT4xfFP4meI/Fmi+OvF3hDw5
NdNY+GPDnh7xBqWk2Om6BZSSpp6zRWE9slzqN0rPe6jeTK8sl3cyxxslpDawQ/zdmOeY7GYurWp4
mvQpN8tKlSqzpxjTi3y3UJJOUvilJ6ttr4Ukv4l4m8QeIM8znGY/DZpmGX4OU3SwWDwmLr4elRwt
NyVLmjRnBTrTTdStUknJzm4pqnGEY/y4V/Dh/wCjIFAGrpgGZj3AQD6Evn+Qr/c79idl2XVc++kR
m9WNN5tgco8M8uwUnb2qy7NMZxxic0jB/F7OWJyfKHUtpzQpX1UT/l3/ANJfzjOKHCv0QOH6E6qy
DM+IPGvOMzgnL2Es4yLLvDHBZHOol7vtYYLiLiFUW/eUKlfl0czWr/fw/wCTg+//APgml8cfi78K
f2vP2ffCPw4+IPiXwb4a+Ln7RX7PfhT4maLoV+1nY+NfDbfE7SdMbRdeiVSbzT2sPEWt2jW5ZUaH
UrlWzvBH4Z9IfgzhXifwr46zXiDIsuzbMeFuAeO8z4dxeNoKrWyfMFw5isQsZgZNr2VdV8Bg6qmk
2p4em1tr+seC3E/EGQ+IPCWX5Nm+Ny7BcQcYcI4DOsNharp0szwbzuhQeGxcUv3lF0sZiabg2k41
pp7n3F8Y/GPhL4m/B79rz9oT9q3wz4x/aZ8Z/Bn9r7Wf2fPgra+Ivi34x8I6T4F8K+LtP+JN7b28
lv4fMg1TRtA1LRbHW7TSYV0y9u9T03S7e510aCNW0bV/xrhLKc04c4s8K+BPDHMcp8O8o4u8KsJx
3xfVwHC2U5ris6zPK6/D9GpUjUx3L9WxeOw+LrYOripvE0aWGxGJqU8E8a8Li8L+n8R5jgM74d8Q
uLuPMFmPGuZcN+IWJ4R4ap4ziDMcvw+V4DMKWc1IQcMJf2+GwlbDUsTTw8VQqVK9GhCeK+qqvhsR
5v8AE/8AZf8A2YdC/ZR1j9trQ/C6WHw9+Jv7O/wk8E/B7wK3i3xU8ei/tk6v401zwp8a0tJ7/wAU
3XibVdP+H2i/DLxd410rT9fub3QtQ0/xfpDSabPbW1sifQcN+JHiPjfE7CeDuNzJ1894c494pzji
zOlleWRljPCXC5Rg8z4PdWFDLaeXYavnuM4iyrJ8TXwNOjjaFfKsUo4iFSpUcvGzzgfgjC8BYjxM
wuBVLKM74Q4fyzh3K3mGPaw3iNiMyxWA4lVOdXHTxuIo5RhskzDMsPSxdSrha1LMMO5UZQhBLD/Y
7+E/7Ld1+zv8EviH8af2fY/jH4n+LP7fcn7LN5ezfFL4keA4dE8Daz4B+FWs2+rW1h4N1qwtr7X9
C1bxXql3pryi2tbmKZ7TV49QjWzex7fFjifxJpce8Y5Dwhx1LhPLuF/A+PiVSow4a4fzueMzrCZ5
xNhKmFqV82wdepRwONwuWYaliFH2lSnKCq4WVCTqxrcvh3kPA1Tg/hnOOJOElxFjs/8AFd8C1Kks
9znKo4bK8TlORYmGIhSy7E0oVcXhcRj69Si5ckJxk6eIjWXs3S958J/sT/s6eANV+HfhrU/2fvHH
7T8fxr/bv+Pf7Lur+KdM8aePfD0/7PHgj4WfEjQfAPhuUxeAmtND1D4i69o2sah8SGvPHizeHLnw
/oVzaxaFaW8d9rkHxOaeMPH2eYbP8xw3HWTeHEuD/BTgjxIwuWYjJ8kx0OPc54l4fxueZhFSztVc
ZQyDA4vCUOH1SyRwx9PHY2nUljatSVHBz+qy/wANOD8pr5Pgq/CWacbx4m8U+K+B8RjqGZZrhJcI
ZZkWc4XKcFK2VezwtbOMVhsRVzl1M15sHPCYWdOOFpwVXFQ6v4O/sHfsWXkXhr4heNrK/wDFHwX+
Dfxp/aD/AGXvj/4mtvGfiLRj45+Ia/tEfCj4Wfs8eMdPez1qB9GFz4C+M1t491m38PpZaFLp/gDV
nvbKTz5zL5fFnjZ4v0pZjkOT1qGW8XcW8IcC+JHA2XVMowGL/sXIXwFxPxLx5lNdVcHNYt0874Sq
ZJhJ451sbGvnmGVGtHkhy+hw74V+GtSOCzfM6dXHcN8OcS8W8D8WY2GZYzDf2pm/+t+Q5FwhmNF0
8TB4ZTyriOGa4mGEVLCyo5TiHUpPmm5fmN+3R8AvBX7LniP4QfA3TtPP/C2fDfwptfEfx719tU1W
5/tLxv458T+IvEXhjSE0i+l+xaHH4f8AhhP4IxFp9jp8l3JqksupR3N5G15P/RngvxxnHiTgOK+M
8RX/AOMXzDiepl/BGB+rYWn9XybJctwGAzLFPFUY+2xssdxJDOfer168aUcNGOHlTpSVKH4j4o8K
ZbwNjOHuF6NF/wBv4LIaeM4rxbr15+2zPNMbjMZgcOsPVl7LCrCZHLLPdpUqMqkq8pVlOovaS+F6
/aT8vMnUwMwnuQ4P0BTH8zX+Af7bHLsupZ99HfNqUaazbG5R4mZdjZJL2ry7K8ZwPicrjN7unHE5
xm7pp6KU6ttXI/6x/wDRoM4zivwr9L/h+vOq8gyziDwUzjLINy9hHOM9y7xOwWeTpr4VVnguHeHl
Wa95wp0ObRRMqv8ADE/6iAJABJIAAySeAAOpJ7AdzTSbdlq3shSkopyk1GMU223ZJLVtt7JHl9z8
bfg5ZXE1pd/FX4eW11bSPDcW8/jHw/FNDLGSrxSxvfh0kRgVZWAKng4r62lwDxxXpU61HhDiWrSq
xjOnUp5JmU4ThJXjKEo4ZxlGS1Uk2mup+IY36TX0dMuxeJwGP8dfCLB43B1qmHxWExPiJwnQxGGr
0pOFWjXo1M1jUpVack4zhOKlGSaaTRB/wvf4J/8ARW/hv/4Wvh3/AOWNaf8AEO+Pf+iM4o/8MWZ/
/Mxzf8TTfRp/6P8A+Df/AIsnhD/57h/wvf4J/wDRW/hv/wCFr4d/+WNH/EO+Pf8AojOKP/DFmf8A
8zB/xNP9Gn/o/wD4N/8AiyeEP/nuH/C9/gn/ANFb+G//AIWvh3/5Y0f8Q749/wCiM4o/8MWZ/wDz
MH/E030af+j/APg3/wCLJ4Q/+e4f8L3+Cf8A0Vv4b/8Aha+Hf/ljR/xDzj3/AKIzin/wxZn/APMw
f8TTfRp/6P8A+Df/AIsnhD/57nYeGvG3g7xnFcz+EfFXh7xRDZukd3LoGs6fq6WskgZo0uGsLicQ
tIFYoJCpYKxXODXiZrkGeZFOlTzrJ8zymdeMpUY5jgcTgpVYxaUpU1iKdNzUW0pON7Nq+6P0Lgvx
M8OvEihjMT4fcdcI8b4fLqlOjj63CnEWU8QUsFVqxlKlTxc8rxeKjh51YwnKnGq4OcYScU1F2/p+
/wCCOn7d/wAO/B2qfBj9nTxD4l8deFtQvtV8Yadrl94l8WTan8NNYj1bToI/Bei6L4XnEdh4S1GP
xCkU9x4nt5HvZkd9O1JRo8r3mm/63+Enij4f+NngjkPhxlzeM8ZfDrw3yqhhMrzbIsNhuI81wXBW
Y1XnOLyLjOhi6+I4uwGD4MxFKOW8GYzDYLFZTlWTYypgaGI/sWnXzT/mJ/aEfRE8X/BXxx8SfpEV
sryiXgV4leJ9fMamb8LZniKmD4fx3GuAprCU+K+EKuCoU+G8XjuL8Nif7S4twmKx+X5vnOeYB4nE
4fFZ5UwWX/tz+3l8f/2IvhPpdjpv7TPg/wALfFXxdPZm48OfD2Pwpovivxo1jJLgXYudSNtB4Y0p
5gX+06jq+mi8MUradb6jPbvEvzOT4PNsTJywFWph6Sdp13UlTpJ9rRu6krdIxlbTmcU7n+UPjjx5
4McLYahh/EfKMs4ozadH2mX5BHK8HmmcOi5aVfaYl04ZbhZTu/aYjF4f2vLJ4enXnBxX5FeBf+Cp
n7NvwwvtZ074ZfsUWXw38MeJYZNM8SXXhHxro8OpeINFmjmt5rHxD4a1DwVe+GvElq1tc3ES6brs
19awC4nNrJbyStLX1MsjzKfLOpm7rVI6qNWlJwUv7slVU4ar4oKLP5Pyb6THhzw5WxmG4d8GaPD2
W5jCWGzCtlOc4SGJx2DnGdOVHH5dXyetl2Y03TqVIrDY2dalD2k/ZuEpOZ65oPjb4M/tkfsqePf2
fvCPwy8d2PgnwPq2o+LPhv418K/CXxI1r8GvE+qX2seI7HS/FWiaJrnxFlvNB8/UtS0a7vPB2s67
rVz4e1SWUeB9Hi063vTyVKeKy3MqWOqVqHta8Y069GeJh/tNOMY05OlKpGi1NKMZpVYxgpxt7aXN
yn1uAzng/wAXfC/POAsq4cz2lk2S4nEZpw9nOWcK5g6fCGZYmvi8woYbNMFgcbn8q2B58RiMHVrZ
Ti8bjKmX4mU1kuEjh6dZ+EfHn4eeNvFH/BJj9nHxCIm1cfA/4pePdF8TS2M66hb2Hh658V+LfDNt
qKXMDSJPptpq0Wk2ENxGTAlvfQuGWELXVg69KnxJjoX5PreHoypprlcpqnTqNWe0nHmk09W0fEcc
5BnOZ/RX8PcwUHi1wVxNnmDzKVCaxFOhgKmaZpltPEKpTclPD0sVHC0IVIvkVOtCSahY/Y3/AIJN
63qet/sJfB1tTllnfS7jx3ollJMzO/8AZmm+PPEUdhEGYkmK1gZbSBfuxQQRQoAkagfL8RwjDOMV
yq3MqM2l/NKjDmfzer82+p/XP0Wcbicb4I8IvEylN4WpneCoym239Ww+eZhGhFNtvlpQapU1tGnC
MErRRzX7e37B3wJ/bgtNU8NTW1p4Y+PWiaQLrw349traW2imeKFJoNF8Ty2qv/aFrJBNEI742t1f
6SrRND9usorvRbz43LPGrwr4v4jx30YPFDC5lxhkGGw0+IKmAwlOOHzngXMsZg8PUef8AcR4hTjl
vEFDB4vB47MMjrQrcNcRYVxwedYWvXpwrYH+2vCrjjxh+j1xDhPHTwZzzDcN5/U9jlWa4LMIzxnC
3H2S4bFTqPh3jnIac6X9oZVVq069HA5thquF4iyCvUnjMjzDBynNYj+I39or9nX4n/swfE7XvhZ8
VfD93oevaNcukMssebPVbIkNa6jp11G0ltd2l1A0c8M9tNNBLFJHLDLLDJHK/wDIHjh4IZ54M57g
4SxtLifgXiWniMfwFx9l+Hq0Mr4nyuhUjCvRrUKjnUybiXJ51KeE4k4axk3jsoxkoSUsVluLy7Mc
b/1B/RG+lx4f/S18PpcScOQfDvG/D31TL/Enw1zHF0q+e8E55XpSnT/eQjSWccMZv7KvieF+KMLR
hgs5wdOrTqU8Dm+BzbKsu8Jr8QP6vCgAoAKAJ7a2uLy4htbWGW5ubmVILe3gRpZpppWCRxRRoCzu
7EKqqCSSABXo5RlGaZ/mmXZHkeXY3N85zfG4bLcryvLcNWxmYZjmGMrQw+EwWCwmHhUr4nE4mvUh
So0aUJVKlSUYxi2zyc+z7JOFskzbiTiTNsuyHh7Icuxmb53neb4yhl+V5TleX0J4rHZhmGOxU6WG
wmDwmGpVK+IxFepClSpQlOckk2f01/8ABOf/AIJPyeEPB2gftN/tIeExq1/qot734f8Aw01CKF4L
KC7tzNpPirxbZ3EcoubCWUxS2ehNDt1hDFLqEiaC5tvEH9qcafWfoP8Ahlj834XyHCcZfSNz2FXI
M445wWMweMy7wAqZpgsTQpZfwV+5xdDNuOMPipQw3EXGGEk6GS4nmyPhvErkx+dYz/ml+lh9M7NP
pocY1fD7hDOM04R+jHkmIdehlEqeLy3N/HitgsRTk854vp82HxWX8BuMHW4a4MxcYVc1pcmfcVYZ
4mpl2SZN/RN8KviBrUr6L4VubKDUbeaXUYrXUV1KzRrXT9MuZ9NeKG3izHNBZ3dhdQ26JIryWqo1
oj2scbt/NfgT45eKE+Jsg8M+OcBDi3Mc7xueZjLi/F8WZbis0jlVDFYnD1q9SKnWnmnsMywOYSp+
2xMcwxeDqRrYWjVwtCnUqfyfxxwRk2Aw+NzXAT/sqGCw2XQWVUcurQw/tcThaGKw8YtKCw3tcHis
K5RVL2dCrenWdOs5wjvfCr9oT4QfHeTxRovgjxFBqep+G7zUNI8ReG9St30/Vo4ILiXT5b1dPufm
vdGu3BSK/tTNb/vFguTb3LGAf3Fg8zwOYutTw9VTnSlKFWlNcs0k3Fy5X8VOXSSutbOz0P5yyPiz
IOJ3jsNluLjXrYOpVoYvB1oOlXUYzlSlUVKf8XD1GrRqw5oaqM+Wb5T8RPjp+yB8KvhT8UPFHh7X
/iJ4h8P6dqN2PEfgvT9N8Lx655fhDVlH2WK9u21Cz23mn6zb65o6oI28+y02zv3ZZLx4Yvz7Mcjw
eCxlalVxVSlGUva0IxoqpahPZSk5x96NRVKe2sYRle8rL+a+J+AMjyLO8bhMVm+KwlGrNYvLqVLB
LE2wFde5GpU9tTtUpYiGJw6VnzU6NOq2nUcY/wAglfwaf+iCFAFyymEUw3HCSDafQHPysfYHgnsC
TX+g/wCzY+kRlXgF9IbB0uLMxoZXwH4mZa+CeJMyxdSFHBZLjquJpYzhnPsZWqSp0qGFwebUll2O
xdepDDYDK84zDH13y4XT/I/9s99EDPfpYfRDzGvwDlGJzzxU8Fc5XiXwbk2X0amIzPiTLKGCr5fx
rwrl2HpQq18Vj8xyCs84yvAYWjUxma55w7lGU4Vc+Od9+v8ArBhONSMZwlGcJxU4Tg1KM4yScZRk
m1KMk0002mmmnY/4IqlOdKc6VWE6dSnOVOpTqRcJ05wbjOE4SSlGcZJxlGSTi000mjV0LXdc8La5
o3ifwxrOq+HPEnhzVdO13w94h0LUbzSNc0LXNIvIdQ0nWdG1bT5re/0vVdLv7e3vtO1GxuILyyvI
Ibm2mimiR158bgsHmWDxeXZjhMLmGX5hha+Cx+AxtClisHjcHiqU6GKwmLwteFShicLiaFSdGvQr
QnSrUpzp1IShJp64XFYrA4rDY3BYmvg8bg69HFYTF4WtUw+KwuKw9SNbD4nDYijKFWhXoVYQq0a1
KcalKpGM4SjKKa3bv4jfELUNE1/wzf8Ajvxle+HPFfiiPxv4p8P3fijW7nRPEvjSJLyOLxfr+lTX
z2Gs+KI49R1COPX9RgudWRL68VbsLdTh+KlkGQ0MZgcxoZJlFHMMsy2WTZbjqWW4OnjMvyiTpSll
WBxUKKr4TLZSw9CUsDh508K3RpN0r04cvVUzjN6uGxeCq5rmVXB4/HLM8dhKmOxM8NjcyiqijmGL
oSqulicclWrJYutCddKrUSqWnK/rfxA/aH1Dxj+z78DP2c9I8P8A/CL+DPg/qfj7xdq8ya1LqU/x
D+IvxA1OCW98YaxbJp2mWth/YfhzT9K8KeG9OC6jNp2nQ6hNJqtzLq9yqfL5FwHQynjrjPj/ABWO
/tLN+K8PkeV4WDwccPDIcgyLDTjRyrCVHiMRVr/Xcwr4nM8wxDeHhiMRPDwjhaccLTb9/NuL62Y8
JcL8H4fCfUct4erZtmGIksTKtLN84zavCVXMcRBUaNOl9VwdGhgMHRSrSo0Y1pOvOWInbyTS/iN8
QtD0fRvDui+O/GekeH/DnjNPiN4e0PS/FGt6fo+hfEKO1sLGPx3o2mWl9DZaX4zjstK0uzTxRYwQ
a2trpthbrfCKzt0j+oxOQZDjMXi8fjMkyjF47MMoeQY/G4nLcHXxeNyGVSvWlkmLxFWjKticolWx
WJqvLa054N1MRXqOjzVajl4FDOM3wuHw2Dw2a5lh8Jg8yWcYTC0MdiqWHwubxp0qSzXDUKdWNKhm
SpUKFNY6lGOKVOjSgqvLTgl3Phj9pT9orwUPGw8H/Hn4yeF/+Fl3+p6r8RP7A+JnjPST461bWmkb
WNX8XGx1mA+ItX1UzT/2jqmrG7v7xbi4W4uJFuJg/jZj4e8BZx/Y/wDavBPCWZf6vUcNhsh+vcO5
Riv7FwuDUVhMLlXtsJP6hhcNyQ9hhsL7KhRdOm6dOLhBr1MFxpxhlv8Aaf8AZ3FXEeB/tqrXr5x9
UzrMsP8A2piMS5PEYjMPZYmP1zEV+aftq+I9pVqKc1ObU5J8Lp3xE+IGkeFL3wHpPjrxjpfgfUtf
0/xVqPgzTvE+t2XhS/8AFGkxiLSvEl74dtr6LSLrX9MiVY9P1iezk1GyjAS2uI1AFe1XyDIsVmdH
O8VkuU4nOcPga+WYfN6+XYOtmdDLcVJyxOX0cfUoyxdLA4iTcq+EhVjQrSbdSnJs8ujnGbYfAVMq
w+aZjQyuti6OPrZbRxuJpYCrjsOuWhjamDhVjh54uhFKNHESputTStCcUVvGfjfxp8R/E2qeNPiH
4v8AFHjzxjrj20mteLPGev6t4o8Tau9lZW2m2b6pr2uXd9qmoPaadZ2lhbNd3UpgsrW2tYikEEUa
6ZRk2UcP5dhsnyHKstyTKcGqkcHleUYHC5bl2FjWrVMRVWGwWDpUcNQVWvVq16ipUoqdarUqSvOc
pPPMszzLOcbXzLN8wx2a5jinB4nH5li6+OxuIdOnCjTdfFYqpVr1XTo06dKDqVJONOnCEbRjFLl6
9Cc404ynOUYQhFznObUYwjFNylKTaUYxSbbbSSTbdjkp051ZwpUoTqVak406dOnFznUnNqMIQhFO
Upyk1GMYpuTaSTbMC9mE03ynKINoPqc/MR7E8D1ABr/lA/aUfSJynx++kLiqXCWPoZlwJ4ZZX/qR
w7meErRr4LPMwpYuvjeJuIMHVhKdKthcXmlZZXgMVh6k8Lj8ryTL8xoPlxjv/wB7f7F/6H+f/RO+
iJga/iBlWJybxT8bM8/4ibxfkuYYeeGzPhfKa+X4XLeCuEsxoVIUq+Hx+X5Hh5Z5mmBxdGljcqzz
iXN8nxScsvjanX+ex/rkeX/G65uLP4N/Fa6tZpLe5t/h34ymgniYpLFLH4f1B45I3GCrowDKw5BA
I6V9dwBSp1+OeD6NaEalKrxNkcKlOaUoThLMsMpRkndOMldNPdaH4d9JrGYrL/o5+O2OwOIq4XGY
Twh8RMRhcTQm6dbD16PCebVKValUi1KFSnOKlCUWnGSTTufhh8Kvgz4l+Lf9vt4fuIozoK6fElhD
onjHxR4g8SaxqsWq3emeGfCnhzwN4Z8UatqWu39hoOu30L6hb6R4dtbfSrhtY8RaUsls03+nR/x9
Hp/xu/Zbuvhd8f8Awx+z74L+I3g34xa74tbwRpukeIfCeo6MmgT+JfGuoLo9ppLz2uv63/Z1udRk
gubG48QPomr3Ph7UNJ13VPD2hLqKWcYBFcfsbfHW50s+KPCHhmfx14BbS7TWrb4jadpfinwV4O1H
SruWyi/tPS7n4w+HPhnq+paPatqmkm91+y0ebw/arqunmXVQZ8KANvf2Kv2mtN0e98Q6j8M2sPD2
nrqjz6/e+MPANrockWleLU8B+fZavP4pj07UrbWvGEg8PeD7nTrm6g8dakskPguTXhDKyAF7Xv2H
f2mPDnh7VfE1/wCALOXTfDvg6+8eeKksPGvga+1Hwt4asb/4g2i32s6Vb+I31ELqdj8L/GniTQDp
9tfxeIvCmkTeIdDlv9PjnlhAOo/YFu7mH433ltFPJHb3ngjW1uoFciKcQX2jywmRPus0UgLRvjcg
Z1UhZHDfgf0jaNKfAFGrOnGVWhn2AdGo0uen7TD42E1GW6U4v3o7S5Ytq8Ytf6ZfspMfjMP9JvH4
Ohia1LCZh4acTRxuGjNqhilhsz4er4d1ad+SU6FVOVGpb2lNTqxjJQq1Yy/a/TNSvdH1Cy1XTbiS
1v8AT7mG7tLiIlXingcSRsMdRkYZTlXUsjAqxB/jbhDiziDgTijIOM+Fcyr5RxHwxm2CzrJsxw8r
VMNj8BXhXoycfhq0ZuHssThqqlQxWHnVw1eFSjVqQl/0L8fcC8K+J3BXFXh5xxlGGz7hHjPIsx4c
4hyjFwUqONyvNMNUwuJgpfHQxEIz9thMXRcMTgsXTo4vC1KWIo0qkf68v2Qvgz+y9/wVa+BNh8TP
iXY654d/aH8Gw6X4L+J/ivwNry6brniN9P0+O08O+Kde0/VLTW9Iv77U9O0+XTrjVLnTlv7+90W8
upHS1mtIU/1uzjinL8/yPhHxV4Iw+HwnB/ibl2Jzb+woxc8Pwfxnl+J+qcdcE0Zc0K8MLkuczjjM
lVXllW4XzjIcU1J15Tf/AA1fSe+gxw5wP41+IvhR4i0c2p8X8EZnh6GXcaZZiYYLF8dcCZlh1i+B
OMsXQq4fFZZXx2Z5GqeBzqVGhP6pxJled5Y6r+oqMfZtb/4JHfsJfAPw5rXxR+M/xG+Jl54G8LW/
2/Vm8WeK9D0fSTGrhYLIDwt4Y0jW7+9v5mSzsdN0u8/tLUryWG0sInuJUif5+HEucYycMPhaGHVa
o+WPs6c5S83+8qShFRWrlJcsVdy0Vz+Z8Z9FPwQ4Fy/GcS8YcQ8R1skyyn7fFvNc0wWEwvKmlCj/
AMJmW4TG161ebVKjh8NV+sYirOFKhGVSUYv4R8af8FZPFPguaz+Ff7D3wp8GfCH4S6LP9g8NW1z4
Vj13xZ4kupmSN9UudO8+Wwt7zU5VQiKeHWtbu2Kzajq09zL9ntvYpcOU6qeIzbEVcViZrmqNVHCn
TS+ypfE1FdU4wW0Ypav8Pzj6U2Z5POjwx4LcL5Pwnwrg5+wy6lUyyOOzTMas2oyxVTD88qEK2Jkk
+WcMZjKrtPEYqdSXJT/Q/wDZc+HH7b3xk8EeP/iN8cNI+G3guy+JOh32mah8INe8Fah4WX416fq1
j9g1W7+JWl6XfJbeBtSu9L/4l+i+M7XwzdeM/MNvPrunanoOm2Wlah4mYV8pwtWjQwkq9V0JKSxU
KqqfVJRfNFUJSV6yUvenSdRUt1CUZyco/v8A4Z8PeM/F+S57xBxphOHMnocRYKthsRwnjsmr5YuM
qGKoOhiqvEeGwtdU8kxFXCv6vg83p5bUzjm9nPHYfE4HD0cLX+3P2PvA2i/DP9mbRfgzotlq1nrn
w7tvFGieJPCuuXFrbeKdJ13Wta1vxEtrqtzYO9nJ/aEOrw3Ola7psk2katpsltqOk3E1o6bfhfES
li82yHimGGwtTMsRmWQ5nRwWFwmNnls8fVnltWhRw2GzBWngatary0VX0nhakuaVnE/ffAfK8u4S
4T4b4XpOtgK/DGOq0M3wmZUqNfH5bicRmuIzKcsdRouVHFQnSxUa+HxGHc8Jj8K6dbC1J0pq2FeW
2m6dp2rWWr2niOz8aQSabHH/AKfFNYPBP5DG11MNarNbiOzWKS1WK8lLs0UTmP7Owr/FXOMuyjh7
KuJco4qyvjrK/FfBVeH8PGm85o4vKZ4LGLBVY5ZxBKeBWKwcKWUwwtbL6WHzOu6lSeGw1V0PqMoL
+9sLXq42tgsTgK+V4jIKkcZPmWGlTr+0pOrGVfCJVXCo3iHUhWlOhCyU6keZVUzz/wDb6/YJ8K/t
ufAqLTL3TrPQ/jP4V8Peb4C8RrLFLIt7BCbgeFtX1GVA1zpN/OZRbXUr50fUbqW9R5bK61iz1P8A
3D8NMZgM68L8H4VeL+W1KfBHFmU5K8/wmDxP9uZpwHxRhMBTwmX8d8KY/ER5q2f5IrQzGF/Z8TZN
LG5Fj6tWGIw+Mw/4rwB4pca+A/i9lXjP4SY+C4q4ZxWMoTwGLlUwGSce8KYvEqrm/A/FOHw8tcmz
iMFUwVflnW4fzilgM9wEI4nBeyqfwXfEX4feKvhZ418R+APGuk3ei+JfC+q3ukarp19BJbXEF1Y3
EttKrwyAPGwlidHjf54pUkhkCyxuo/z88W/C7iLwd46zfgfiN0MTVwXsMblGdYCXtMo4o4bzKmsV
kPFGSYi7WIynO8vnSxmGbftsPKVXBYuFHHYXE0KX/Wx9Hrx44I+kn4TcLeLfAWIqf2Vn+GnRzPJ8
W4xzfhTibANYfiHhHPqKUfYZxkGYqrg8Tyx+r4ulGhmWAqYjLcbg8TW4qvzU/agoAKAP6Q/+CK3/
AATWtfiZqFv+1J8b/D63XgXRLqWH4e+FtVt5RB4o1u2kUPqt5BJGsd1oelsrrcwF3g1C8aPS7gS2
8Ouaef8ATvwK8O5/R94PwHiBm1CphfHLxByadfhihVjFYvwr8Os6w06Mc4nRqQVXL+POPMvqupll
WP8AtHD/AAVi44mE6GZ8Q8mXf81n7TH6Yz8eOMs1+jp4cZrDEeCfh9nNKj4j5zgKvNhfFHxEyTEw
rvhvD4mhVlTxvAvAOZUoQzOEkqGfccYGpD2dbLOHMHisz/oZ+Lv7SH7NL+KbX4VeKfijBo2v6ZrE
cN69jp9/eaRpV8qyWkmm6trENnLpFjPF5hjnMlzjTpVK3nlFZYx+CeOHBvBfi3l+V8JZ7xTjslqZ
VntLNJYjLqMcRGVRYfEYSthcTKtCWGhOVPEz5a8pSlhaqvOE4upTl/Lnh34N+NFHIq3HWQ8C1Mwy
jGZbOWFp4nFYTC4/G4S8K9PF5dls8RTzDE0ZqmpUVToL65TaeG51KnN+5eEvhJoXhy+W/wDtTapb
2ciTeG4GVoY9IhMslz8kkNwY7svJMXDmNIi2+byvMnc1894cfRa4V4G4nq8R5hm+M4rjleNp4jgT
C46OIw0uE6EcTica6ft6GYSp5nUliMS5J1MPh8KpxqYj6r7bF1mvzPiLxDzbOsLLCOgsDXxMJU87
qqUas8xqqnCh70atFTw6jTpqHKpzqKPLS9pyUoI/HDxX+zD8a/2dv2rrX4weErG4X4UQfEeLxXd+
OLG6g/s3w54J8Qa+h8Sad4rtUnS9ttO0rS9SvbPU7hrZ7KTS4W1NpYo0n+zfr1fKMfledLHUIv6k
sUqzxEWuWlh6tRe1hWjfmUYRlKMnblcFz3Wtv4Yx3BXEfCPHUOIMBSmsijnEcdPMqVSPssHluLxS
+uUcdBSVSFKhQrVKdabg6boR9s3FKXJ6v+0d+zV+1X+0N8S7n4o+CNS8IW/gXW9F0dPBVhe62tnf
2PhuGB5LL7fBdaPMY7zU5Z7jXZo4Z5oYP7WFsJDJBIF7c0ynOc0xbxmHnh1h6lOn9XjKdpRpJe7z
KVN2lNt1Gk2lz23TPd4v4O454szmed5bWy+GWYnDYdZbSqYn2dWlg4xbp+1jPDytUrSlPFSjGUow
9vyXvFn8NNf56n/oGBQAUAXoL6SIBHHmIOBk4ZR6A4OR7H6AgV/p79Fr9qB4seAuS5XwHxvk9HxZ
8PMpoxweUUcfmVXKuMeHcDBtUcFlnEUsPmFHMcqwcZS+rZXnOAxFajRp4fL8Bm+V5fQpUKf+In05
/wBh94B/Ss4lzzxU8MuIsR4BeL2f4ipmPEOJyrJaOfeHfGOaVEpYjMs74Pji8oxGT57mM4ReNz3h
zNcHh8RiKuLzfNuH88zfFV8VVuf2lD3SX8kP/s4/lX+jGD/bMfRtnhqUsw8PfHDDYtxTrUMHkfAe
Ow1OdtY0sVW8RMuq1op3tKeDoNrXkWy/x2zH/Rwvpm0sZXhlPi79GLG5fGbWGxOY8UeKuV4yrTu7
Sr4HDeEGb0cPNqzcKeY4lJ3SqStdr/aUH9yX/vlP/jldP/FZb6MP/RCePP8A4jHh7/8ATROL/i3G
+m9/0dP6Kv8A4m/i5/8ASOD+0oP7kv8A3yn/AMco/wCKy30Yf+iE8ef/ABGPD3/6aIf8W4303v8A
o6f0Vf8AxN/Fz/6Rwf2lB/cl/wC+U/8AjlH/ABWW+jD/ANEJ48/+Ix4e/wD00Q/4txvpvf8AR0/o
q/8Aib+Ln/0jg/tKD+5L/wB8p/8AHKP+Ky30Yf8AohPHn/xGPD3/AOmiH/FuN9N7/o6f0Vf/ABN/
Fz/6Rwf2lB/cl/75T/45R/xWW+jD/wBEJ48/+Ix4e/8A00Q/4txvpvf9HT+ir/4m/i5/9I4T+0oe
yS/kg/8AZzXNjP2zH0bYYerLL/D3xwxOLUW6NDGZHwHgcPUnbSNXFUfETMKtKLdryhg6zS15Hs+3
Lv8ARwvpm1cZQhm3i79GLBZfKaWJxOXcUeKuaYylTuryoYHE+EGUUcRNK7UKmY4ZN2TqRvdU576S
UFFHloeDg5Zh6E8YHsB9SRX+cv0o/wBqB4tePWS5rwHwVlGH8J/DzN6c8JmtHLswrZnxjxDl82lV
wGacRKlgaOAyzFxivrWW5LgMLVr0alfL8fmuZZfXrUKn+xn0Gv2H/gB9FTiXIfFTxKz/ABfj34vZ
BVpZhkOJzfKMPknh3whm9NSlRzTIuD3iMzxGa51l85y+o51xLmuPoYXEUcLm+VZFkub4ahiqNGv8
xD/bgKAPOvi/pOoa98KPiVomk20l5qmr+BPFem6daRDMt1e3uh31va28Y7vNNIkaDuzAV9RwTjMN
l3GPCuPxlWNDCYLiHJ8Via03aFGhQx+Hq1akn0jCnGUpPsmfj30hsgzbirwG8Z+GshwdXMc7z/wu
47yfKMBQV62NzLMeGczwmCwlGP2quIxFWnSprrKaR+Evw3+LHjH4NXniKy07S9MuE1kWUGsaL4ks
9XtpbXUdHlujp+oWd9oup+HvE2h6tYw6hq2nNJp2sWQudN1jVNO1GC7tbpol/wBPKVWlXpwrUKtO
tRqxU6dWlONSnUhJXjOFSDcZxa1UotprZn/HpjcDjctxeIwGY4TFYDHYOrPD4vBY3D1cLi8LXpvl
qUcRhq8KdahVpyTjOnVhGcXpKKZha/8AE7xbrfjyw+JFpff8Ip4p0T/hDh4XvfBj3fhyTwinw90b
RNA8D/8ACNXlndHVNOuvDGleHNEh0zVjqM2t/adPi1O61O51V5r2XQ5TttV/aX+NOtaNa+Hb/wAW
2Z0PTbe/tdC0qz8H+B9MsfC0OsJqS+If+EMg0zw3Zp4Lm8WHV9Sk8Z3HhQaPceNLi8mufFUusXBW
VQBsP7S/xytm0Wa3+IOowXvh6HTLTSdXh07Qotet7DRPHeifE7RNLl8QJpS63e6PonxB8O6T4v0X
R76/udL0nXIbm9060tpNS1P7YAZmu/Hz4reJ9Fm8OeIfEdnrOgy6ZY6NFpOoeFfB9zY6dpejw39v
4es9DgfQCnh+Lwnbavrlp4L/ALDGnt4LtPEfie18KNo9v4m16PUQD6M/YB8PazdfFvU/EkWn3J0L
SvCeqWV5qhicWcd9f3emfZLFZ9vlvdzRwzzCFTuWGCSR9o2B/wCefpH5ngaPBeFyueJpLMMXnODr
0MIpxdeWHw1HF+2xDp35o0YSnTpubVnOpGKv73L/AKlfsn+EeI8d4/51xjh8oxsuF8j4CzzLcxzy
VCpHLqWaZrj8l+oZZHFOPsqmPxFLD4rErDxk5Qw2Gq1Z8t6SqfsfX8On/RYftL/wQz/aEvPhH+2J
o/w/vb3yvCvxo0+58F31tNIwhGt3TW8vhya2h8xITqNxr1rpFgs0ivJHp89/DB89wUl/vb6H3EdT
iDhDxf8AB7GVZTjhcop+NHBtOSuqGf8AB7w2U8Z4Si0pTjDOOBMyxOa42KXJUnwTlkqk6aoLn/w7
/bL+EWF/sfwb+kVluEiszyDiF+DfGmIppKeI4R40hjs44SxWLcrKVPIOOcuWW4FKanCfHmYWp1FV
cqX9cfjb4wf2J4s8T6Dr+iWPiLwbYX2naVq+lTWttPcRWmoaVZXcN8sF0r21/C16b6G6trtfKBFh
GksDzZf8E4z+ktmnh94l8U5bj8up5rwnw7m+UZVmOFwXJRzrLcNmmRZdjsLnOCqSlCnjIvMJ5phs
bh8TKnTT/sujTxOGniOap/lfk/hDlvF3B2U1FVhRzXOMBjsVR+vQWIyvHVMJmOKw9bL8XRlGfsrY
aODq0J04Tk7YypOjWjStD074eeGfg55Efiv4b+DvAekveB1bU/DnhTQ9D1EOQBPb3cljp9peRTKH
2zRTEEhudyMCf6m4T47yHxAyPC59w1ncc4yvFJqM1UqqpQqwUfa4bFYWty1sNiaDlGNWjVpxlFtN
XhKMpfhWN8PcHwPm1fC1eFcpyLM6fx1cFleAws6tOd+WrSxWEow9tRqpNxnGpJSV07NSS8T/AGiv
2xfB/wCzl4++FngPxF4U8Sa7P8S7rYdU0g2yWuh2R1Sy0hbnyZsy6vdC5vPMl060MMsdtGsgleW4
t4JPssJl9TF0q9WE4RVFfDK95PlcrXXwqy3fX0Z+a8b+J2WcD5zw9k+Oy7HYuefVLfWMNyKnhKTx
FLDKpyzvLE1FUqc0qFPllGmk+ZynCMvrI6Xp51NdZ+yQrqosjpxvkXZcSWBm+0LaTuhX7RBFcF5r
eOfzFtpZrh7cRNczmTi5pcvJd8t+a3S9rXXZ20dt9L7I/Rfq1D6ysX7KCxKo/V/bpWqSoc/tFSnJ
W56cajc6cZ8ypynUlDldSfN8wePvjlD8P/jXpvgrWPDOkL4a8RWGiNqXiBo9upvLez3Fpa307kGG
50/TZoFt3gljeZEimlhmUqkJ7sBwBkGYYDOc8pZXlks3zSdGObVHluDlVzaGVUqccDSzGs6LrYye
Fo8scF9YnUjh4qEKUYJXXxedeIeYZJxPleQYpVFlHsacqFf6xXU8JUx9WpCpWw8Of2NOj7WNsRGE
Iznac3Nu0XB8Nv2kNR+JnxcvfB2i+HrQeDYLbVZodYL3LaoYtOASHUp/nFpHbX85jjhtPI86NbiI
tO7q617mZcOU8symGMrYif1yUqUZUEoqknU1lTjpzuVON2583K+V2ik0zzsg49r8QcT1sqwmBprK
6dPEyjinKo8Q40NIV56+yjTrTcVGny80VOLc200fjD/wXf8A2FLbxP4Wj/a9+HekhfEPh37DpPxV
s7G2Uf2hpT7LPSfFMxjUMZoXFrod8WMjTTyaH5ESPPqVxJ8/4hcFR8c/CTG8NU6Ma/iX4TZXnPFf
hvit8ZxBwbhVVznjnw5doyrYurg8NDHcbcIYePPOljcJxHleHjz5/SVP/Sz9nn9Jut9Grx6wXD/E
GYSoeDfjrnGT8LcY0a039R4U8QsU6WUcDeIFNTnGhgsPmGJng+DOMMS/Z06mW4vIs3xlV0uGYwn/
ACUAEnA5J4AHUmv8sEnJqMU3JtJJK7beiSS1bb0SW5/1TSlGMXKTUYxTlKUmlGMUrttvRJLVt6Ja
svahpWp6TKkGq6bf6ZPLEs8cOoWdxZSyQOSEmSO5jjd4nKkLIoKMQcE4NfT8V8EcacB47DZZxxwh
xRwbmWNwNHM8Hl/FeQZtw7jsXluJc44fMMNhM3wmDr18DiJU6kaOLpU54eq6c1CpJxlb4zgfxI8O
/E7LcZnPhtx7wX4hZRl2ZYjJswzXgfinI+LMtwOcYSNOWLyrGY7Icdj8LhsywsatKWIwNerDFUY1
abqUoqcb/WP7EP7MHiP9qv46aD4C0exubnSNNhvfE3iqe3i8wwaHoNjdatcxLuSSIz3q2LWdnFMB
HeXs1tp29Jr2En+hPoqeF+XcV8TZ14l8ZZX/AGp4a+DdDAcR8QZXOShT4t4lxlXEQ4F4DTlGbqUe
Ic5wU8VnsKcXOPCeUcQShUoV3h60f4h/aPfScq+BXhLlvh/wfntPI/F7x7xeM4H4JzXWVTg3h5fU
aPH/AIkTUalJ0p8J5HmlOjkdaU1GPFuccP1J0sThaGLoS/oP+Fn7bvxp+E+p+HLHRr+wPw38Mw2O
jWfwtXS9MtfD9r4a08LBFpNjdpYNq9rfJApZtcmvLnVL2/L32sS6lLNcLN9XnHi7xtxHxVmnFfEG
ZPM8dnWZYjMcyo1KNClQqTxNWVSdHDwp0o/VKNGLVHCUqPLSw1CnSoU6aoU401/BNb6GfgnT4Fw3
COR5HWyjG4DLYYXLuKaePzDF5t9cp0VFZhj41sY8LmLxNVOtjKNWlGFV1KsqMqFaarL501PQdW1/
xZfywrcfZ9b1XU9Th1nV91rb/YJpptRm1XU7qTelusNm5vL9mZ5IyJECySgI359Uw1bEYubSko16
tWoq1a8IezblUlVqzd1FRhedRttqzSu9D+icDmmAyrIMJCo6XtcswGCwVTLsvtXq/WqVOng6eBwV
CHLKrKpiIrD4VRjGM04SbhTfMv1U0D/gp9oHhvUvC3hCx+G+oXvw80Cx0bw3P4mvtaEPim7sdMtb
fTW11dFis7iyid4oPth0uTU5pZMmI30chyP0/D+JdDDVMLhIZdUnl9CFHDSxM61sVOFKMaft/YqE
oJtR5/ZOo29udM/hDNvoQ5tnOCzziHE8Z4TDcXZrisxzmlkmFy11Mjw+KxtetjFlbzOeJo4mcYzq
/V1jo4KlThZTWFnBa/qj8QfEdjp3wx8V+Jo45dTsB4R1G+tYbOD7VPqUd5prizgs7b/l6nvmuIYr
a36zyyxxfx1+q4mtBYOtXXvwdCU48q5nOM4e6orq53SiuraR/mzxF7TKsBnMcVRqKtgqGNo1sOoc
9X29JVKMqEaevPUdVezjD7U7R6n5B6/+0Z/wUT1zWdRk+GvwW8TeC/BmmXDaJonh3/hWY1WawtNL
VbeFLvUtasJJr68MIi+0T2aw6aXGyzhVUYt8RUzXiipUl9UwFWhQg/Z06X1TncYw0Sc6kW5Sta7j
aPRI/njF8XeLeJxFV5Nw5jcuy6jL6thsJ/Y31iVKnQShFVK2IpOVSpy8vPKmo0b6U4pJt/xJV/n4
f+g2FABQAUAFABQAUAFABQAUAFABQAUAFAFTUL+00uxu9Sv50trKwtpru7uJDtjgt7eNpZpZGwdq
RorMzYOFBODiu7LMvxObZjgMrwUVPF5jjMPgcLBvlUsRiqsKNKLk9Ip1JxTfTc+d4v4oyjgjhTiT
jLP60sPkfCmRZrxFnFeEHVnRyzJsDXzDHVIUo61Jww2HqSjBazklFbn563n/AAUI+HS3U62vgDxP
c2yyMsNxMNFglmjBIWV4RfTeWX67TI5AIBIPA/qCl9GbMvZ0/a8ZUIVeSPtIUstr1KcZ2XNGFSWN
pSnBO6jJ0qbkkm4R+Ff46439r1wp9bxP1PwEzDE4RVqiwuIxnFuW4bFVqCm1Sq4nDUuH8ZToVpwt
KpRp4zFQpzbhGvVSU3X/AOHhXgH/AKJz4iz/ANdNG/8Akn/9fetP+JZsd/0WkP8Aw1Vv/ngcv/FX
vh3/AKR+r/8AiaYH/wChYP8Ah4V4C/6Jx4i9v3ujdf8AwJ/p6Uf8SzY7/otIf+Gqt/8APAP+KvXD
v/SP9f8A8TTA/wD0LB/w8J8Bf9E58Rf9/dG/+Sf8/pT/AOJZsd/0Wkf/AA11v/ngH/FXrh3/AKR/
r/8AiaYH/wChYT/h4T4C7/DnxF/390b/AOSaX/Es2O/6LSH/AIaq3/zxD/ir3w7/ANI/V/8AxNMD
/wDQsSp/wUP8DxjCfD3xMgPUJcaOoPocC6HNTL6MWLnrLjGlJ/3spqv88wZtT/bB5LRTVHwFxtJP
dU+OMJBN+fLwurj/APh4n4K/6EHxR/4FaT/8l1P/ABLBiP8Aor6H/hoqf/N5r/xWJyr/AKMVmH/i
d4b/AOhg9L+Df/BVzwz8Hvip4C+KOl/Drxbc6h4E8T6V4ltLeLUtJtJJp9LuUuY40ufPl8li6KVk
8p9jAOFJAr9Y8D/C7E+D3iZkPH088hneEyzC8R5ZmWTU8FPA/wBq5TxPwznHC+aYKWJlicTCnGrg
c5rt8+HqxvGPup2lH+evpVftEsk+kv4C8eeC9XwjxfDlbi2nw/Xy7iGtxXh80jkubcM8U5JxXlWY
rAwyLBVK0qOPySguWni6EuWclzuLlGX7qeJf+DuT4ZeIvGmv+JpP2NfHUVh4ij+zX2lP8T/D8rfZ
TZWtsNtwPDqKbm2ubSDUrKQw7Iry3t2kjljR0f8AEuMfonYni3xF4p40qcZ4ahlnFtH6nmeRVMkq
15fUpZfgsIuTFrMqUXisHjMBhc3y+rLDunQzDCYSVWlWpU506n8QZF4sU8k4WyfIYZNVqYzJZ+3w
mYxxsacfrCxOIrvmovDTl7GvRxNbA4qCqqVTC1qyhOnOcZQq6F/wdzeCvDjeHJNH/ZO+IthLod5e
3Fy9r8UdCiGpQX1zaTTWk0Y0Dy2heK0SJ0nWeNiQ5TKgVxcMfRP4p4QlwhVyDxVrZXX4azDMcXi6
mByfEUFm+GzLGYCviMDiaSzZ0p4edDAwoVKWJhiaU21UdNOEUb5t4r5PnazyGZ8JU8ZTzbC4WhQj
iMZSqPBVcJQxNOliKU3hOeNWNTEyqRlRlSnFJx5mpM9jX/g8u+D+s/2Tc+Ov+Cfuq+K9X8O37ah4
e1a58f8AhCS50i53IUvdPOoeD799N1JRFFvvdNltGkaNGWOLYoH9W8OZVxPgsBLDcR5zl2aYxTnG
ONyzL8Rlca+Gu/ZRxeFnjMXTliIx0qVaE6VCq/ehhqOx+HZ7lnDmZ4zC4rC5SoxwklXw0M0WFzKr
gcX7vPWwGJlhqdTDpuMXGydaHLFOvUsmuf8Ai9/wePeBviR4Xh8P6H+yF8T/AAbMuqQXt3e2Xxe0
FzfWkVvdRNp84tvDVlN5LTTQ3OBOY2ktkEkT/KyfY5R7HLsVLEV6FPGL2UoQhNL3JuUH7SPMpxul
GUfhulJ2a6/LcUcO4zPsujgcJm1bKpLEQrVK1GM37alGFWLoT5KtKfI5ThU0nZumk4vRr9Mv+Cc/
/BQTwF/wUn+FWpfE7Q7HxbpeoeDdYk8Ga54S8b6iNT1Hw3qcNtZawYtM1GC7uILzRru21S3u7C5g
+yMzvciaxs7lZ4h/MP0zvEXxC4G4K4N4j8Nc2x/C1CXE+MwPEmJyyjhv3lavlsauT08XKph6tOeH
qewx65akOWpVhSjUTcaSPqfo9+GHDGa8a8R8N+I1DCcWzfDWFx/DKzGvinyYejmM6Wa/V4e3jVo4
ijKtgpNUqkuSnVnOnL36tvv7SYtXXxPH4W8G+Jj4WfXmtLS5lfW7rRLGa73SfZ7S91GA7/JJkQxw
3Ltbi5kAwJSgH6d9FbjLxC488HMNxf4r0o5rmFXOMz/sDMHlmEw+NzPhqhSwdKjmFfB4alQw3tXm
UM1w9KvRw2HeIweHoVnGpGft635V4+cMcJcI+KtbhHwwxjybCTy3LoZ1g/7Vxf1LB5/Vq4qpLL4Y
2rUrYj2SwU8vrToV8RWp0cXXq0uaE4+xpfqH4L+El8/we1j4XfFrWG8eW/inT9c0jXo7qe5vIk0T
XbL7BPo1tfXubyeOCJp5re7kSKS3nuMW6RpbwEfqlLiavlPE+W8T8Nx/srHZLjsDmOXVYQgnHG4C
vHEUq9SjH924ynCMatF80KtNSjU5lUmn7WScPYqlw3VyPiHGzzaWMp4mjiZSqVZ+zw+Jpqm8NSr1
H7aSpLmnTrPklCc/3aiqcD+GXxh+y9pHwI/bs8VfAr4kKE0Pw/4l1SXQJbpEtoNW06S7Fz4evFjY
eVHa3+lONUtI5P8AVWMlrPMisCg8rhDwY4L4a+lnmOd5bw5kue5HxX4ScQ+O/gLwbmFCOPyefFrr
xniuD55SrUM3xHhvmWX8eRy7Iav1j6/DhPJ62IoV4YuNKp/rx45/TP8AGvjv9kdjOKuBs7zzAeKP
A3H3D30f/pBcWZXUrU8/w3DeFy+dKfG1LMov67gf+Ii5bmHh/LM85w31eVLHcWZ7l2Er0p4eVWn7
j/wUWax8VeEPDXi7xprsGr+PdD0zwz4L8KzRaVoGiSL4T8P2tppVjp72fh7TtLtbj7Pp8Uskl5cQ
tcXsqvKXKWUUUP0/0l89yXxU+jx4hcU8XcE5Dw1i+CM6yHEeHee4TEZzPM8X4ica8ZYOpxnw/h8Z
nmaY7GZjl2a8JxzzPcbkVOrVwmRVOHMrx8YUsTiZ1Mf/AA/+xM8XPHF/TOybgXh3MMdjvDTi7gzP
/wDiLeT0sHSWR4PJeDeE82nwZxLXpYehGhgM3wfFlXI8hw2aRjDEYzD59icrrTlQxEPYfrV/wSg+
G+gfsV/sP+LP2u/Gnh641Pxj8TJIbbQtMjAtL5/D41mDSdO09b2eFmsINa8QxibV5TDcwLp3hvTb
+yjuppjDP8hmOEp+B3ghwD4ZTwsaea08Dg/FDxEpUpWxGN4949y3BV8myjHTkpezlwbwZWybKZUF
aGBzbG8SqNOpVrTq1/2rx24nzH6bX03eNcZk2dKnwpk2PzHwm8PMdiIutgMv4F8OcVmT4l4hy/DU
qkFi3xfxdSz7N8HVc4Vsxy7/AFbp4ivhsPhqdPCeDfEr4q6D8VvGk3ic/Cvwf4JvdT1A3V5H4NfW
YE1CeeYu8l1p99NrGkTX1yzfvpbLQrRLu5YzXFu7ySlv5SzHNcNmmMeJ/srCYKdSpzT+pyrL2km7
typzdai5ye7hh4KcneUXdn+hnBfAmacBcNU8jXHPEPE2GwOEVHDT4jWXVZYSlSpqMadDF4anl2YU
8LRS/dwxOaYiWHoRVKlVjGEFH6s+JHif4cf8KU03RLfRJrqC0sdHupNJ0zVtKstc8Lwxvd2cUGsa
gNB1Q3cF9dzyagQsniPTY9U1K6tp9M8KW8enqv2eaYrK3kNKhDDOUadKhOVKlWpU8RhVedNKvU+r
1eeNSpP2mjxNJVasoypYOKp2/DuD8m4u/wCIj4vMq2ZQoVcRiswoQx+NwGOxGWZ3UlChiZ1Muwrz
TBfV6uGw9KOEXNDJ8ZPBYOhWp43PK08W3wn7K37OPwg+PnjDT7C++LE+jXNo/wDaeofDS+0J4fEW
safZyI9za6R4nF5Fo99C6Z+03FtYx6pBb+ZL/Y0Uam5j8Xhjh7KM9xlOE81lRlB+0qZbUoWxFWnB
pyhSxSnGjUTXxSjTVVRu/YpLmX0Hjt4x+IXhRw9i8VheAaeY0MRH6lg+NMLmsamUZdi8RCUaNfMc
jeHqZjhakZfwaVbFTwNWtyU/7RqTkqE/2f1rSPjhL4j+JOlXll4Pf4OWOnfD6X4YWOjJcDxU1zou
qaHe+IbPVEdjE0QistRiij2iN4f7MjtPmN9n9wnTx/tMVTcaH9nwhhfqcaaftv3cqbqxmr2taMku
jXIo/aP8QszfFmPzTiCrjngsRk9Z5fi8ulS53mVbFLF0MTmLxV21J1JRxD6KV6Ps9XVPoBvFPheO
4urSTxJ4fjurCf7LfWsmtaalxZ3Pkw3H2e6ge5EtvP8AZ7iCfyplSTyZopNuyRGPvRw2InFSjRqy
jLVSUJWa2unbVX0utD3pZrlcKlWjPMsvjVoT9lWpSxmGjUo1OWM/Z1YOopU58k4T5ZpS5Zxla0k3
/l5V/lof9ywUAFABQAUAFABQAUAFABQAUAFABQAUAcL8UP8AknHjv/sUtf8A/TZc19l4ef8AJe8G
f9lTkX/qzwx+DfSm/wCUafH/AP7M34k/+shm5+Kf7Pvg34LeJdL+J0/xd8W6doOpXHhmXwh8J9Pu
tQn01x8UPEek+INU8LeLtWvY722srDwR4f1bwvp/hvxjfa5HJpNjZ+O7bVSWudLihl/02P8AkCPp
XT/2d/2Lda8QXdhZ/G/xJZeHdLs9cutT1tfG/gDxB4rup9K8f/FDwvoumeCPhrb+FNEuviDqfjPQ
PhdpOu6dp+j+K7TVdJuvjt8Mof7L13QNB8Q+JtUAKfwq/Zw/Zd0342a/4L+N3xisj4V+Gnxg8L+A
fHmpQeOPC3g/Trqz8OvY2vxF1HQY7mHUtf8AHvg/xp4zs/EHgTwf4j8Dvaah4F8OyaX8VPHFomiS
z6fbAGX8Mf2c/gF8SfhY/wAUtb8ef8IS0WneLNU8a6avxM+Hka/DiOT4n6X4X8Pas3g6407VfFuq
6Bo/hfV4tU0/wrdahpnxI+L2r2msaT4L0fR9N0aw8SeKgDsPCn7Nv7Hvj/x74H+HukfGPVbCbxT4
6tPhzp2sad4j8M6leyJb/FX4gaDdeM9f025tWtL5/GHhXRPDk/hGx0Gfwxp2g2XifwtcamPFsttq
+seIABfD/wACv2X9B8RfGH4M/EnU7Pwj4hufGvwWtfA3xI8YfEnRri48J+BPEWl6Lr/i7xBoI01/
CfhDxfYXukeIP7Z0nxHrcFhHqmgW2iGPw5oGq3epWIAOWh/Zr/ZIlD3ur/tRWfg6FLjQBqWiHUfC
XxD1Lw+91p/hP/hLNJtNX8ITW2n+PG8KeIfi98PdM0zxNoVhpWjeNtH+Fv7SHiTT7XSY/COjabOA
YHj/AOB/7LWmaJ4xfwL8UNWv/Emi/Da48a2Nr4n+KXweGk2viG31xtHuvAUd34QtvEkvj7xCi2F5
f+GU8HCKLxdpOr6Vq+uv8M4bG4ttRAOlh/Zv/ZFD+AdJ1n9pCy0XUvGWneExr/iLTPGPhHxj4W+H
l5LrHi6DxNrV7aWOg6PrviDR9R0Pw9oWq6L4evo/A/i/wrN4zstP8S2WpXukXNvfAF+L4Q/sk+KP
HXj3w3c+L9F8AaJouk/svS+H49N+LXgPxDq2qavc/s5a1efHHw9pPxI1S/0X4V6reRfFuO21DX/E
s0Fj4afVtLj8KeHp/D//AAk2mgAGUf2a/wBk2AeBZNR/aXs7Rb7w38L9Q8W2+keIfA3iV77UfHfi
fWNF1B/D90s+inwmuhaRpuneIvE3hzx3Yxa54Ag8Qpa+J55otHfUNWAOR1P4WfBXwzrOgXnw88S/
8JgmufD79rttd0PxF4o+Hni7VfDl34I+FXjS48H3jWHgRtX0q3sVZo7jw74vvdattS8XaxoGoeJN
J8G+DtDtfD194hAP6of+DXXwYt9+xV+1748tRfPqvhv9ojwRpwNs7rBaaXqPgOyF3dyrGN5ZrtrC
3WRmEcSu2QWkDL14bBZRnsa/DXEGV5XnOTZpRnLFZbm2Cw+PweLqUHCpShUw2Lp1aFRQcZVYqVNt
ThGUWnFH5d4kxzHLKGC4ryTH5llucZPUhSw+OyzFV8HXwlDEynCvWjXw0qdaDk5U6LaqKPJOUZJ8
2n9cmlfsyy678GvDvj6ODXU8WZutZ1jQ7JY5dR1vw0dSuZEOk2t88Ef/AAkp0lUv9Jgkure01XNv
YTPazXIvovRnxBRyXFzyjL8Jg6eVYGjh8FgaFCEcPhsFDD4enShh6MKEfZwwtHlVGNKEEqShaFoJ
RPgI8CV834cw3EeJnmNbO8RUxOY5lTc/b43MsPWxVarOtD6zJOpmdSi/rNH2lWMcXKUaVSUKlT20
frD4gfEVNI/Z7bxj8N9Yl8RRTaHo2maF4ih/0i6K3VxbaJLqdyvlK8Wq2knnJd280EVxZauj291b
wzwyQp85lOFp47O6dHGQjShOtWrVaPww92M6ypLX4HZJa609m73P0XPM8+qcESzTIsTLGqWDwuHw
eNj79T95Up4SWImuVOOIpvnVWEoRnSxKcJwjOEor+Vb/AIKQfAnV9ak+B/x3iN7NJ4h+I174E1vU
Fa4+0pPpI8Pakt/c3/mec99qEWrLa2zM6yKuiSFZAd2P2rMeAP8AiLH/ABBHC4PE4rLs28IPpAeH
uPq5pl2JxeCx2VeG3iLm1PDcV4jB4nA1qNfCPJ8+4V4fxGCrUXRnhsXxBiZ08TRnXSn+/wD0EPpC
vwR4Q+l/knEMcFmvD3i39GfxVxWWZPnmHwWZ5dm3ib4b8N18VwvgsZgczw+Iw2Mw2a5JxXxWs0w9
aGJp4vDZPQhWweIhR9z5m/aP+CWreOPjl+z34L0RJb+bxhfaB4Nhs3lmeGe8WWx0+3gRG3LG93DY
NFJKEBdpodwLDn5fj/hiP0ieJPo5Z7nixH+q2V+OnjHiPELLOZrAU+EZ0Mv8b8zxUcJTUsFh8XjM
kyzjjKa+MqQlUqQwmU4SVSph8LhqMPoP2Xn0lqXgL9Gr9oJHARwGC44p+HnA+c+HOa08Nhqeay4q
4lzLN/C3KKNbGr2eOxmDyzizjHgzNMJl8a3s8MnnOJpKlPE1qr/s6tPh38DPHPwp1X9lm11rRfEO
jfDrw14W8DeJNC0TVrBfEXhebS9PspvD+oahBZPLJpWstNp8WrRS3UDRXN9Fcx3cV0v223b8v47z
Gp4iZxxPnPEF62J4ozbMM3x/LUk3SxOPxtTG2oVJ3lGOFqTjDDxacYU6UKfJ7Nch+SeFHG78NM/y
jE8CZvl/9t8AwwmBqYWM6GJ9jSngXhamEzbB05qSpZlgp16deLdKrL2lWdKrTxNNVaf5u/tHfse/
D/8AZb+C/iH4geFbjxL4y8WX2s6X4a0/WtfaxNr4L0zWnuEvNXtbHT7O3hbVZIYRo9pqt2XNhc6n
FeadHZ6lFa3CfhPEPCOA4YybEY/CyxOMxU61LDwrV+TlwVKs5KdaMKcIr2rS9jCrO/s5VYzpqFRQ
kv8ASfwd+kTxX45eJOTcJZ/TyThvIcPl+OzjE5ZlSxKxHE+Py2FKph8trYnGYmrVWChKUszxGAwy
j9bw+Aq4fGTxOCniKUvjP9lb4TXPx5+Iut/DqS6uraLWPAfimd9YxcTxaNfWQtr/AEXULxY2Bezb
xEmnW93GWBuIbyeND5soNfH8L5XPPMwr5e5ziq2BxTdX3pKlOHLUoznZ6weIVOMk37ym0tWf0h47
cfUfCvg/LOMIUKFapl3FWRUo5delSqZjhcS62FzLCYZzi1HERyiWMrYeaTVKph6U5e5Bn05+z5+x
b8cvhv8AGvRfHfxEsbPwN4H+FWpy+L9c8XHWdOv7PU9M8PRy3s8OjwaddT388GoW0T/aZL60sktt
Oe5a4ja6RLCb6nhngfiDD8QYOriaEcNRweJhU9vGtSqLEOMrQpYeMJucvbNqLdSMFGDlze8lCX4F
45/Sp8Hsz8HuJ8HlGa1M5zDiHIcVhHltbLsdhJZJTq0HPE5hm9XE4eGHpf2XCE60YYOti6lbFUqS
oP2MpYqn8uftB/8ABUn4l/tCa98YfBXwp1G9+GvwX0b4b+N5tHvdMzYeOfFdzpklkml6/qevRhdS
8PQXsxQW2haJcWTLaXklnrN3qhlMUP8AWWX8PYfBQoVcTGOIxUqlPmUlzUqV73hCL0m11nJO7V4q
PX/mK4++k1xHx7juLMn4XxFbhzg/B8P5xPB1sM/YZ1mlXDSorDY7E46NsRgIVpteywOCqUbUqrpY
yrinJxh+fHwK+P8ArHw98LazpbTXF5Nqniq/166uri5aSee5vNM0e1llmlmdpZZXNgGeR2ZnYksS
c171fDxqSi7WUYKKSWiScnZJadT8E4K46xmQ5Zi8NzTrTxOZ18dVq1KjlUnUrYfCUpSnKTcpyl7C
7lJttvVn4y1/iyf+pmFABQAUAFABQAUAFABQAUAFABQAUAFAGR4g0eHxBoWsaFcMyQaxpt7pszoc
Osd7byW7shIOGCyEg4ODzivXyHNZ5FnmT51TpRrTynM8DmUKM24xqywWJp4hU5SV2lN0+VtJtJ3s
z4nxL4Lw/iP4d8deH+KxdTAYbjfhHiHhSvjqMI1K2CpZ/lOKyupiqVObUalTDxxTqwhJqM5QUZNJ
tn5M3f8AwTy+JqXM62PjLwVcWayuLae7/tuzuZYQSEkntIdNv4beVlALRR3t0iE4E0g5r+0aX0kO
ApU4Sq4TiKlUcIudNYHB1FCbS5oqazGPOk7pS5Y8ySbjFuy/5+cZ+ye+ktRxeJpYPOvC3GYSnWqR
w2KlxFnuGniKCm1SrTw8uFqroVKlPllUoqrWjSm5QjWqxiqkoU/4J7/FpGV08XeBEdGDI63fiFWV
lOVZWGhAqwIBDAgg4ORWn/Ex3h//AM+OIv8Aw3YT/wCeRzf8UovpPf8AQy8LP/Epzz/6Ehv/AA71
+K//AENngL/wJ8Qf/KGj/iY7w/8A+fHEX/huwn/zyD/ilF9J7/oZeFn/AIlOef8A0Jjh/wAE9/i0
ocL4u8CKHXa4F34gAdQyuFYDQsModEfByNyq2MqCD/iY7w//AOfHEX/huwn/AM8g/wCKUX0nv+hj
4Wf+JTnn/wBCY6L/AIJ8/FyGSOaHxh4GhmidJYpYrzxDHJFJGwdJI3TQgyOjAMjqQysAQQRmj/iY
7w//AOfHEX/huwn/AM8g/wCKUX0nv+hl4Wf+JTnn/wBCZLefsAfGPULqe+v/ABt4Lvr27lee6u7z
UPElzdXM8jbpJri4m0R5ZpZGJZ5JHZ2JJZic0f8AEx3h/wD8+OIv/Ddg/wD55B/xSi+k9/0MvCz/
AMSnPP8A6Eit/wAO9Piv/wBDZ4C/8CfEH/yho/4mO8P/APnxxF/4bsJ/88g/4pRfSe/6GXhZ/wCJ
Tnn/ANCRPbf8E7vizc3EFsvi/wCH8bXE0UCyS3fiFYkaV1QNIy6AxVFLZchWIUEgHpXo5P498B51
m+VZPR/tjC1s2zHA5bSxWOwWHpYLDVMfiaWFhXxlWnjq1SlhaMqqqYipTo1ZwpRnKNKpJKL8jiD9
l19Jjh7Ic74gxGI8Ocbh8jyjMs4r4LLeJM4r5ji6OWYOtjauFwFCtwzh6VbG4iFGVLC0quIoU6le
UITrUotzX7F6P/waUf8ABSrxB8PtG+JWifED9lfU/D2veFdK8Y6ZFa+P/iI+qXGi6xplvq9qy2h+
FCxm6SxuUkmtluGYOkkURmkCK/67x/jKnhzLi2jnWXZjjsZwZic2wmb4DJaNLGY2dXJMTWw2YrAU
a9fBrFOk8PWqwhz06telTtRpzrSp0p/5w8N1sNxP/Y0sDiaGHo57TwdXBYnHylQoRjj6UKuG+sTp
xr+x5/aU4SlyyhTlK85RgpTXlf8AxDE/t+f9D9+zX/4Wnjz/AOdnX8k/8TweDX/QJxr/AOGXLP8A
5+n7V/xArjX/AJ/ZH/4W4r/5gJ7b/g2D/b6uLiGB/iP+zJaLNIkbXNz40+IYt4AzbfMmMHwtnmEa
9XMcMjBedpArqwX01/BfF4vD4aouLMBCvWhSljMbk2DWEwynJR9tiHhs2xVeNGF7zlSw9WUYpvkd
jGv4H8b0aNWrFZTiZU4SmqFDG1fbVXFN8lJVsJRpucrWip1IJuy5kazf8Gu3/BRXSZ9QH/CwP2cL
Gey06WWWWDx34/VbvTNQki0iVrK6tvho0V7Y6hBqRjMkUrWd7p80wEksUhR/Vxv0uvDDL62aUMbl
vGdCeVYKlmVSTynK6lDGYCvj8Fl2HxmWYqjnlTDZhg8TUx9CvhsZh608JicJzV6FeouRT46HhBxR
iqeEqUMVktSOMxE8JFfW8VCpQxNPDYjE1aGLo1MBCrhq9KGGqU6tCrCNelWtTqU4u9v7Vv8Ag32/
4Jfa9/wS+/Y+8baN8VfiJ4T8b+N/jX4z/wCFneMZ/DRul8EeEtO0zw/ZaBZaBaajrdrpt3qJ0+DT
bu71rVL7T9NiW7nmtorf7NYpdXP7Tw7xjk/HHD2TcW5DWqSyfN8DHG4SpXUaNenFylCtRxMIVKkK
OIwlenVw2JhGrUjTrUakY1JJcz/L+I8sxGQ5lmOU5tClSr5ZUqUcU5SToOMYqp7ZTqRgnh6lJxrU
5zjG9KUZyjG7S/WX40/tXfC34I6J4P1zVZNR8Vaf45S+m8NXPgpdO1jT72x0wWX2q+j1U6hb6W9s
v2+2WH7PdTvMWYomyNmH2WRZHiuIVWnga2G9lRjTlKrOreEva8/J7P2cajnfkl7y91d7n5Zxb4jc
P8IYXLMVi3iMfSzb20sDLK40cTSqUsP7L2tVYh1qdBwXtqajyVJyld2jZNnlnw4+NPw7udR1P4o/
DHVWv/hJ4r1e0X4t+Erq3isdQ+E3jXUpFjsviRLpbuXtfDfiaRVs/G01sZ9KivrSDxjbXKCDxhcP
rmWUY/L5wwuOpcldRk8LWg+eliqUfipQqLRzp3vCL5aijLklH3qSPn8h4ryOtWr8QcP4l1uG8xxN
JcSZbUhGjX4bzWvJKlnksO23TwOPklSzWVNzw0atOGaU5rkzOcqv7Xn7OHhXxV8JNHtrTwFoXivw
T8N/Efi34n6/4BvvFmt+CptXu5PBfiq0stV0jxdpOl69qdpfeHNe1Sx8QQ6M8MGmarFpyWlxcRx2
dtZXP654KeKmD4CzriPHZ1jsdgqud5HleV4bN8Nw7lXFkMF/ZfEuR59OliOHc3x2W4DEvF0Molhs
LmSrvG5VjZ0cXhIwqSni8P1cd8NUcPw6q2WYNPL8mef5nmGXYfNsdkuIxmFzLIM3yzHU6GZ4KFfE
qnUo5jVp4rL58uDzHAzrYHEuWHf1er+Z37JHwLuvil8TdH1dtA8O6x4x+G178JvF2gfELWPEmq6V
L8O5fC/jeHUfHWoaV4RstE1HTPHGoeOdCX/hHrGz1i40S30GQHUIL6CWUyR/RcKeLPAOQcL+IOSU
MpzHLsLn2d+IWNyrJI5JlWcPGZTxVwljOGOFMshxHjsxpZpwdW4U+uY6eZ5jk6zDE5/luZ4jLMTH
EYaNSjW/nvwRwuaY/MM5wOWVq+GdZ8M4rMMRHO8wwGDeXZPxHgs5rYbHZLhKc8FxJGtisBgq2Cwm
acuHy/MMJhcyoTpYqhRq0/pn4PfDbx98E/8Ago3+0R408Tyy2Pww8ReDfFfxDv8AxTdO66Rd6J4w
8S6ZfaFZR9XuNX03XYL/AESCwhikvWOnzi1haO+tPtH8upOM5Po03fybv/wD7rhjIs54U8ceN81z
CUqPD+OyrMc7rZhUk1hqmEzLH4erhKUbXc8VQxcK2EhRjGVV+xn7ODVWlz/pivg22+JMf9tfE3Qb
a60iWC6j0DwDrlrBd2OkWF5b3NnNqfiKzm862vPFOp6ddSxTo4a28O2U7aXYhrw6pqeolahSxNOd
HEUoVqNWLhOjUipwnF7qUZJp39NOmup/R2RYnNsLm2X8VU8TjMozLK6/1vII4avUwuKyeqo1IRx8
61Gaks1q0akoTcZezwdCpLC0+aUsTXr/ADyLPSPhy+j2/wCxZ8Nfhdrepat450Wy+LCG6On3Wn+B
2e4F3qlneXmrabcXENpcMBA9lLq9nau4MGjXrygJ5dXIanD0aM8myTDYf63iaccXKS5H7BXcmpuo
pJxvdQvLl1aoy6d/iD41+JHiTLJaS4lnx1PK8xpUcXh81xyhRy3Lq37vFY3C0YTwdGdaThThPGQh
iqjSScK3MkuR/wCCin7Ra/BfwJ8M/AemXsMfiT47/E/w98P7i1yrXMfw+uLqGLx7qCRsCDE9re6b
oEpIBKa+7xMskO5PuOH8F9axVWtJfu8JRlWv09t/y5XrdSmv8Guh/P8A9IPxAXB+RcO5Hhq0I5jx
vxJgMinS0dSOQzqQjnmIjF3XLKnWw2Ak2r2x0pQfNC6/lq+EHww+zeAfif8AED4j6vcfDf4Za34X
Xwb4a8Z3+i317c+NPENv498E6zrPh74c6Sv2X/hLNZi8NaNrEN/LHe2egaFNf6bH4m1zRYtStZZP
0StVvUpU6cfaVYy55wTS5IunOMZVHryR5pK2jk7PljKzP8yeFOG/Z5HxHnvEGLnw/wAOYzLVlOX5
vXwdatUzfHwzvJ8Xi8BkGFXs/wC1MXHLsJi415RrUsDgpVsOsxxmDjiKc5bekfti+I/hpanwp8D/
AAH8MPCPgCzk32Vt43+GHw3+KnjTV71oYYr3xD4q8Y+NfCmqXl1rGqtBG8lho8ekeG9Jto7bT9I0
iBLeW5u08HGq+evUqzqPdwqVKUEukYQhJJRXd80m7tvWy7ML4s5hw7S/szg3JOG8qyOlLmo0854b
4e4mzfFVnCMa2PzPNs4yvE1qmLxLhFyoYSOFy/C0406GFwsFCVSr+MFf4sn/AKmQUAFABQAUAFAB
QAUAFABQAUAFABQAUAch8QPEsngzwL4y8XQ263cvhjwvruvx2rkqlxJpOmXN8kLsCCqytAEJBBAb
g5r3OGsqhnvEOR5LOo6MM2zbL8unVik5U44zFUsPKok9G4Ko5LR6rY/PfFvjSt4ceFviL4gYfCQx
2I4K4J4o4qoYKpJwp4utkGS43NKeGqSi04wrzwqpykmmlJtNM/MX4I6z/wAFG/2r9f8AEum/s4eE
Piz8Y9T8PWsOr+ItH+FHwwt/FcfhrTr65a3sXvk0zw5fvYW88ySQWX264a4uvInZXnaC4kX/AEMy
f6PPA+YYeawPByxtLBKjSxONxGZZhSgqtZVPYrEYqrmOHwscTiVRrVKdGLhKpGjWlRpKlRnyf8xW
f/tIfpV08wqYnF+M+IymeY1sRiMLleXcLcIujRowlD2lPBYRcN4zGvB4RVaFKVatUxE4upR+s4mp
XrKdT6T/AOGPf+C5/wD0af8Ate/+I/Xv/wAx9ep/xLVwj/0ROX/+JDL/AOfp4v8AxUn+lH/0fPPf
/EM4b/8AoND/AIY9/wCC5/8A0af+17/4j9e//MfR/wAS1cI/9ETl/wD4kMv/AJ+h/wAVJ/pR/wDR
889/8Qzhv/6DTmPGn7OP/BaX4c+E/EHjrxz+zb+1Z4W8HeFNLutb8S+I9X+Al5baVoej2SGW91TU
7pvBxS0sLKENPd3cu2G1t0knneOGN3VS+jXwlGE5R4Ew9bkhOpKnhc4xWLrunSi6lSUcPhc4rV6k
adOMqlTkpy5KcJ1JWhGTTj+0m+lFKUIy8d82pc84U4zxPCnCmFoqdSShTjKvieEaVGDqTlGEOepH
nqShCN5yin87/BPxN/wUU/aW1vVvDP7OPg/48/H/AMR6Bpqazrvh/wCCfwS1H4q63oujyXKWkeq6
tpXgTwNr1/p2myXbpapfXlvDatcOsCymRgp+c/4gx4Zf9Erhv/C/N/8A54Hvf8T/AH0vv+j05v8A
+I7wT/8AQyfSv/DK3/Bdf/ox/wD4KH/+IO/GL/5ztH/EGPDL/olcN/4XZt/88B/8T/8A0v8A/o9O
b/8AiOcE/wD0Mi/8Mrf8F1/+jH/+Ch//AIg78Yv/AJztH/EGPDL/AKJXDf8Ahdm3/wA8A/4n++l9
/wBHpzf/AMRzgn/6GTn/ABX8Av8Agtd4C8M6/wCN/HH7Iv7eHgvwb4T0fUPEPinxd4r/AGN/ij4c
8M+GdA0i1lvtV1zX9f1f4TWelaNo+mWUM15qGp6jd21lZWsUtxczxRRs4a8GfDOLUo8LYdSi04tY
/N001qmmswumnqmtUTL6fv0vZRcZeNGbSjJOMoy4c4IcZRas4yT4Zaaa0aejWj0PoL9mH/grv/wc
CfFK3s/gx+yJ8Yv2lvjPL4A8KpdQ+Afgr+zj4M+MniHw14NsLq102PUL7TvDPwZ8V+IbbQbO8v7H
TU1HUc2dvLdWNgLhDLbQn9czzMMZxJm2PzzO631/Nc0ryxWYYycKdKeKxM0lUr1YYeFKk6tW3NVq
KmpVajlVqOVSc5S/jDCZfg8Bh6WEwdCGHw1CLjRo03L2dKHM5KnTTk+SnFtqnTjaFOFqdOMYRjFf
Q11+0D/wdYXkjyz/AAL/AOCinmSOzu8P/BN2+tmZ2JZmJtv2ZIfmZiWJ6knJ5r8ZxngT4PY+rVr4
nw64X9tWnOpVqYfL44OU6lSTlOcng5UPelKTblvd3PuKHHfGOGhCnS4kzbkpxUYRq4uddRjFJKKV
f2miSSS2SKsfx3/4OsITug+DP/BS61b+9af8E/PFVo3/AH1bfs6RH9a4qX0fPB+hLnwvBtHBz358
DnPEWBnfvzYTN6Mr/M6JeIXGNRctXOZV4/y4jBZbiF91fB1EZ/ij9rP/AIOmPBHhTxH4p8d+Bv8A
gop4b8E+HtD1TVvFPibx1+wnq9l4a8NeHbG0e61nWNW13xN+z2um+H9KsrKB7rUdVnu7K3tLe3Nx
cXEaQB09J+CnhlPDYjCYnhr+0cNisNXwlSlm+c8QZwoUMTGMK8MK81zXGSwUqsYU+epg5UKrdOlL
n5qVNx5P9deJlVpV6WZLC1aNWnWhPBYLL8C5VKMnKnKssHhKCxCg3K0K6qQtKa5bTkn++X/BvN/w
W+1f9ov4G/tH/BH/AIKH+OtL12f4d3HhCPwZ4xtfAupprfj3wx8VbfxzbeJfDOvaT8OfD/8AYcD+
HrjwyklvqkGm6Cb238SiCQXV5ZyXUv6/4NfRkz/iHKMy4V8GeEZYzI8il9ZzHL6vEOAw2GymGe1c
XVjGniOJs4w9edHF18Nj63saFbEypTVabjTjON/zPxP8RckpVaeM8Qc2hGtn2GrYHneW4qrLMKWE
o0qFaNSnlGCqxjONDEUKLnOFLmjyKMm4tr+g2H9pH9ju++HmhfCr4d/DO5+JHgnwhoXiLxXBofi3
RLjwXo2jeG/Dltfa74h1LRvEPxej0WPXfFFwbm5bTNG0i9m1DU5rm5ae903T7e5uYv3TIfop+JfD
9oZzjsq4LprGZfkmBhgc0pcXY2vmucYuGHwNDGYHg7E51isuyx15ynj80zCMKWFShGlQxVetRoS/
HKvF/hviMowuQ5Xkv9v5Xl+FxeYRw2PwdXLcLhcJg4VcTi62GxfElPCfWcfLnm6OHw05VazqTcqt
GlCpOPuemy/sQ6RcT+CtNl+HXgvWvF+iw6dqWl2V9N4S8QtY6/Yxy/2Jqur2FzZXenXU1vdIk+lX
OqQzpMwjeHzMCvwzM8Bxd7TEwxqxuNp5di8RRlVjL61hXWwlWpQqVsPa8a1LmhPkr04OE4e8nZnv
0JeE2Dq1Mow88hynGZlho4fEYejWeW410sZSjL6piMTRqUqlGpKFRKWHqYiM1JpOHNY6SbzfAvgn
x18Btd1a81KGD4YeLdT+Fmv6nK9zf+IvAun6XJZ3vh7Ub6QgX3iTwK13p9he3B2yan4b1LQdWK3F
4muG2+ZrL2yeIjFK8uWvBKyhUabU0t1CrZtdIzU46Llv14jmynJM94PxeIqYiEeGs3rcPYzESdSt
jcppYKpRq4OvWf8AFx2TurRpVJ6SxGBrYPEe/VWLdP45/wCCbMefG3xLm7R+FdIjJ9PN1Z3H6Qn8
q5EfiX0cI/8AC1xLPpHK8JFv/Hi5P/2xnt3xg/ab/Zk0LW4Pi9448e6DrunfC+41vQ/A/grwrrdh
r/iHxf4yivrVdR1e68N2F/5TW/h3UNMjg8JT+JDaWFjqcd/4qilhZfDuoGHKPxN6LRJdX6flf1P0
7ibj7gLB4qHEubZxg8XQyCpisJlOVZdi6ONxuZZpGrTVfE1MDRrcvJga2HjDLpY72dGjiFWzBSh/
sVYxP2fv+CkPws/aU8a3Xwrh8A+PPC+r6vpGvXGlySrZa3Z6hY6Zpl1fahHcT6TILnSr1rCGZ7YS
W09nJcBLUX4uZraOfbCt1sRQpQcITqVqcISqyjClGUppRlUnL3YQTac5S92Mbt6I4+DPHPh3jzNa
nDkMnzjL8TisNjJ4eU1SxVOtRoYepVrxnPDS58NV9jGTp80J0pTtTVZVJU4z8v8AhD+0l8NPhBrm
oa1rvhD4uatH4j8G+HvE+gX/AIJ8N6T8R9L0vwF4xlnv/D83jO88Da5rCeGfFt//AGLM114c1FIr
iwitWcNOtwjJ/XucfRj4szCjCllvF3AqrYTNszyPG4TiLM8XwbjK+fZJ7GnmtLIqHEWCwrz3KMO8
Zh40c4wjjRxEqvL7Gk4NHj8LcVZHwxjMRi8TgeIcUsbluExmDq5bgcPm9DD5TmM5VsJLMqmV4vEr
B5hV+qvnwlZQlSjTbXMppr5//at+Of7Onxi8beFvix8FfjJ8MvBHx58KJbaQ4/aP+EvxL1zSrHRI
XM9uvh1dc8L+JvBXgvVLa6u5pbuePwu97qf2lrxdY017Lzrrko/RZ8ecjwVWtX4IjmuTSUqqx2Qc
U8J4qnO3MnPkhnVLF4inJQtFqHLF2p8rnNQXwHifjOGuNc8yniTgbiHh/Acd5f7PBwp8ccM59mdG
GEi3OnHLKWJwGZ5XleKp1Kk51JU8qnWxHtHWWLw8qPNV/O34s/Az44a54yt/iX8Dfjn4M+IGu+Ko
4Y/EGmfDf9o7SG1rQdYtW+zJBoVv4k8Q+FvFUngS+gENx4UsLywS98O27XHhe5huIdHtdY1f5fGc
Acd5NQ5s68PeMcBg+R1aWOrcLZpWy2tS1blDMcJhMTl9SUVrUlTxEotONS6U0l/OPGnhd4lU88ln
XDPEOFzPNMY1SzTA5NxvSp5hluOot03Rw1DNMZlWaRyirBQqZdhsRh4YjLoOpltWnOGEpYvFe1+D
P+Cev/BRTxboFrr+reH/AICrdX7PIJPiVo/wi8W+LrmL5dlxqGux+C/F016HO4Rm91y5vE2ssixx
+UD+e4nO8qwtadCp9dp1KbtOEYYijyS6xdOpOlKLXVciPoMp8APpA5pgaWNxWX8Dxq1m3fiLC8K5
nms46WniMasnzWdW+qj7bG1Kqs1JRXKfy6V/jif+mEFABQAUAFABQAUAFABQAUAFABQAUAFAHlHx
3/5In8W/+yb+NP8A1HdRr7Lw7/5L3gz/ALKjIv8A1Z4Y/BfpT/8AKNPj9/2ZzxI/9ZHNz9vP+DLv
X9Z8JfCv/gpT4r8O+GB4w1rQX+B+p2/hyO/GlXWsw2Nj8Rrm7sbO/wDsOobb1rVLh9Pt5Lfybu+E
FrLPaRzvdw/6tSt/qhRu1FS4mrKUrXUV/ZmGSlJJq6hzNvX3YuTV37r/AOM7OsdjMszPNMxwGXvN
cVgeFo4qllyxH1aeLVLMMRUrUaNZ0a6jXnShL2EHT5a1aNKlOdKMnVh/b58BP2nvDPx0i1OC38Oe
IvCOraNNpljfWuu25jsJtW1G11K8k0bTb2aOzvJ9T0q20x59Xsr7StMubGG90yR4mW/gJ+OzDMsv
yvGZXgMdjsLQxWd4jEYXKKMqqVXH18NhK2Oq06MHrKccHhsRiLRco8lCp7/MkpRwHxvR48w2aVsJ
lGaYCeSwwbzP63Raw1CrjJ1Kaw8K8o0qsq9CcaccTSr4bDVaX1nCydNxrxZ7/r/iLSPDGnvqmtXa
2dmjBDIUkkJJBZiEiV32RRq808pAjggjknmdIo3YY5tnGAyTDU8XmNWpSo1cXg8DTdLD4jEzlicf
iaeFw0XTw1KrOFN1qsPa15xjh8NS5q+Iq0qMJ1I/dxhKUK80lyYbD18XXlKcIRp4fDU3UrVJSqSj
H3Yq0Y35qk3GnTjKpOEZfFXx9+KmofEL9nP9rDS4PBj2fhS3/Zo+N93B4mm1e31O31HPw/8AEOmp
YCPTLO80UXovrbXotSsF8QXFxY2dnpN20VwNamh0n3vD7NZY/jnhqjDDxjSpcQ5FKVX2yqNc2aYV
U4yjCLhGUnGrePteZRjCa5lNqH59nWcYjMuH+KoRy6dHArhrPKlHHyrKpCu44GvR9nB0ac8M6sK0
MVGvTjjJzowp4eo4v6zKND+N3/gx6mFjaf8ABUrVEhgaayh/Yp3XEkJlktrGSX9rGS/ZBHtmeMR2
8c8sCSxLK1tEzt+6THDOTjCUknJpXstW+9ldJu212l3aR9xXqOlSlUXKlCzlKUZSjCmmvaTcYe/J
QheTUdXY/ue+H3x58J+O3tFtrvT3tdUurm10LX9L1GLUPD+uyQSS+XDZXeIZUvZIIpXlsnjka1mt
b62mlD2uZPLweayxOY5vltbLc1wFXJ6+Fw88Rj8FPC4THvE4aniPbZbVlKTxOHpOpGhVqyjTSrtR
SalFvx8v4gy3H4TJ8ZRzPJ8ZRzuOLlgquVZjDHQTwuJq0I0MWlCnLC4uvTpTrwwz9q4wp1lOalSa
fqHinxVo/hDS21XWLgRRNNb2ttCrR/aLy8u547a2tbZJHRXlmuJookBYAvIiDMjxo/qTmoRbd3aM
pcsU3JqEXOVku0U23slq2kehj8ww2W0PrGKmoRdSnSpxvFSqVa1SNKlThzOKcp1JxirtaySV5OKf
zB8RfixpXxJ+CX7Rel6a2ltcaH8KPiRba9oqXL3upaPbXnw+12azfWcRR29ldX0yvGmlTIbg2m28
3tDIBXl5Lmss7yzAZtDLszy2hmEK1Slhs4wNXL8dTjRrTov22Gre/DmnDmho1Km1NSacb4Uszo1c
4zXJqWOyfG4nJ69GjjllOYRzOnRjiMKsRRnPE0YRowqSk/ZvDv8AeRSc9VdL+GL/AIMpNY1Lw94r
/wCCmWu6Ros3iPU9I+FP7P8AqNnoVtKIbnVprPXPjFOtjbyFJNs9xsMcWI5GLkBUdiEb0cRUnSpT
qU4qco8rtJtJR5lzydtXyQ5p2WrtZPU9vDUoV8ThqFWvTwtKtiKNKriqqbpYalUqxhUxFSMbOUKM
JSqSinG6i1zRvdf3Y/Cz9rPwd481TTPDHiXSL/wB4r1SLTRY2eq3EF7o+pX+oRur6Zp+s24j231l
qEcujXdnq1npV7BrKrpMtsmpuLQePgeIMDjasqVOrRmlVnRhXoVoVqE6tOfJKnzwb5KkZ3g4ttKa
cea9kcXEGLw3DvF2N4Sx/wBbw+IovCzy3G4zBVcFhc6wmOoRrYXGZe6zbnh63N7KlWTlh8RUUfqd
fE06tKpP3f4gfEXwh8MPD8niXxnq8Gk6Ys8VpAXy9ze3k5PlWllbJmW4mKK80mwbLe2imurh4reG
WVPVxWLo4Ok6taVle0YrWc5fyxWl31bbSS1bRy5nmuByfCvF5hXhQoqUacXLWVSpL4YU4q8pSsnK
VlaEIyqSahGUl8xTfHy7+LvhT4l6VpPgDULLwe/wd8c6tdeItT1Gzee0uH0jU7a00jUdKtkuIYLj
ULRo72OB795zbyF/s7wK848bI+J8m4iePp5VmmW43F5RmNTLc3y/C4qnicVldeFJzUMaqcn7Cs5W
g6E4Jp865+elOC8/CZln2Ix+XxxPCmbZXkmbZKs5y3Ocxi6FPGUp4j2VKNClyOE41abjWhOFeUnC
UZqm6Uo1T+B3/g0p0Txhrfwu/wCCtMPw71iz8NePI/Av7KTaB4lur+DRpNLhTWP2grzXI7bX52iG
iTalo1ld2MWpfabRLS6ltrqS5gFuJ4sOM8zeVcOZpiqma0snwOHwlbG4/G4rGLA4GhhsFH2tbEYy
vUqU6FKnRw7xFq9aSjRU5y5opyZPFOScQcQ5TiMo4TpVqvE2YRjg8mhhYyeNq4mviMOpYXBzgnUh
iMXTjLD0ZQcWqs6cnKPKpR/tm/YbsNS8a/AaHQvjZrvhr4teINA8a6hqmn2Wv3lh481PwpprW9tH
okOp3upLqBbU0vU1690y/juLowaZfw2VveqLeS1tvz7wR8ZMk4uyvM8f4c+ItPNa+Eq1cszWtw7n
OLw+Ow2ExXLKGCzFUp4bFywuLnQqVaPtYzwmJjTXs5VPYtQzwXhL4k8AZDl+UeMvDWLwmZYnF18w
ymlnsMPmfLhIRoxpqGN5sXhq2Kw9adaclTxFSvhKeJpUajp6U4y/Hj9gbwd8Z/H+ofEOx8Z6r4N1
XXfsr+ILKHSrXWNPvbq1tYbJb60R7vT5rG4nt7eL7WrS3MM86m4VIpJJfM/e8m40xWU4KGBnhKWK
pUeZUJOpKlOEZSc3CTUZqcVKT5dItL3btJW/LuMfBnK+Ks6rZ3SzXE5XicZ7N42lDDU8TRrVKdOF
JVqadSjKlOcIR9peVSM5LnSjJy5tbxL+zPceEvgRqnh2X4xfETWk+Hek6x4t8IXl5B4UN1od/o+h
aiFsdPvLjQbzV4dDvtPlv9D1DSZNXmtZtC1S/wBLUR2s2xfEzLOFmGKq4pYDCYWVaEoVo0XWtVvr
7SonUUHUTtLnhTg3OKlK8tTfNOAp5XwTjsDLifO8WskwOOzLLKtaOXe0wdbC4DEWo0KssHVxMcHV
oyq4SvhpYmdOeDxFfDrlhNJfFn7C2l634o8VeOfBdgz2Hh7X9D0ubxzrMFw1vfL4X029uUuPDeky
RHz7bUfFl3f2mn3OoRmN7Dw5D4iksbmy1x9Gu4fD1vbp1/y+Z+I+A+HxWYZnn+VUXKjg8ZgsHPNc
VCbhVWXUK1ZVMDhpR9+nXzGrWpUZ104yo4GGNlRqUsW8NUj99/FbxdeeA/HPwU+B/h39na18c/Cj
xvcxaP4gvbXQI7nwr4KsftsVgjSaYmk3WiW0Gk28z61f/wBpG3SfT45Y7VvtBeRfLxuOxOGxuBwt
LA1MRQxU+WtXgpctBOSjd2i4pQT9pLnavFNR1P6Nz3GPK8w4b4XwnB9LM+H8ynHDYqUMHGeX5bR9
oqSvh1h6mFhGhCTxNX23Ip0lJQfPdr33w18Ovhd8MYdV1Hwb4D8D+BIpbeW51i78L+F9E8PNPbW6
tcSNeyaTY2rzxQhGlCSl0TblFFer7sU3okk23totWz6/JuGsny6uqOQ5FleXYjHVKdHlyzLsJg6m
JqVJqFOnJ4elSc7zcVFSbSdrWPk3w7+1N4D8MT65baH8KrXw9pd9qF3qcZ8Nro+ly6tqNxJ+91HW
rS1sLOD7deqBJdXnnXtxvARzcY80RmPF2a5zUo1M5x+aZrPCYangsFUzHMMTjqmGwdBNUMJRliql
V0MNSTfs6FKSpUk2oQP6So/RvngsNRWW5vlWEr4ip7fM6NPK/q2H9vVV61alPDS5sVV5vdc69KlK
tbnlOn8B674R8SfCj9pzSdW07xf8N/Dutto/kJd6H400TRPFVr9kvfP+z3NnLqFhIhXzIp1cC3gk
t5drKT5ivXs8L8bcTcPYiWM4X4hz7hvGQlGTrZLm+OyyrJvZupga9CU01HlkpXTS5ZJxPy/xN8Jq
PDjwVDPaGU8RZdmEK0aFbE5dSqRjVocjq0amHxUcQqckqsJ05QqSU05NcsotHwx+0h/wRm/Zf+MN
nqOq/DCLVvgL48n8+eHVvB19qUnh66uZFXC3+hSX8clum5BHbtpV9a2Vgs08h0e/GyAfpVHxSWd1
Ki49yzHY3E4mk6MuOPDzPMZ4U+KeBnPliswhxbwfHB4XiXF0Ic3JS47ybiilUV6UJ4VVa1SWXBni
J4g+HKwdDhjOMn4i4dwValU/4hr4w8MZV4w+FuMoUnOTy2HC3GtPMMTwxg8ROSlXq8B5vwri5TjT
qTrVXRpRj/Ox8Zf+Ca3/AAUO+DfjvUvBej2Pjbx9o0Ecd7pPivwhJ4g8RaPq1hNLPBFOt5Y6ddTW
9wGtXWWz1WHTdVQCOefTYILi2km8nGcOfSuzmt9e8JvprVc54NnCMcGvF/xKr8KcfZTXSvVyfPMH
nEc6hmFXCU5UKizrJ81xuUZi8RKpSlQxkMdg8P8A3twR9KD9mVi8goVfHD6B2ScE+Intakc3y7wk
8KMq4k4BzCKUHTzrIMTlOK4deAw2NqSrU/7GzPKcPmWWfV1QnVxeE+p4yv8AjxX+OR/0zBQAUAFA
BQAUAFABQAUAFABQAUAFABQB5R8d/wDkifxb/wCyb+Nf/Ud1GvsvDv8A5L3gz/sqMi/9WeGPwX6U
/wDyjT4/f9mc8SP/AFkM3P3v/wCDHoA+Hf8AgoKCMg6z8BwQe4Nj8Rsiv9Wpa8H0k9nxJiP/AFV4
Y/444/8AJVVf+yfof+rHEn9aPiDStb8JeI28OeLPHVlpkunavZa94Hs4fFf2PU9fMWqTf2bpN1Y6
fHqOuPp2pXl8ttfRz2SKuoC2tNK1BTfOi/xBwBmvEnhpx7mnAnifxxwxHIcZUjjOAI5txNlFPPcX
Sxua/wBkZdSwmCxeLhnVbD4l5h9QxeCx0XgKGZ0MFSyjE1qdath6f63x1w9g+L+F8FxTwTwzn8s2
y+oqfEs8pyfNauV06uDwNTNcSsbiMuwWIwGHlH6q8bDFUVSxU8HWxE8eqM1Sqz8o+LOm+I/idL4W
8T3/AItm07xb8Pta8dt4Ig17UrbwnovirxRa+KNcFppV3JezW2s6LAl5o2haNeXGkagTY+H7zXtG
+3aX9timvv6EznLMJm2bZXiMZj62Bo4DEqSp0pRmqtKWIhOap1lFSVTkh7N1oSs6LqU1GCrNy+c8
J/E3xIyXgPxVyThTgPC8Z47Pssz7BU83zKjj6dPLMfhsRnmAwUMTh+eph6+DoTrUa1LLZQi44zD0
cXPEP+y8LB9hYeDvjR4U/wCCfn7VF18edV0C+8d+Kfg58cdeGneGYNCj0jwzog+EF7pOnaFDP4fg
FjeXHm6ZeaxfTC/1sxX2r3Nmmt6tHbLf3H6nwRgsowvGvDtXKMHRw1PF8TcPVMRiKcY+2x9anjsF
QjicTUTcqs4UKdHDU5VHzRo0YQtGKUV+B8PYTjvCeF3Ea8QcxoYzOa2V8QYjD4LB/VVgsky+eVTj
TyzDfU4LD3liYYrMcTyVcWli8fXh9cxXL9Yqfyd/8GMihh/wVGVgCrD9idWB5BBH7XAII9CODXhn
7I1dNPVPRn9Rnij4a+P9I8SeJ7bxI/g/RynjCG88N6P4Q+0W6Xs2tO5n1S4PhLwze3vhi4+xwadf
2Ot6ibKddek12PVNmnaxpuoW/wA9ledcub5jlOZZnllXHU6NKpQwFPF4WpmFHCclacHPCVK1KpLD
1KaTjSqU5VJRjVnTtRowU/lvGHhHgChkHBGO8Ocr4gy7MG8Z/rjVxeExlDKsXnmJnl0aFbDYnLMP
mNPAZhCvHMqrxmFeCy+lhKuUU8Up5lj8yq4Wzr2o+L/iVd6Qmu+L3vtM0q9vtNE2oLq1zANahv8A
UbtrhYNF0HUtKs47+1srQG+1eW3t/D1hFENNijvtXjlt/ezLMMHlWBxOOxdXDYClh8EsRUxOLrUs
PhqNH2cE6letXrU5KEZz972d3Xek2401GX47goZrxfm+UYPHYzHZng45u8FUpYWjisVi6taOOr1K
6wWHwmAxODhXr4SjFUauNnToZfSjT9goVcX7Sn7P4a8I+MPDH7J37R9x4003wlY3+veBfivqVlc+
Fjpc76vYt4H1cTaprN/pVpaR397c6s+p/Ypb3z9Xj0ZdOh1qZtXjvQPJyPFYvG4KOKxWKw2MhVl/
seIwleniaE8LHSPsqlG9H2cantFSjT+Gmoqf73nP6r4nyfw4yfNYw8M8tzPLMpxGX4OtmtLNsOqG
Mq57KWIli61WVSviMXXryw08HHMa2LquMs3jmP8AZ8Y5T9QR/GJ/wY6jPxT/AOCiYPIPw/8A2cgQ
e/8AxUXxfr2T58/q++Ivwo8Q6H4o8XeG9X8UeC/Clk2svqPwv0e1eylub211PVLS6vdNvtlm11pd
vqGn2qwPcavrEJtdX07SL3R5NK0+1a3l/EOJeLPDTgHE5PwzmGd5Xw/nua4hyo4DEYmfNip4qpOM
KrcPbU8JSnKH7uri5YShTpRVPmtCKj73HvC+eeKXDWCzzKsBnea8R8P4SlQzjN6sI1aE8FhIU1PA
K9SMK0qiVPEUFg6NPFwrTWIxFSviJ1KkknXx78YZLnxB8Utbs4fC3wcg1jQPEN9q1mZ9Pi1rSlKX
8eoTLaQYs72+j0q/8QeItMhhjm0nTBp0T2YuZbkfVZrR4gzbKsbUyyphaePpZdi6WWYjHa4OnmH1
ef1atiqcZRqVKKxPsZYhRcOanFxjOL1X4BklDGcUZ9lcOIqeNzDLMpzKhluZ4LL8LVxOPxkliqVL
E4LBQpYepOeKxr9n/tOGoTlNQp0YwXtJN/Vnw60i4h/Zy+Knie6v/DGst4v8HeNNTtNe8LXOl31j
q2nQ+HdWjhle90cfYJpI7t75XWEkxSGRJx9pEzN8p4F8G8TcK8P5xieNKOUf6z8QZ3XzHHYzK44B
1cZh3FSoTzGvluGwuDr4v6zWx1VyoUrKOITqzq4mVetU/pfjvH5fis3o4PJ6Gb4HKspwGGy/DZZn
EcZRrYCrSUlVpUcJmE5Y3CUvZLDU1QxKp1YOi4qlSpRpU4/5yH/Bsn4x1jRtJ/bw8I6fcSW2n+ME
/ZiuNa8p2Rru28OXHx7ktrGUqw320l1qqXUsRBV5bK3JHyV/Gf7TrjjPMi4B8P8AgvLMTUwuWcb5
vn2Jz50ZzpzxmF4Yp5HVwmX1XGS58JVxmb08XWpSTjOtgcM38Fn/AE39C7hbK814s4o4mxtCFfG8
L5fltDKVUjGccNXz2WY08RjKakny4inhsunhqdRNONPF10viuv6nvDvinxN4Svv7T8K+INa8O6iV
WM3mianeaZcSRh1kEMslnNC00JdVZoJS8TkDcjCv8buGOL+LOC8xWa8H8S59wxmbjGnLHZBmuOyn
FVaUakaioVquBr0J16DqQjKWHqudGbS56clof6HZ5w7w/wATYP8As/iPJMqz3AqTmsLm+AwuYUIV
HCUPa06eKpVY0qqhKUY1qajUim+WaP208W/Haf4GfATwP8SPijoHinxJq13aeENL8Q2nhXS7a51S
LV9asfMnvr22nubC1tI42hdbnMkQOoTQ2cUaNOgT/pb8O864lp+FnAOa8d069bivGcLcPVeIvZYe
FKs82xWXUKuLqV8OlSp0K7qNvE0oxpxhiZTpwpwVoR/wb8Zs94Y4L4k4sxeR4TG4vhjD8VY/L8no
5eli5wwMsbiKeGlCpVqxU8LCnT5aNWdWUqkXQTlOVTmPjb/gpj+1B4c+Eq/s/eBrvRtQ8av8Ute8
SXGpfDyLxT4l8Cw6zon/AAh+q6fo03iTUfDF3Y+IYLTT/EGpW+v22mMjWuoXPhe8tbv7Lcw2txF/
df0cstyeEs8zvO61LLZZpWw3CvCuZ1OH8o4jxUM3wmUZ54i8UPB5Xn+Hr5Z7DDcAcDcRUs3x9WMZ
5fSzPA03zwx8qVT8245yzNM64c4lzTAYWOMyLw/4OxfiDxxh8VmGNyjC/wCr+Kzzhzw+yPDYnEYK
dLEPGZlxrx1w7hsuy6WmMlRx02ksBUlT/LXQPjfH+zzqfhfU9dufELDxBe+CPCcHjfRfF3iTw6/g
TWoPFmhXF942vPDGi3lvp3xE+36Wmo6SvhXxI0uns188YE0F7qFtcfWeCtXgbiyr4mx/1dyjJo5z
xbhlk+T4vKsgzfDY3LuPcVmnDHB/AMc5zlUcVwnhsNxfm3DeCoZ7w7SqZnisbjsvUsLTeCwmIw38
p+DeBznM83z/AAeRUqtTH5bwrnHGVatQx+bYbEYLJOA8JV4p4qzLD4DLaFahnFTK+FMvzvNa+CzW
WFy5ZdgMfUq4lNLD4j+kv4w/GGx+Fnh7S9WgsU168164MWkW8d2ILOWCOFLie+kvI47gNbpHLB5Q
hRjO1xGVZYwzj+LsdWlgbwqUpKsqkqUqVROnOnOm7VI1IyXNGUH7sotKSlo7Wdv9KPDvgapx5mGJ
oRx0cFgcFhqeIxOLhTWInL28nHD06FP2lOMnV5Zz9o58kYQbtJuMX+bOr/FHxhqXiHxXr1trGoaa
PF41K21TTob2Wazk0zUYJbP+zpIpf3MsdrZSm3tZTCktttWS2MMgBHz0sRVlOrNSlH2vMpRvdcsk
1y67pJ2Wmm6sz+wMBwXkGEyrI8srYDC4t5C8JVwWLqYenTxEcZhKkK/1uM4e/CdbEQ9tWgqkoVru
FZVItp8JZ2N3fvNHZwPO8Fpd30ypj93aWNvJdXU7ZIASGCN5G7kDCgsQDlGMpNqKbajKTt0jFOUn
8kmz6etXo4eMJVqkacalWlQg5X96rXqRpUoK19Z1JRiumutlqdN4T8e+KvA66r/wi2pvpE+swW9r
eXkEcTXQt7eR5Vjt5ZUkFuXd8vLGgnG1RHLGC4e6VepR5vZy5XNJNpa2Xa+3rv2aPFzzhjJOJHgf
7bwccfTy+pVrYehUnOND2tWEYOdWFOUHV5YxtGE5Onq3OEmo8v1p+y78SvG3iTxhqmh+JfFc2r6a
2iT3dva61ercag2oRXVt5Z0152N3KiWzXb3cSM8McaxyMiEK1enl1etUqyhUquUeW6U3eXNdfC3r
te626n4Z40cIcOZRkOCzLJ8kp4DFrMaeHq1suw8qWEWFnRrcyxkaS9hCUqyoRoVJRjOcnKClK7R5
t8ZPjf8At/eGfib4w0P4b/AXQNf8CafqrQ+Fdb/sLVNZk1TSDBC9vez31p4s0+Frmcszz262Vu1l
KWs3WRoDNJ5WOzDiWli69PC5bSq4aM7Uans51HOFlaTlGvBXe7XKuV+7ra7/AM/+IeJfFTB51mGG
yfhfC4rK6VdxwOJ+q1sQ62H5YuFSVWGOpRc5XbnD2cHTlem0+Xml/AZX+dB/33BQAUAFABQAUAFA
BQAUAFABQAUAFABQB5R8d/8Akifxb/7Jv40/9R3Ua+y8O/8AkveDP+yoyL/1Z4Y/BfpT/wDKNPj9
/wBmc8SP/WQzc/fD/gx5/wCRd/4KCf8AYa+A3/pD8Rq/1al/ySFL/spMR/6q8Kf8ccf+Sqq/9k/Q
/wDVjiT+yb4xfss6d8S/iR4c+JNpq97p2o6Vd6Q99aQ3slolzFYXPm/a7S5WK4fTNStylnOtzbW8
r6imnW2mXBtrcm5X+NfFz6LnD/iv4l8HeJeN4k4lyvMeFngqbweXZjKnSqUMuxeIzHAVMqrVVV/s
DF0M1rUsfi8TgaFSWZ08LTwNWOGVati5f0P4e+M2acB8JcVcGUclyXG5ZxLDGVnia2ApVMTHHYzB
U8vxCzSlzUYZ1ga2BoRwNKjiqsZ5fGtUxFJ4h0qGGjR179lttWZHn1T7bJd+N/iNqt6YruLTorfw
l4z8T6n4p07REE2j6nLNJpF5Lp8WIGtIzG+qIjlZ43X+iMflM8RKm6TS/i05vnUOWlJynCydOd3G
XKrWSS5t9EfmXhlxBmvh1lXE2X0a1DGSzXOs34gyv2uHrVaWCx2OzPEV8NRkoY7B+zoQy/F1qVac
YYiTxFLDzpw5YPm7L9qLTl0j9j39pDTVmluBafs5/GeIzzCISzMPhv4mLSSCCKCLczEk7IkHtnJr
7Lw3wE8rz3gfL547GZnPB55w9QlmGYPDyx2MlTzLCp4jFSwmHwmGlXqP3qkqWGoxlJuXIm238Rxl
NVeGeKqqp06PtMizyo6VL2nsoOeX4qUlD2tSrU5eZu3PUm+8mfxN/wDBjH/zlF/7sm/9+4ryz3T+
3bx/8D7PxdqviO+E97HH4l0trR306+GnX2nai8a27XqXhDSJAIkjmiktc3tncCSWzVJCjV8HT8PO
H8NxzW8QqFHEviGvRjRm54qTwWmEp4FzeEso1PaYajRpTp1JzoxVNzo0adWrXnVyz7GZznPC+I4M
q4iKyCupzpxw8IUMfQxU8Q8TzxxrvKnThWlOtCrh4wxcZy5XUnCnQjS5p/2c4IbrUr1Lye4ury88
czWENrdfZtLs08S61p2qaU+o2kw23EumxWTW/wBotonuLa3uL60txLBePj3+MOH8BxjkWK4dzKNZ
ZfjsLhKFephqnsMXTqYKSq4avQrK/K6eIjCs4ThVpVXRpxrUqlNyg/luGMhxnCOfQ4kyytGpmWHx
2f4nBU564CMM4xFKcKeMw00vaOnh41MP7ahyYihTr144aonNTj1HxY8OweFP2YPi9oFvI0qWHwZ+
JqvKxY+ZNN4Q16edl3ksI/OlcRKSSkQRSSQSa4cyDLuFsky7h/KYVYZdldD6vhY169TEVVT551Hz
1qspTm3Ocmru0U1CCjCMYr7bF4zGZjia+PzCpTrY7F1HWxVWlShQpzrSS5nClTSjCOiSVnJpXnKU
3KT/AIRv+DHT/kqn/BRL/sn/AOzj/wCpH8X69s5z+3z4x/s0X3xC8bP4r0nXrexttYTSLfXrG6s0
nnj/ALLYKLvTLlpEMDywRxRSKMkMPM2yKvkP/H/jL9F+v4mccy4sy/iajlWFzejkmF4iwGJyynjc
RyZPVbjjMnxcq1KWCxNbDwo4apH3otpVpc8abw1f9e4M8Vp8JZD/AGRLJnmFbCVcxxGV4iGPnhKc
KmY0ownRzCgqdSOLw0KrqV42aqRu6UEnNVqPlPxK/ZS+KGo+E/EvgPwh4j0q78KfEnx541vfGul3
l9cabbDwZ4kljfTrGaRbeS5eWyie8S6/soQXgn+xNaXXlRSAf1nlWWYDAYCeW1oTq4WND2GHjTly
OFOMIU6afK6aUlCC1ScVr7j91x/l54LxE4a4hy7ibgbOsJg82pcYYjiOrVxCjKhTi8XHGYVVqGJo
YiGJpwk6sMVQUL1ouFPndPnR9A6X8JtA+B37L3iT4ZeGnuptM8O/Dr4hSNdXtzPd3d7qWtWOv67r
N/PcXDvLJJe6tqV7csXbP73kA5rfD0IYejCjTvywva7bbcpOcm29W3KTbfmfqucZ3m/EeZYnOs9x
FHFZrjvYPFVcNhqWDwqWGw1HB4ahhcJRSpYfC4TCYfD4XDUopuNCjT55Tqc85f5qX/BsboFnqdp+
3frF1d/ZDoGn/s4vETLF/pD6hN8dYIbSK1cLJdTXF7DZKTby77Oz+2X0kE0FvM0X+ZH7TTh3BZpw
d4e53i8Z9SfDtfi+dJurR/2meZUeG6FHB0sJNRq4vEYjH0MDBvDVlPBYL69mFWhXw+GrOj/YP0Mc
4xOB4h4ryyhhniVnFLIIztCp+4jg6mcVamIqYiPNDD0qOFqYqSVany4nE/VsJCrSrVqaqf1mfB/W
/AXhvx/oWv8AxH03V9Y8N6PcrqB0zSILO4e7v7Z0ksVvYb27s4ptPimAuLm3EpNz5SW7q0Msor/K
TwSz7w54W8SOHuJPFLKs6zzhbJMVDMpZTkuHwOJqY3McLOFTL446hj8ZgqVbLaNdLE4rDKs3i/Yw
w1SEqFWsn/cviflPGefcF5xkvAmPyzK8+zShLBLMMzq4qhDDYKvGUMY8LVwmGxVSljalJ+xoVnTt
Q9pKvCUatOmz92fhz8Wvh38XdLlv/BWvWmsLbeV/aGmTxva6tprsQY/t+lXaR3MSF1Kw3axyWU8k
b/ZbmYxsV/6H/Czxr8NPGfKq2Z+H3EuFzj6p7NZlldWFTA53lUqnwLMMpxcaWLo0pyUoUcZCnVwG
JnTqRwuKr+zm4/458feGHGvhtjqeA4xyOvl6xLk8Fjoyp4vK8wVN3k8HmGHlUw1WpBcs6mGlOGLo
RnTlXw9Lnjf+Un/goR8WZPjN/wAFU7PwvBci60P4LWNp4T0+PzEZYLrS9p1+3ZQMCWHxFfa4MHMh
iutjNt+Rf7GyzM1lHF/AnA+GUIVeDfozeOHjNnk4cjmuI/GPKv8AiH+R88byqKvgeB/9XMThpfun
To59iqkE6VWq6vtcT8Lvhz9mz9JrxcxaqwxHjJ9IDwi8EMh5oVeWXC3hNiK/HOY1aM1yU/q2Y8a1
c8w2K1qwniuF8HGdq1Ckqfnf7YdnNcfA/X7y2LJc6Le6frcEqAF4ZNMaW6ikGTgeXPHFJkg8oODX
wnDWOzDB8E+OTyrEVcLmmF8IM14pyjEUeV1MJnHhzxXwh4lZbmFKM4yjKrl9bhF4qDa9xQnPWzjL
+MP2d2Iyqn9NX6PeWZ9h6OMyLi/jPE+HGd4HESlGhmOTeKHDmeeHOaZbVcGp+zzHAcU4jAyjFxc1
X5eaN+Zf0F/snavo37Yf7BXwO8U6hri2Gv8AhnwrFot3rV2fMXTtT8I2x0WZdZkldDNDqGhWulan
f3Hn+ckk8dxcO1zDcQNv4vU8uzvif/XDKKdDDZH4kZJw/wCKGSRwz/2bD4TjzJ8LxFi8BCm5OWHj
lObY3Msnq4apyzw1bLp0uVRpq/8AXnBlXiHwI464t8Oczw2KzTMvD7i/ibwqz3CVU443MqnBnEGM
yPL8yw84QcK1fMsFhcHmmDr04To43DZkpU9K8Zw+e9U06Kw1W50201Ow1qOC4+zxalppulsLs7gu
+3e/trKYx7jt8ySBI2wXjd4isjfiko2k4qUZ2dlKN+V+nMk/w/DU/trBYueKwVHF18HisunUpe1n
hMZ7F4mgrX5aqw1bEU1O2vLGpKSvyyjGacV9jfDbwl4JuVvk8SwaOPGUng3xFZSaN8Ltbh8QXWsa
bNotwl7Pqtnp9tqnhXw9rUduu23ktNetYr29ZLQ6GLube/v4Shh3zKsqf1h4etH2eDqKrKrB02pO
cYKdGlUtty1YqUvd9nd6/jnE2b55TdCWWTxjyaOc5bXjjeKsDPLqWCxMMdTlQp4StiKuFzXMcFKo
71I1svqzo0FKr9edKHLH5y+I9voaz6ZceGLjwZJ4eNu8OnweGX1X+1IsNvkbxKniKGDXpdTkLYad
1bSgF2aYIYsxnysWqd4Oi8O6VrRVFz5139t7VKo5vv8AB0hZaH6Tw1Uxzp4qnmlPOo5iqiniKmaR
wn1Waa5YrLHls54COFilpTTWL15sU5ytJfQv7K3wrF9qlt8TLzVbF4dHe8t9M0mxu47i9W+uLaey
lm1hI2JsY0tZ5XtbST9/cmSO4YRwIouOrLsNeSxDkrQuoxi7u7TXvdtHot3vtv8AkHjZxt9WwVbg
7D4HExqY+OHq4zHYmhOlh3hqValiIU8BKaSxMpVqUI1q8P3dFRnSXPVk3S/QGvauns720+fY/lo/
y1a/y7P+4cKACgAoAKACgAoAKACgAoAKACgAoAKAOD+Kegah4q+GnxA8M6Ssb6p4g8GeJdG05JXE
Ub32paPeWdqkkjfLGjTzIGduFBLHgV9HwhmWGyfivhvNcY5RwmW55lWOxUoxcpRw+FxtCvVlGK1l
JU4Sait3oflXjpwpm3Hfgt4s8FZDClVzzizw54z4cyelWqRo0amZ5zw9mGX4GnVrT92lTnicRTjO
pLSEW5PRHwj+wN/wUD/4KQ/8EtvEfxIvP2T5/EXgib4kWul6V480LxJ8Kbfxz4f1Sbw5cXj6Rfpp
uv6LfWtvqVh9uv4INRsJI/tFpeOkjXEa2zRf6e5R4kcL0cBPDvNeEs4yvG1KGOhhcxzWMIQxFOnU
p08TQq4DNMuzDDVHRrTpV6McTCjXSprF4epUwuGlQ/5G8++jr4z0M2qQxHhl4r5LnWWPE5biKuA4
KzmrJ0/bRdbDVY4rIsyy3F0VWoxq4bExo1XBOc8FiY0cVW9v+nP/ABFF/wDBev8A6Gvw1/4ir4N/
+Ziu/wD4iLwL/wBAHh9/4kOf/wD0ZHl/8QC8cP8AolvGj/xXsf8A6Bg/4ii/+C9f/Q1+Gv8AxFXw
b/8AMxS/4iLwL/0AeH3/AIkOf/8A0ZB/xALxw/6Jbxo/8V7H/wCgY89+LP8Awcgf8Fy/jN8M/Hfw
n8WeNobPwv8AEXwrrfg3xHP4X/Zx8NeG9e/sLxDYT6Xq0Gm63p3hpL3Tprqwubi2NxbsJEjmfYQ2
GG1DxP4TwVWGKy2HAWXY+i+fCZhQzrMcRiMFXX8PFYanmXE+OwaxNGVqmHq1cLVdGtGFamo1YQnH
Kt9HnxkxdOWGzDgvxkx+CqrkxWBrcCYrD0MZRf8AEw2IqZfwhgsW8PWjenXp0sTSValKdKo5U5zh
L86/2BP29/8Agox/wTK8S/EPxR+x3rPi74c3XxW0XQ9C+IOmar8JtN8ceH/Ett4YvL++8NXd3onj
HwvrVjDq3h+bWNbj0nVrOO2vrW11zWbMTNa6lcxP8t/rTwz/ANFFkX/h3y//AOaD6T/iDfi//wBG
q8SP/EG4n/8AnWfp7/xE7/8ABfn/AKKfaf8AiJ3ws/8AndUf608M/wDRRZF/4d8v/wDmgP8AiDfi
/wD9Gq8SP/EG4n/+dYf8RO//AAX5/wCin2n/AIid8LP/AJ3VH+tPDH/RR5F/4d8v/wDmgP8AiDfi
/wD9Gq8SP/EG4n/+dZwvxO/4ONv+C73xZ+HXjj4X+Jvi1qdj4a+IfhXXPBniO48L/s0/Dnw14h/s
DxJp8+k61Do/iLTPh7Dqug311pt1dWsOsaRcWesaYZvtmk31jqMNteQH+tPDH/RR5F/4d8v/APmg
P+IN+L//AEarxI/8Qbif/wCdZ+en7A/7dv8AwUR/4Jm+LvHfjT9jzVfFXw51P4m6Dpfhzx5Z6r8I
dH8daL4j03Qr251HQzc6V408J65b2t9o93fX8lhqOmiyvo47++tXnktLy4gkP9aeGf8Aoosi/wDD
vl//AM0B/wAQb8X/APo1XiR/4g3E/wD86z9fvCH/AAcT/wDByJ8QY72XwDd+LvG8WmtCmoy+EP2I
fBfiWOwe4Eht0vX0X4VXq2rTiGYwrOYzKIpCgbY2PZw9ali8DSzTCVaeKyyvicRg6GY4eca2BrYv
B08PVxeFpYum5YepicLSxmEqYihCo6tCnisPOrGMa9Jy+MzzIs74YzJ5NxLk+a8PZwsJQx7yrPMv
xeU5ksDiquIo4bGvA4+jh8UsJia2ExVKhifZexrVcNiKdOcpUaijt6v/AMHAX/BzPoCwvr2l/EnR
EuS6276v+wZ4a01Z2j2mQQtefCKESlA6lwhYruXdjIz4mdcVcL8ORoT4h4kyDIYYpzjhp51nGXZX
HESpqLqRoSx2IoKs6anFzVNycVKPNa6vOWZLnGdSqxyfKczzaVBRlXjlmAxWPlRjNtQdVYWlVdNT
cZKLnZScWlezOK8ff8F3/wDg5N8ffDnxT4c1if4t6R4S8aaNqng/VPEfhz9ifw1oN9FZ69Y3FhqF
tovim2+EnmaNq9xp8t1Faalplxb6zprMb/SLux1G2tr235Z8dcE08rjnk+MeFoZLLERwkc3nxDlM
crlipxnOOGjmDxawjxEownKNFVvaOMJtRai2uiPDXEc8e8rhkGdyzONF4iWXRyrHSx8aEXGMq7wi
oPEKjGUop1HT5E5RTldo9z/4N7v2ePjf8F/CP7R/jv4neB/FPw60L4q3Xww0jwxpni3Sr/w5retR
+BB48utS1N9E1SG01OPR2fxhYxadd3VrFb6hLDcy2pnihWWv8gf2kni7wdxjmHh1wjwXxVl/EFbh
+jxNjeJKmQ5hQzDLKf8AbMshhl2CrY7BVKuFr4mk8pxVbEYWFao8P7Sh7eMKrjGP+gH0POAOIOH8
Lxdn/EeR4rKqea1Mmw+TRzXCVMJjZLL45pLF4mnhsTCFelRqfX6FOlXlTgq3JV9lKULt/wBH+oaX
qWkzJb6nY3VhNLb293DHdwSQGa0u4UuLS7g8xQJrW6t5I57a4iLwzwyJLE7oysf8x8zyjNMmr08N
m2X4zLq9bDYbG0aeMw9XDyr4LG0KeKweMw/tIxVfCYvDVaWIwuKoudDEUKlOtRqTpzjJ/wBo4HMc
BmdKdfL8ZhsbSp16+Gqzw1aFZUsThqs6GJw1bkk3SxGHr06lGvQqKNWjVhOnUhGcWl9+/sReGLjw
pafEf48+ITPp/hHwv4Q1m0ilk3QxaklhFHr2vXaFuHttIs9KijMpVomubxlRzJZzov8Aql+y+8G+
Jc840zjxBVHE4fLs0p4bw64ZhKM4QzzOc6zXLauNq0o6e3w2VvD4LDc6Tp1MXj506VT2uCxEI/wb
9OXxFybAcO5TwXGpQr47C16vF2dSi4znleX5dgsZRwtOo1f2VbH+3xNblupww+EU5w9niaMpfyr/
AAI8Y3/xQ/bX8eePdWnFzqHiTUfGusXU55E1xPqcMrzpgKAJTE8gwq/63JUHIr/YjhbijCcXfSV+
m9xBltZYjJcj8J8bwHw1OLvGXD3CXir4MeHuQV8P70oxoVci4foztfmnDFOTSqSkeV+0M4DxHhF+
x6+hdwJisNLBZxmnidwJxrxPQsozp8RcdeHPjD4hcS4PF3jGU69DiDiyvRk9YweCVOMnTjTPvH9p
2FJvgT8SFkGQPD12V4BKsV2bhkEBgrtg44Ne3wJTWIo+JuCqX9jjvAXx/wANXSs24f8AEHONK8ZR
5lKKnTrUKVanKUZKFSnCaXNFNf4n/Q5xlbAfS6+ixjMPLlrYb6RvgjVhdzUZcviXwzzU58koSdKr
Hmp1YKUeelOcG7SZ9Kf8EL/Gmq/FH9m39oT9my01lNP1fSNZ0bxp4eSeabZPaX09tbeIIp9hZobO
4Fj4d0xpIkOFk2zLJCgiP534dZrLjf6L/hpj3V9pmfhxxJxj4UZjOpJzrLKK+Lo+InCEqtRw5vYV
P9aOK8rwcJVJ+yo5J7OEYUoKJ/ul9P8A4XwXgx9PvPuLq+WyqcPeLPBnCPihSjh6dOFGGf4PLsX4
ZcU/VqTkqc8bhv8AVfhviDGPlpuvic9VZzeIqyqr7N13RNR8N6zqWg6tEsOpaTeTWN7FHLHOiXED
FJFSWJmjkXI+VlJBB/CvnJwlTnKEvii2n11RrlmY4TN8vweZ4GbqYPHYenicPOUJU5SpVY80XKE0
pQdt4ySaZoaH4r8QeFY72DRbyTS57u80y5uLmFTHfRy6LPPcWsKSnmOIXUqzXERQ+ZLbW4YhY2R7
p16tFSVOTg5Sg21pK9NtxV+13drq0uxjjspy7NpUKmNoxxdOlRxVOnTm+bDzhjadOnVm4rScvZQc
Kc1L3YValruSccS/mnury5vbmFIJb6aW+aOG3Frbj7XI0/8Ao0CKscVtlz5CRgRpGFWP5QKzk3KU
pNJOTcmkrL3tdF0WuiWltjuw8KdKjTo0qjqQw8I4dSlU9rU/cxVO1Wo25Sqrl/eOT5nK7lq2fXHw
h0LW/AnhPRvHkPiBdP0vxncmG+ghieS7Ftpt7fWYgFlJ+7vCY0urmOdJbcR+aqF0dY3f+WfpD8U8
e8B5vwPxHl/GVHhjgLE1/wCzc6w2FoRx+b5hiqleq8aoZVUoRjjqdPKo3wrjjsLDD1/aVJ1qFaWG
nL8S41zHK+Kc6zPheplDxmYcPUVVwtWpOMMP7XGYfDYj2jxUHz4e1SVGjOlKnVc+STUZxc4x9Y0z
x14l8Lx3Gj+ErB9b0yC8uGl1LU9J1Ge8nvnK/aC5tNRaFE2rEYo8+YIyrS5kZif544c8b/EXwzw+
P4T8K8jq8Z8OYPN8dVxPEHEvC3EGOzfG51WdNY+VaeVcQ1MHRpckMNLDYdNV44eVOpik8RUqTl8d
juFMlz6dHMeIcXHK8dVw1KNPBYDMMHSw1LCx5vYqCxGDjVnLmdRVJ/A5qSp+5GKX+cZXrn/YAFAB
QAUAFABQAUAFABQAUAFABQAUAFABQAmB1wM/Snd92Jxi94xfqkG0eg/IUXfd/eLkh/JD/wABX+QY
HoPyou+7+9hyQ/kj/wCAr/INq+g/IUXfd/eHJD+SP/gK/wAgwPQfkKLvu/vDkh/JH/wFf5Bgeg/K
i77v7w5IfyR/8BX+QYHoPyou+7+8OSH8kf8AwFf5BtX0H5Ci77v7w5IfyR/8BX+QbV9B+Qou+7+9
h7OH8kP/AAFf5H6Yf8Etf20J/wBjr9ovSdU1qSab4ZePGg8L+P8AT1LOiafeTxrDq9vBhkbUNLuB
BeWxCrJcG2GnGe2tr66mX+1foh+IOV1MXnngTxdj6OX5L4lY7AY7gbO8dWjSwHC3irgaVTBZLLGV
qnuYTJuN8HWfCWeYlyhSw2KfDmbYtrC5LUa/yP8A2r30Xsz8SvDvJ/pA+H+VTzDj3wRy/NP9acmy
/DOrmXGPhHjJwx/EGEwlOl+8xmb8EYuhLi/I8Io1KmIwcuKcswVOpj84w0Jf3q2cnhDxxoGla3aL
oHinw1rNjZ61o+oCGx1XStQ0+9t0urHUbOWRJreWCe3lSaGdODG4IIzX7ZxFwzSWJxmQ8VZFQqYr
K8dWw2NynPMuo154LMMHUnQrUq2ExlKapYnD1YzpyvBThJSVz/nuyrOa1KnRzPJM0rUaeLw8KlDH
ZbjKlFV8LXjGpCUK+HqQc6NSDjJe84yVmfIv7VmmaX8Xv2f2v/hReaL4y07wt4ustVnHg25sdbtn
ttHs9X0nVLS2OkPcQtLpp1WO8uLaLLxwW7SBMbc/xN9OrgXOOOfo/SocFYGeZT4S4qyjiWvlmTUf
b1amU5fl+dZVjYYXC4RNzWBjm9PHVaNKDcMPhKs4wfIkf0d9EXj7hzLPFZZhmWdYGpg+IMlzXh6n
m1XHUauFp5rWxuWY6lHEYyVSUIVK88ulhFKpUTdXE0lJpTufjeI2D+UI2DhtnlhCHDg4KbMZ3Z42
4znjGa/wTjRr1a8cPClVqYmdVUYUIwnOvOtKSpxpRpJOpKq5tQVNRcnK0Ur6H+unPTUPac8FT5ef
n5koclr8/Nfl5ba817W1vY/bL4Qn4Y698Nvg38PvHdj4U8Y+Km8FRT29lfabpviP+x7Wxt2uVtby
6eK6TSZ7ay2W7W0kkUgktpYnUFQG/wCizwb8MMhzXwL8I+HPFbgzh/iHO8o4NyujLBcTZHl2bYrK
IeydXDYF/X8PXqYKvg8FPD4XEUIunKnVozpTjeLR/kZ4m47jbKPEPxP4v4KzLiDhvIJ8VV4VMVl+
YY/JY5nXxFWNGeJw9GlUw7zClXxanXjXjCpBwr06kZNSbXnH/BSTx/pHwH/YQ+N99odpp2jQ3XhE
+CdC0uwt4LCw/wCKquo9O1G0tra2SOJAdCm1q48qGI+a0bBl2u7j+1fBHC5LwBnEeI8qyvK8m4f8
IeEuL/EnD5dgMDQweWYOfAvDuZ8QZNh6OBwdCNCnDF8R4fKsIoU6K5q2LTSlUklL8EocPZt4w8bc
G+H2NxmY5pm3i74icD+HmLxuIxVTEZhVw/GXEuV5Fm+Lq4vE1vaN4LI8RmGLnVqVV7OjhW3KMIXj
/E1+xDqK2vx60wzyDdf6PqtsrPkmS4uJbQDnqWYNIeeCeT0wf5d+hbjamMz/AMfsvxFadXFZ94DZ
5ioym+aticbkniV4acU4mcpyT5n/AGflWZ4itdpzjCTUudRT/wBpv2+HDbr/AELfD/FZbhVTwvCX
j7wVVq06KjChg8pxHAXiTkVOMaacVGEcbjcqw9JRT5FNRUeVtx/UH9qrUItO+A/xAaRgv2rR5rOL
OOZ5o3aJeeuXjHA5PSv6c4QxEcuyTxhzqo1GnlHgD42T53blhiM58Pc74Wy7mvo1UzTP8FRUW/fl
VjGN5yin/wAz/wBBzIMRxN9Mz6K2U4anKrKX0gfCXMMRCHNzf2fknG+TZ5mk04puPsssy7F1XO1o
KDnK0Ytnhv8AwQ4+MS/DH9t/wx4evLoW2k/FTRNU8C3haREEt3fxrNoVqiv997vxLBoa7FKsRFuD
MUWKT+dfoa5v/anD3jx4Z1JOVTHcJZH4p5HSm7web+GucSwWbQow51JV58F8ZcTY6o4wnF4fKJOr
yRpxnH/p3/bQeHv1jg3wE8ZcNQUqvB3H2c+G2eVacH7SGQeKGSrGYLEYiooOP1bD8W8EcP4CkqlS
Djic9hGkpOtOL/tCuvgj8O7/AMbTePb/AEdr3Wp5Ybl4LmdpNK+2wokaXx0/AiluCI0ZhOZYDKvn
eSJPmr9NeEoSrOtKPNNtOzd43XXl2b9bq+tj/GSh4jcV4Xh2nwxhcwWGy+nCpRjVo0owx31apKU5
YZYq7nCleUknSUKqg/Z+05ND4p+Inwo0O5+OWoeFv+E10/SG8VX51W2WXS9RupbHUNela7h0qZYl
htC000xNtN9tRBDLCJgkp2Hya+Gg8XKn7WMfaS5leMm1Keqi7JLVvR36q+p/RPCnHGZUfDbC51/q
7iscskwqwVaUMbhKEMThcsgqFTHU3OVTEWp06aVan9WcnUhUdNyguZfZJ+BHw81Dw34X0LxLoltr
Vx4Z0ez0qLVke70u8uFtk3Sl5bC6hna3luXmnS2mnnWEzPtYszu3q/U6EqdOFSCm6cVFS1i3bfWL
Ts3d2bdrn8/LxN4rwmb5zmWUZjVy6lnGPxGOngZRoY3D0nVlaHLDFUKlJVYUo06cq1OnSlUVON0o
qMV1XhxPht4p8O2tj4Vm8KeJfDHh+8bSrePRbzTtb0zTdR0kCKaxaW0muoo7618wC6hmkNx++3XA
bziW8TO+GOE+McDh8Dn+TZPxFl+BxtLF4fDZhhsPmGHw+OwylGFWMKiqQVWEak4TT+OE506kZQnK
L+KwnFeMx2Mx2c4DPZYnGYyriKOPx+Cxsak6laUk8RRq1MPNxjOMuX90uX2VockYJQt5L4z+Of7M
fwC1n/hC/HHxC8IeBdbvbaPxJ/Ymq3l492bTUZZrSG82LFd+RDO+mzRwwl48JBvWJY5Ed/tfCb6J
ub4rhzH4nwo8Kcxx/DlfPsdVxVfAKrisKs6nhsC8XTpTx+MlOnGnhvqSVDD2wtGNo04xl7RHg8V+
KmVYLMaFDirinC4TMY4Gi6VLESVKq8H7WvGlOSoUkpc1WNf353qSabk2uVn+bXX+VZ/3MBQAUAFA
BQAUAFABQAUAFABQAUAFABQAUAFABQAUAFABQAUAFABQAUAFAH9Cn/BKj/grHF8GrK3/AGbv2lL2
81X4Q6oZ7Hwz4qkL3N94LfUz5dzZ3Q81Z7vw/eGaaa9toGa8imeW/wBLin1KW6sta/0v8HvGPKvH
7Lcs4F8Qs6y/JPGrKsBRyrhjjTP8dRy7JfFbJ8vwtPD5bwzxXmuKlChl/iLg8PSWCyHiXH1aWE4v
w0MLlGd4mhn1LCZjmv8Azi/tA/2euaeEeY8ReOXgDwxjs88Ic8xWJzXxE8LeG8vxGPzfw0zPMq9a
tm3FvBWUYKE6+M8OsbVqvFZ9wzl1GeK4KxFTE5rk2HqcK1MTguHP6ov2ePhf8Hvhd8Pbe0+CE1tf
+B/E97N4qtNZs/EB8T2msvqEUMK3lprCz3ENzaJbWsFrb+RIUVIMSM8/myN69bhnE8I4vG5Dj8sx
2T5jgcVUpZhluZ0MRhsfhMVGynRxOHxUYV6NSEVFck4RfLZ63u/8i+Esl4eyXKYQ4ZlCtlmNqSx1
PE0sX9dp4l1lGKqU8QpzjOmoQjThyOyUNbz5m/UvENjcLpGuXeg6XpVz4jbS7/8As0XtvD5N3qBt
ZRawXkv7t2gmm2RyhpVUoxDMFya8+WAwcKtXGUcDg1jpxl/tP1aiq85NW9+vGCqyvs7z1R9/lWLh
LHZbhs0x+PpZPHG4X62sPXq81DCqvB1qmHp+/CNWnDmnTtTbUkmk2fmbfeFPiHAt/wCE5vhhqVhP
LHba7qsPhyKeR3tLm6jVzAwOqwW1tPFHHay6fYSJCLi0iluraT7IscXwmYZLxLV4jyjM8BnlbA5R
hsPiaGd8NTy2hi8Jm0KylLDYzDYxTpYvLcfhq3IpSjPFYXEYePsp4SFRKsf2Jhc94SqfV87hxjg8
VCE6uW4Crm1SnBQr0aMnFVYtYKpXq05znWhisVB1PZV5wo14Ku5z/OP/AILwfHfwtpfwD+Ff7O3g
PV7m/W28VlvGESTT3Z0S98M6Paw6boOrakoNnPrUEGratHrGmJM8ulzm3iuoLeYxxRfsXilg878N
foseIfFGPyvG5dPxWxXA3hlwviq1GpGtPIswzTGcccQ5irwf1fBY/CcA4XJ8NWr+y/tTDZljHgPb
UKNepHX9nLw/g/GH6e3CuMxOOyzGx8Isk8Q/FnO8Ph6mHhhcTntPB4XgHIsPhaHPGpiKmX5hx/LP
W8PGqsBXyrBvHTp1q9GFX+Yz4SeMbnwH8RvCXii1QyNp2sWvmxL1lhnkWFxjo7R71njQ/K8sSK/y
k1/EP0YOPaXh7428GZnjsNXx2QcQVsw4B4qwGGu6+K4X8QssxfB2cyw8FOn7XG5dhs4/tjLaTq0o
1Mzy7BxnVpw5pL/d39oD4EYT6R30Q/Grw0qYqhl2Zz4WqcXcM5niUlRwPE/Alejxdk6r1OWo8Pg8
zrZRLI8zxMKdWrQyrNcdVo051YwR+nX/AAUZ8Q+LfA3hrwN8NfE3hy58Naj8QPD2gfEa2gk1fw/q
6XXhHWbO31LQ9QE/h7VdVitZbuKZV+wX72eorBNM9zZQxtbtcf3L9KWjgfATwc4l4SwfEeXcVcS+
M2f43gGOYZPgM9y+jkfCnhnxBk2e8b0MTPPMtyys8fm/EkODcuo0sBDGYCvluHzpSzKUoPD1f+dP
9h59FfNuMPpO5l438T0cLhch8BuFsFnGVYOWJw2Lr5pxb4pZPn+ScMVFRw9WtShg8t4ew/FWa16l
eccThM1p8Punhpe2lXw35pfA/wAd6x8Mvi/8N/HugTS2+seFvGOhatp00HmebFd22oQNBNGIv3rS
Qy7ZoljxIZY0MZWQKw/h/wCipxdHgv6QnhbmVfD18blmbcSUuCs/wOGpTxFbHcN+IOGxPA3EGGp4
SFKvLGVZZRxBi6mHwsaNWdbE06MaUVV9nKP/AEefTz8MsP4t/RA8fOEqlfC4PG4bgHMeNcix2MrU
sLh8DxL4b1KHiDw5iK2NrVsPSwGHec8NYOhi8bUr0qVDBVsTKvKWH9rCf+jz4Q+ImlfG34RReNfh
Z4i0+Z/FHheV9Iv4LhJ/7C8QXmlLKunaqiCWWw1PR7y4iiv7S4g+02ki7ntnBRX/ALo464M4g4C4
jznhTP8ABYjL80yzE4nDw+sUZ0oYuhCtVpYbMcJOUeXEYHGRp+2w2JoudGrB3hJtNL/kr4K4hyHO
XkOey5cyySpicBiMww9CcZVHQU6NXG4GpHmi4YmFNypVKFSVKak+WUoKSmvzi8feGPH/AIe8T6ba
ePry4PizVYrC5sNSuNZbUZ7e1+********************************/HsdhM6WcZbWWOo0sB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------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: image/gif
Content-Transfer-Encoding: base64
Content-Location: https://pagead2.googlesyndication.com/pagead/gen_204?id=xbid&dbm_b=AKAmf-B59g69rRNGlEh9D1eD7nkecCqVTBNXLS0tyCtW8Fpf6RL0005vAUQhabToLPraKnOiXFAw17gT3C2rEC7PaU2kVri0naQcwka9gBrt4WFy3LQA__Q

R0lGODlhAQABAIAAAAAAAP///yH5BAEAAAAALAAAAAABAAEAAAIBRAA7

------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

.mute_panel { z-index: 2147483646; }

.abgac { position: absolute; left: 0px; top: 0px; z-index: 2147483646; disp=
lay: none; width: 100%; height: 100%; background-color: rgb(250, 250, 250);=
 }

.mlsc { height: 100%; display: flex; justify-content: center; align-items: =
center; }

.mls { animation: 2s linear 0s infinite normal none running mlskf; height: =
50%; width: 50%; }

.mlsd { stroke-dasharray: 1, 189; stroke-dashoffset: 0; animation: 1.4s eas=
e-in-out 0s infinite normal none running mlsdkf; }

@keyframes mlskf {=20
  100% { transform: rotate(360deg); }
}

@keyframes mlsdkf {=20
  0% { stroke-dasharray: 1, 189; stroke-dashoffset: 0; }
  50% { stroke-dasharray: 134, 189; stroke-dashoffset: -53px; }
  100% { stroke-dasharray: 134, 189; stroke-dashoffset: -188px; }
}
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: text/css
Content-Transfer-Encoding: quoted-printable
Content-Location: cid:<EMAIL>

@charset "utf-8";

div { margin: 0px; padding: 0px; }

.abgcp { height: 15px; padding: 1px 1px 13px 9px; right: 0px; top: 0px; pos=
ition: absolute; width: 31px; z-index: 2147483646; }

.abgc { display: block; height: 15px; position: absolute; right: 1px; top: =
1px; text-rendering: geometricprecision; z-index: 2147483646; }

.abgb { display: inline-block; height: 15px; }

.abgc, .abgcp, .jar .abgc, .jar .abgcp, .jar .cbb { opacity: 1; }

.abgc { cursor: pointer; }

.cbb { cursor: pointer; height: 15px; width: 15px; z-index: 2147483646; bac=
kground-color: rgb(255, 255, 255); opacity: 0; }

.cbb svg { position: absolute; top: 0px; right: 0px; height: 15px; width: 1=
5px; stroke: rgb(0, 174, 205); fill: rgb(0, 174, 205); stroke-width: 1.25; =
}

.abgb { position: absolute; right: 16px; top: 0px; }

.cbb { position: absolute; right: 0px; top: 0px; }

.abgs { display: none; height: 100%; }

.abgl { text-decoration: none; }

.abgs svg, .abgb svg { display: inline-block; height: 15px; width: auto; ve=
rtical-align: top; }

.abgc .il-wrap { background-color: rgb(255, 255, 255); height: 15px; white-=
space: nowrap; }

.abgc .il-wrap.exp { border-bottom-left-radius: 5px; }

.abgc .il-text, .abgc .il-icon { display: inline-block; }

.abgc .il-text { padding-right: 1px; padding-left: 5px; height: 15px; width=
: 74px; }

.abgc .il-icon { height: 15px; width: 15px; }

.abgc .il-text svg { fill: rgb(0, 0, 0); }

.abgc .il-icon svg { fill: rgb(0, 174, 205); }
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://googleads.g.doubleclick.net/xbbe/pixel?d=CJfnugEQi4Dw4AIYqaaarAIwAQ&v=APEucNWxGsyml_3RpcpLnuPli3X7iiFlNVMwM5bHjOfPa-OXTgihN_9unF5IywL2_liKg0TxFAHkhuzeff-ofxnlrRiOcdXANMv7Jg_fFCA3uTMFOUTTRYQ

<html><head><meta http-equiv=3D"Content-Type" content=3D"text/html; charset=
=3DUTF-8"></head><body></body></html>
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://contextual.media.net/checksync.php?vsSync=1&cs=6&cv=31&https=1&cid=8CU9R268D&prvid=2034%2C2033%2C2030%2C590%2C2073%2C2072%2C273%2C550%2C2028%2C2027%2C2025%2C117%2C238%2C359%2C636%2C99%2C2089%2C3011%2C3010%2C201%2C3007%2C246%2C4%2C203%2C404%2C9%2C3022%2C3020%2C251%2C175%2C2009%2C178%2C255%2C3018%2C3017%2C214%2C3014%2C459%2C77%2C38%2C182%2C141%2C262%2C461%2C222%2C301%2C225%2C10000%2C80%2C229&itype=EBDA&purpose1=1&gdprconsent=1&gdpr=0&usp_status=0&usp_consent=1

<html><head><meta http-equiv=3D"Content-Type" content=3D"text/html; charset=
=3DUTF-8"></head> <body> </body></html>
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://pagead2.googlesyndication.com/pagead/s/cookie_push_onload.html#aHR0cHM6Ly91cy11Lm9wZW54Lm5ldC93LzEuMC9wZD9waD1iYmI4MmZhZS0xZDI3LTRkOTAtYmIxMC1lMjQxNjRlY2Q3YmMmZ29vZ2xlX2dpZD1DQUVTRUc1ZDc5aUYtVTVZdUZacHkzVi0xMG8mZ29vZ2xlX2N2ZXI9MSZnb29nbGVfcHVzaD1BWGNvT21SQjJqa205MGpQLVNKenB4Z01iWGV6Mkd5SGFRRGZvVW5mSE40UjBlM1dvX3NVMk9hVWMxWHlReDZRaXJlUWxPblRERzkwMVRQTHNGMzFBOGxNQlJTaU5Ic3ltdGRuSE05ZEhpUVRlQS0wS2JCUEItQTJmTHBsdGhUTjFjc0FFNW5GaXIwbzZ0MA==,aHR0cHM6Ly9pbWFnZTYucHVibWF0aWMuY29tL0FkU2VydmVyL1VDb29raWVTZXRQdWc_b2lkPTEmcmQ9aHR0cHMlM0ElMkYlMkZjbS5nLmRvdWJsZWNsaWNrLm5ldCUyRnBpeGVsJTNGZ29vZ2xlX25pZCUzRHBtZWIlMjZnb29nbGVfc2MlM0QxJTI2Z29vZ2xlX2htJTNEJTIzJTIzQjY0XzE2Ql9QTV9VSUQlMjZnb29nbGVfcmVkaXIlM0RodHRwcyUyNTI1M0ElMjUyNTJGJTI1MjUyRmltYWdlOC5wdWJtYXRpYy5jb20lMjUyNTJGQWRTZXJ2ZXIlMjUyNTJGSW1nU3luYyUyNTI1M0ZzZWMlMjUyNTNEMSUyNTI1MjZwJTI1MjUzRDE1NjU3OCUyNTI1MjZtcGMlMjUyNTNENCUyNTI1MjZmcCUyNTI1M0QxJTI1MjUyNnB1JTI1MjUzRGh0dHBzJTI1MjUyNTNBJTI1MjUyNTJGJTI1MjUyNTJGaW1hZ2U0LnB1Ym1hdGljLmNvbSUyNTI1MjUyRkFkU2VydmVyJTI1MjUyNTJGU1B1ZyUyNTI1MjUzRnAlMjUyNTI1M0QxNTY1NzglMjUyNTI1MjZzYyUyNTI1MjUzRDEmZ29vZ2xlX2dpZD1DQUVTRU11ZmxndXo5MlM4TWZnajR2UVdsV1kmZ29vZ2xlX2N2ZXI9MSZnb29nbGVfcHVzaD1BWGNvT21TZXdLeDJwcmV3ZEZZSGhhWElKY0h1NjZ1NDd6SjB1SFdrMHlZNmlEQ1U3eUs5NTdYVE9jOUFWT2pOemp6LTZhRVViNkllY2dMenJPNm92UGZMSVcwVC0xelB6dkgzTEFPblRrUUJrby11OVJ5OWJqdjMxZ1FERUpKMFloRmhaRUEyM1AtV1Bwdw==,aHR0cHM6Ly90b2tlbi5ydWJpY29ucHJvamVjdC5jb20vdG9rZW4_cGlkPTIyNDkmcHQ9biZnb29nbGVfZ2lkPUNBRVNFQ1p4d2dCSzBJU1BqaWZCMElQU1RqVSZnb29nbGVfY3Zlcj0xJmdvb2dsZV9wdXNoPUFYY29PbVFRMjZobzYtU2IzMzRUMC1YTnQ1TkthelEzdkpjR1piT0JQdFRSdDhMVzRVb1o1OElGSGk4cU95U0d0UnFyVWxMQWdGNm5lZTc0RHFtRkQ2dmxRSU1mOUZmclFXQVNXWFNlYlgtVGJrTzZZVnJWY1RFdExKWHlJdDBLN0s2VXJQNzBQZU03ME5N,aHR0cHM6Ly9kc3AuYWRrZXJuZWwuY29tL3N5bmM_ZXhjaGFuZ2U9MTEmZ29vZ2xlX2dpZD1DQUVTRU9Nem1qSmRsVW1TUi1WR0hxZlEwazQmZ29vZ2xlX2N2ZXI9MSZnb29nbGVfcHVzaD1BWGNvT21RX3pqYjBGak80dWR0T2VHQzU0Y09BcTlrZ2ZTUDFpQ0hKa19CbFBhTlBLcEJ1eVpvTEo0eVFmckw3di16REdyU0dnTXRtLUFCY0E4cUpUVU1yNUQ0T1M1Sl9TbEUzaTVMY1l3elIwTU5ZNWhpQ2x2MGQwWDJqazkzeUhFWlNSQ0c3eS1QSmtocw==,aHR0cHM6Ly9zeW5jLjFyeC5pby91c2Vyc3luYzIvcm1wc3NwP3N1Yj1nb29nbGVjbWEmZ29vZ2xlX2dpZD1DQUVTRUFyVUFoVmNVTkVEcGt6aTltR3VPSjQmZ29vZ2xlX2N2ZXI9MSZnb29nbGVfcHVzaD1BWGNvT21SY3lMc2lVdEREdjRLRVFMckx0TW51UmF5VVA1TklncDZMRHdhX2dHVVVNSE9wNW83ejdPSnQwRlZlTV9fTGNGMW5TNHZXTWZwYUd3RnRZdjN1VHdZR1dZeUdXaTJnaFllMnpqMlR3UWNnQ2Yxb0x5WFNkYUdJM3hPLXhlc29ITld6dG5FTjNXOA==,aHR0cHM6Ly95Lm9uZS5pbXBhY3QtYWQuanAvdWxfY2IvZWJkYV9jcz9nb29nbGVfZ2lkPUNBRVNFT1JLaTF2WjBQLTdSTDBneE43YmFwbyZnb29nbGVfY3Zlcj0xJmdvb2dsZV9wdXNoPUFYY29PbVRRMlJqNGphZGs2dndRZU4wTTl5ZmFUaGdpazlyQWVmRnBkaFpnazVqTUZ6UUY4N1ZxVUVEVUhXcjg3MWpuSEY0NldCSW04QVVaeGMxMUN4Vi1JMGN5cjkwT3M1WkNrbV9sbnV1bzkta1d4YkIxU0pCOU9qd1J1dDZBaHR1S192eUxLOUY3SE44,aHR0cHM6Ly9jbS5nLmRvdWJsZWNsaWNrLm5ldC9waXhlbC9hdHRyP2Q9QUhORjEzSUJNTUFZNTVtbG1yT3czV3E0T2lrTDFMU3lORkRqRGFxSkpmTzByM21yeEQ5RnFzLTZfQTlIbUNJSWNicU9NZw==

<!DOCTYPE html><html><head><meta http-equiv=3D"Content-Type" content=3D"tex=
t/html; charset=3DUTF-8">
  <title></title>
 =20
</head>
<body>

</body></html>
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.googleadservices.com/pagead/managed/js/activeview/current/reach_worklet.html

<html><head><meta http-equiv=3D"Content-Type" content=3D"text/html; charset=
=3DUTF-8">
<meta http-equiv=3D"origin-trial" content=3D"AxjhRadLCARYRJawRjMjq4U8V8okQv=
SnrBIJWdMajuEkN3/DfVAcLcFhMVrUWnOXagwlI8dQD84FwJDGj9ohqAYAAABveyJvcmlnaW4iO=
iJodHRwczovL2dvb2dsZWFkc2VydmljZXMuY29tOjQ0MyIsImZlYXR1cmUiOiJGZXRjaExhdGVy=
QVBJIiwiZXhwaXJ5IjoxNzI1NDA3OTk5LCJpc1RoaXJkUGFydHkiOnRydWV9"></head>

<body>
 =20



</body></html>
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://www.googleadservices.com/pagead/managed/js/activeview/current/reach_worklet.html

<html><head><meta http-equiv=3D"Content-Type" content=3D"text/html; charset=
=3DUTF-8">
<meta http-equiv=3D"origin-trial" content=3D"AxjhRadLCARYRJawRjMjq4U8V8okQv=
SnrBIJWdMajuEkN3/DfVAcLcFhMVrUWnOXagwlI8dQD84FwJDGj9ohqAYAAABveyJvcmlnaW4iO=
iJodHRwczovL2dvb2dsZWFkc2VydmljZXMuY29tOjQ0MyIsImZlYXR1cmUiOiJGZXRjaExhdGVy=
QVBJIiwiZXhwaXJ5IjoxNzI1NDA3OTk5LCJpc1RoaXJkUGFydHkiOnRydWV9"></head>

<body>
 =20



</body></html>
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable
Content-Location: https://tpc.googlesyndication.com/sodar/62bHydCX.html

<!DOCTYPE html><html><head><meta http-equiv=3D"Content-Type" content=3D"tex=
t/html; charset=3DUTF-8">
</head><body></body></html>
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt----
Content-Type: text/html
Content-ID: <<EMAIL>>
Content-Transfer-Encoding: quoted-printable

<html><head><meta http-equiv=3D"Content-Type" content=3D"text/html; charset=
=3DUTF-8"></head><body></body></html>
------MultipartBoundary--NrvbZqRm5HjAqMBOymjo4fDFqVr4Gwwle0XE5FOhCt------
