<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.p3p-interface {
    background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.gender-selector {
    background-color: #fce4ec;
    border: 2px solid #e91e63;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.p3p-result {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.protagonist-badge {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    margin: 5px;
    color: white;
}
.male-mc { background-color: #2196f3; }
.female-mc { background-color: #e91e63; }
.p3p-arcana {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    margin: 3px;
    color: white;
}
.fool { background-color: #ffeb3b; color: #333; }
.magician { background-color: #f44336; }
.priestess { background-color: #2196f3; }
.empress { background-color: #4caf50; }
.emperor { background-color: #ff9800; }
.hierophant { background-color: #795548; }
.lovers { background-color: #e91e63; }
.chariot { background-color: #9c27b0; }
.justice { background-color: #607d8b; }
.hermit { background-color: #8bc34a; }
.fortune { background-color: #ff5722; }
.strength { background-color: #ffc107; color: #333; }
.hanged { background-color: #3f51b5; }
.death { background-color: #424242; }
.temperance { background-color: #00bcd4; }
.devil { background-color: #9e9e9e; }
.tower { background-color: #f44336; }
.star { background-color: #673ab7; }
.moon { background-color: #1976d2; }
.sun { background-color: #ff9800; }
.judgement { background-color: #4caf50; }
.aeon { background-color: #e91e63; }
</style>

<h1>Persona 3 Portable Fusion Calculator</h1>
<p>Calculate Persona fusion combinations in Persona 3 Portable with our specialized P3P fusion calculator. Features both Male and Female protagonist options, unique Social Link differences, and portable-specific fusion mechanics for optimal Persona team building.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="p3pform" id="p3p-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">P3P Fusion</a></li>
                <li><a href="#" data-tab="social">Social Links</a></li>
                <li><a href="#" data-tab="differences">MC Differences</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="p3p-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">🎭 P3P Fusion Calculator</h3>
                
                <div class="gender-selector">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">👤 Protagonist Selection</h4>
                    <div style="text-align: center; margin-bottom: 20px;">
                        <label style="margin-right: 30px;">
                            <input type="radio" name="protagonist" value="male" checked style="margin-right: 8px;">
                            <span class="protagonist-badge male-mc">Male Protagonist</span>
                        </label>
                        <label>
                            <input type="radio" name="protagonist" value="female" style="margin-right: 8px;">
                            <span class="protagonist-badge female-mc">Female Protagonist (FeMC)</span>
                        </label>
                    </div>
                    
                    <div style="background-color: rgba(233, 30, 99, 0.1); border-radius: 8px; padding: 15px; margin-top: 15px;">
                        <h5 style="color: #2c3e50;">Protagonist Differences:</h5>
                        <ul style="color: #2c3e50; margin: 10px 0; text-align: left;">
                            <li><strong>Male MC:</strong> Traditional P3 Social Links, Orpheus initial Persona</li>
                            <li><strong>Female MC:</strong> New Social Links (Akihiko, Ken, Shinjiro), Orpheus initial Persona</li>
                        </ul>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div style="background-color: #fce4ec; border: 2px solid #e91e63; border-radius: 10px; padding: 20px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🎭 First Persona</h4>
                        <div>
                            <label for="p3p-persona1-arcana"><strong>Arcana:</strong></label>
                            <select id="p3p-persona1-arcana" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Select Arcana</option>
                                <option value="fool">Fool</option>
                                <option value="magician">Magician</option>
                                <option value="priestess">High Priestess</option>
                                <option value="empress">Empress</option>
                                <option value="emperor">Emperor</option>
                                <option value="hierophant">Hierophant</option>
                                <option value="lovers">Lovers</option>
                                <option value="chariot">Chariot</option>
                                <option value="justice">Justice</option>
                                <option value="hermit">Hermit</option>
                                <option value="fortune">Wheel of Fortune</option>
                                <option value="strength">Strength</option>
                                <option value="hanged">Hanged Man</option>
                                <option value="death">Death</option>
                                <option value="temperance">Temperance</option>
                                <option value="devil">Devil</option>
                                <option value="tower">Tower</option>
                                <option value="star">Star</option>
                                <option value="moon">Moon</option>
                                <option value="sun">Sun</option>
                                <option value="judgement">Judgement</option>
                                <option value="aeon">Aeon</option>
                            </select>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="p3p-persona1-name"><strong>Persona:</strong></label>
                            <select id="p3p-persona1-name" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Select Persona</option>
                            </select>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="p3p-persona1-level"><strong>Level:</strong></label>
                            <input type="number" id="p3p-persona1-level" value="1" min="1" max="99" class="innormal" style="width: 80px; margin-left: 10px;">
                        </div>
                    </div>
                    
                    <div style="background-color: #fce4ec; border: 2px solid #e91e63; border-radius: 10px; padding: 20px;">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🎭 Second Persona</h4>
                        <div>
                            <label for="p3p-persona2-arcana"><strong>Arcana:</strong></label>
                            <select id="p3p-persona2-arcana" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Select Arcana</option>
                                <option value="fool">Fool</option>
                                <option value="magician">Magician</option>
                                <option value="priestess">High Priestess</option>
                                <option value="empress">Empress</option>
                                <option value="emperor">Emperor</option>
                                <option value="hierophant">Hierophant</option>
                                <option value="lovers">Lovers</option>
                                <option value="chariot">Chariot</option>
                                <option value="justice">Justice</option>
                                <option value="hermit">Hermit</option>
                                <option value="fortune">Wheel of Fortune</option>
                                <option value="strength">Strength</option>
                                <option value="hanged">Hanged Man</option>
                                <option value="death">Death</option>
                                <option value="temperance">Temperance</option>
                                <option value="devil">Devil</option>
                                <option value="tower">Tower</option>
                                <option value="star">Star</option>
                                <option value="moon">Moon</option>
                                <option value="sun">Sun</option>
                                <option value="judgement">Judgement</option>
                                <option value="aeon">Aeon</option>
                            </select>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="p3p-persona2-name"><strong>Persona:</strong></label>
                            <select id="p3p-persona2-name" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Select Persona</option>
                            </select>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="p3p-persona2-level"><strong>Level:</strong></label>
                            <input type="number" id="p3p-persona2-level" value="1" min="1" max="99" class="innormal" style="width: 80px; margin-left: 10px;">
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #e8f5e9; border: 2px solid #4caf50; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🔮 P3P Settings</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="p3p-protagonist-level"><strong>Protagonist Level:</strong></label>
                            <input type="number" id="p3p-protagonist-level" value="10" min="1" max="99" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                        
                        <div>
                            <label for="p3p-social-link"><strong>Social Link Rank:</strong></label>
                            <select id="p3p-social-link" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="0">No Social Link</option>
                                <option value="1">Rank 1</option>
                                <option value="2">Rank 2</option>
                                <option value="3">Rank 3</option>
                                <option value="4">Rank 4</option>
                                <option value="5">Rank 5</option>
                                <option value="6">Rank 6</option>
                                <option value="7">Rank 7</option>
                                <option value="8">Rank 8</option>
                                <option value="9">Rank 9</option>
                                <option value="10">Rank 10 (MAX)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="p3p-difficulty"><strong>Difficulty:</strong></label>
                            <select id="p3p-difficulty" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="beginner">Beginner</option>
                                <option value="easy">Easy</option>
                                <option value="normal" selected>Normal</option>
                                <option value="hard">Hard</option>
                                <option value="maniac">Maniac</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate P3P Fusion" id="calculate-p3p-btn" style="padding: 15px 30px; font-size: 18px; background-color: #e91e63; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Clear Selection" id="clear-p3p-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> P3P fusion mechanics include portable-specific features and Social Link differences between protagonists.
        </div>
        
        <div id="p3p-results" style="display: none; margin-top: 30px;">
            <div class="p3p-result">
                <h3 style="margin-bottom: 20px;">✨ P3P Fusion Results</h3>
                <div id="p3p-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-p3p-btn');
    const clearBtn = document.getElementById('clear-p3p-btn');
    const resultsDiv = document.getElementById('p3p-results');
    const resultsContent = document.getElementById('p3p-results-content');
    
    // P3P Persona data
    const p3pPersonaData = {
        fool: [
            { name: 'Orpheus', level: 1, skills: ['Cadenza', 'Agi', 'Bash'], exclusive: false },
            { name: 'Slime', level: 12, skills: ['Acid Breath', 'Poison Mist'], exclusive: false }
        ],
        magician: [
            { name: 'Nekomata', level: 5, skills: ['Agi', 'Cleave', 'Sukukaja'], exclusive: false },
            { name: 'Jack Frost', level: 8, skills: ['Bufu', 'Ice Break', 'Mabufu'], exclusive: false }
        ],
        priestess: [
            { name: 'Pixie', level: 2, skills: ['Zio', 'Dia', 'Patra'], exclusive: false },
            { name: 'Angel', level: 4, skills: ['Hama', 'Dia', 'Media'], exclusive: false }
        ],
        lovers: [
            { name: 'Pixie', level: 2, skills: ['Zio', 'Dia', 'Patra'], exclusive: 'female' },
            { name: 'Raphael', level: 78, skills: ['Salvation', 'Samarecarm'], exclusive: 'female' }
        ],
        death: [
            { name: 'Ghoul', level: 18, skills: ['Cleave', 'Poison Skewer'], exclusive: false },
            { name: 'Thanatos', level: 64, skills: ['Door of Hades', 'Mamudoon'], exclusive: false }
        ]
    };
    
    // Social Link bonuses for P3P
    const socialLinkBonuses = {
        0: 0, 1: 1, 2: 1, 3: 1, 4: 2, 5: 2, 6: 2, 7: 3, 8: 3, 9: 3, 10: 5
    };
    
    // Populate persona dropdowns
    document.getElementById('p3p-persona1-arcana').addEventListener('change', function() {
        populateP3PPersonaDropdown('p3p-persona1-name', this.value);
    });
    
    document.getElementById('p3p-persona2-arcana').addEventListener('change', function() {
        populateP3PPersonaDropdown('p3p-persona2-name', this.value);
    });
    
    function populateP3PPersonaDropdown(dropdownId, arcana) {
        const dropdown = document.getElementById(dropdownId);
        const protagonist = document.querySelector('input[name="protagonist"]:checked').value;
        dropdown.innerHTML = '<option value="">Select Persona</option>';
        
        if (arcana && p3pPersonaData[arcana]) {
            p3pPersonaData[arcana].forEach(persona => {
                // Check if persona is available for selected protagonist
                if (persona.exclusive === false || persona.exclusive === protagonist) {
                    const option = document.createElement('option');
                    option.value = persona.name.toLowerCase().replace(/\s+/g, '_');
                    option.textContent = `${persona.name} (Lv.${persona.level})`;
                    if (persona.exclusive) {
                        option.textContent += ` [${persona.exclusive.toUpperCase()} only]`;
                    }
                    dropdown.appendChild(option);
                }
            });
        }
    }
    
    // Update persona lists when protagonist changes
    document.querySelectorAll('input[name="protagonist"]').forEach(radio => {
        radio.addEventListener('change', function() {
            const arcana1 = document.getElementById('p3p-persona1-arcana').value;
            const arcana2 = document.getElementById('p3p-persona2-arcana').value;
            if (arcana1) populateP3PPersonaDropdown('p3p-persona1-name', arcana1);
            if (arcana2) populateP3PPersonaDropdown('p3p-persona2-name', arcana2);
        });
    });
    
    calculateBtn.addEventListener('click', function() {
        const protagonist = document.querySelector('input[name="protagonist"]:checked').value;
        const arcana1 = document.getElementById('p3p-persona1-arcana').value;
        const arcana2 = document.getElementById('p3p-persona2-arcana').value;
        const persona1 = document.getElementById('p3p-persona1-name').value;
        const persona2 = document.getElementById('p3p-persona2-name').value;
        const level1 = parseInt(document.getElementById('p3p-persona1-level').value) || 1;
        const level2 = parseInt(document.getElementById('p3p-persona2-level').value) || 1;
        const protagonistLevel = parseInt(document.getElementById('p3p-protagonist-level').value) || 10;
        const socialLinkRank = parseInt(document.getElementById('p3p-social-link').value) || 0;
        const difficulty = document.getElementById('p3p-difficulty').value;
        
        if (!arcana1 || !arcana2 || !persona1 || !persona2) {
            alert('Please select both Personas and their arcanas.');
            return;
        }
        
        const fusionResult = calculateP3PFusion(protagonist, arcana1, arcana2, persona1, persona2, 
            level1, level2, protagonistLevel, socialLinkRank, difficulty);
        displayP3PResults(fusionResult);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('p3p-calculator-form').reset();
        document.getElementById('p3p-persona1-name').innerHTML = '<option value="">Select Persona</option>';
        document.getElementById('p3p-persona2-name').innerHTML = '<option value="">Select Persona</option>';
        resultsDiv.style.display = 'none';
    });
    
    function calculateP3PFusion(protagonist, arcana1, arcana2, persona1, persona2, 
                               level1, level2, protagonistLevel, socialLinkRank, difficulty) {
        
        // P3P fusion chart
        const fusionChart = {
            'fool+magician': 'death',
            'fool+priestess': 'magician',
            'magician+priestess': protagonist === 'female' ? 'lovers' : 'emperor',
            'priestess+lovers': 'empress',
            'lovers+empress': 'justice'
        };
        
        const fusionKey1 = `${arcana1}+${arcana2}`;
        const fusionKey2 = `${arcana2}+${arcana1}`;
        
        let resultArcana = fusionChart[fusionKey1] || fusionChart[fusionKey2] || 'fool';
        
        // Calculate result level with P3P bonuses
        const averageLevel = Math.floor((level1 + level2) / 2) + 1;
        const socialBonus = socialLinkBonuses[socialLinkRank] || 0;
        let resultLevel = averageLevel + socialBonus;
        
        // Difficulty modifiers
        const difficultyModifiers = {
            beginner: 2, easy: 1, normal: 0, hard: -1, maniac: -2
        };
        resultLevel += difficultyModifiers[difficulty] || 0;
        
        resultLevel = Math.max(1, Math.min(99, Math.min(resultLevel, protagonistLevel)));
        
        // Find result persona
        const availablePersonas = p3pPersonaData[resultArcana] || p3pPersonaData.fool;
        let resultPersona = availablePersonas.find(p => 
            (p.exclusive === false || p.exclusive === protagonist) && p.level <= protagonistLevel
        ) || availablePersonas[0];
        
        // Calculate fusion cost (P3P specific)
        const fusionCost = calculateP3PFusionCost(resultPersona.level, difficulty);
        
        return {
            protagonist: protagonist,
            arcana: resultArcana,
            persona: resultPersona,
            level: resultLevel,
            socialLinkRank: socialLinkRank,
            socialBonus: socialBonus,
            difficulty: difficulty,
            materials: [
                { arcana: arcana1, name: persona1, level: level1 },
                { arcana: arcana2, name: persona2, level: level2 }
            ],
            fusionCost: fusionCost,
            protagonistLevel: protagonistLevel
        };
    }
    
    function calculateP3PFusionCost(level, difficulty) {
        const baseCost = level * level * 8 + 1500;
        const difficultyMultipliers = {
            beginner: 0.5, easy: 0.75, normal: 1.0, hard: 1.25, maniac: 1.5
        };
        return Math.round(baseCost * (difficultyMultipliers[difficulty] || 1.0));
    }
    
    function displayP3PResults(result) {
        const protagonistClass = result.protagonist === 'male' ? 'male-mc' : 'female-mc';
        const protagonistName = result.protagonist === 'male' ? 'Male Protagonist' : 'Female Protagonist';
        
        let html = `
            <div style="margin-bottom: 25px;">
                <div class="protagonist-badge ${protagonistClass}">${protagonistName}</div>
                <div style="margin-top: 10px; font-size: 16px;">
                    Playing as ${result.protagonist === 'female' ? 'FeMC with unique Social Links' : 'Male MC with traditional routes'}
                </div>
            </div>
            
            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 25px;">
                <div style="text-align: center; margin: 0 15px;">
                    <h4>${result.materials[0].name}</h4>
                    <div class="p3p-arcana ${result.materials[0].arcana}">${result.materials[0].arcana.toUpperCase()}</div>
                    <div>Lv.${result.materials[0].level}</div>
                </div>
                
                <div style="font-size: 24px; margin: 0 20px;">+</div>
                
                <div style="text-align: center; margin: 0 15px;">
                    <h4>${result.materials[1].name}</h4>
                    <div class="p3p-arcana ${result.materials[1].arcana}">${result.materials[1].arcana.toUpperCase()}</div>
                    <div>Lv.${result.materials[1].level}</div>
                </div>
                
                <div style="font-size: 24px; margin: 0 20px;">=</div>
                
                <div style="text-align: center; margin: 0 15px; padding: 20px; background-color: rgba(255,255,255,0.2); border-radius: 10px;">
                    <h3 style="margin: 0;">${result.persona.name}</h3>
                    <div class="p3p-arcana ${result.arcana}">${result.arcana.toUpperCase()}</div>
                    <div style="margin-top: 10px;">Level ${result.level}</div>
                    ${result.persona.exclusive ? `<div style="font-size: 12px; margin-top: 5px;">[${result.persona.exclusive.toUpperCase()} Exclusive]</div>` : ''}
                </div>
            </div>
            
            <div style="background: white; border-radius: 10px; padding: 20px; margin: 20px 0; color: #2c3e50;">
                <h4 style="color: #e91e63; margin-bottom: 15px;">📊 P3P Fusion Details</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h5>Fusion Calculation:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Base Level:</strong> ${Math.floor((result.materials[0].level + result.materials[1].level) / 2) + 1}</li>
                            <li><strong>Social Link Bonus:</strong> +${result.socialBonus} (Rank ${result.socialLinkRank})</li>
                            <li><strong>Difficulty Modifier:</strong> ${result.difficulty}</li>
                            <li><strong>Final Level:</strong> ${result.level}</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5>P3P Features:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Protagonist:</strong> ${protagonistName}</li>
                            <li><strong>Fusion Cost:</strong> ¥${result.fusionCost.toLocaleString()}</li>
                            <li><strong>Max Level:</strong> ${result.protagonistLevel}</li>
                            <li><strong>Exclusive Access:</strong> ${result.persona.exclusive ? 'Yes' : 'No'}</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 P3P Specific Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>FeMC Route:</strong> Access to unique Personas and different Social Link options</li>
                    <li><strong>Difficulty Settings:</strong> Affect fusion costs and level bonuses</li>
                    <li><strong>Portable Features:</strong> Direct control in battles, skill cards system</li>
                    <li><strong>Social Links:</strong> Different available links between Male/Female protagonists</li>
                    <li><strong>Compendium:</strong> Register and resummon Personas with inherited skills</li>
                    <li><strong>Vision Quest:</strong> Additional dungeon exclusive to P3P</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
