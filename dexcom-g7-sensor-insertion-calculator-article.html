<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.dexcom-interface {
    background: linear-gradient(135deg, #00bcd4 0%, #0097a7 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.sensor-input {
    background-color: #e0f2f1;
    border: 2px solid #00bcd4;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.dexcom-result {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.insertion-site {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 15px;
    margin: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    width: 140px;
}
.insertion-site:hover {
    border-color: #00bcd4;
    transform: translateY(-3px);
}
.insertion-site.selected {
    border-color: #00bcd4;
    background-color: #e0f2f1;
}
.schedule-timeline {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.sensor-status {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    margin: 5px;
    color: white;
}
.active { background-color: #4caf50; }
.expiring { background-color: #ff9800; }
.expired { background-color: #f44336; }
.upcoming { background-color: #2196f3; }
</style>

<h1>Dexcom G7 Sensor Insertion Calculator</h1>
<p>Calculate optimal Dexcom G7 sensor insertion schedules and track your continuous glucose monitoring routine. Plan sensor changes, rotation sites, and maintain consistent glucose monitoring with our comprehensive G7 calculator.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="dexcomform" id="dexcom-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">G7 Calculator</a></li>
                <li><a href="#" data-tab="schedule">Schedule</a></li>
                <li><a href="#" data-tab="sites">Insertion Sites</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="dexcom-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">📱 Dexcom G7 Sensor Calculator</h3>
                
                <div class="sensor-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🩺 Current Sensor Information</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="insertion-date"><strong>Current Sensor Insertion Date:</strong></label>
                            <input type="date" id="insertion-date" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                        
                        <div>
                            <label for="insertion-time"><strong>Insertion Time:</strong></label>
                            <input type="time" id="insertion-time" value="08:00" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="sensor-duration"><strong>Sensor Duration:</strong></label>
                            <select id="sensor-duration" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="10" selected>10 Days (Standard G7)</option>
                                <option value="14">14 Days (Extended wear)</option>
                                <option value="7">7 Days (Early change)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="sensors-available"><strong>Sensors Available:</strong></label>
                            <input type="number" id="sensors-available" value="3" min="0" max="20" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #e0f2f1; border: 2px solid #00bcd4; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">📍 Preferred Insertion Sites</h4>
                    
                    <div style="text-align: center; margin-bottom: 20px;">
                        <div class="insertion-site" data-site="upper-arm-left">
                            <div style="font-size: 24px;">💪</div>
                            <div><strong>Left Upper Arm</strong></div>
                            <div style="font-size: 12px;">Most common</div>
                        </div>
                        
                        <div class="insertion-site" data-site="upper-arm-right">
                            <div style="font-size: 24px;">💪</div>
                            <div><strong>Right Upper Arm</strong></div>
                            <div style="font-size: 12px;">Most common</div>
                        </div>
                        
                        <div class="insertion-site" data-site="abdomen-left">
                            <div style="font-size: 24px;">🫄</div>
                            <div><strong>Left Abdomen</strong></div>
                            <div style="font-size: 12px;">Alternative site</div>
                        </div>
                        
                        <div class="insertion-site" data-site="abdomen-right">
                            <div style="font-size: 24px;">🫄</div>
                            <div><strong>Right Abdomen</strong></div>
                            <div style="font-size: 12px;">Alternative site</div>
                        </div>
                        
                        <div class="insertion-site" data-site="thigh-left">
                            <div style="font-size: 24px;">🦵</div>
                            <div><strong>Left Thigh</strong></div>
                            <div style="font-size: 12px;">Less common</div>
                        </div>
                        
                        <div class="insertion-site" data-site="thigh-right">
                            <div style="font-size: 24px;">🦵</div>
                            <div><strong>Right Thigh</strong></div>
                            <div style="font-size: 12px;">Less common</div>
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #f1f8e9; border: 2px solid #4caf50; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">⚙️ Preferences & Settings</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="reminder-hours"><strong>Reminder Before Expiry:</strong></label>
                            <select id="reminder-hours" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="24" selected>24 hours</option>
                                <option value="12">12 hours</option>
                                <option value="6">6 hours</option>
                                <option value="2">2 hours</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="rotation-pattern"><strong>Site Rotation Pattern:</strong></label>
                            <select id="rotation-pattern" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="alternating" selected>Alternating Arms</option>
                                <option value="clockwise">Clockwise Rotation</option>
                                <option value="random">Random Selection</option>
                                <option value="custom">Custom Pattern</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate G7 Schedule" id="calculate-dexcom-btn" style="padding: 15px 30px; font-size: 18px; background-color: #00bcd4; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Calculator" id="clear-dexcom-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Disclaimer:</strong> This calculator is for planning purposes only. Always follow your healthcare provider's instructions and Dexcom's official guidelines.
        </div>
        
        <div id="dexcom-results" style="display: none; margin-top: 30px;">
            <div class="dexcom-result">
                <h3 style="margin-bottom: 20px;">📅 G7 Sensor Schedule</h3>
                <div id="dexcom-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-dexcom-btn');
    const clearBtn = document.getElementById('clear-dexcom-btn');
    const resultsDiv = document.getElementById('dexcom-results');
    const resultsContent = document.getElementById('dexcom-results-content');
    const insertionSites = document.querySelectorAll('.insertion-site');
    
    let selectedSites = ['upper-arm-left', 'upper-arm-right']; // Default selection
    
    // Set default insertion date to today
    const today = new Date();
    document.getElementById('insertion-date').value = today.toISOString().split('T')[0];
    
    // Handle insertion site selection
    insertionSites.forEach(site => {
        site.addEventListener('click', function() {
            const siteId = this.dataset.site;
            
            if (selectedSites.includes(siteId)) {
                selectedSites = selectedSites.filter(s => s !== siteId);
                this.classList.remove('selected');
            } else {
                selectedSites.push(siteId);
                this.classList.add('selected');
            }
        });
    });
    
    // Pre-select default sites
    insertionSites.forEach(site => {
        if (selectedSites.includes(site.dataset.site)) {
            site.classList.add('selected');
        }
    });
    
    calculateBtn.addEventListener('click', function() {
        const insertionDate = document.getElementById('insertion-date').value;
        const insertionTime = document.getElementById('insertion-time').value;
        const sensorDuration = parseInt(document.getElementById('sensor-duration').value) || 10;
        const sensorsAvailable = parseInt(document.getElementById('sensors-available').value) || 3;
        const reminderHours = parseInt(document.getElementById('reminder-hours').value) || 24;
        const rotationPattern = document.getElementById('rotation-pattern').value;
        
        if (!insertionDate) {
            alert('Please select the current sensor insertion date.');
            return;
        }
        
        if (selectedSites.length === 0) {
            alert('Please select at least one insertion site.');
            return;
        }
        
        const dexcomSchedule = calculateDexcomSchedule(insertionDate, insertionTime, sensorDuration, 
            sensorsAvailable, reminderHours, rotationPattern, selectedSites);
        
        displayDexcomResults(dexcomSchedule);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('dexcom-calculator-form').reset();
        insertionSites.forEach(site => site.classList.remove('selected'));
        selectedSites = ['upper-arm-left', 'upper-arm-right'];
        insertionSites.forEach(site => {
            if (selectedSites.includes(site.dataset.site)) {
                site.classList.add('selected');
            }
        });
        const today = new Date();
        document.getElementById('insertion-date').value = today.toISOString().split('T')[0];
        resultsDiv.style.display = 'none';
    });
    
    function calculateDexcomSchedule(insertionDate, insertionTime, duration, sensorsAvailable, 
                                   reminderHours, rotationPattern, sites) {
        
        const startDateTime = new Date(`${insertionDate}T${insertionTime}`);
        const currentTime = new Date();
        
        // Calculate current sensor status
        const timeElapsed = currentTime - startDateTime;
        const hoursElapsed = timeElapsed / (1000 * 60 * 60);
        const daysElapsed = hoursElapsed / 24;
        const totalHours = duration * 24;
        const remainingHours = Math.max(0, totalHours - hoursElapsed);
        
        // Calculate expiry date
        const expiryDate = new Date(startDateTime.getTime() + (duration * 24 * 60 * 60 * 1000));
        
        // Calculate reminder time
        const reminderDate = new Date(expiryDate.getTime() - (reminderHours * 60 * 60 * 1000));
        
        // Generate future sensor schedule
        const schedule = [];
        let currentSensorDate = new Date(startDateTime);
        let siteIndex = 0;
        
        for (let i = 0; i < sensorsAvailable + 1; i++) {
            const sensorStart = new Date(currentSensorDate);
            const sensorEnd = new Date(sensorStart.getTime() + (duration * 24 * 60 * 60 * 1000));
            const sensorReminder = new Date(sensorEnd.getTime() - (reminderHours * 60 * 60 * 1000));
            
            // Determine insertion site based on rotation pattern
            let insertionSite = sites[siteIndex % sites.length];
            if (rotationPattern === 'alternating' && sites.includes('upper-arm-left') && sites.includes('upper-arm-right')) {
                insertionSite = i % 2 === 0 ? 'upper-arm-left' : 'upper-arm-right';
            } else if (rotationPattern === 'random') {
                insertionSite = sites[Math.floor(Math.random() * sites.length)];
            }
            
            // Determine status
            let status = 'upcoming';
            if (i === 0) {
                if (currentTime < sensorReminder) status = 'active';
                else if (currentTime < sensorEnd) status = 'expiring';
                else status = 'expired';
            }
            
            schedule.push({
                sensorNumber: i + 1,
                insertionDate: sensorStart,
                expiryDate: sensorEnd,
                reminderDate: sensorReminder,
                insertionSite: insertionSite,
                status: status,
                daysRemaining: Math.max(0, Math.ceil((sensorEnd - currentTime) / (1000 * 60 * 60 * 24)))
            });
            
            currentSensorDate = new Date(sensorEnd);
            siteIndex++;
        }
        
        return {
            currentSensor: {
                insertionDate: startDateTime,
                expiryDate: expiryDate,
                reminderDate: reminderDate,
                hoursElapsed: hoursElapsed,
                daysElapsed: daysElapsed,
                remainingHours: remainingHours,
                remainingDays: Math.max(0, Math.ceil(remainingHours / 24)),
                status: schedule[0].status
            },
            settings: {
                duration: duration,
                sensorsAvailable: sensorsAvailable,
                reminderHours: reminderHours,
                rotationPattern: rotationPattern,
                selectedSites: sites
            },
            schedule: schedule
        };
    }
    
    function displayDexcomResults(data) {
        const siteNames = {
            'upper-arm-left': 'Left Upper Arm',
            'upper-arm-right': 'Right Upper Arm',
            'abdomen-left': 'Left Abdomen',
            'abdomen-right': 'Right Abdomen',
            'thigh-left': 'Left Thigh',
            'thigh-right': 'Right Thigh'
        };
        
        const statusClass = data.currentSensor.status;
        const statusText = {
            active: 'Active & Working',
            expiring: 'Expiring Soon',
            expired: 'Expired - Replace Now',
            upcoming: 'Upcoming'
        };
        
        let html = `
            <div style="margin-bottom: 25px;">
                <div class="sensor-status ${statusClass}">
                    ${statusText[data.currentSensor.status]}
                </div>
                <div style="font-size: 18px; margin-top: 10px;">
                    ${data.currentSensor.remainingDays} days remaining
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Current Sensor</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #00bcd4;">
                        Day ${Math.ceil(data.currentSensor.daysElapsed)}
                    </div>
                    <div style="font-size: 12px; color: #666;">of ${data.settings.duration} days</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Next Change</h4>
                    <div style="font-size: 20px; font-weight: bold; color: #4caf50;">
                        ${data.currentSensor.expiryDate.toLocaleDateString()}
                    </div>
                    <div style="font-size: 12px; color: #666;">${data.currentSensor.expiryDate.toLocaleTimeString([], {hour: '2-digit', minute:'2-digit'})}</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Reminder Alert</h4>
                    <div style="font-size: 20px; font-weight: bold; color: #ff9800;">
                        ${data.currentSensor.reminderDate.toLocaleDateString()}
                    </div>
                    <div style="font-size: 12px; color: #666;">${data.settings.reminderHours}h before expiry</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Sensors Available</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #2196f3;">
                        ${data.settings.sensorsAvailable}
                    </div>
                    <div style="font-size: 12px; color: #666;">In supply</div>
                </div>
            </div>
            
            <div class="schedule-timeline">
                <h4 style="color: #00bcd4; margin-bottom: 20px;">📅 Upcoming Sensor Schedule</h4>
                
                <div style="margin-bottom: 20px;">
                    <h5>Rotation Pattern: ${data.settings.rotationPattern.charAt(0).toUpperCase() + data.settings.rotationPattern.slice(1)}</h5>
                    <div style="font-size: 14px; color: #666;">
                        Selected sites: ${data.settings.selectedSites.map(site => siteNames[site]).join(', ')}
                    </div>
                </div>
                
                <div style="max-height: 300px; overflow-y: auto;">
        `;
        
        data.schedule.forEach((sensor, index) => {
            const isCurrentSensor = index === 0;
            const bgColor = isCurrentSensor ? '#e3f2fd' : '#f5f5f5';
            
            html += `
                <div style="display: flex; justify-content: space-between; align-items: center; padding: 15px; margin: 10px 0; background-color: ${bgColor}; border-radius: 8px; border-left: 4px solid ${isCurrentSensor ? '#00bcd4' : '#e0e0e0'};">
                    <div>
                        <div style="font-weight: bold;">Sensor #${sensor.sensorNumber} ${isCurrentSensor ? '(Current)' : ''}</div>
                        <div style="font-size: 14px; color: #666;">
                            ${sensor.insertionDate.toLocaleDateString()} - ${sensor.expiryDate.toLocaleDateString()}
                        </div>
                        <div style="font-size: 12px; color: #888;">
                            Site: ${siteNames[sensor.insertionSite]}
                        </div>
                    </div>
                    <div style="text-align: right;">
                        <div class="sensor-status ${sensor.status}" style="font-size: 12px; padding: 4px 8px;">
                            ${statusText[sensor.status]}
                        </div>
                        ${sensor.status === 'upcoming' ? `<div style="font-size: 12px; color: #666; margin-top: 5px;">In ${sensor.daysRemaining} days</div>` : ''}
                    </div>
                </div>
            `;
        });
        
        html += `
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 Dexcom G7 Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Site Rotation:</strong> Rotate insertion sites to prevent skin irritation</li>
                    <li><strong>Skin Prep:</strong> Clean insertion site with alcohol and let dry completely</li>
                    <li><strong>Adhesive:</strong> Use skin tac or adhesive patches for better retention</li>
                    <li><strong>Warm-up:</strong> G7 has a 30-minute warm-up period after insertion</li>
                    <li><strong>Accuracy:</strong> Most accurate readings are from upper arms</li>
                    <li><strong>Backup:</strong> Always have backup sensors and supplies available</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
