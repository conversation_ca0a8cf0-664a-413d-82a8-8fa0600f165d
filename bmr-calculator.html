<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>BMR Calculator</title>
    <meta name="description" content="Calculate your Basal Metabolic Rate (BMR) using <PERSON><PERSON><PERSON><PERSON>, <PERSON>, or <PERSON><PERSON><PERSON><PERSON><PERSON> equations. Free BMR calculator with detailed analysis.">
    <link rel="stylesheet" href="./Carbohydrate Calculator_files/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Mobile responsive styles */
        @media screen and (max-width: 768px) {
            #contentout { width: 100% !important; padding: 0 10px; }
            #content { width: 100% !important; margin: 0; }
            #right { display: none; }
            .panel2 { width: 100% !important; padding: 5px !important; }
            table { width: 100% !important; font-size: 14px; }
            td { padding: 8px 4px !important; word-wrap: break-word; }
            input, select { width: 100% !important; max-width: 100% !important; box-sizing: border-box; font-size: 16px; }
            .inlongest, .innormal, .inlongesthalf { width: 100% !important; }
            h1 { font-size: 24px; }
            .cinfoT { font-size: 12px; overflow-x: auto; }
            .cinfoT td { padding: 4px 2px !important; }
            .cbcontainer { font-size: 12px; margin: 2px 0; }
            #topmenu ul li { display: inline-block; margin: 2px; font-size: 12px; }
        }
        @media screen and (max-width: 480px) {
            table td:first-child { width: 30% !important; }
            table td:last-child { width: 70% !important; }
            input[type="submit"], input[type="button"] {
                width: 100% !important; margin: 5px 0 !important; padding: 12px !important; font-size: 14px;
            }
            .inuifoot, .inuiinch { width: 45% !important; display: inline-block; }
            .cbcontainer { display: block; margin: 3px 0; }
        }
    </style>
    <link rel="apple-touch-icon" sizes="180x180" href="https://d26tpo4cm8sb6k.cloudfront.net/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-16x16.png">
    <link rel="manifest" href="https://www.calculator.net/manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="https://www.calculator.net/"><img src="./Carbohydrate Calculator_files/calculator-white.svg" width="208" height="22" alt="Calculator.net"></a></div>
        <div id="login"><a href="https://www.calculator.net/my-account/sign-in.php">sign in</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/" itemprop="item"><span itemprop="name">home</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/fitness-and-health-calculator.html" itemprop="item"><span itemprop="name">fitness &amp; health</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="#" itemprop="item"><span itemprop="name">bmr calculator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="window.print();return false;">Print</a></div>
        
        <h1>BMR Calculator</h1>
        <p>The <i>BMR Calculator</i> will calculate your Basal Metabolic Rate (BMR); the number of calories you'd burn if you stayed in bed all day. If you've noticed that every year, it becomes harder to eat whatever you want and stay slim, you've also learnt that your BMR decreases as you age.</p>
        
        <div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
        <span id="ucframe"></span>
        
        <div id="topmenu" class="topmenucenter">
            <ul>
                <li><a href="#" onclick="return showUnits('standard');">US Units</a></li>
                <li id="menuon"><a href="#" onclick="return showUnits('metric');">Metric Units</a></li>
                <li><a href="#" onclick="return showUnits('other');">Other Units</a></li>
            </ul>
        </div>

        <div class="panel2" style="padding:10px;">
            <form name="bmrForm" id="bmr-calculator-form">
                <table id="bmr-input-table" width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">Age</td>
                            <td width="550"><input type="number" name="age" id="age" value="25" class="inlongesthalf" min="15" max="80"> ages 15 - 80</td>
                        </tr>
                        <tr>
                            <td>Gender</td>
                            <td>
                                <label for="male" class="cbcontainer"><input type="radio" name="gender" id="male" value="male" checked=""><span class="rbmark"></span>male</label> &nbsp; 
                                <label for="female" class="cbcontainer"><input type="radio" name="gender" id="female" value="female"><span class="rbmark"></span>female</label>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- US Units -->
                <table width="620" id="standard-units" bgcolor="#eeeeee" style="display: none;">
                    <tbody>
                        <tr>
                            <td width="70">Height</td>
                            <td width="550">
                                <input type="number" name="feet" id="feet" value="5" class="inlongesthalf inuifoot" min="0" max="8"><span class="inuifootspan">feet</span>
                                &nbsp;&nbsp;
                                <input type="number" name="inches" id="inches" value="10" class="inlongesthalf inuiinch" min="0" max="11"><span class="inuiinchspan">inches</span>
                            </td>
                        </tr>
                        <tr>
                            <td>Weight</td>
                            <td><input type="number" name="pounds" id="pounds" value="160" class="inlongest inuipound" min="50" max="1000"><span class="inuipoundspan">pounds</span></td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- Metric Units -->
                <table width="620" id="metric-units" bgcolor="#eeeeee" style="display: block;">
                    <tbody>
                        <tr>
                            <td width="70">Height</td>
                            <td width="550"><input type="number" name="height_cm" id="height_cm" value="180" class="inlongest inuick" min="100" max="250" step="0.1"><span class="inuickspan">cm</span></td>
                        </tr>
                        <tr>
                            <td>Weight</td>
                            <td><input type="number" name="weight_kg" id="weight_kg" value="70" class="inlongest inuick" min="20" max="300" step="0.1"><span class="inuickspan">kg</span></td>
                        </tr>
                    </tbody>
                </table>
                
                <table width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">Formula</td>
                            <td width="550">
                                <select id="formula" name="formula" class="inlongest">
                                    <option value="mifflin" selected="">Mifflin-St Jeor Equation</option>
                                    <option value="harris">Revised Harris-Benedict Equation</option>
                                    <option value="katch">Katch-McArdle Equation</option>
                                </select>
                            </td>
                        </tr>
                        <tr id="bodyfat-row" style="display: none;">
                            <td>Body Fat</td>
                            <td><input type="number" name="bodyfat" id="bodyfat" value="15" class="innormal inpct" min="5" max="50" step="0.1"></td>
                        </tr>
                        <tr>
                            <td width="70">&nbsp;</td>
                            <td width="550">
                                <input type="submit" name="x" value="Calculate BMR" id="calculate-btn">
                                <input type="button" value="Clear" id="clear-btn" onclick="clearBMRForm();">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </form>
        </div>

        <ul style="margin-top:0px;margin-left:-20px;">
            <li class="smalltext"><b>BMR:</b> The number of calories your body burns at rest to maintain vital functions.</li>
            <li class="smalltext"><b>Mifflin-St Jeor:</b> Most accurate for most people (recommended).</li>
            <li class="smalltext"><b>Katch-McArdle:</b> More accurate if you know your body fat percentage.</li>
        </ul>

        <!-- Results Display Area -->
        <div id="bmr-results" style="display: none; margin-top: 20px;">
            <div class="h2result" id="bmr-results-content"></div>
        </div>

        <br>
        <fieldset>
            <legend>Related</legend>
            <a href="#">Calorie Calculator</a> | 
            <a href="#">Body Fat Calculator</a> | 
            <a href="#">Macro Calculator</a>
        </fieldset>

        <br>
        <h3>What is BMR?</h3>
        <p>Basal Metabolic Rate (BMR) is the number of calories required to keep your body functioning at rest. BMR is also known as your body's metabolism; therefore, any increase to your metabolic weight, such as exercise, will increase your BMR. To get your BMR, simply input your height, gender, age and weight below. Once you've determined your BMR, you can begin to monitor how many calories a day you need to maintain or lose weight.</p>

        <h3>BMR Formulas</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Formula</td>
                    <td class="cinfoHdL">Men</td>
                    <td class="cinfoHdL">Women</td>
                    <td class="cinfoHdL">Notes</td>
                </tr>
                <tr>
                    <td><strong>Mifflin-St Jeor</strong></td>
                    <td>10×weight + 6.25×height - 5×age + 5</td>
                    <td>10×weight + 6.25×height - 5×age - 161</td>
                    <td>Most accurate for general population</td>
                </tr>
                <tr>
                    <td><strong>Harris-Benedict</strong></td>
                    <td>88.362 + 13.397×weight + 4.799×height - 5.677×age</td>
                    <td>447.593 + 9.247×weight + 3.098×height - 4.330×age</td>
                    <td>Original formula, less accurate</td>
                </tr>
                <tr>
                    <td><strong>Katch-McArdle</strong></td>
                    <td colspan="2">370 + 21.6×LBM (Lean Body Mass)</td>
                    <td>Most accurate if body fat % known</td>
                </tr>
            </tbody>
        </table>

        <h3>Activity Level Multipliers</h3>
        <p>To calculate your Total Daily Energy Expenditure (TDEE), multiply your BMR by the appropriate activity factor:</p>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Activity Level</td>
                    <td class="cinfoHdL">Multiplier</td>
                    <td class="cinfoHdL">Description</td>
                </tr>
                <tr><td><strong>Sedentary</strong></td><td>1.2</td><td>Little or no exercise</td></tr>
                <tr><td><strong>Light</strong></td><td>1.375</td><td>Light exercise 1-3 times/week</td></tr>
                <tr><td><strong>Moderate</strong></td><td>1.55</td><td>Moderate exercise 4-5 times/week</td></tr>
                <tr><td><strong>Active</strong></td><td>1.725</td><td>Heavy exercise 6-7 times/week</td></tr>
                <tr><td><strong>Very Active</strong></td><td>1.9</td><td>Very heavy exercise, physical job</td></tr>
            </tbody>
        </table>

        <h3>Factors Affecting BMR</h3>
        <ul>
            <li><strong>Age:</strong> BMR decreases with age as muscle mass typically decreases</li>
            <li><strong>Gender:</strong> Men generally have higher BMR due to more muscle mass</li>
            <li><strong>Body Composition:</strong> More muscle mass increases BMR</li>
            <li><strong>Body Size:</strong> Larger bodies require more energy to maintain</li>
            <li><strong>Temperature:</strong> Cold environments can increase BMR</li>
            <li><strong>Health:</strong> Fever, illness, and some medications can affect BMR</li>
            <li><strong>Genetics:</strong> Some people naturally have faster or slower metabolisms</li>
        </ul>

        <h3>How to Increase BMR</h3>
        <ul>
            <li>Build muscle through strength training exercises</li>
            <li>Eat enough protein to maintain muscle mass</li>
            <li>Stay hydrated and get adequate sleep</li>
            <li>Include cardiovascular exercise in your routine</li>
            <li>Don't severely restrict calories (can lower BMR)</li>
            <li>Consider high-intensity interval training (HIIT)</li>
        </ul>
    </div>
    
    <div id="right">
        <div style="padding-top:10px; min-height:280px; text-align:center;">
            <div style="width: 300px; height: 250px; background: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; color: #666;">
                Advertisement Space
            </div>
        </div>
        
        <form name="calcSearchForm" onsubmit="return false;" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td><input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators..."></td>
                        <td><span id="bluebtn" onclick="performSearch();">Search</span></td>
                    </tr>
                    <tr>
                        <td colspan="2"><div id="calcSearchOut"></div></td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="#">Fitness and Health Calculators</a>
            </div>
            <div id="occontent">
                <a href="#">BMI Calculator</a>
                <a href="#">Body Fat Calculator</a>
                <a href="#">Calorie Calculator</a>
                <a href="#">BMR Calculator</a>
                <a href="#">Ideal Weight Calculator</a>
                <a href="#">Macro Calculator</a>
                <a href="#">Pace Calculator</a>
                <a href="#">Pregnancy Calculator</a>
                <a href="#">Heart Rate Calculator</a>
                <a href="#">More Fitness Calculators</a>
            </div>
            <div id="ocother">
                <a href="#">Financial</a> | 
                <a href="#">Fitness and Health</a> | 
                <a href="#">Math</a> | 
                <a href="#">Other</a>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="#">about us</a> | 
            <a href="#">sitemap</a> | 
            <a href="#">terms of use</a> | 
            <a href="#">privacy policy</a> &nbsp;  © 2008 - 2025 <a href="#">calculator.net</a>
        </div>
    </div>
</div>

<div class="topNavAbs">
    <a href="#">Financial</a>
    <a href="#" class="topNavOn">Fitness &amp; Health</a>
    <a href="#">Math</a>
    <a href="#">Other</a>
</div>

<script>
let currentUnits = 'metric';

function showUnits(unitType) {
    document.getElementById('standard-units').style.display = 'none';
    document.getElementById('metric-units').style.display = 'none';
    
    if (unitType === 'standard') {
        document.getElementById('standard-units').style.display = 'block';
    } else if (unitType === 'metric') {
        document.getElementById('metric-units').style.display = 'block';
    }
    
    const menuItems = document.querySelectorAll('#topmenu ul li');
    menuItems.forEach(item => {
        item.id = '';
        const link = item.querySelector('a');
        if (link && link.textContent.toLowerCase().includes(unitType === 'standard' ? 'us' : unitType)) {
            item.id = 'menuon';
        }
    });
    
    currentUnits = unitType;
    return false;
}

// Show/hide body fat field based on formula selection
document.getElementById('formula').addEventListener('change', function() {
    const bodyfatRow = document.getElementById('bodyfat-row');
    bodyfatRow.style.display = this.value === 'katch' ? '' : 'none';
});

document.getElementById('calculate-btn').addEventListener('click', function(e) {
    e.preventDefault();
    calculateBMR();
});

function calculateBMR() {
    const age = parseInt(document.getElementById('age').value);
    const gender = document.querySelector('input[name="gender"]:checked').value;
    const formula = document.getElementById('formula').value;
    
    let height, weight;
    
    if (currentUnits === 'metric') {
        height = parseFloat(document.getElementById('height_cm').value);
        weight = parseFloat(document.getElementById('weight_kg').value);
    } else {
        const feet = parseFloat(document.getElementById('feet').value) || 0;
        const inches = parseFloat(document.getElementById('inches').value) || 0;
        const pounds = parseFloat(document.getElementById('pounds').value) || 0;
        
        height = ((feet * 12) + inches) * 2.54; // Convert to cm
        weight = pounds * 0.453592; // Convert to kg
    }
    
    if (age <= 0 || height <= 0 || weight <= 0) {
        alert('Please enter valid values for all fields');
        return;
    }
    
    let bmr;
    
    if (formula === 'mifflin') {
        if (gender === 'male') {
            bmr = 10 * weight + 6.25 * height - 5 * age + 5;
        } else {
            bmr = 10 * weight + 6.25 * height - 5 * age - 161;
        }
    } else if (formula === 'harris') {
        if (gender === 'male') {
            bmr = 88.362 + (13.397 * weight) + (4.799 * height) - (5.677 * age);
        } else {
            bmr = 447.593 + (9.247 * weight) + (3.098 * height) - (4.330 * age);
        }
    } else if (formula === 'katch') {
        const bodyfat = parseFloat(document.getElementById('bodyfat').value);
        if (!bodyfat || bodyfat <= 0) {
            alert('Please enter a valid body fat percentage for Katch-McArdle formula');
            return;
        }
        const leanBodyMass = weight * (1 - bodyfat / 100);
        bmr = 370 + (21.6 * leanBodyMass);
    }
    
    // Calculate TDEE for different activity levels
    const activityLevels = [
        { name: 'Sedentary', multiplier: 1.2 },
        { name: 'Light Exercise', multiplier: 1.375 },
        { name: 'Moderate Exercise', multiplier: 1.55 },
        { name: 'Heavy Exercise', multiplier: 1.725 },
        { name: 'Very Heavy Exercise', multiplier: 1.9 }
    ];
    
    let tdeeTable = '<table class="cinfoT" style="margin-top: 10px;"><tbody>';
    tdeeTable += '<tr><td class="cinfoHd">Activity Level</td><td class="cinfoHd">Calories/Day</td></tr>';
    
    activityLevels.forEach(level => {
        const tdee = Math.round(bmr * level.multiplier);
        tdeeTable += `<tr><td><strong>${level.name}</strong></td><td>${tdee}</td></tr>`;
    });
    
    tdeeTable += '</tbody></table>';
    
    const resultsDiv = document.getElementById('bmr-results-content');
    resultsDiv.innerHTML = `
        Your BMR is ${Math.round(bmr)} calories/day<br>
        Formula used: ${getFormulaName(formula)}<br><br>
        <strong>Total Daily Energy Expenditure (TDEE):</strong><br>
        ${tdeeTable}
    `;
    document.getElementById('bmr-results').style.display = 'block';
}

function getFormulaName(formula) {
    const names = {
        'mifflin': 'Mifflin-St Jeor Equation',
        'harris': 'Revised Harris-Benedict Equation',
        'katch': 'Katch-McArdle Equation'
    };
    return names[formula] || formula;
}

function clearBMRForm() {
    document.getElementById('age').value = '25';
    document.getElementById('male').checked = true;
    document.getElementById('formula').selectedIndex = 0;
    document.getElementById('bodyfat').value = '15';
    
    if (currentUnits === 'metric') {
        document.getElementById('height_cm').value = '180';
        document.getElementById('weight_kg').value = '70';
    } else {
        document.getElementById('feet').value = '5';
        document.getElementById('inches').value = '10';
        document.getElementById('pounds').value = '160';
    }
    
    document.getElementById('bodyfat-row').style.display = 'none';
    document.getElementById('bmr-results').style.display = 'none';
}

function performSearch() {
    const searchTerm = document.getElementById('calcSearchTerm').value;
    const searchOut = document.getElementById('calcSearchOut');
    
    if (searchTerm.trim() === '') {
        searchOut.innerHTML = '';
        return;
    }
    
    const calculators = [
        'BMI Calculator', 'Body Fat Calculator', 'Calorie Calculator', 
        'BMR Calculator', 'Ideal Weight Calculator', 'Macro Calculator'
    ];
    
    const results = calculators.filter(calc => 
        calc.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    if (results.length > 0) {
        searchOut.innerHTML = '<div>' + results.map(result => 
            `<a href="#" style="display: block; padding: 2px 0;">${result}</a>`
        ).join('') + '</div>';
    } else {
        searchOut.innerHTML = '<div>No calculators found</div>';
    }
}

showUnits('metric');
</script>

</body>
</html>
