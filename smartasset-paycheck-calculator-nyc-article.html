<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.paycheck-interface {
    background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.salary-input {
    background-color: #e3f2fd;
    border: 2px solid #1976d2;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.paycheck-result {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.tax-breakdown {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.deduction-item {
    background: #f5f5f5;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.nyc-specific {
    background-color: #fff3e0;
    border: 2px solid #ff9800;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.pay-frequency {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    margin: 5px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}
.pay-frequency.selected { background-color: #1976d2; }
.pay-frequency:not(.selected) { background-color: #9e9e9e; }
.take-home-display {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
}
</style>

<h1>SmartAsset Paycheck Calculator NYC</h1>
<p>Calculate your take-home pay in New York City with our comprehensive NYC paycheck calculator. Account for federal, state, and city taxes, plus all deductions specific to NYC employees. Get accurate net pay calculations for all pay frequencies.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="nycpaycheckform" id="nyc-paycheck-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">NYC Paycheck</a></li>
                <li><a href="#" data-tab="taxes">Tax Breakdown</a></li>
                <li><a href="#" data-tab="benefits">Benefits Guide</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="paycheck-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">💰 NYC Paycheck Calculator</h3>
                
                <div class="salary-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">💵 Salary Information</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="annual-salary"><strong>Annual Salary:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="annual-salary" value="75000" min="0" max="1000000" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="pay-frequency"><strong>Pay Frequency:</strong></label>
                            <div style="margin-top: 10px;">
                                <div class="pay-frequency selected" data-frequency="weekly">Weekly (52)</div>
                                <div class="pay-frequency" data-frequency="biweekly">Bi-weekly (26)</div>
                                <div class="pay-frequency" data-frequency="semimonthly">Semi-monthly (24)</div>
                                <div class="pay-frequency" data-frequency="monthly">Monthly (12)</div>
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="filing-status"><strong>Filing Status:</strong></label>
                            <select id="filing-status" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="single">Single</option>
                                <option value="married-joint">Married Filing Jointly</option>
                                <option value="married-separate">Married Filing Separately</option>
                                <option value="head">Head of Household</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="allowances"><strong>Federal Allowances:</strong></label>
                            <input type="number" id="allowances" value="1" min="0" max="20" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                    </div>
                </div>
                
                <div class="nyc-specific">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🏙️ NYC Specific Information</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="nyc-resident">
                                <input type="checkbox" id="nyc-resident" checked style="margin-right: 8px;">
                                <strong>NYC Resident (subject to NYC tax)</strong>
                            </label>
                            
                            <div style="margin-top: 15px;">
                                <label for="ny-state-allowances"><strong>NY State Allowances:</strong></label>
                                <input type="number" id="ny-state-allowances" value="1" min="0" max="20" class="innormal" style="width: 100%; margin-top: 5px;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="disability-insurance">
                                <input type="checkbox" id="disability-insurance" checked style="margin-right: 8px;">
                                <strong>NY Disability Insurance</strong>
                            </label>
                            
                            <div style="margin-top: 15px;">
                                <label for="family-leave">
                                    <input type="checkbox" id="family-leave" checked style="margin-right: 8px;">
                                    <strong>NY Paid Family Leave</strong>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #f1f8e9; border: 2px solid #4caf50; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🏥 Pre-tax Deductions</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="health-insurance"><strong>Health Insurance:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="health-insurance" value="0" min="0" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="dental-insurance"><strong>Dental Insurance:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="dental-insurance" value="0" min="0" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="retirement-401k"><strong>401(k) Contribution:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="retirement-401k" value="0" min="0" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate NYC Paycheck" id="calculate-nyc-btn" style="padding: 15px 30px; font-size: 18px; background-color: #1976d2; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Calculator" id="clear-nyc-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Disclaimer:</strong> This calculator provides estimates based on 2024 tax rates. Consult a tax professional for precise calculations.
        </div>
        
        <div id="nyc-results" style="display: none; margin-top: 30px;">
            <div class="take-home-display">
                <h3 style="margin-bottom: 20px;">💸 Your NYC Take-Home Pay</h3>
                <div id="nyc-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-nyc-btn');
    const clearBtn = document.getElementById('clear-nyc-btn');
    const resultsDiv = document.getElementById('nyc-results');
    const resultsContent = document.getElementById('nyc-results-content');
    const payFrequencyBtns = document.querySelectorAll('.pay-frequency');
    
    let selectedFrequency = 'weekly';
    
    // Handle pay frequency selection
    payFrequencyBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            payFrequencyBtns.forEach(b => b.classList.remove('selected'));
            this.classList.add('selected');
            selectedFrequency = this.dataset.frequency;
        });
    });
    
    // NYC tax rates and thresholds (2024)
    const nycTaxData = {
        federal: {
            standardDeduction: { single: 13850, marriedJoint: 27700, marriedSeparate: 13850, head: 20800 },
            brackets: [
                { min: 0, max: 11000, rate: 0.10 },
                { min: 11000, max: 44725, rate: 0.12 },
                { min: 44725, max: 95375, rate: 0.22 },
                { min: 95375, max: 182050, rate: 0.24 },
                { min: 182050, max: 231250, rate: 0.32 },
                { min: 231250, max: 578125, rate: 0.35 },
                { min: 578125, max: Infinity, rate: 0.37 }
            ]
        },
        nyState: {
            standardDeduction: { single: 8000, marriedJoint: 16050, marriedSeparate: 8025, head: 11200 },
            brackets: [
                { min: 0, max: 8500, rate: 0.04 },
                { min: 8500, max: 11700, rate: 0.045 },
                { min: 11700, max: 13900, rate: 0.0525 },
                { min: 13900, max: 80650, rate: 0.055 },
                { min: 80650, max: 215400, rate: 0.06 },
                { min: 215400, max: 1077550, rate: 0.0685 },
                { min: 1077550, max: Infinity, rate: 0.0965 }
            ]
        },
        nyc: {
            brackets: [
                { min: 0, max: 12000, rate: 0.03078 },
                { min: 12000, max: 25000, rate: 0.03762 },
                { min: 25000, max: 50000, rate: 0.03819 },
                { min: 50000, max: Infinity, rate: 0.03876 }
            ]
        },
        socialSecurity: { rate: 0.062, cap: 160200 },
        medicare: { rate: 0.0145, additionalRate: 0.009, threshold: 200000 },
        nyDisability: { rate: 0.005, cap: 142800 },
        nyFamilyLeave: { rate: 0.00455, cap: 142800 }
    };
    
    calculateBtn.addEventListener('click', function() {
        const annualSalary = parseFloat(document.getElementById('annual-salary').value) || 0;
        const filingStatus = document.getElementById('filing-status').value;
        const allowances = parseInt(document.getElementById('allowances').value) || 0;
        const nyStateAllowances = parseInt(document.getElementById('ny-state-allowances').value) || 0;
        const nycResident = document.getElementById('nyc-resident').checked;
        const disabilityInsurance = document.getElementById('disability-insurance').checked;
        const familyLeave = document.getElementById('family-leave').checked;
        
        const preeTaxDeductions = {
            healthInsurance: parseFloat(document.getElementById('health-insurance').value) || 0,
            dentalInsurance: parseFloat(document.getElementById('dental-insurance').value) || 0,
            retirement401k: parseFloat(document.getElementById('retirement-401k').value) || 0
        };
        
        if (annualSalary <= 0) {
            alert('Please enter a valid annual salary.');
            return;
        }
        
        const paycheckCalculation = calculateNYCPaycheck(annualSalary, selectedFrequency, filingStatus, 
            allowances, nyStateAllowances, nycResident, disabilityInsurance, familyLeave, preeTaxDeductions);
        
        displayNYCResults(paycheckCalculation);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('nyc-paycheck-calculator-form').reset();
        payFrequencyBtns.forEach(b => b.classList.remove('selected'));
        payFrequencyBtns[0].classList.add('selected');
        selectedFrequency = 'weekly';
        resultsDiv.style.display = 'none';
    });
    
    function calculateNYCPaycheck(annualSalary, frequency, filingStatus, allowances, nyStateAllowances, 
                                 nycResident, disabilityInsurance, familyLeave, preTaxDeductions) {
        
        const payPeriods = { weekly: 52, biweekly: 26, semimonthly: 24, monthly: 12 };
        const periodsPerYear = payPeriods[frequency];
        
        // Calculate gross pay per period
        const grossPayPerPeriod = annualSalary / periodsPerYear;
        
        // Calculate annual pre-tax deductions
        const annualPreTaxDeductions = Object.values(preTaxDeductions).reduce((sum, val) => sum + val, 0) * periodsPerYear;
        
        // Adjusted gross income for tax calculations
        const adjustedGrossIncome = annualSalary - annualPreTaxDeductions;
        
        // Calculate federal taxes
        const federalTax = calculateFederalTax(adjustedGrossIncome, filingStatus, allowances);
        
        // Calculate NY state taxes
        const nyStateTax = calculateNYStateTax(adjustedGrossIncome, filingStatus, nyStateAllowances);
        
        // Calculate NYC taxes
        const nycTax = nycResident ? calculateNYCTax(adjustedGrossIncome) : 0;
        
        // Calculate payroll taxes
        const socialSecurityTax = Math.min(annualSalary * nycTaxData.socialSecurity.rate, 
                                          nycTaxData.socialSecurity.cap * nycTaxData.socialSecurity.rate);
        
        let medicareTax = annualSalary * nycTaxData.medicare.rate;
        if (annualSalary > nycTaxData.medicare.threshold) {
            medicareTax += (annualSalary - nycTaxData.medicare.threshold) * nycTaxData.medicare.additionalRate;
        }
        
        // Calculate NY specific taxes
        const nyDisabilityTax = disabilityInsurance ? 
            Math.min(annualSalary * nycTaxData.nyDisability.rate, nycTaxData.nyDisability.cap * nycTaxData.nyDisability.rate) : 0;
        
        const nyFamilyLeaveTax = familyLeave ? 
            Math.min(annualSalary * nycTaxData.nyFamilyLeave.rate, nycTaxData.nyFamilyLeave.cap * nycTaxData.nyFamilyLeave.rate) : 0;
        
        // Calculate totals
        const totalTaxes = federalTax + nyStateTax + nycTax + socialSecurityTax + medicareTax + nyDisabilityTax + nyFamilyLeaveTax;
        const totalDeductions = totalTaxes + annualPreTaxDeductions;
        const netAnnualPay = annualSalary - totalDeductions;
        const netPayPerPeriod = netAnnualPay / periodsPerYear;
        
        return {
            annualSalary: annualSalary,
            frequency: frequency,
            periodsPerYear: periodsPerYear,
            grossPayPerPeriod: grossPayPerPeriod,
            netPayPerPeriod: netPayPerPeriod,
            netAnnualPay: netAnnualPay,
            taxes: {
                federal: federalTax,
                nyState: nyStateTax,
                nyc: nycTax,
                socialSecurity: socialSecurityTax,
                medicare: medicareTax,
                nyDisability: nyDisabilityTax,
                nyFamilyLeave: nyFamilyLeaveTax,
                total: totalTaxes
            },
            preTaxDeductions: preTaxDeductions,
            annualPreTaxDeductions: annualPreTaxDeductions,
            totalDeductions: totalDeductions,
            effectiveTaxRate: (totalTaxes / annualSalary) * 100,
            nycResident: nycResident
        };
    }
    
    function calculateFederalTax(income, filingStatus, allowances) {
        const standardDeduction = nycTaxData.federal.standardDeduction[filingStatus] || nycTaxData.federal.standardDeduction.single;
        const allowanceAmount = allowances * 4300; // Approximate allowance value
        const taxableIncome = Math.max(0, income - standardDeduction - allowanceAmount);
        
        return calculateTaxFromBrackets(taxableIncome, nycTaxData.federal.brackets);
    }
    
    function calculateNYStateTax(income, filingStatus, allowances) {
        const standardDeduction = nycTaxData.nyState.standardDeduction[filingStatus] || nycTaxData.nyState.standardDeduction.single;
        const allowanceAmount = allowances * 1000; // NY state allowance value
        const taxableIncome = Math.max(0, income - standardDeduction - allowanceAmount);
        
        return calculateTaxFromBrackets(taxableIncome, nycTaxData.nyState.brackets);
    }
    
    function calculateNYCTax(income) {
        return calculateTaxFromBrackets(income, nycTaxData.nyc.brackets);
    }
    
    function calculateTaxFromBrackets(income, brackets) {
        let tax = 0;
        let remainingIncome = income;
        
        for (const bracket of brackets) {
            if (remainingIncome <= 0) break;
            
            const taxableInBracket = Math.min(remainingIncome, bracket.max - bracket.min);
            tax += taxableInBracket * bracket.rate;
            remainingIncome -= taxableInBracket;
        }
        
        return tax;
    }
    
    function displayNYCResults(calc) {
        const frequencyNames = {
            weekly: 'Weekly',
            biweekly: 'Bi-weekly',
            semimonthly: 'Semi-monthly',
            monthly: 'Monthly'
        };
        
        let html = `
            <div style="font-size: 48px; font-weight: bold; margin-bottom: 20px;">
                $${calc.netPayPerPeriod.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
            </div>
            
            <div style="font-size: 18px; margin-bottom: 25px;">
                ${frequencyNames[calc.frequency]} Take-Home Pay
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Gross ${frequencyNames[calc.frequency]}</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #1976d2;">
                        $${calc.grossPayPerPeriod.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    </div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Annual Net Pay</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #4caf50;">
                        $${calc.netAnnualPay.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    </div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Effective Tax Rate</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #ff9800;">
                        ${calc.effectiveTaxRate.toFixed(1)}%
                    </div>
                </div>
            </div>
            
            <div class="tax-breakdown">
                <h4 style="color: #1976d2; margin-bottom: 20px;">📊 Tax & Deduction Breakdown</h4>
                
                <div class="deduction-item">
                    <span><strong>Federal Income Tax:</strong></span>
                    <span>$${calc.taxes.federal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                
                <div class="deduction-item">
                    <span><strong>NY State Income Tax:</strong></span>
                    <span>$${calc.taxes.nyState.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                
                ${calc.nycResident ? `
                <div class="deduction-item">
                    <span><strong>NYC Income Tax:</strong></span>
                    <span>$${calc.taxes.nyc.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                ` : ''}
                
                <div class="deduction-item">
                    <span><strong>Social Security Tax:</strong></span>
                    <span>$${calc.taxes.socialSecurity.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                
                <div class="deduction-item">
                    <span><strong>Medicare Tax:</strong></span>
                    <span>$${calc.taxes.medicare.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                
                ${calc.taxes.nyDisability > 0 ? `
                <div class="deduction-item">
                    <span><strong>NY Disability Insurance:</strong></span>
                    <span>$${calc.taxes.nyDisability.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                ` : ''}
                
                ${calc.taxes.nyFamilyLeave > 0 ? `
                <div class="deduction-item">
                    <span><strong>NY Paid Family Leave:</strong></span>
                    <span>$${calc.taxes.nyFamilyLeave.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                ` : ''}
                
                <div class="deduction-item" style="background-color: #ffebee; border: 2px solid #f44336;">
                    <span><strong>Total Taxes:</strong></span>
                    <span style="color: #f44336; font-weight: bold;">$${calc.taxes.total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 NYC Tax Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>NYC Resident Tax:</strong> Additional 3-4% tax for NYC residents</li>
                    <li><strong>NY Disability:</strong> Provides short-term disability benefits</li>
                    <li><strong>Paid Family Leave:</strong> Provides paid time off for family care</li>
                    <li><strong>Pre-tax Benefits:</strong> Maximize 401(k) and health insurance contributions</li>
                    <li><strong>Commuter Benefits:</strong> Use pre-tax dollars for transit and parking</li>
                    <li><strong>Tax Planning:</strong> Consider quarterly estimated payments if needed</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
