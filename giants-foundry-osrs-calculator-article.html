<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.foundry-interface {
    background: linear-gradient(135deg, #d84315 0%, #bf360c 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.metal-selection {
    background-color: #fff3e0;
    border: 2px solid #d84315;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.metal-bar {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 15px;
    margin: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    width: 140px;
}
.metal-bar:hover {
    border-color: #d84315;
    transform: translateY(-3px);
}
.metal-bar.selected {
    border-color: #d84315;
    background-color: #fff3e0;
}
.sword-design {
    background-color: #e8f5e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.foundry-result {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.metal-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    margin: 3px;
    color: white;
}
.bronze { background-color: #cd7f32; }
.iron { background-color: #c0c0c0; color: #333; }
.steel { background-color: #71797e; }
.mithril { background-color: #4a90e2; }
.adamant { background-color: #228b22; }
.rune { background-color: #4169e1; }
.dragon { background-color: #dc143c; }
.quality-meter {
    background-color: #e0e0e0;
    border-radius: 10px;
    height: 25px;
    margin: 15px 0;
    overflow: hidden;
}
.quality-fill {
    height: 100%;
    background: linear-gradient(90deg, #f44336, #ff9800, #4caf50);
    border-radius: 10px;
    transition: width 0.5s ease;
}
.xp-breakdown {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.profit-analysis {
    background-color: #e8f5e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
</style>

<h1>Giants Foundry OSRS Calculator</h1>
<p>Calculate optimal metal combinations, experience rates, and profit margins for the Giants' Foundry minigame in Old School RuneScape. Plan your smithing training, maximize your XP per hour, and optimize your foundry runs with our comprehensive calculator.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="foundryform" id="foundry-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Foundry Calculator</a></li>
                <li><a href="#" data-tab="metals">Metal Guide</a></li>
                <li><a href="#" data-tab="optimization">Optimization</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="foundry-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">🔥 Giants' Foundry Calculator</h3>
                
                <div class="metal-selection">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">⚒️ Select Metal Bars (Choose 2 types)</h4>
                    <div style="text-align: center; margin-bottom: 20px;">
                        <div class="metal-bar" data-metal="bronze">
                            <div style="font-size: 24px;">🥉</div>
                            <div><strong>Bronze</strong></div>
                            <div class="metal-badge bronze">Lv. 1</div>
                            <div style="font-size: 12px;">12.5 XP/bar</div>
                        </div>
                        
                        <div class="metal-bar" data-metal="iron">
                            <div style="font-size: 24px;">⚪</div>
                            <div><strong>Iron</strong></div>
                            <div class="metal-badge iron">Lv. 15</div>
                            <div style="font-size: 12px;">25 XP/bar</div>
                        </div>
                        
                        <div class="metal-bar" data-metal="steel">
                            <div style="font-size: 24px;">⚫</div>
                            <div><strong>Steel</strong></div>
                            <div class="metal-badge steel">Lv. 30</div>
                            <div style="font-size: 12px;">37.5 XP/bar</div>
                        </div>
                        
                        <div class="metal-bar" data-metal="mithril">
                            <div style="font-size: 24px;">🔵</div>
                            <div><strong>Mithril</strong></div>
                            <div class="metal-badge mithril">Lv. 50</div>
                            <div style="font-size: 12px;">50 XP/bar</div>
                        </div>
                        
                        <div class="metal-bar" data-metal="adamant">
                            <div style="font-size: 24px;">🟢</div>
                            <div><strong>Adamant</strong></div>
                            <div class="metal-badge adamant">Lv. 70</div>
                            <div style="font-size: 12px;">62.5 XP/bar</div>
                        </div>
                        
                        <div class="metal-bar" data-metal="rune">
                            <div style="font-size: 24px;">🔷</div>
                            <div><strong>Rune</strong></div>
                            <div class="metal-badge rune">Lv. 85</div>
                            <div style="font-size: 12px;">75 XP/bar</div>
                        </div>
                        
                        <div class="metal-bar" data-metal="dragon">
                            <div style="font-size: 24px;">🔴</div>
                            <div><strong>Dragon</strong></div>
                            <div class="metal-badge dragon">Lv. 90</div>
                            <div style="font-size: 12px;">87.5 XP/bar</div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="metal1-quantity"><strong>First Metal Quantity:</strong></label>
                            <input type="number" id="metal1-quantity" value="14" min="1" max="28" class="innormal" style="width: 100%; margin-top: 5px;">
                            <div id="metal1-display" style="margin-top: 10px; font-weight: bold; color: #d84315;">No metal selected</div>
                        </div>
                        
                        <div>
                            <label for="metal2-quantity"><strong>Second Metal Quantity:</strong></label>
                            <input type="number" id="metal2-quantity" value="14" min="1" max="28" class="innormal" style="width: 100%; margin-top: 5px;">
                            <div id="metal2-display" style="margin-top: 10px; font-weight: bold; color: #d84315;">No metal selected</div>
                        </div>
                    </div>
                </div>
                
                <div class="sword-design">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">⚔️ Sword Design Requirements</h4>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                        <div>
                            <label for="blade-type"><strong>Blade Type:</strong></label>
                            <select id="blade-type" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="light">Light Blade</option>
                                <option value="medium">Medium Blade</option>
                                <option value="heavy">Heavy Blade</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="forte-type"><strong>Forte Type:</strong></label>
                            <select id="forte-type" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="light">Light Forte</option>
                                <option value="medium">Medium Forte</option>
                                <option value="heavy">Heavy Forte</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="tip-type"><strong>Tip Type:</strong></label>
                            <select id="tip-type" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="light">Light Tip</option>
                                <option value="medium">Medium Tip</option>
                                <option value="heavy">Heavy Tip</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="profit-analysis">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">💰 Economic Settings</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="smithing-level"><strong>Your Smithing Level:</strong></label>
                            <input type="number" id="smithing-level" value="60" min="1" max="99" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                        
                        <div>
                            <label for="goal-type"><strong>Training Goal:</strong></label>
                            <select id="goal-type" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="xp">Maximum XP/hour</option>
                                <option value="profit">Maximum Profit</option>
                                <option value="balanced">Balanced XP/Profit</option>
                                <option value="afk">AFK Training</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Foundry Run" id="calculate-foundry-btn" style="padding: 15px 30px; font-size: 18px; background-color: #d84315; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Calculator" id="clear-foundry-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Calculations are based on current OSRS Giants' Foundry mechanics. Actual results may vary based on performance and market prices.
        </div>
        
        <div id="foundry-results" style="display: none; margin-top: 30px;">
            <div class="foundry-result">
                <h3 style="margin-bottom: 20px;">🔨 Foundry Run Results</h3>
                <div id="foundry-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const metalBars = document.querySelectorAll('.metal-bar');
    const calculateBtn = document.getElementById('calculate-foundry-btn');
    const clearBtn = document.getElementById('clear-foundry-btn');
    const resultsDiv = document.getElementById('foundry-results');
    const resultsContent = document.getElementById('foundry-results-content');
    
    let selectedMetals = [];
    
    // Metal data with XP rates and costs (approximate GP values)
    const metalData = {
        bronze: { xp: 12.5, cost: 50, level: 1, weight: 'light' },
        iron: { xp: 25, cost: 150, level: 15, weight: 'light' },
        steel: { xp: 37.5, cost: 400, level: 30, weight: 'medium' },
        mithril: { xp: 50, cost: 800, level: 50, weight: 'medium' },
        adamant: { xp: 62.5, cost: 2000, level: 70, weight: 'heavy' },
        rune: { xp: 75, cost: 12000, level: 85, weight: 'heavy' },
        dragon: { xp: 87.5, cost: 25000, level: 90, weight: 'heavy' }
    };
    
    // Handle metal selection
    metalBars.forEach(bar => {
        bar.addEventListener('click', function() {
            const metal = this.dataset.metal;
            
            if (selectedMetals.includes(metal)) {
                // Deselect metal
                selectedMetals = selectedMetals.filter(m => m !== metal);
                this.classList.remove('selected');
            } else if (selectedMetals.length < 2) {
                // Select metal
                selectedMetals.push(metal);
                this.classList.add('selected');
            } else {
                alert('You can only select 2 metal types for Giants\' Foundry.');
                return;
            }
            
            updateMetalDisplays();
        });
    });
    
    function updateMetalDisplays() {
        const metal1Display = document.getElementById('metal1-display');
        const metal2Display = document.getElementById('metal2-display');
        
        if (selectedMetals.length >= 1) {
            metal1Display.textContent = `${selectedMetals[0].charAt(0).toUpperCase() + selectedMetals[0].slice(1)} Bars`;
            metal1Display.style.color = '#4caf50';
        } else {
            metal1Display.textContent = 'No metal selected';
            metal1Display.style.color = '#d84315';
        }
        
        if (selectedMetals.length >= 2) {
            metal2Display.textContent = `${selectedMetals[1].charAt(0).toUpperCase() + selectedMetals[1].slice(1)} Bars`;
            metal2Display.style.color = '#4caf50';
        } else {
            metal2Display.textContent = 'No metal selected';
            metal2Display.style.color = '#d84315';
        }
    }
    
    calculateBtn.addEventListener('click', function() {
        if (selectedMetals.length !== 2) {
            alert('Please select exactly 2 metal types.');
            return;
        }
        
        const metal1Qty = parseInt(document.getElementById('metal1-quantity').value) || 14;
        const metal2Qty = parseInt(document.getElementById('metal2-quantity').value) || 14;
        const bladeType = document.getElementById('blade-type').value;
        const forteType = document.getElementById('forte-type').value;
        const tipType = document.getElementById('tip-type').value;
        const smithingLevel = parseInt(document.getElementById('smithing-level').value) || 60;
        const goalType = document.getElementById('goal-type').value;
        
        const results = calculateFoundryRun(selectedMetals, [metal1Qty, metal2Qty], 
            [bladeType, forteType, tipType], smithingLevel, goalType);
        displayResults(results);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        selectedMetals = [];
        metalBars.forEach(bar => bar.classList.remove('selected'));
        updateMetalDisplays();
        document.getElementById('foundry-calculator-form').reset();
        resultsDiv.style.display = 'none';
    });
    
    function calculateFoundryRun(metals, quantities, design, smithingLevel, goalType) {
        const metal1 = metalData[metals[0]];
        const metal2 = metalData[metals[1]];
        
        // Calculate base XP
        const baseXP = (metal1.xp * quantities[0]) + (metal2.xp * quantities[1]);
        
        // Calculate quality score based on design match
        const qualityScore = calculateQualityScore(metals, design);
        
        // Apply quality multiplier
        const qualityMultiplier = 1 + (qualityScore / 100);
        const totalXP = Math.round(baseXP * qualityMultiplier);
        
        // Calculate costs
        const totalCost = (metal1.cost * quantities[0]) + (metal2.cost * quantities[1]);
        
        // Calculate foundry reputation
        const foundryReputation = Math.round(totalXP / 10);
        
        // Calculate time estimate (based on performance)
        const timeEstimate = calculateTimeEstimate(qualityScore, smithingLevel);
        
        // Calculate XP rates
        const xpPerHour = Math.round(totalXP / (timeEstimate / 60));
        const costPerXP = totalCost / totalXP;
        
        // Calculate profit/loss (simplified)
        const estimatedReward = calculateRewards(foundryReputation, goalType);
        const netProfit = estimatedReward - totalCost;
        
        return {
            metals: metals,
            quantities: quantities,
            design: design,
            baseXP: baseXP,
            totalXP: totalXP,
            qualityScore: qualityScore,
            qualityMultiplier: qualityMultiplier,
            totalCost: totalCost,
            foundryReputation: foundryReputation,
            timeEstimate: timeEstimate,
            xpPerHour: xpPerHour,
            costPerXP: costPerXP,
            estimatedReward: estimatedReward,
            netProfit: netProfit,
            smithingLevel: smithingLevel,
            goalType: goalType
        };
    }
    
    function calculateQualityScore(metals, design) {
        const metal1Weight = metalData[metals[0]].weight;
        const metal2Weight = metalData[metals[1]].weight;
        
        let matches = 0;
        const weights = [metal1Weight, metal2Weight];
        
        // Check design matches
        if (weights.includes(design[0])) matches++;
        if (weights.includes(design[1])) matches++;
        if (weights.includes(design[2])) matches++;
        
        // Base quality score
        let qualityScore = (matches / 3) * 100;
        
        // Metal tier compatibility bonus
        const level1 = metalData[metals[0]].level;
        const level2 = metalData[metals[1]].level;
        const levelDiff = Math.abs(level1 - level2);
        
        if (levelDiff <= 15) qualityScore += 10;
        else if (levelDiff <= 30) qualityScore += 5;
        
        return Math.min(100, qualityScore);
    }
    
    function calculateTimeEstimate(qualityScore, smithingLevel) {
        // Base time: 8 minutes
        let baseTime = 8;
        
        // Quality affects time (better quality = faster completion)
        const qualityModifier = 1 - (qualityScore / 200);
        
        // Smithing level affects efficiency
        const levelModifier = 1 - (smithingLevel / 200);
        
        return Math.max(5, baseTime * qualityModifier * levelModifier);
    }
    
    function calculateRewards(reputation, goalType) {
        // Simplified reward calculation
        let baseReward = reputation * 15; // Base GP per reputation point
        
        if (goalType === 'profit') {
            baseReward *= 1.2; // 20% bonus for profit focus
        } else if (goalType === 'xp') {
            baseReward *= 0.8; // 20% penalty for XP focus
        }
        
        return Math.round(baseReward);
    }
    
    function displayResults(results) {
        const qualityClass = results.qualityScore >= 80 ? 'high' : results.qualityScore >= 50 ? 'medium' : 'low';
        
        let html = `
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Total Experience</h4>
                    <div style="font-size: 28px; font-weight: bold; color: #4caf50;">${results.totalXP.toLocaleString()}</div>
                    <div style="font-size: 14px; color: #666;">Base: ${results.baseXP.toLocaleString()}</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Quality Score</h4>
                    <div style="font-size: 28px; font-weight: bold; color: #ff9800;">${results.qualityScore.toFixed(1)}%</div>
                    <div class="quality-meter">
                        <div class="quality-fill" style="width: ${results.qualityScore}%"></div>
                    </div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>XP per Hour</h4>
                    <div style="font-size: 28px; font-weight: bold; color: #2196f3;">${results.xpPerHour.toLocaleString()}</div>
                    <div style="font-size: 14px; color: #666;">Est. ${results.timeEstimate.toFixed(1)} min/run</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Net Profit</h4>
                    <div style="font-size: 28px; font-weight: bold; color: ${results.netProfit >= 0 ? '#4caf50' : '#f44336'};">
                        ${results.netProfit >= 0 ? '+' : ''}${results.netProfit.toLocaleString()} GP
                    </div>
                    <div style="font-size: 14px; color: #666;">Cost: ${results.totalCost.toLocaleString()} GP</div>
                </div>
            </div>
            
            <div class="xp-breakdown">
                <h4 style="color: #d84315; margin-bottom: 20px;">📊 Detailed Breakdown</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h5>Metal Combination:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>${results.metals[0].charAt(0).toUpperCase() + results.metals[0].slice(1)}:</strong> ${results.quantities[0]} bars</li>
                            <li><strong>${results.metals[1].charAt(0).toUpperCase() + results.metals[1].slice(1)}:</strong> ${results.quantities[1]} bars</li>
                            <li><strong>Quality Multiplier:</strong> ${results.qualityMultiplier.toFixed(2)}x</li>
                            <li><strong>Foundry Reputation:</strong> ${results.foundryReputation}</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5>Economic Analysis:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Cost per XP:</strong> ${results.costPerXP.toFixed(2)} GP</li>
                            <li><strong>Estimated Rewards:</strong> ${results.estimatedReward.toLocaleString()} GP</li>
                            <li><strong>Goal Focus:</strong> ${results.goalType.charAt(0).toUpperCase() + results.goalType.slice(1)}</li>
                            <li><strong>Smithing Level:</strong> ${results.smithingLevel}</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 Optimization Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Design Matching:</strong> Match sword parts to your metal weights for higher quality</li>
                    <li><strong>Metal Pairing:</strong> Use metals with similar levels for better compatibility</li>
                    <li><strong>Quality Focus:</strong> Higher quality gives more XP and better rewards</li>
                    <li><strong>Cost Efficiency:</strong> Consider iron/steel for budget training</li>
                    <li><strong>High-Level Training:</strong> Rune/Dragon for maximum XP rates</li>
                    <li><strong>Reputation Rewards:</strong> Save reputation for valuable unlocks</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
