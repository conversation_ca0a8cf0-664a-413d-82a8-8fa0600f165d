<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>One Rep Max Calculator</title>
    <meta name="description" content="Calculate your one rep max (1RM) using multiple formulas including <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, and more. Free 1RM calculator for strength training.">
    <link rel="stylesheet" href="./Carbohydrate Calculator_files/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Mobile responsive styles */
        @media screen and (max-width: 768px) {
            #contentout { width: 100% !important; padding: 0 10px; }
            #content { width: 100% !important; margin: 0; }
            #right { display: none; }
            .panel2 { width: 100% !important; padding: 5px !important; }
            table { width: 100% !important; font-size: 14px; }
            td { padding: 8px 4px !important; word-wrap: break-word; }
            input, select { width: 100% !important; max-width: 100% !important; box-sizing: border-box; font-size: 16px; }
            .inlongest, .innormal, .inlongesthalf { width: 100% !important; }
            h1 { font-size: 24px; }
            .cinfoT { font-size: 12px; overflow-x: auto; }
            .cinfoT td { padding: 4px 2px !important; }
            #topmenu ul li { display: inline-block; margin: 2px; font-size: 12px; }
        }
        @media screen and (max-width: 480px) {
            table td:first-child { width: 30% !important; }
            table td:last-child { width: 70% !important; }
            input[type="submit"], input[type="button"] {
                width: 100% !important; margin: 5px 0 !important; padding: 12px !important; font-size: 14px;
            }
        }
    </style>
    <link rel="apple-touch-icon" sizes="180x180" href="https://d26tpo4cm8sb6k.cloudfront.net/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-16x16.png">
    <link rel="manifest" href="https://www.calculator.net/manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="https://www.calculator.net/"><img src="./Carbohydrate Calculator_files/calculator-white.svg" width="208" height="22" alt="Calculator.net"></a></div>
        <div id="login"><a href="https://www.calculator.net/my-account/sign-in.php">sign in</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/" itemprop="item"><span itemprop="name">home</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/fitness-and-health-calculator.html" itemprop="item"><span itemprop="name">fitness &amp; health</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="#" itemprop="item"><span itemprop="name">one rep max calculator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="window.print();return false;">Print</a></div>
        
        <h1>One Rep Max Calculator</h1>
        <p>The <i>One Rep Max Calculator</i> estimates your maximum single repetition lift based on the weight you can lift for multiple repetitions. This calculator uses several established formulas to provide accurate 1RM estimates for strength training and powerlifting.</p>
        
        <div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
        <span id="ucframe"></span>
        
        <div id="topmenu" class="topmenucenter">
            <ul>
                <li><a href="#" onclick="return showUnits('standard');">US Units (lbs)</a></li>
                <li id="menuon"><a href="#" onclick="return showUnits('metric');">Metric Units (kg)</a></li>
                <li><a href="#" onclick="return showUnits('other');">Other Units</a></li>
            </ul>
        </div>

        <div class="panel2" style="padding:10px;">
            <form name="oneRepMaxForm" id="onerepmax-calculator-form">
                <!-- US Units -->
                <table width="620" id="standard-units" bgcolor="#eeeeee" style="display: none;">
                    <tbody>
                        <tr>
                            <td width="70">Weight Lifted</td>
                            <td width="550"><input type="number" name="weight_lbs" id="weight_lbs" value="225" class="inlongest" min="1" max="2000" step="1"> lbs</td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- Metric Units -->
                <table width="620" id="metric-units" bgcolor="#eeeeee" style="display: block;">
                    <tbody>
                        <tr>
                            <td width="70">Weight Lifted</td>
                            <td width="550"><input type="number" name="weight_kg" id="weight_kg" value="100" class="inlongest" min="1" max="1000" step="0.5"> kg</td>
                        </tr>
                    </tbody>
                </table>
                
                <table width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">Repetitions</td>
                            <td width="550">
                                <input type="number" name="reps" id="reps" value="5" class="innormal" min="1" max="20"> reps
                                <span class="smalltext">(Number of reps performed)</span>
                            </td>
                        </tr>
                        <tr>
                            <td>Exercise</td>
                            <td>
                                <select id="exercise" name="exercise" class="inlongest">
                                    <option value="bench_press" selected="">Bench Press</option>
                                    <option value="squat">Squat</option>
                                    <option value="deadlift">Deadlift</option>
                                    <option value="overhead_press">Overhead Press</option>
                                    <option value="row">Barbell Row</option>
                                    <option value="other">Other Exercise</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>Formula</td>
                            <td>
                                <select id="formula" name="formula" class="inlongest">
                                    <option value="epley" selected="">Epley Formula (most common)</option>
                                    <option value="brzycki">Brzycki Formula</option>
                                    <option value="lander">Lander Formula</option>
                                    <option value="lombardi">Lombardi Formula</option>
                                    <option value="mayhew">Mayhew Formula</option>
                                    <option value="oconner">O'Conner Formula</option>
                                    <option value="wathen">Wathen Formula</option>
                                    <option value="average">Average of All Formulas</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td width="70">&nbsp;</td>
                            <td width="550">
                                <input type="submit" name="x" value="Calculate 1RM" id="calculate-btn">
                                <input type="button" value="Clear" id="clear-btn" onclick="clearOneRepMaxForm();">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </form>
        </div>

        <ul style="margin-top:0px;margin-left:-20px;">
            <li class="smalltext"><b>1RM:</b> The maximum weight you can lift for one repetition.</li>
            <li class="smalltext"><b>Accuracy:</b> Most accurate for 2-10 reps; less accurate beyond 10 reps.</li>
            <li class="smalltext"><b>Safety:</b> Use estimates for training planning, not actual max attempts.</li>
        </ul>

        <!-- Results Display Area -->
        <div id="onerepmax-results" style="display: none; margin-top: 20px;">
            <div class="h2result" id="onerepmax-results-content"></div>
        </div>

        <br>
        <fieldset>
            <legend>Related</legend>
            <a href="#">Calories Burned Calculator</a> | 
            <a href="#">BMI Calculator</a> | 
            <a href="#">Body Fat Calculator</a>
        </fieldset>

        <br>
        <h3>What is One Rep Max (1RM)?</h3>
        <p>One Rep Max (1RM) is the maximum amount of weight that a person can possibly lift for one repetition. It's a measure of the maximum strength of a muscle or muscle group. 1RM testing is commonly used in strength training, powerlifting, and athletic performance assessment.</p>

        <h3>1RM Calculation Formulas</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Formula</td>
                    <td class="cinfoHdL">Equation</td>
                    <td class="cinfoHdL">Best For</td>
                    <td class="cinfoHdL">Rep Range</td>
                </tr>
                <tr><td><strong>Epley</strong></td><td>1RM = w × (1 + r/30)</td><td>General use</td><td>2-10 reps</td></tr>
                <tr><td><strong>Brzycki</strong></td><td>1RM = w × 36/(37-r)</td><td>Bench press</td><td>2-10 reps</td></tr>
                <tr><td><strong>Lander</strong></td><td>1RM = w × 100/(101.3-2.67123×r)</td><td>General use</td><td>2-10 reps</td></tr>
                <tr><td><strong>Lombardi</strong></td><td>1RM = w × r^0.10</td><td>General use</td><td>2-10 reps</td></tr>
                <tr><td><strong>Mayhew</strong></td><td>1RM = w × 100/(52.2+41.9×e^(-0.055×r))</td><td>Bench press</td><td>2-10 reps</td></tr>
                <tr><td><strong>O'Conner</strong></td><td>1RM = w × (1 + r/40)</td><td>General use</td><td>2-10 reps</td></tr>
                <tr><td><strong>Wathen</strong></td><td>1RM = w × 100/(48.8+53.8×e^(-0.075×r))</td><td>General use</td><td>2-10 reps</td></tr>
            </tbody>
        </table>
        <p class="smalltext">w = weight lifted, r = repetitions performed</p>

        <h3>Training Percentages Based on 1RM</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">% of 1RM</td>
                    <td class="cinfoHdL">Rep Range</td>
                    <td class="cinfoHdL">Training Goal</td>
                    <td class="cinfoHdL">Sets</td>
                </tr>
                <tr><td><strong>90-100%</strong></td><td>1-3</td><td>Maximum Strength</td><td>3-5</td></tr>
                <tr><td><strong>85-90%</strong></td><td>3-5</td><td>Strength</td><td>3-5</td></tr>
                <tr><td><strong>80-85%</strong></td><td>5-6</td><td>Strength/Power</td><td>3-4</td></tr>
                <tr><td><strong>75-80%</strong></td><td>6-8</td><td>Power/Hypertrophy</td><td>3-4</td></tr>
                <tr><td><strong>70-75%</strong></td><td>8-10</td><td>Hypertrophy</td><td>3-4</td></tr>
                <tr><td><strong>65-70%</strong></td><td>10-12</td><td>Hypertrophy/Endurance</td><td>2-3</td></tr>
                <tr><td><strong>60-65%</strong></td><td>12-15</td><td>Muscular Endurance</td><td>2-3</td></tr>
                <tr><td><strong>50-60%</strong></td><td>15+</td><td>Endurance/Recovery</td><td>2-3</td></tr>
            </tbody>
        </table>

        <h3>Safety Considerations</h3>
        <ul>
            <li><strong>Warm-up:</strong> Always perform a thorough warm-up before heavy lifting</li>
            <li><strong>Spotter:</strong> Use a spotter for maximum attempts, especially on bench press</li>
            <li><strong>Proper Form:</strong> Maintain correct technique even at maximum loads</li>
            <li><strong>Progressive Loading:</strong> Work up to heavy weights gradually</li>
            <li><strong>Recovery:</strong> Allow adequate rest between maximum attempts</li>
            <li><strong>Experience:</strong> Beginners should focus on technique before testing 1RM</li>
        </ul>

        <h3>When to Test 1RM</h3>
        <ul>
            <li>After completing a strength training cycle</li>
            <li>To establish baseline strength levels</li>
            <li>For powerlifting competition preparation</li>
            <li>To track strength progress over time</li>
            <li>When well-rested and not fatigued</li>
            <li>With proper supervision and safety measures</li>
        </ul>

        <h3>Alternatives to Direct 1RM Testing</h3>
        <ul>
            <li><strong>3RM or 5RM:</strong> Test with slightly higher reps for safety</li>
            <li><strong>Estimated 1RM:</strong> Use calculators like this one</li>
            <li><strong>Velocity-Based Training:</strong> Use bar speed to estimate 1RM</li>
            <li><strong>Rate of Perceived Exertion:</strong> Use RPE scales</li>
            <li><strong>Progressive Loading:</strong> Gradually increase weight over sessions</li>
        </ul>

        <h3>Using 1RM for Program Design</h3>
        <p>Once you know your 1RM, you can design effective training programs by working at specific percentages. This allows for:</p>
        <ul>
            <li>Precise load prescription for different training goals</li>
            <li>Progressive overload planning</li>
            <li>Periodization of training intensity</li>
            <li>Comparison of strength across different exercises</li>
            <li>Tracking of strength improvements over time</li>
        </ul>
    </div>
    
    <div id="right">
        <div style="padding-top:10px; min-height:280px; text-align:center;">
            <div style="width: 300px; height: 250px; background: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; color: #666;">
                Advertisement Space
            </div>
        </div>
        
        <form name="calcSearchForm" onsubmit="return false;" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td><input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators..."></td>
                        <td><span id="bluebtn" onclick="performSearch();">Search</span></td>
                    </tr>
                    <tr>
                        <td colspan="2"><div id="calcSearchOut"></div></td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="#">Fitness and Health Calculators</a>
            </div>
            <div id="occontent">
                <a href="#">BMI Calculator</a>
                <a href="#">Body Fat Calculator</a>
                <a href="#">Calorie Calculator</a>
                <a href="#">BMR Calculator</a>
                <a href="#">Ideal Weight Calculator</a>
                <a href="#">Macro Calculator</a>
                <a href="#">Pace Calculator</a>
                <a href="#">Pregnancy Calculator</a>
                <a href="#">Heart Rate Calculator</a>
                <a href="#">More Fitness Calculators</a>
            </div>
            <div id="ocother">
                <a href="#">Financial</a> | 
                <a href="#">Fitness and Health</a> | 
                <a href="#">Math</a> | 
                <a href="#">Other</a>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="#">about us</a> | 
            <a href="#">sitemap</a> | 
            <a href="#">terms of use</a> | 
            <a href="#">privacy policy</a> &nbsp;  © 2008 - 2025 <a href="#">calculator.net</a>
        </div>
    </div>
</div>

<div class="topNavAbs">
    <a href="#">Financial</a>
    <a href="#" class="topNavOn">Fitness &amp; Health</a>
    <a href="#">Math</a>
    <a href="#">Other</a>
</div>

<script>
let currentUnits = 'metric';

function showUnits(unitType) {
    document.getElementById('standard-units').style.display = 'none';
    document.getElementById('metric-units').style.display = 'none';
    
    if (unitType === 'standard') {
        document.getElementById('standard-units').style.display = 'block';
    } else if (unitType === 'metric') {
        document.getElementById('metric-units').style.display = 'block';
    }
    
    const menuItems = document.querySelectorAll('#topmenu ul li');
    menuItems.forEach(item => {
        item.id = '';
        const link = item.querySelector('a');
        if (link && link.textContent.toLowerCase().includes(unitType === 'standard' ? 'us' : unitType)) {
            item.id = 'menuon';
        }
    });
    
    currentUnits = unitType;
    return false;
}

document.getElementById('calculate-btn').addEventListener('click', function(e) {
    e.preventDefault();
    calculateOneRepMax();
});

function calculateOneRepMax() {
    const reps = parseInt(document.getElementById('reps').value);
    const exercise = document.getElementById('exercise').value;
    const formula = document.getElementById('formula').value;
    
    let weight;
    
    if (currentUnits === 'metric') {
        weight = parseFloat(document.getElementById('weight_kg').value);
    } else {
        weight = parseFloat(document.getElementById('weight_lbs').value);
    }
    
    if (weight <= 0 || reps <= 0) {
        alert('Please enter valid weight and repetition values');
        return;
    }
    
    if (reps > 15) {
        alert('Warning: 1RM calculations are less accurate for repetitions above 15');
    }
    
    // Calculate 1RM using selected formula or all formulas
    let oneRepMax;
    let allFormulas = {};
    
    if (formula === 'average') {
        allFormulas = calculateAllFormulas(weight, reps);
        oneRepMax = Object.values(allFormulas).reduce((a, b) => a + b, 0) / Object.keys(allFormulas).length;
    } else {
        oneRepMax = calculateByFormula(weight, reps, formula);
        allFormulas = calculateAllFormulas(weight, reps);
    }
    
    // Generate training percentages
    const trainingPercentages = generateTrainingPercentages(oneRepMax);
    
    // Display results
    displayOneRepMaxResults(oneRepMax, allFormulas, trainingPercentages, exercise, formula, weight, reps);
}

function calculateByFormula(weight, reps, formula) {
    switch(formula) {
        case 'epley':
            return weight * (1 + reps / 30);
        case 'brzycki':
            return weight * 36 / (37 - reps);
        case 'lander':
            return weight * 100 / (101.3 - 2.67123 * reps);
        case 'lombardi':
            return weight * Math.pow(reps, 0.10);
        case 'mayhew':
            return weight * 100 / (52.2 + 41.9 * Math.exp(-0.055 * reps));
        case 'oconner':
            return weight * (1 + reps / 40);
        case 'wathen':
            return weight * 100 / (48.8 + 53.8 * Math.exp(-0.075 * reps));
        default:
            return weight * (1 + reps / 30); // Default to Epley
    }
}

function calculateAllFormulas(weight, reps) {
    return {
        'Epley': calculateByFormula(weight, reps, 'epley'),
        'Brzycki': calculateByFormula(weight, reps, 'brzycki'),
        'Lander': calculateByFormula(weight, reps, 'lander'),
        'Lombardi': calculateByFormula(weight, reps, 'lombardi'),
        'Mayhew': calculateByFormula(weight, reps, 'mayhew'),
        'O\'Conner': calculateByFormula(weight, reps, 'oconner'),
        'Wathen': calculateByFormula(weight, reps, 'wathen')
    };
}

function generateTrainingPercentages(oneRepMax) {
    const percentages = [95, 90, 85, 80, 75, 70, 65, 60];
    return percentages.map(pct => ({
        percentage: pct,
        weight: (oneRepMax * pct / 100),
        reps: getRepRange(pct)
    }));
}

function getRepRange(percentage) {
    if (percentage >= 90) return '1-3';
    if (percentage >= 85) return '3-5';
    if (percentage >= 80) return '5-6';
    if (percentage >= 75) return '6-8';
    if (percentage >= 70) return '8-10';
    if (percentage >= 65) return '10-12';
    if (percentage >= 60) return '12-15';
    return '15+';
}

function displayOneRepMaxResults(oneRepMax, allFormulas, trainingPercentages, exercise, formula, weight, reps) {
    const unit = currentUnits === 'metric' ? 'kg' : 'lbs';
    const exerciseName = document.getElementById('exercise').options[document.getElementById('exercise').selectedIndex].text;
    const formulaName = document.getElementById('formula').options[document.getElementById('formula').selectedIndex].text;
    
    let formulaTable = '';
    if (formula === 'average') {
        formulaTable = '<strong>All Formula Results:</strong><br><table class="cinfoT" style="margin-top: 10px;"><tbody>';
        formulaTable += '<tr><td class="cinfoHd">Formula</td><td class="cinfoHd">1RM</td></tr>';
        
        Object.entries(allFormulas).forEach(([name, value]) => {
            formulaTable += `<tr><td><strong>${name}</strong></td><td>${value.toFixed(1)} ${unit}</td></tr>`;
        });
        
        formulaTable += '</tbody></table><br>';
    }
    
    let trainingTable = '<strong>Training Percentages:</strong><br><table class="cinfoT" style="margin-top: 10px;"><tbody>';
    trainingTable += '<tr><td class="cinfoHd">% of 1RM</td><td class="cinfoHd">Weight</td><td class="cinfoHd">Rep Range</td></tr>';
    
    trainingPercentages.forEach(item => {
        trainingTable += `<tr><td><strong>${item.percentage}%</strong></td><td>${item.weight.toFixed(1)} ${unit}</td><td>${item.reps}</td></tr>`;
    });
    
    trainingTable += '</tbody></table>';
    
    const resultsDiv = document.getElementById('onerepmax-results-content');
    resultsDiv.innerHTML = `
        <strong>One Rep Max Results:</strong><br>
        Exercise: ${exerciseName}<br>
        Weight Lifted: ${weight} ${unit} for ${reps} reps<br>
        Formula Used: ${formulaName}<br>
        <strong>Estimated 1RM: ${oneRepMax.toFixed(1)} ${unit}</strong><br><br>
        
        ${formulaTable}
        ${trainingTable}
    `;
    document.getElementById('onerepmax-results').style.display = 'block';
}

function clearOneRepMaxForm() {
    document.getElementById('reps').value = '5';
    document.getElementById('exercise').selectedIndex = 0;
    document.getElementById('formula').selectedIndex = 0;
    
    if (currentUnits === 'metric') {
        document.getElementById('weight_kg').value = '100';
    } else {
        document.getElementById('weight_lbs').value = '225';
    }
    
    document.getElementById('onerepmax-results').style.display = 'none';
}

function performSearch() {
    const searchTerm = document.getElementById('calcSearchTerm').value;
    const searchOut = document.getElementById('calcSearchOut');
    
    if (searchTerm.trim() === '') {
        searchOut.innerHTML = '';
        return;
    }
    
    const calculators = [
        'BMI Calculator', 'Body Fat Calculator', 'Calorie Calculator', 
        'BMR Calculator', 'One Rep Max Calculator', 'Pace Calculator'
    ];
    
    const results = calculators.filter(calc => 
        calc.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    if (results.length > 0) {
        searchOut.innerHTML = '<div>' + results.map(result => 
            `<a href="#" style="display: block; padding: 2px 0;">${result}</a>`
        ).join('') + '</div>';
    } else {
        searchOut.innerHTML = '<div>No calculators found</div>';
    }
}

showUnits('metric');
</script>

</body>
</html>
