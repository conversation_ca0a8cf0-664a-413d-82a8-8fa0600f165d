<?php
/**
 * Pace Calculator Theme Functions
 */

// Theme setup
function pace_calculator_theme_setup() {
    // Add theme support for various features
    add_theme_support('title-tag');
    add_theme_support('post-thumbnails');
    add_theme_support('html5', array(
        'search-form',
        'comment-form',
        'comment-list',
        'gallery',
        'caption',
    ));
    
    // Register navigation menus
    register_nav_menus(array(
        'primary' => __('Primary Menu', 'pace-calculator'),
        'top-nav' => __('Top Navigation', 'pace-calculator'),
    ));
}
add_action('after_setup_theme', 'pace_calculator_theme_setup');

// Enqueue scripts and styles
function pace_calculator_scripts() {
    // Main stylesheet
    wp_enqueue_style('pace-calculator-style', get_stylesheet_uri());
    
    // Calculator JavaScript
    wp_enqueue_script('pace-calculator-js', get_template_directory_uri() . '/js/calculator.js', array('jquery'), '1.0', true);
    
    // Localize script for AJAX
    wp_localize_script('pace-calculator-js', 'pace_calculator_ajax', array(
        'ajax_url' => admin_url('admin-ajax.php'),
        'nonce' => wp_create_nonce('pace_calculator_nonce')
    ));
}
add_action('wp_enqueue_scripts', 'pace_calculator_scripts');

// Register sidebar
function pace_calculator_widgets_init() {
    register_sidebar(array(
        'name'          => __('Right Sidebar', 'pace-calculator'),
        'id'            => 'sidebar-right',
        'description'   => __('Add widgets here to appear in the right sidebar.', 'pace-calculator'),
        'before_widget' => '<div id="%1$s" class="widget %2$s">',
        'after_widget'  => '</div>',
        'before_title'  => '<h3 class="widget-title">',
        'after_title'   => '</h3>',
    ));
}
add_action('widgets_init', 'pace_calculator_widgets_init');

// AJAX handler for pace calculations
function handle_pace_calculation() {
    // Verify nonce
    if (!wp_verify_nonce($_POST['nonce'], 'pace_calculator_nonce')) {
        wp_die('Security check failed');
    }
    
    $calculation_type = sanitize_text_field($_POST['calculation_type']);
    $result = array();
    
    switch ($calculation_type) {
        case 'pace':
            $result = calculate_pace($_POST);
            break;
        case 'time':
            $result = calculate_time($_POST);
            break;
        case 'distance':
            $result = calculate_distance($_POST);
            break;
        case 'multipoint':
            $result = calculate_multipoint_pace($_POST);
            break;
        case 'converter':
            $result = convert_pace($_POST);
            break;
        case 'finish_time':
            $result = calculate_finish_time($_POST);
            break;
    }
    
    wp_send_json_success($result);
}
add_action('wp_ajax_pace_calculation', 'handle_pace_calculation');
add_action('wp_ajax_nopriv_pace_calculation', 'handle_pace_calculation');

// Pace calculation functions
function calculate_pace($data) {
    $time = sanitize_text_field($data['time']);
    $distance = floatval($data['distance']);
    $distance_unit = sanitize_text_field($data['distance_unit']);
    
    // Convert time to seconds
    $time_parts = explode(':', $time);
    $total_seconds = 0;
    
    if (count($time_parts) == 3) {
        $total_seconds = ($time_parts[0] * 3600) + ($time_parts[1] * 60) + $time_parts[2];
    } elseif (count($time_parts) == 2) {
        $total_seconds = ($time_parts[0] * 60) + $time_parts[1];
    } else {
        $total_seconds = $time_parts[0];
    }
    
    // Convert distance to standard unit (kilometers)
    switch ($distance_unit) {
        case 'Miles':
            $distance_km = $distance * 1.60934;
            break;
        case 'Meters':
            $distance_km = $distance / 1000;
            break;
        case 'Yards':
            $distance_km = $distance * 0.0009144;
            break;
        default:
            $distance_km = $distance;
    }
    
    // Calculate pace (seconds per kilometer)
    $pace_per_km = $total_seconds / $distance_km;
    $pace_per_mile = $pace_per_km * 1.60934;
    
    // Format pace
    $pace_km_formatted = format_time($pace_per_km);
    $pace_mile_formatted = format_time($pace_per_mile);
    
    return array(
        'pace_per_km' => $pace_km_formatted,
        'pace_per_mile' => $pace_mile_formatted,
        'speed_kph' => round(3600 / $pace_per_km, 2),
        'speed_mph' => round(3600 / $pace_per_mile, 2)
    );
}

function calculate_time($data) {
    $pace = sanitize_text_field($data['pace']);
    $distance = floatval($data['distance']);
    $distance_unit = sanitize_text_field($data['distance_unit']);
    $pace_unit = sanitize_text_field($data['pace_unit']);
    
    // Convert pace to seconds
    $pace_parts = explode(':', $pace);
    $pace_seconds = 0;
    
    if (count($pace_parts) == 3) {
        $pace_seconds = ($pace_parts[0] * 3600) + ($pace_parts[1] * 60) + $pace_parts[2];
    } elseif (count($pace_parts) == 2) {
        $pace_seconds = ($pace_parts[0] * 60) + $pace_parts[1];
    } else {
        $pace_seconds = $pace_parts[0];
    }
    
    // Convert distance and pace to consistent units
    $distance_km = convert_distance_to_km($distance, $distance_unit);
    $pace_per_km = convert_pace_to_per_km($pace_seconds, $pace_unit);
    
    // Calculate total time
    $total_time = $distance_km * $pace_per_km;
    
    return array(
        'total_time' => format_time($total_time),
        'total_seconds' => $total_time
    );
}

function calculate_distance($data) {
    $pace = sanitize_text_field($data['pace']);
    $time = sanitize_text_field($data['time']);
    $pace_unit = sanitize_text_field($data['pace_unit']);
    
    // Convert inputs to seconds
    $pace_seconds = time_to_seconds($pace);
    $total_seconds = time_to_seconds($time);
    
    // Convert pace to per km
    $pace_per_km = convert_pace_to_per_km($pace_seconds, $pace_unit);
    
    // Calculate distance in km
    $distance_km = $total_seconds / $pace_per_km;
    
    return array(
        'distance_km' => round($distance_km, 2),
        'distance_miles' => round($distance_km / 1.60934, 2),
        'distance_meters' => round($distance_km * 1000, 0),
        'distance_yards' => round($distance_km / 0.0009144, 0)
    );
}

function calculate_multipoint_pace($data) {
    $points = array();
    $results = array();
    
    // Extract point data
    for ($i = 1; $i <= 12; $i++) {
        if (!empty($data["distance$i"]) && !empty($data["time$i"])) {
            $points[] = array(
                'distance' => floatval($data["distance$i"]),
                'distance_unit' => sanitize_text_field($data["distance_unit$i"]),
                'time' => sanitize_text_field($data["time$i"])
            );
        }
    }
    
    // Calculate pace between consecutive points
    for ($i = 1; $i < count($points); $i++) {
        $prev_point = $points[$i-1];
        $curr_point = $points[$i];
        
        // Calculate segment distance and time
        $segment_distance = $curr_point['distance'] - $prev_point['distance'];
        $segment_time = time_to_seconds($curr_point['time']) - time_to_seconds($prev_point['time']);
        
        // Convert to km
        $segment_distance_km = convert_distance_to_km($segment_distance, $curr_point['distance_unit']);
        
        // Calculate pace
        if ($segment_distance_km > 0) {
            $pace = $segment_time / $segment_distance_km;
            $results[] = array(
                'segment' => $i,
                'distance' => $segment_distance,
                'time' => format_time($segment_time),
                'pace' => format_time($pace)
            );
        }
    }
    
    return $results;
}

function convert_pace($data) {
    $pace = sanitize_text_field($data['pace']);
    $from_unit = sanitize_text_field($data['from_unit']);
    $to_unit = sanitize_text_field($data['to_unit']);
    
    $pace_seconds = time_to_seconds($pace);
    
    // Convert to base unit (per km)
    $pace_per_km = convert_pace_to_per_km($pace_seconds, $from_unit);
    
    // Convert to target unit
    $result_pace = convert_pace_from_per_km($pace_per_km, $to_unit);
    
    return array(
        'converted_pace' => format_pace_for_unit($result_pace, $to_unit)
    );
}

function calculate_finish_time($data) {
    $current_distance = floatval($data['current_distance']);
    $current_distance_unit = sanitize_text_field($data['current_distance_unit']);
    $elapsed_time = sanitize_text_field($data['elapsed_time']);
    $full_distance = floatval($data['full_distance']);
    $full_distance_unit = sanitize_text_field($data['full_distance_unit']);
    
    // Convert to km
    $current_km = convert_distance_to_km($current_distance, $current_distance_unit);
    $full_km = convert_distance_to_km($full_distance, $full_distance_unit);
    
    // Calculate current pace
    $elapsed_seconds = time_to_seconds($elapsed_time);
    $current_pace = $elapsed_seconds / $current_km;
    
    // Calculate remaining distance and time
    $remaining_km = $full_km - $current_km;
    $remaining_time = $remaining_km * $current_pace;
    $total_time = $elapsed_seconds + $remaining_time;
    
    return array(
        'finish_time' => format_time($total_time),
        'remaining_time' => format_time($remaining_time),
        'current_pace' => format_time($current_pace)
    );
}

// Helper functions
function time_to_seconds($time_str) {
    $parts = explode(':', $time_str);
    $seconds = 0;
    
    if (count($parts) == 3) {
        $seconds = ($parts[0] * 3600) + ($parts[1] * 60) + $parts[2];
    } elseif (count($parts) == 2) {
        $seconds = ($parts[0] * 60) + $parts[1];
    } else {
        $seconds = $parts[0];
    }
    
    return $seconds;
}

function format_time($seconds) {
    $hours = floor($seconds / 3600);
    $minutes = floor(($seconds % 3600) / 60);
    $secs = $seconds % 60;
    
    if ($hours > 0) {
        return sprintf('%02d:%02d:%02d', $hours, $minutes, $secs);
    } else {
        return sprintf('%02d:%02d', $minutes, $secs);
    }
}

function convert_distance_to_km($distance, $unit) {
    switch ($unit) {
        case 'Miles':
            return $distance * 1.60934;
        case 'Meters':
            return $distance / 1000;
        case 'Yards':
            return $distance * 0.0009144;
        default:
            return $distance;
    }
}

function convert_pace_to_per_km($pace_seconds, $unit) {
    switch ($unit) {
        case 'tpm': // per mile
            return $pace_seconds / 1.60934;
        case 'mph': // miles per hour
            return 3600 / ($pace_seconds * 1.60934);
        case 'kph': // km per hour
            return 3600 / $pace_seconds;
        case 'mpm': // meters per minute
            return ($pace_seconds * 1000) / 60;
        case 'mps': // meters per second
            return $pace_seconds * 1000;
        case 'ypm': // yards per minute
            return ($pace_seconds / 0.0009144) / 60;
        case 'yps': // yards per second
            return $pace_seconds / 0.0009144;
        default: // tpk - per km
            return $pace_seconds;
    }
}

function convert_pace_from_per_km($pace_per_km, $unit) {
    switch ($unit) {
        case 'tpm': // per mile
            return $pace_per_km * 1.60934;
        case 'mph': // miles per hour
            return 3600 / ($pace_per_km * 1.60934);
        case 'kph': // km per hour
            return 3600 / $pace_per_km;
        case 'mpm': // meters per minute
            return ($pace_per_km / 1000) * 60;
        case 'mps': // meters per second
            return $pace_per_km / 1000;
        case 'ypm': // yards per minute
            return ($pace_per_km * 0.0009144) * 60;
        case 'yps': // yards per second
            return $pace_per_km * 0.0009144;
        default: // tpk - per km
            return $pace_per_km;
    }
}

function format_pace_for_unit($pace, $unit) {
    if (in_array($unit, array('mph', 'kph', 'mpm', 'mps', 'ypm', 'yps'))) {
        return round($pace, 2);
    } else {
        return format_time($pace);
    }
}

// Add custom post type for calculators (optional)
function register_calculator_post_type() {
    register_post_type('calculator', array(
        'labels' => array(
            'name' => 'Calculators',
            'singular_name' => 'Calculator'
        ),
        'public' => true,
        'supports' => array('title', 'editor', 'custom-fields'),
        'menu_icon' => 'dashicons-calculator'
    ));
}
add_action('init', 'register_calculator_post_type');
