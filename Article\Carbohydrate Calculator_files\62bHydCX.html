<!DOCTYPE html>
<!-- saved from url=(0053)https://tpc.googlesyndication.com/sodar/62bHydCX.html -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8"><script>
(function(){/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var n,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},p="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ba=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");
},r=ba(this),t=function(a,b){if(b)a:{var c=r;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&p(c,a,{configurable:!0,writable:!0,value:b})}};
t("Symbol",function(a){if(a)return a;var b=function(h,f){this.Ca=h;p(this,"description",{configurable:!0,writable:!0,value:f})};b.prototype.toString=function(){return this.Ca};var c="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",d=0,e=function(h){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(h||"")+"_"+d++,h)};return e});
t("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=r[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&p(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ca(aa(this))}})}return a});
var ca=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a},u=function(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if("number"==typeof a.length)return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},da="function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ea;
if("function"==typeof Object.setPrototypeOf)ea=Object.setPrototypeOf;else{var fa;a:{var ha={a:!0},ia={};try{ia.__proto__=ha;fa=ia.a;break a}catch(a){}fa=!1}ea=fa?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}
var ja=ea,ka=function(a,b){a.prototype=da(b.prototype);a.prototype.constructor=a;if(ja)ja(a,b);else for(var c in b)if("prototype"!=c)if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Ya=b.prototype};
t("Promise",function(a){function b(){this.C=null}function c(f){return f instanceof e?f:new e(function(g){g(f)})}if(a)return a;b.prototype.sa=function(f){if(null==this.C){this.C=[];var g=this;this.ta(function(){g.Ga()})}this.C.push(f)};var d=r.setTimeout;b.prototype.ta=function(f){d(f,0)};b.prototype.Ga=function(){for(;this.C&&this.C.length;){var f=this.C;this.C=[];for(var g=0;g<f.length;++g){var k=f[g];f[g]=null;try{k()}catch(l){this.Ea(l)}}}this.C=null};b.prototype.Ea=function(f){this.ta(function(){throw f;
})};var e=function(f){this.i=0;this.H=void 0;this.R=[];this.xa=!1;var g=this.ka();try{f(g.resolve,g.reject)}catch(k){g.reject(k)}};e.prototype.ka=function(){function f(l){return function(m){k||(k=!0,l.call(g,m))}}var g=this,k=!1;return{resolve:f(this.Ta),reject:f(this.qa)}};e.prototype.Ta=function(f){if(f===this)this.qa(new TypeError("A Promise cannot resolve to itself"));else if(f instanceof e)this.Va(f);else{a:switch(typeof f){case "object":var g=null!=f;break a;case "function":g=!0;break a;default:g=
!1}g?this.Sa(f):this.va(f)}};e.prototype.Sa=function(f){var g=void 0;try{g=f.then}catch(k){this.qa(k);return}"function"==typeof g?this.Wa(g,f):this.va(f)};e.prototype.qa=function(f){this.Aa(2,f)};e.prototype.va=function(f){this.Aa(1,f)};e.prototype.Aa=function(f,g){if(0!=this.i)throw Error("Cannot settle("+f+", "+g+"): Promise already settled in state"+this.i);this.i=f;this.H=g;2===this.i&&this.Ua();this.Ia()};e.prototype.Ua=function(){var f=this;d(function(){if(f.Oa()){var g=r.console;"undefined"!==
typeof g&&g.error(f.H)}},1)};e.prototype.Oa=function(){if(this.xa)return!1;var f=r.CustomEvent,g=r.Event,k=r.dispatchEvent;if("undefined"===typeof k)return!0;"function"===typeof f?f=new f("unhandledrejection",{cancelable:!0}):"function"===typeof g?f=new g("unhandledrejection",{cancelable:!0}):(f=r.document.createEvent("CustomEvent"),f.initCustomEvent("unhandledrejection",!1,!0,f));f.promise=this;f.reason=this.H;return k(f)};e.prototype.Ia=function(){if(null!=this.R){for(var f=0;f<this.R.length;++f)h.sa(this.R[f]);
this.R=null}};var h=new b;e.prototype.Va=function(f){var g=this.ka();f.ca(g.resolve,g.reject)};e.prototype.Wa=function(f,g){var k=this.ka();try{f.call(g,k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.then=function(f,g){function k(q,z){return"function"==typeof q?function(Y){try{l(q(Y))}catch(O){m(O)}}:z}var l,m,x=new e(function(q,z){l=q;m=z});this.ca(k(f,l),k(g,m));return x};e.prototype.catch=function(f){return this.then(void 0,f)};e.prototype.ca=function(f,g){function k(){switch(l.i){case 1:f(l.H);
break;case 2:g(l.H);break;default:throw Error("Unexpected state: "+l.i);}}var l=this;null==this.R?h.sa(k):this.R.push(k);this.xa=!0};e.resolve=c;e.reject=function(f){return new e(function(g,k){k(f)})};e.race=function(f){return new e(function(g,k){for(var l=u(f),m=l.next();!m.done;m=l.next())c(m.value).ca(g,k)})};e.all=function(f){var g=u(f),k=g.next();return k.done?c([]):new e(function(l,m){function x(Y){return function(O){q[Y]=O;z--;0==z&&l(q)}}var q=[],z=0;do q.push(void 0),z++,c(k.value).ca(x(q.length-
1),m),k=g.next();while(!k.done)})};return e});var v=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};
t("WeakMap",function(a){function b(){}function c(k){var l=typeof k;return"object"===l&&null!==k||"function"===l}function d(k){if(!v(k,h)){var l=new b;p(k,h,{value:l})}}function e(k){var l=Object[k];l&&(Object[k]=function(m){if(m instanceof b)return m;Object.isExtensible(m)&&d(m);return l(m)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),l=Object.seal({}),m=new a([[k,2],[l,3]]);if(2!=m.get(k)||3!=m.get(l))return!1;m.delete(k);m.set(l,4);return!m.has(k)&&4==m.get(l)}catch(x){return!1}}())return a;
var h="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var f=0,g=function(k){this.X=(f+=Math.random()+1).toString();if(k){k=u(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};g.prototype.set=function(k,l){if(!c(k))throw Error("Invalid WeakMap key");d(k);if(!v(k,h))throw Error("WeakMap key fail: "+k);k[h][this.X]=l;return this};g.prototype.get=function(k){return c(k)&&v(k,h)?k[h][this.X]:void 0};g.prototype.has=function(k){return c(k)&&v(k,h)&&v(k[h],this.X)};
g.prototype.delete=function(k){return c(k)&&v(k,h)&&v(k[h],this.X)?delete k[h][this.X]:!1};return g});
t("Map",function(a){if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var g=Object.seal({x:4}),k=new a(u([[g,"s"]]));if("s"!=k.get(g)||1!=k.size||k.get({x:4})||k.set({x:4},"t")!=k||2!=k.size)return!1;var l=k.entries(),m=l.next();if(m.done||m.value[0]!=g||"s"!=m.value[1])return!1;m=l.next();return m.done||4!=m.value[0].x||"t"!=m.value[1]||!l.next().done?!1:!0}catch(x){return!1}}())return a;var b=new WeakMap,c=function(g){this[0]={};this[1]=
h();this.size=0;if(g){g=u(g);for(var k;!(k=g.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(g,k){g=0===g?0:g;var l=d(this,g);l.list||(l.list=this[0][l.id]=[]);l.j?l.j.value=k:(l.j={next:this[1],B:this[1].B,head:this[1],key:g,value:k},l.list.push(l.j),this[1].B.next=l.j,this[1].B=l.j,this.size++);return this};c.prototype.delete=function(g){g=d(this,g);return g.j&&g.list?(g.list.splice(g.index,1),g.list.length||delete this[0][g.id],g.j.B.next=g.j.next,g.j.next.B=g.j.B,g.j.head=
null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].B=h();this.size=0};c.prototype.has=function(g){return!!d(this,g).j};c.prototype.get=function(g){return(g=d(this,g).j)&&g.value};c.prototype.entries=function(){return e(this,function(g){return[g.key,g.value]})};c.prototype.keys=function(){return e(this,function(g){return g.key})};c.prototype.values=function(){return e(this,function(g){return g.value})};c.prototype.forEach=function(g,k){for(var l=this.entries(),m;!(m=l.next()).done;)m=
m.value,g.call(k,m[1],m[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(g,k){var l=k&&typeof k;"object"==l||"function"==l?b.has(k)?l=b.get(k):(l=""+ ++f,b.set(k,l)):l="p_"+k;var m=g[0][l];if(m&&v(g[0],l))for(g=0;g<m.length;g++){var x=m[g];if(k!==k&&x.key!==x.key||k===x.key)return{id:l,list:m,index:g,j:x}}return{id:l,list:m,index:-1,j:void 0}},e=function(g,k){var l=g[1];return ca(function(){if(l){for(;l.head!=g[1];)l=l.B;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};
l=null}return{done:!0,value:void 0}})},h=function(){var g={};return g.B=g.next=g.head=g},f=0;return c});var la="function"==typeof Object.assign?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)v(d,e)&&(a[e]=d[e])}return a};t("Object.assign",function(a){return a||la});
var ma=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var h=c++;return{value:b(h,a[h]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};t("Array.prototype.keys",function(a){return a?a:function(){return ma(this,function(b){return b})}});t("Array.prototype.values",function(a){return a?a:function(){return ma(this,function(b,c){return c})}});
t("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(g){return g};var e=[],h="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof h){b=h.call(b);for(var f=0;!(h=b.next()).done;)e.push(c.call(d,h.value,f++))}else for(h=b.length,f=0;f<h;f++)e.push(c.call(d,b[f],f));return e}});t("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)v(b,d)&&c.push(b[d]);return c}});
t("Object.is",function(a){return a?a:function(b,c){return b===c?0!==b||1/b===1/c:b!==b&&c!==c}});t("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(0>c&&(c=Math.max(c+e,0));c<e;c++){var h=d[c];if(h===b||Object.is(h,b))return!0}return!1}});
t("String.prototype.includes",function(a){return a?a:function(b,c){if(null==this)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return-1!==(this+"").indexOf(b,c||0)}});
var w=this||self,na=function(a){a=a.split(".");for(var b=w,c=0;c<a.length;c++)if(b=b[a[c]],null==b)return null;return b},oa=function(a){var b=typeof a;b="object"!=b?b:a?Array.isArray(a)?"array":b:"null";return"array"==b||"object"==b&&"number"==typeof a.length},pa=function(a){var b=typeof a;return"object"==b&&null!=a||"function"==b},qa=function(a,b,c){return a.call.apply(a.bind,arguments)},ra=function(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=
Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}},sa=function(a,b,c){sa=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?qa:ra;return sa.apply(null,arguments)},ta=function(a,b){var c=Array.prototype.slice.call(arguments,1);return function(){var d=c.slice();d.push.apply(d,arguments);return a.apply(this,d)}},ua=function(a,b){function c(){}c.prototype=b.prototype;a.Ya=b.prototype;
a.prototype=new c;a.prototype.constructor=a;a.sb=function(d,e,h){for(var f=Array(arguments.length-2),g=2;g<arguments.length;g++)f[g-2]=arguments[g];return b.prototype[e].apply(d,f)}},va=function(a){return a};function y(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,y);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));void 0!==b&&(this.cause=b)}ua(y,Error);y.prototype.name="CustomError";var wa,xa=na("CLOSURE_FLAGS"),ya=xa&&xa[610401301];wa=null!=ya?ya:!1;var za=/&/g,Aa=/</g,Ba=/>/g,Ca=/"/g,Da=/'/g,Ea=/\x00/g,Fa=/[\x00&<>"']/;var A,Ga=w.navigator;A=Ga?Ga.userAgentData||null:null;function Ha(a){return wa?A?A.brands.some(function(b){return(b=b.brand)&&-1!=b.indexOf(a)}):!1:!1}function B(a){var b;a:{if(b=w.navigator)if(b=b.userAgent)break a;b=""}return-1!=b.indexOf(a)};function C(){return wa?!!A&&0<A.brands.length:!1}function Ia(){return C()?!1:B("Trident")||B("MSIE")}function Ja(){return C()?Ha("Chromium"):(B("Chrome")||B("CriOS"))&&!(C()?0:B("Edge"))||B("Silk")};var Ka=Ia();var La=function(){};var Ma;var D=function(a,b){this.Xa=a===Na&&b||"";this.Da=Oa},Pa=function(a){return a instanceof D&&a.constructor===D&&a.Da===Oa?a.Xa:"type_error:Const"},Oa={},Na={};var Qa=function(a){this.za=a};Qa.prototype.toString=function(){return this.za+""};
var Ua=function(a,b){var c=Pa(a);if(!Ra.test(c))throw Error("Invalid TrustedResourceUrl format: "+c);a=c.replace(Sa,function(d,e){if(!Object.prototype.hasOwnProperty.call(b,e))throw Error('Found marker, "'+e+'", in format string, "'+c+'", but no valid label mapping found in args: '+JSON.stringify(b));d=b[e];return d instanceof D?Pa(d):encodeURIComponent(String(d))});return Ta(a)},Sa=/%{(\w+)}/g,Ra=RegExp("^((https:)?//[0-9a-z.:[\\]-]+/|/[^/\\\\]|[^:/\\\\%]+/|[^:/\\\\%]*[?#]|about:blank#)","i"),Va=
{},Ta=function(a){if(void 0===Ma){var b=null;var c=w.trustedTypes;if(c&&c.createPolicy)try{b=c.createPolicy("goog#html",{createHTML:va,createScript:va,createScriptURL:va})}catch(d){w.console&&w.console.error(d.message)}Ma=b}a=(b=Ma)?b.createScriptURL(a):a;return new Qa(a,Va)};var Wa=function(a){Fa.test(a)&&(-1!=a.indexOf("&")&&(a=a.replace(za,"&amp;")),-1!=a.indexOf("<")&&(a=a.replace(Aa,"&lt;")),-1!=a.indexOf(">")&&(a=a.replace(Ba,"&gt;")),-1!=a.indexOf('"')&&(a=a.replace(Ca,"&quot;")),-1!=a.indexOf("'")&&(a=a.replace(Da,"&#39;")),-1!=a.indexOf("\x00")&&(a=a.replace(Ea,"&#0;")));return a};var Xa=function(){var a=document;var b="IFRAME";"application/xhtml+xml"===a.contentType&&(b=b.toLowerCase());return a.createElement(b)};var Ya=function(a){var b=Number;b=void 0===b?function(c){return c}:b;this.Pa=a;this.transform=b},Za={},$a=(Za[1]=new Ya("4CGeArbVQ"),Za[6]=new Ya("4CGeArbVR"),Za);var ab=document,bb=window;var cb={jb:0,pb:1,rb:2,lb:3,hb:4,qb:5,ib:6};var db={kb:0,fb:1,eb:2,cb:3,gb:4,ob:5,nb:6,mb:7};var eb=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}}(function(){var a=!1;try{var b=Object.defineProperty({},"passive",{get:function(){a=!0}});w.addEventListener("test",null,b)}catch(c){}return a}),fb=function(a){var b=window,c={capture:void 0};if(b.addEventListener){var d=b.addEventListener;c=c?c.passive&&eb()?c:c.capture||!1:!1;d.call(b,"message",a,c)}};var gb=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),hb=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var h=a[c].substring(0,d);e=a[c].substring(d+1)}else h=a[c];b(h,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};/*

 SPDX-License-Identifier: Apache-2.0
*/
function ib(a,b){a.src=b instanceof Qa&&b.constructor===Qa?b.za:"type_error:TrustedResourceUrl";var c,d;(c=(b=null==(d=(c=(a.ownerDocument&&a.ownerDocument.defaultView||window).document).querySelector)?void 0:d.call(c,"script[nonce]"))?b.nonce||b.getAttribute("nonce")||"":"")&&a.setAttribute("nonce",c)};function jb(a){var b=window;b.google_image_requests||(b.google_image_requests=[]);var c=b.document;c=void 0===c?document:c;c=c.createElement("img");c.src=a;b.google_image_requests.push(c)};var kb=function(a,b){this.Na=100;this.Fa=a;this.Ra=b;this.ia=0;this.ga=null};kb.prototype.get=function(){if(0<this.ia){this.ia--;var a=this.ga;this.ga=a.next;a.next=null}else a=this.Fa();return a};kb.prototype.put=function(a){this.Ra(a);this.ia<this.Na&&(this.ia++,a.next=this.ga,this.ga=a)};var lb,mb=function(){var a=w.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!B("Presto")&&(a=function(){var e=Xa();e.style.display="none";document.documentElement.appendChild(e);var h=e.contentWindow;e=h.document;e.open();e.close();var f="callImmediate"+Math.random(),g="file:"==h.location.protocol?"*":h.location.protocol+"//"+h.location.host;e=sa(function(k){if(("*"==g||k.origin==g)&&k.data==f)this.port1.onmessage()},this);h.addEventListener("message",
e,!1);this.port1={};this.port2={postMessage:function(){h.postMessage(f,g)}}});if("undefined"!==typeof a&&!Ia()){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var e=c.ua;c.ua=null;e()}};return function(e){d.next={ua:e};d=d.next;b.port2.postMessage(0)}}return function(e){w.setTimeout(e,0)}};function nb(a){w.setTimeout(function(){throw a;},0)};var ob=function(){this.ja=this.T=null};ob.prototype.add=function(a,b){var c=pb.get();c.set(a,b);this.ja?this.ja.next=c:this.T=c;this.ja=c};ob.prototype.remove=function(){var a=null;this.T&&(a=this.T,this.T=this.T.next,this.T||(this.ja=null),a.next=null);return a};var pb=new kb(function(){return new qb},function(a){return a.reset()}),qb=function(){this.next=this.scope=this.ma=null};qb.prototype.set=function(a,b){this.ma=a;this.scope=b;this.next=null};
qb.prototype.reset=function(){this.next=this.scope=this.ma=null};var rb,sb=!1,tb=new ob,vb=function(a,b){rb||ub();sb||(rb(),sb=!0);tb.add(a,b)},ub=function(){if(w.Promise&&w.Promise.resolve){var a=w.Promise.resolve(void 0);rb=function(){a.then(wb)}}else rb=function(){var b=wb;"function"!==typeof w.setImmediate||w.Window&&w.Window.prototype&&(C()||!B("Edge"))&&w.Window.prototype.setImmediate==w.setImmediate?(lb||(lb=mb()),lb(b)):w.setImmediate(b)}},wb=function(){for(var a;a=tb.remove();){try{a.ma.call(a.scope)}catch(b){nb(b)}pb.put(a)}sb=!1};var F=function(a){this.i=0;this.H=void 0;this.O=this.D=this.M=null;this.fa=this.la=!1;if(a!=La)try{var b=this;a.call(void 0,function(c){E(b,2,c)},function(c){E(b,3,c)})}catch(c){E(this,3,c)}},xb=function(){this.next=this.context=this.P=this.Y=this.J=null;this.U=!1};xb.prototype.reset=function(){this.context=this.P=this.Y=this.J=null;this.U=!1};
var yb=new kb(function(){return new xb},function(a){a.reset()}),zb=function(a,b,c){var d=yb.get();d.Y=a;d.P=b;d.context=c;return d},G=function(){var a=new F(La);E(a,2);return a},H=function(a){return new F(function(b,c){c(a)})},Bb=function(a,b,c){Ab(a,b,c,null)||vb(ta(b,a))},Cb=function(a){return new F(function(b,c){var d=a.length,e=[];if(d)for(var h=function(l,m){d--;e[l]=m;0==d&&b(e)},f=function(l){c(l)},g=0,k;g<a.length;g++)k=a[g],Bb(k,ta(h,g),f);else b(e)})},Eb=function(){var a,b,c=new F(function(d,
e){a=d;b=e});return new Db(c,a,b)};F.prototype.then=function(a,b,c){return Fb(this,"function"===typeof a?a:null,"function"===typeof b?b:null,c)};F.prototype.$goog_Thenable=!0;var Hb=function(a,b){b=zb(b,b);b.U=!0;Gb(a,b);return a};F.prototype.Za=function(a,b){return Fb(this,null,a,b)};F.prototype.catch=F.prototype.Za;F.prototype.cancel=function(a){if(0==this.i){var b=new I(a);vb(function(){Ib(this,b)},this)}};
var Ib=function(a,b){if(0==a.i)if(a.M){var c=a.M;if(c.D){for(var d=0,e=null,h=null,f=c.D;f&&(f.U||(d++,f.J==a&&(e=f),!(e&&1<d)));f=f.next)e||(h=f);e&&(0==c.i&&1==d?Ib(c,b):(h?(d=h,d.next==c.O&&(c.O=d),d.next=d.next.next):Jb(c),Kb(c,e,3,b)))}a.M=null}else E(a,3,b)},Gb=function(a,b){a.D||2!=a.i&&3!=a.i||Lb(a);a.O?a.O.next=b:a.D=b;a.O=b},Fb=function(a,b,c,d){var e=zb(null,null,null);e.J=new F(function(h,f){e.Y=b?function(g){try{var k=b.call(d,g);h(k)}catch(l){f(l)}}:h;e.P=c?function(g){try{var k=c.call(d,
g);void 0===k&&g instanceof I?f(g):h(k)}catch(l){f(l)}}:f});e.J.M=a;Gb(a,e);return e.J};F.prototype.ab=function(a){this.i=0;E(this,2,a)};F.prototype.bb=function(a){this.i=0;E(this,3,a)};
var E=function(a,b,c){0==a.i&&(a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself")),a.i=1,Ab(c,a.ab,a.bb,a)||(a.H=c,a.i=b,a.M=null,Lb(a),3!=b||c instanceof I||Mb(a,c)))},Ab=function(a,b,c,d){if(a instanceof F)return Gb(a,zb(b||La,c||null,d)),!0;if(a)try{var e=!!a.$goog_Thenable}catch(f){e=!1}else e=!1;if(e)return a.then(b,c,d),!0;if(pa(a))try{var h=a.then;if("function"===typeof h)return Nb(a,h,b,c,d),!0}catch(f){return c.call(d,f),!0}return!1},Nb=function(a,b,c,d,e){var h=!1,f=function(k){h||
(h=!0,c.call(e,k))},g=function(k){h||(h=!0,d.call(e,k))};try{b.call(a,f,g)}catch(k){g(k)}},Lb=function(a){a.la||(a.la=!0,vb(a.Ha,a))},Jb=function(a){var b=null;a.D&&(b=a.D,a.D=b.next,b.next=null);a.D||(a.O=null);return b};F.prototype.Ha=function(){for(var a;a=Jb(this);)Kb(this,a,this.i,this.H);this.la=!1};
var Kb=function(a,b,c,d){if(3==c&&b.P&&!b.U)for(;a&&a.fa;a=a.M)a.fa=!1;if(b.J)b.J.M=null,Ob(b,c,d);else try{b.U?b.Y.call(b.context):Ob(b,c,d)}catch(e){Pb.call(null,e)}yb.put(b)},Ob=function(a,b,c){2==b?a.Y.call(a.context,c):a.P&&a.P.call(a.context,c)},Mb=function(a,b){a.fa=!0;vb(function(){a.fa&&Pb.call(null,b)})},Pb=nb,I=function(a){y.call(this,a)};ua(I,y);I.prototype.name="cancel";var Db=function(a,b,c){this.promise=a;this.resolve=b;this.reject=c};var Qb=function(a){if(a.L&&"function"==typeof a.L)return a.L();if("undefined"!==typeof Map&&a instanceof Map||"undefined"!==typeof Set&&a instanceof Set)return Array.from(a.values());if("string"===typeof a)return a.split("");if(oa(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b},Rb=function(a){if(a.na&&"function"==typeof a.na)return a.na();if(!a.L||"function"!=typeof a.L){if("undefined"!==typeof Map&&a instanceof Map)return Array.from(a.keys());
if(!("undefined"!==typeof Set&&a instanceof Set)){if(oa(a)||"string"===typeof a){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}b=[];c=0;for(var d in a)b[c++]=d;return b}}},Sb=function(a,b,c){if(a.forEach&&"function"==typeof a.forEach)a.forEach(b,c);else if(oa(a)||"string"===typeof a)Array.prototype.forEach.call(a,b,c);else for(var d=Rb(a),e=Qb(a),h=e.length,f=0;f<h;f++)b.call(c,e[f],d&&d[f],a)};var J=function(a){this.A=this.N=this.I="";this.S=null;this.K=this.u="";this.o=this.Ka=!1;if(a instanceof J){this.o=a.o;Tb(this,a.I);var b=a.N;K(this);this.N=b;b=a.A;K(this);this.A=b;Ub(this,a.S);b=a.u;K(this);this.u=b;Vb(this,a.s.clone());a=a.K;K(this);this.K=a}else a&&(b=String(a).match(gb))?(this.o=!1,Tb(this,b[1]||"",!0),a=b[2]||"",K(this),this.N=L(a),a=b[3]||"",K(this),this.A=L(a,!0),Ub(this,b[4]),a=b[5]||"",K(this),this.u=L(a,!0),Vb(this,b[6]||"",!0),a=b[7]||"",K(this),this.K=L(a)):(this.o=!1,
this.s=new M(null,this.o))};J.prototype.toString=function(){var a=[],b=this.I;b&&a.push(N(b,Wb,!0),":");var c=this.A;if(c||"file"==b)a.push("//"),(b=this.N)&&a.push(N(b,Wb,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.S,null!=c&&a.push(":",String(c));if(c=this.u)this.A&&"/"!=c.charAt(0)&&a.push("/"),a.push(N(c,"/"==c.charAt(0)?Xb:Yb,!0));(c=this.s.toString())&&a.push("?",c);(c=this.K)&&a.push("#",N(c,Zb));return a.join("")};
J.prototype.resolve=function(a){var b=this.clone(),c=!!a.I;c?Tb(b,a.I):c=!!a.N;if(c){var d=a.N;K(b);b.N=d}else c=!!a.A;c?(d=a.A,K(b),b.A=d):c=null!=a.S;d=a.u;if(c)Ub(b,a.S);else if(c=!!a.u){if("/"!=d.charAt(0))if(this.A&&!this.u)d="/"+d;else{var e=b.u.lastIndexOf("/");-1!=e&&(d=b.u.slice(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var h=[],f=0;f<e.length;){var g=e[f++];"."==g?d&&f==e.length&&h.push(""):".."==
g?((1<h.length||1==h.length&&""!=h[0])&&h.pop(),d&&f==e.length&&h.push("")):(h.push(g),d=!0)}d=h.join("/")}else d=e}c?(K(b),b.u=d):c=""!==a.s.toString();c?Vb(b,a.s.clone()):c=!!a.K;c&&(a=a.K,K(b),b.K=a);return b};J.prototype.clone=function(){return new J(this)};
var Tb=function(a,b,c){K(a);a.I=c?L(b,!0):b;a.I&&(a.I=a.I.replace(/:$/,""))},Ub=function(a,b){K(a);if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("Bad port number "+b);a.S=b}else a.S=null},Vb=function(a,b,c){K(a);b instanceof M?(a.s=b,a.s.ra(a.o)):(c||(b=N(b,$b)),a.s=new M(b,a.o))};J.prototype.getQuery=function(){return this.s.toString()};var P=function(a,b,c){K(a);a.s.set(b,c);return a};J.prototype.removeParameter=function(a){K(this);this.s.remove(a);return this};
var K=function(a){if(a.Ka)throw Error("Tried to modify a read-only Uri");};J.prototype.ra=function(a){this.o=a;this.s&&this.s.ra(a)};
var L=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""},N=function(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,ac),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null},ac=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)},Wb=/[#\/\?@]/g,Yb=/[#\?:]/g,Xb=/[#\?]/g,$b=/[#\?@]/g,Zb=/#/g,M=function(a,b){this.l=this.g=null;this.m=a||null;this.o=!!b},Q=function(a){a.g||(a.g=new Map,a.l=0,a.m&&hb(a.m,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g,
" ")),c)}))};M.prototype.add=function(a,b){Q(this);this.m=null;a=R(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.l+=1;return this};M.prototype.remove=function(a){Q(this);a=R(this,a);return this.g.has(a)?(this.m=null,this.l-=this.g.get(a).length,this.g.delete(a)):!1};M.prototype.clear=function(){this.g=this.m=null;this.l=0};var bc=function(a,b){Q(a);b=R(a,b);return a.g.has(b)};n=M.prototype;
n.forEach=function(a,b){Q(this);this.g.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};n.na=function(){Q(this);for(var a=Array.from(this.g.values()),b=Array.from(this.g.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],h=0;h<e.length;h++)c.push(b[d]);return c};n.L=function(a){Q(this);var b=[];if("string"===typeof a)bc(this,a)&&(b=b.concat(this.g.get(R(this,a))));else{a=Array.from(this.g.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
n.set=function(a,b){Q(this);this.m=null;a=R(this,a);bc(this,a)&&(this.l-=this.g.get(a).length);this.g.set(a,[b]);this.l+=1;return this};n.get=function(a,b){if(!a)return b;a=this.L(a);return 0<a.length?String(a[0]):b};
n.toString=function(){if(this.m)return this.m;if(!this.g)return"";for(var a=[],b=Array.from(this.g.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.L(d);for(var h=0;h<d.length;h++){var f=e;""!==d[h]&&(f+="="+encodeURIComponent(String(d[h])));a.push(f)}}return this.m=a.join("&")};n.clone=function(){var a=new M;a.m=this.m;this.g&&(a.g=new Map(this.g),a.l=this.l);return a};var R=function(a,b){b=String(b);a.o&&(b=b.toLowerCase());return b};
M.prototype.ra=function(a){a&&!this.o&&(Q(this),this.m=null,this.g.forEach(function(b,c){var d=c.toLowerCase();if(c!=d&&(this.remove(c),this.remove(d),0<b.length)){this.m=null;c=this.g;var e=c.set;d=R(this,d);var h=b.length;if(0<h){for(var f=Array(h),g=0;g<h;g++)f[g]=b[g];h=f}else h=[];e.call(c,d,h);this.l+=b.length}},this));this.o=a};M.prototype.extend=function(a){for(var b=0;b<arguments.length;b++)Sb(arguments[b],function(c,d){this.add(d,c)},this)};function cc(a,b){b=void 0===b?window:b;var c=void 0===c?{}:c;return new F(function(d,e){function h(){f.onload=null;f.onerror=null;f.onreadystatechange=null;w.setTimeout(dc(function(){return document.body.removeChild(f)},"l:lsc"),0)}var f=b.document.createElement("SCRIPT".toString());f.onload=f.onreadystatechange=function(){f.readyState&&"loaded"!=f.readyState&&"complete"!=f.readyState||(h(),d())};f.onerror=function(){h();e(null)};f.type="text/javascript";ib(f,a);for(var g in c)f.setAttribute(g,c[g]);
b.document.body.appendChild(f)})}function ec(a){return P(P(P(new J("//pagead2.googlesyndication.com/pagead/gen_204"),"id","sodar"),"v",44),"t",a)}function fc(a,b,c,d){!d||0>=c||(a.postMessage(b,d),a.parent&&fc(a.parent,b,c-1,d))}function S(a,b,c,d,e,h,f,g,k){var l=ec(1);P(l,"e",a);c&&P(l,"li",c);d&&P(l,"cv",d);e&&P(l,"sid",e);h&&P(l,"sc",h);g&&P(l,"dt",g);k&&P(l,"et",k);b&&P(l,"bgai",b);f&&P(l,"ds",f);jb(l.toString())}
var ic=function(){void 0===gc&&(gc=hc())},gc,jc=function(){new ic;return gc||null},hc=function(){var a=window.GoogleTyFxhY;if(!a)return S(13),null;if(0==a.length)return S(1),null;a=a.shift();return("true"===a._isfl_||a._scs_||a._cv_||a._sid_)&&(a._upb_||a._bgu_&&a._bgp_)?a:(S(2),null)};
function dc(a,b){return function(){try{return a.apply(this,arguments)}catch(k){if(!(1<=kc.count)){var c=k,d=ec(3),e=jc()._scs_,h=jc()._sid_;e&&P(d,"bgai",e);h&&P(d,"sid",h);P(d,"c",b);h=c.toString();c.name&&-1==h.indexOf(c.name)&&(h+=": "+c.name);c.message&&-1==h.indexOf(c.message)&&(h+=": "+c.message);if(c.stack){c=c.stack;var f=h;try{-1==c.indexOf(f)&&(c=f+"\n"+c);for(var g;c!=g;)g=c,c=c.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");h=c.replace(RegExp("\n *","g"),"\n")}catch(l){h=
f}}P(d,"ex",h);d=d.toString();6E4<d.length?S(11,e):jb(d);kc.count+=1}}}}var kc={count:0};function lc(a){if(!pa(a))return!1;switch(a["0"]){case "0":return"0"!==a["0"]||"string"!==typeof a["1"]||"string"!==typeof a["2"]||"string"!==typeof a["3"]||"string"!==typeof a["4"]||"string"!==typeof a["8"]||"5"in a&&"string"!==typeof a["5"]||"string"!==typeof a["9"]||"string"!==typeof a["13"]?!1:!0;case "2":return"2"===a["0"];case "3":return"3"===a["0"]}return!1};!B("Android")||Ja();Ja();B("Safari")&&(Ja()||(C()?0:B("Coast"))||(C()?0:B("Opera"))||(C()?0:B("Edge"))||(C()?Ha("Microsoft Edge"):B("Edg/"))||C()&&Ha("Opera"));var mc={},nc=null;var oc="undefined"!==typeof Uint8Array,pc=!Ka&&"function"===typeof btoa;var T="function"===typeof Symbol&&"symbol"===typeof Symbol()?Symbol():void 0,qc=T?function(a,b){a[T]|=b}:function(a,b){void 0!==a.F?a.F|=b:Object.defineProperties(a,{F:{value:b,configurable:!0,writable:!0,enumerable:!1}})};function rc(a){var b=U(a);1!==(b&1)&&(Object.isFrozen(a)&&(a=Array.prototype.slice.call(a)),V(a,b|1))}
var U=T?function(a){return a[T]|0}:function(a){return a.F|0},W=T?function(a){return a[T]}:function(a){return a.F},V=T?function(a,b){a[T]=b}:function(a,b){void 0!==a.F?a.F=b:Object.defineProperties(a,{F:{value:b,configurable:!0,writable:!0,enumerable:!1}})};function sc(){var a=[];qc(a,1);return a}function tc(a,b){V(b,(a|0)&-255)}function uc(a,b){V(b,(a|34)&-221)}function vc(a){a=a>>11&1023;return 0===a?536870912:a};var wc={};function X(a){return null!==a&&"object"===typeof a&&!Array.isArray(a)&&a.constructor===Object}var Z,xc=[];V(xc,39);Z=Object.freeze(xc);var yc;function zc(a,b,c){null==a&&(a=yc);yc=void 0;if(null==a){var d=96;c?(a=[c],d|=512):a=[];b&&(d=d&-2095105|(b&1023)<<11)}else{if(!Array.isArray(a))throw Error();d=U(a);if(d&64)return a;d|=64;if(c&&(d|=512,c!==a[0]))throw Error();a:{c=a;var e=c.length;if(e){var h=e-1,f=c[h];if(X(f)){d|=256;b=+!!(d&512)-1;e=h-b;1024<=e&&(Ac(c,b,f),e=1023);d=d&-2095105|(e&1023)<<11;break a}}b&&(f=+!!(d&512)-1,b=Math.max(b,e-f),1024<b&&(Ac(c,f,{}),d|=256,b=1023),d=d&-2095105|(b&1023)<<11)}}V(a,d);return a}
function Ac(a,b,c){for(var d=1023+b,e=a.length,h=d;h<e;h++){var f=a[h];null!=f&&f!==c&&(c[h-b]=f)}a.length=d+1;a[d]=c};function Bc(a){switch(typeof a){case "number":return isFinite(a)?a:String(a);case "boolean":return a?1:0;case "object":if(a&&!Array.isArray(a)&&oc&&null!=a&&a instanceof Uint8Array){if(pc){for(var b="",c=0,d=a.length-10240;c<d;)b+=String.fromCharCode.apply(null,a.subarray(c,c+=10240));b+=String.fromCharCode.apply(null,c?a.subarray(c):a);a=btoa(b)}else{void 0===b&&(b=0);if(!nc){nc={};c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split("");d=["+/=","+/","-_=","-_.","-_"];for(var e=
0;5>e;e++){var h=c.concat(d[e].split(""));mc[e]=h;for(var f=0;f<h.length;f++){var g=h[f];void 0===nc[g]&&(nc[g]=f)}}}b=mc[b];c=Array(Math.floor(a.length/3));d=b[64]||"";for(e=h=0;h<a.length-2;h+=3){var k=a[h],l=a[h+1];g=a[h+2];f=b[k>>2];k=b[(k&3)<<4|l>>4];l=b[(l&15)<<2|g>>6];g=b[g&63];c[e++]=""+f+k+l+g}f=0;g=d;switch(a.length-h){case 2:f=a[h+1],g=b[(f&15)<<2]||d;case 1:a=a[h],c[e]=""+b[a>>2]+b[(a&3)<<4|f>>4]+g+d}a=c.join("")}return a}}return a};function Cc(a,b,c){a=Array.prototype.slice.call(a);var d=a.length,e=b&256?a[d-1]:void 0;d+=e?-1:0;for(b=b&512?1:0;b<d;b++)a[b]=c(a[b]);if(e){b=a[b]={};for(var h in e)b[h]=c(e[h])}return a}function Dc(a,b,c,d,e,h){if(null!=a){if(Array.isArray(a))a=e&&0==a.length&&U(a)&1?void 0:h&&U(a)&2?a:Ec(a,b,c,void 0!==d,e,h);else if(X(a)){var f={},g;for(g in a)f[g]=Dc(a[g],b,c,d,e,h);a=f}else a=b(a,d);return a}}
function Ec(a,b,c,d,e,h){var f=d||c?U(a):0;d=d?!!(f&32):void 0;a=Array.prototype.slice.call(a);for(var g=0;g<a.length;g++)a[g]=Dc(a[g],b,c,d,e,h);c&&c(f,a);return a}function Fc(a){return a.oa===wc?a.toJSON():Bc(a)};function Gc(a,b,c){c=void 0===c?uc:c;if(null!=a){if(oc&&a instanceof Uint8Array)return b?a:new Uint8Array(a);if(Array.isArray(a)){var d=U(a);return d&2?a:!b||d&68||!(d&32||0===d)?Ec(a,Gc,d&4?uc:c,!0,!1,!0):(V(a,d|34),a)}a.oa===wc&&(b=a.G,c=W(b),a=c&2?a:Hc(a,b,c,!0));return a}}function Hc(a,b,c,d){a=a.constructor;yc=b=Ic(b,c,d);b=new a(b);yc=void 0;return b}function Ic(a,b,c){var d=c||b&2?uc:tc,e=!!(b&32);a=Cc(a,b,function(h){return Gc(h,e,d)});qc(a,32|(c?2:0));return a};var Jc=function(a,b,c){this.G=zc(a,b,c)};n=Jc.prototype;n.toJSON=function(){var a=Ec(this.G,Fc,void 0,void 0,!1,!1);return Kc(this,a,!0)};n.getExtension=function(a){var b=a.V?a.ea(this,a.V,a.W,!0):a.ub?a.ea(this,a.W,!0):a.ea(this,a.W,a.defaultValue,!0);return a.tb&&null==b?a.defaultValue:b};
n.hasExtension=function(a){if(a.V){var b=a.V;a=a.W;var c=this.G,d=W(c);a:if(-1===a)var e=null;else{if(a>=vc(d)){if(d&256){e=c[c.length-1][a];break a}}else{e=c.length;if(d&256){var h=c[e-1][a];if(null!=h){e=h;break a}}h=a+(+!!(d&512)-1);if(h<e){e=c[h];break a}}e=void 0}h=!1;if(null==e||"object"!==typeof e||(h=Array.isArray(e))||e.oa!==wc)if(h){var f=h=U(e);0===f&&(f|=d&32);f|=d&2;f!==h&&V(e,f);b=new b(e)}else b=void 0;else b=e;if(b!==e&&null!=b)a:{h=vc(d);e=d;if(d&256)h=c[c.length-1];else{if(null==
b)break a;h=c[h+(+!!(d&512)-1)]={};e|=256}h[a]=b;e!==d&&V(c,e)}a=void 0!==b}else a=a.V?a.ea(this,a.V,a.W,!0):a.ea(this,a.W,null,!0),a=void 0!==(null===a?void 0:a);return a};n.clone=function(){var a=this.G;return Hc(this,a,W(a),!1)};n.oa=wc;n.toString=function(){return Kc(this,this.G,!1).toString()};
function Kc(a,b,c){var d=a.constructor.Qa,e=vc(W(c?a.G:b)),h=!1;if(d){if(!c){b=Array.prototype.slice.call(b);var f;if(b.length&&X(f=b[b.length-1]))for(h=0;h<d.length;h++)if(d[h]>=e){Object.assign(b[b.length-1]={},f);break}h=!0}e=b;c=!c;f=W(a.G);a=vc(f);f=+!!(f&512)-1;for(var g,k,l=0;l<d.length;l++)if(k=d[l],k<a){k+=f;var m=e[k];null==m?e[k]=c?Z:sc():c&&m!==Z&&rc(m)}else g||(m=void 0,e.length&&X(m=e[e.length-1])?g=m:e.push(g={})),m=g[k],null==g[k]?g[k]=c?Z:sc():c&&m!==Z&&rc(m)}d=b.length;if(!d)return b;
var x;if(X(g=b[d-1])){a:{var q=g;e={};c=!1;for(var z in q)a=q[z],Array.isArray(a)&&a!=a&&(c=!0),null!=a?e[z]=a:c=!0;if(c){for(var Y in e){q=e;break a}q=null}}q!=g&&(x=!0);d--}for(;0<d;d--){g=b[d-1];if(null!=g)break;var O=!0}if(!x&&!O)return b;b=h?b:Array.prototype.slice.call(b,0,d);h&&(b.length=d);q&&b.push(q);return b};var Lc=function(a){this.G=zc(a)};ka(Lc,Jc);Lc.Qa=[1];new Lc;function Mc(){var a={},b=na("yt.abuse.playerAttLoaderRun");return b?b(a):Promise.reject("Att run not ready")};var Nc=function(a){a=void 0===a?{}:a;var b=void 0===a.La?!1:a.La;this.reportError=void 0===a.reportError?!0:a.reportError;this.Ma=b;this.Z=window;this.pa=this.ha=this.aa=this.h=null;this.ba=Eb()},Tc=function(a){var b=Oc(a).then(function(d){a.h=d},function(d){return H(d||13)}).then(function(){return"y"===a.h._upb_?G():Pc(a)},function(d){return H(d)}),c=a.ya();a.pa=Cb([b,c]).then(function(){if("y"!==a.h._upb_)return Qc(a)}).then(function(){return Rc()},function(d){return H(d)});return Sc(a)},Pc=function(a){var b=
a.h._bgu_.split("/").pop().replace(/\.js/g,"");b=Ua(new D(Na,"//pagead2.googlesyndication.com/bg/%{botguard}.js"),{botguard:Wa(b)});if(a.Ma){var c=document.createElement("IFRAME".toString());c.style.display="none";document.body.appendChild(c);a.Z=c.contentWindow||window}return cc(b,a.Z).then(function(){return G()},function(){return H(7)})};Nc.prototype.ya=function(){return G()};
var Qc=function(a){if(!a.Z.botguard||!a.Z.botguard.bg)return H(5);a.aa=new a.Z.botguard.bg(a.h._bgp_,function(){});return G()},Rc=function(){return new F(function(a){a()})},Vc=function(a,b,c,d){if("y"===a.h._upb_)Mc().then(function(e){if(!e)return H(6);Uc(a,e,b,c,d);a.ba.resolve()},function(e){a.ba.reject(e||6)});else{if(!a.aa||!a.aa.invoke)return H(5);a.aa.invoke(function(e){Uc(a,e,b,c,d);a.ba.resolve()},!0)}return a.ba.promise},Sc=function(a,b,c,d){return 6===d||7===d?Hb(Wc(a,b,c,d).catch(function(e){e&&
a.reportError&&Xc(a,e,b,c,d)}),function(){return void a.da()}):a.pa?Hb(a.pa.then(function(){return Vc(a,b,c,d)},function(e){return H(e)}).then(function(){return G()},function(e){e&&a.reportError&&Xc(a,e);return H(e)}),function(){return a.da()}):(a.reportError&&Xc(a,14,b,c,d),Hb(H(14),function(){return a.da()}))},Uc=function(a,b,c,d,e){5===e&&(a.ha=b);var h=a.h._scs_,f=a.h._li_,g=a.h._cv_,k=a.h._sid_,l=a.h._upb_;if("true"==a.h._isfl_){h=a.h._inj_orig_;c={id:"sodar",bg:encodeURIComponent(b),v:44,t:2,
li:f,cv:g,sid:k,sc:c,dt:d,et:e,upb:l};d=[];for(var m in c)c[m]&&d.push("&"+m+"="+c[m]);fc(window,"__FledgeSodar__"+d.join(""),3,h)}else m=ec(2),f&&P(m,"li",f),g&&P(m,"cv",g),k&&P(m,"sid",k),c&&P(m,"sc",c),d&&P(m,"dt",d),e&&P(m,"et",e),h&&P(m,"bgai",h),l&&P(m,"upb",l),P(m,"bg",b),m=m.toString(),6E4<m.length?S(4,h,f,g,k,c,void 0,d,e):jb(m)},Xc=function(a,b,c,d,e){S(b,a.h?a.h._scs_:void 0,a.h?a.h._li_:void 0,a.h?a.h._cv_:void 0,a.h?a.h._sid_:void 0,c,void 0,d,e)},Wc=function(a,b,c,d){if(!a.ha)return H(14);
Uc(a,a.ha,b,c,d);7===d&&(a.ha=null);return G()};Nc.prototype.da=function(){};var Yc=Eb(),Zc=!1,$c=null;window.onNewContextInstance=function(a){$c=a;$c.onAnalyticsEvent(function(b){if(!Zc){Zc=!0;var c=u(b.split("&"));b=c.next().value;c=c.next().value;if(b&&c){var d={};b=(d._bgu_=b,d._bgp_=c,d);Yc.resolve(b)}else Yc.reject(2)}})};var ad=function(){Nc.call(this);this.Ba=Eb();this.Ja=Eb();this.wa="*"};ka(ad,Nc);
var Oc=function(a){fb(dc(function(b){if(b.source===window.parent&&(b=b.data,b=lc(b)?b:null))switch(b["0"]){case "0":var c={};c=(c._scs_=b["1"],c._bgu_=b["2"],c._bgp_=b["3"],c._li_=b["4"],c._cv_=b["5"],c._sid_=b["9"],c._isfl_=b["13"],c._inj_orig_=b["8"],c);a.Ba.resolve(c);a.wa=b["8"];break;case "2":for(c in b)if("0"!==c){var d=$a[Number(c)];document[d.Pa]=d.transform(b[c])}a.Ja.resolve();break;case "3":c=0;d=b["11"];Object.values(cb).includes(d)&&(c=d);d=0;var e=b["12"];Object.values(db).includes(e)&&
(d=e);Sc(a,b["10"],c,d)}},"r:i"));return a.Ba.promise};ad.prototype.ya=function(){return G()};ad.prototype.da=function(){window.parent.postMessage("",this.wa)};var bd=function(){var a=[];a.push(new ad);return a},cd=function(){bd().forEach(function(a){return Tc(a)})};bb.GoogleTyFxhYEET||(bb.GoogleTyFxhYEET={});bb.GoogleTyFxhYEET[(ab.currentScript||{}).src]=cd;cd();}).call(this);
</script>
</head><body></body></html>