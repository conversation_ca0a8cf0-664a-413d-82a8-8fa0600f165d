<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.g7-interface {
    background: linear-gradient(135deg, #673ab7 0%, #512da8 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.battery-input {
    background-color: #ede7f6;
    border: 2px solid #673ab7;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.battery-result {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.battery-meter {
    background-color: #e0e0e0;
    border-radius: 20px;
    height: 30px;
    margin: 15px 0;
    overflow: hidden;
    position: relative;
}
.battery-fill {
    height: 100%;
    border-radius: 20px;
    transition: width 0.5s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}
.battery-high { background: linear-gradient(90deg, #4caf50, #8bc34a); }
.battery-medium { background: linear-gradient(90deg, #ff9800, #ffc107); }
.battery-low { background: linear-gradient(90deg, #f44336, #ff5722); }
.usage-pattern {
    background-color: #f1f8e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.transmitter-info {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
</style>

<h1>Dexcom G7 Transmitter Battery Life Calculator</h1>
<p>Calculate and track your Dexcom G7 transmitter battery life with our comprehensive battery calculator. Monitor usage patterns, predict replacement dates, and optimize your continuous glucose monitoring system performance.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="g7batteryform" id="g7-battery-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Battery Calculator</a></li>
                <li><a href="#" data-tab="tracking">Usage Tracking</a></li>
                <li><a href="#" data-tab="optimization">Optimization</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="g7-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">🔋 G7 Transmitter Battery Calculator</h3>
                
                <div class="battery-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">📱 Transmitter Information</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="transmitter-id"><strong>Transmitter ID:</strong></label>
                            <input type="text" id="transmitter-id" value="G7ABC123" maxlength="8" class="innormal" style="width: 100%; margin-top: 5px;" placeholder="e.g., G7ABC123">
                        </div>
                        
                        <div>
                            <label for="activation-date"><strong>First Activation Date:</strong></label>
                            <input type="date" id="activation-date" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="current-battery"><strong>Current Battery Level:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <input type="range" id="current-battery" min="0" max="100" value="85" class="innormal" style="width: 70%;">
                                <span id="battery-percentage" style="margin-left: 10px; font-weight: bold;">85%</span>
                            </div>
                        </div>
                        
                        <div>
                            <label for="transmitter-generation"><strong>Transmitter Type:</strong></label>
                            <select id="transmitter-generation" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="g7-standard" selected>Dexcom G7 (Standard)</option>
                                <option value="g7-extended">Dexcom G7 (Extended Life)</option>
                                <option value="g6-comparison">Dexcom G6 (Comparison)</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="usage-pattern">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">📊 Usage Patterns</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="daily-readings"><strong>Daily Readings:</strong></label>
                            <select id="daily-readings" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="light">Light (< 20/day)</option>
                                <option value="moderate" selected>Moderate (20-50/day)</option>
                                <option value="heavy">Heavy (50-100/day)</option>
                                <option value="intensive">Intensive (100+/day)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="bluetooth-usage"><strong>Bluetooth Connectivity:</strong></label>
                            <select id="bluetooth-usage" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="minimal">Minimal (Phone only)</option>
                                <option value="standard" selected>Standard (Phone + 1 device)</option>
                                <option value="multiple">Multiple (Phone + 2+ devices)</option>
                                <option value="continuous">Continuous (Always connected)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="alert-frequency"><strong>Alert Frequency:</strong></label>
                            <select id="alert-frequency" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="low">Low (Few alerts)</option>
                                <option value="normal" selected>Normal (Standard alerts)</option>
                                <option value="high">High (Many alerts)</option>
                                <option value="maximum">Maximum (All alerts on)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <label for="environmental-factors"><strong>Environmental Factors:</strong></label>
                        <div style="margin-top: 10px;">
                            <label style="margin-right: 20px;">
                                <input type="checkbox" id="extreme-temps" style="margin-right: 5px;">
                                Extreme temperatures
                            </label>
                            <label style="margin-right: 20px;">
                                <input type="checkbox" id="high-humidity" style="margin-right: 5px;">
                                High humidity
                            </label>
                            <label>
                                <input type="checkbox" id="interference" style="margin-right: 5px;">
                                Electronic interference
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Battery Life" id="calculate-battery-btn" style="padding: 15px 30px; font-size: 18px; background-color: #673ab7; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Calculator" id="clear-battery-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Battery life estimates are based on typical usage patterns. Actual battery life may vary based on individual usage and environmental conditions.
        </div>
        
        <div id="battery-results" style="display: none; margin-top: 30px;">
            <div class="battery-result">
                <h3 style="margin-bottom: 20px;">🔋 Battery Life Analysis</h3>
                <div id="battery-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-battery-btn');
    const clearBtn = document.getElementById('clear-battery-btn');
    const resultsDiv = document.getElementById('battery-results');
    const resultsContent = document.getElementById('battery-results-content');
    const batterySlider = document.getElementById('current-battery');
    const batteryPercentage = document.getElementById('battery-percentage');
    
    // Set default activation date to 30 days ago
    const defaultDate = new Date();
    defaultDate.setDate(defaultDate.getDate() - 30);
    document.getElementById('activation-date').value = defaultDate.toISOString().split('T')[0];
    
    // Update battery percentage display
    batterySlider.addEventListener('input', function() {
        batteryPercentage.textContent = this.value + '%';
    });
    
    calculateBtn.addEventListener('click', function() {
        const transmitterId = document.getElementById('transmitter-id').value || 'G7ABC123';
        const activationDate = document.getElementById('activation-date').value;
        const currentBattery = parseInt(document.getElementById('current-battery').value) || 85;
        const transmitterType = document.getElementById('transmitter-generation').value;
        const dailyReadings = document.getElementById('daily-readings').value;
        const bluetoothUsage = document.getElementById('bluetooth-usage').value;
        const alertFrequency = document.getElementById('alert-frequency').value;
        
        const environmentalFactors = {
            extremeTemps: document.getElementById('extreme-temps').checked,
            highHumidity: document.getElementById('high-humidity').checked,
            interference: document.getElementById('interference').checked
        };
        
        if (!activationDate) {
            alert('Please select the transmitter activation date.');
            return;
        }
        
        const batteryAnalysis = calculateG7BatteryLife(transmitterId, activationDate, currentBattery, 
            transmitterType, dailyReadings, bluetoothUsage, alertFrequency, environmentalFactors);
        
        displayBatteryResults(batteryAnalysis);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('g7-battery-calculator-form').reset();
        batteryPercentage.textContent = '85%';
        const defaultDate = new Date();
        defaultDate.setDate(defaultDate.getDate() - 30);
        document.getElementById('activation-date').value = defaultDate.toISOString().split('T')[0];
        resultsDiv.style.display = 'none';
    });
    
    function calculateG7BatteryLife(transmitterId, activationDate, currentBattery, transmitterType, 
                                   dailyReadings, bluetoothUsage, alertFrequency, environmentalFactors) {
        
        const activationDateTime = new Date(activationDate);
        const currentTime = new Date();
        const daysActive = Math.floor((currentTime - activationDateTime) / (1000 * 60 * 60 * 24));
        
        // Base battery life for different transmitter types (in days)
        const baseBatteryLife = {
            'g7-standard': 365,      // 12 months
            'g7-extended': 450,      // 15 months
            'g6-comparison': 90      // 3 months (for comparison)
        };
        
        const maxLife = baseBatteryLife[transmitterType] || 365;
        
        // Usage pattern multipliers
        const usageMultipliers = {
            dailyReadings: {
                light: 1.1, moderate: 1.0, heavy: 0.9, intensive: 0.8
            },
            bluetoothUsage: {
                minimal: 1.05, standard: 1.0, multiple: 0.95, continuous: 0.85
            },
            alertFrequency: {
                low: 1.02, normal: 1.0, high: 0.98, maximum: 0.95
            }
        };
        
        // Calculate adjusted battery life
        let adjustedLife = maxLife;
        adjustedLife *= usageMultipliers.dailyReadings[dailyReadings] || 1.0;
        adjustedLife *= usageMultipliers.bluetoothUsage[bluetoothUsage] || 1.0;
        adjustedLife *= usageMultipliers.alertFrequency[alertFrequency] || 1.0;
        
        // Environmental factor adjustments
        if (environmentalFactors.extremeTemps) adjustedLife *= 0.95;
        if (environmentalFactors.highHumidity) adjustedLife *= 0.98;
        if (environmentalFactors.interference) adjustedLife *= 0.97;
        
        adjustedLife = Math.round(adjustedLife);
        
        // Calculate expected vs actual battery drain
        const expectedBatteryAtCurrentDay = Math.max(0, 100 - (daysActive / adjustedLife * 100));
        const batteryDrainRate = (100 - currentBattery) / daysActive;
        const projectedLife = currentBattery / batteryDrainRate;
        
        // Calculate remaining life
        const remainingDays = Math.max(0, Math.round(projectedLife));
        const replacementDate = new Date(currentTime.getTime() + (remainingDays * 24 * 60 * 60 * 1000));
        
        // Battery health assessment
        let batteryHealth = 'Good';
        let healthColor = '#4caf50';
        
        if (currentBattery < expectedBatteryAtCurrentDay - 10) {
            batteryHealth = 'Below Expected';
            healthColor = '#ff9800';
        } else if (currentBattery < expectedBatteryAtCurrentDay - 20) {
            batteryHealth = 'Poor';
            healthColor = '#f44336';
        } else if (currentBattery > expectedBatteryAtCurrentDay + 10) {
            batteryHealth = 'Excellent';
            healthColor = '#4caf50';
        }
        
        return {
            transmitterId: transmitterId,
            transmitterType: transmitterType,
            activationDate: activationDateTime,
            daysActive: daysActive,
            currentBattery: currentBattery,
            expectedBattery: Math.round(expectedBatteryAtCurrentDay),
            maxLife: maxLife,
            adjustedLife: adjustedLife,
            remainingDays: remainingDays,
            replacementDate: replacementDate,
            batteryHealth: batteryHealth,
            healthColor: healthColor,
            usageFactors: {
                dailyReadings: dailyReadings,
                bluetoothUsage: bluetoothUsage,
                alertFrequency: alertFrequency,
                environmentalFactors: environmentalFactors
            }
        };
    }
    
    function displayBatteryResults(data) {
        const batteryClass = data.currentBattery >= 70 ? 'battery-high' : 
                           data.currentBattery >= 30 ? 'battery-medium' : 'battery-low';
        
        const typeNames = {
            'g7-standard': 'Dexcom G7 Standard',
            'g7-extended': 'Dexcom G7 Extended',
            'g6-comparison': 'Dexcom G6 (Comparison)'
        };
        
        let html = `
            <div style="margin-bottom: 25px;">
                <div style="font-size: 48px; font-weight: bold; margin-bottom: 10px;">
                    ${data.currentBattery}%
                </div>
                <div class="battery-meter">
                    <div class="battery-fill ${batteryClass}" style="width: ${data.currentBattery}%;">
                        ${data.currentBattery}%
                    </div>
                </div>
                <div style="font-size: 16px; margin-top: 10px;">
                    Transmitter: ${data.transmitterId}
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Days Active</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #673ab7;">
                        ${data.daysActive}
                    </div>
                    <div style="font-size: 12px; color: #666;">Since ${data.activationDate.toLocaleDateString()}</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Estimated Remaining</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #4caf50;">
                        ${data.remainingDays}
                    </div>
                    <div style="font-size: 12px; color: #666;">days</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Replacement Date</h4>
                    <div style="font-size: 18px; font-weight: bold; color: #ff9800;">
                        ${data.replacementDate.toLocaleDateString()}
                    </div>
                    <div style="font-size: 12px; color: #666;">Estimated</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Battery Health</h4>
                    <div style="font-size: 18px; font-weight: bold;" style="color: ${data.healthColor};">
                        ${data.batteryHealth}
                    </div>
                    <div style="font-size: 12px; color: #666;">vs Expected: ${data.expectedBattery}%</div>
                </div>
            </div>
            
            <div class="transmitter-info">
                <h4 style="color: #673ab7; margin-bottom: 20px;">📊 Detailed Analysis</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h5>Transmitter Information:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Type:</strong> ${typeNames[data.transmitterType]}</li>
                            <li><strong>Max Life:</strong> ${data.maxLife} days (${Math.round(data.maxLife/30)} months)</li>
                            <li><strong>Adjusted Life:</strong> ${data.adjustedLife} days</li>
                            <li><strong>Current Age:</strong> ${((data.daysActive / data.maxLife) * 100).toFixed(1)}% of max life</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5>Usage Factors:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Daily Readings:</strong> ${data.usageFactors.dailyReadings}</li>
                            <li><strong>Bluetooth Usage:</strong> ${data.usageFactors.bluetoothUsage}</li>
                            <li><strong>Alert Frequency:</strong> ${data.usageFactors.alertFrequency}</li>
                            <li><strong>Environmental Impact:</strong> ${Object.values(data.usageFactors.environmentalFactors).some(v => v) ? 'Yes' : 'None'}</li>
                        </ul>
                    </div>
                </div>
                
                <div style="margin-top: 20px; padding: 15px; background-color: ${data.currentBattery < 20 ? '#ffebee' : '#f5f5f5'}; border-radius: 8px;">
                    <h5 style="color: ${data.currentBattery < 20 ? '#f44336' : '#2c3e50'};">
                        ${data.currentBattery < 20 ? '⚠️ Low Battery Warning' : '💡 Battery Status'}
                    </h5>
                    <p style="margin: 10px 0; color: #2c3e50;">
                        ${data.currentBattery < 20 ? 
                          'Your transmitter battery is critically low. Consider ordering a replacement soon.' :
                          data.currentBattery < 50 ?
                          'Your transmitter battery is getting low. Plan for replacement in the coming weeks.' :
                          'Your transmitter battery is in good condition. Continue monitoring regularly.'}
                    </p>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 Battery Optimization Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Reduce Readings:</strong> Limit unnecessary glucose checks to extend battery life</li>
                    <li><strong>Bluetooth Management:</strong> Disconnect unused devices from transmitter</li>
                    <li><strong>Alert Settings:</strong> Customize alerts to reduce unnecessary notifications</li>
                    <li><strong>Temperature:</strong> Avoid extreme temperatures that can drain battery faster</li>
                    <li><strong>Interference:</strong> Keep away from strong electromagnetic fields</li>
                    <li><strong>Replacement Planning:</strong> Order new transmitter when battery drops below 30%</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
