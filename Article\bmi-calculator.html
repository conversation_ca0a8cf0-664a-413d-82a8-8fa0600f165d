<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>BMI Calculator</title>
    <meta name="description" content="Calculate your Body Mass Index (BMI) to determine if you're underweight, normal weight, overweight, or obese. Free BMI calculator with detailed health analysis.">
    <link rel="stylesheet" href="./Carbohydrate Calculator_files/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="https://d26tpo4cm8sb6k.cloudfront.net/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-16x16.png">
    <link rel="manifest" href="https://www.calculator.net/manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="https://www.calculator.net/"><img src="./Carbohydrate Calculator_files/calculator-white.svg" width="208" height="22" alt="Calculator.net"></a></div>
        <div id="login"><a href="https://www.calculator.net/my-account/sign-in.php">sign in</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/" itemprop="item"><span itemprop="name">home</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/fitness-and-health-calculator.html" itemprop="item"><span itemprop="name">fitness &amp; health</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="#" itemprop="item"><span itemprop="name">bmi calculator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="window.print();return false;">Print</a></div>
        
        <h1>BMI Calculator</h1>
        <p>The <i>BMI Calculator</i> finds the body mass index given weight and height. Use the metric units tab for the International System of Units or the other units tab to convert units into either US or metric units.</p>
        
        <div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
        <span id="ucframe"></span>
        
        <div id="topmenu" class="topmenucenter">
            <ul>
                <li><a href="#" onclick="return showUnits('standard');">US Units</a></li>
                <li id="menuon"><a href="#" onclick="return showUnits('metric');">Metric Units</a></li>
                <li><a href="#" onclick="return showUnits('other');">Other Units</a></li>
            </ul>
        </div>

        <div class="panel2" style="padding:10px;">
            <form name="bmiForm" id="bmi-calculator-form">
                <table id="bmi-input-table" width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">Age</td>
                            <td width="550"><input type="number" name="age" id="age" value="25" class="inlongesthalf" min="2" max="120"> ages 2 - 120</td>
                        </tr>
                        <tr>
                            <td>Gender</td>
                            <td>
                                <label for="male" class="cbcontainer"><input type="radio" name="gender" id="male" value="male" checked=""><span class="rbmark"></span>male</label> &nbsp; 
                                <label for="female" class="cbcontainer"><input type="radio" name="gender" id="female" value="female"><span class="rbmark"></span>female</label>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- US Units -->
                <table width="620" id="standard-units" bgcolor="#eeeeee" style="display: none;">
                    <tbody>
                        <tr>
                            <td width="70">Height</td>
                            <td width="550">
                                <input type="number" name="feet" id="feet" value="5" class="inlongesthalf inuifoot" min="0" max="8"><span class="inuifootspan">feet</span>
                                &nbsp;&nbsp;
                                <input type="number" name="inches" id="inches" value="10" class="inlongesthalf inuiinch" min="0" max="11"><span class="inuiinchspan">inches</span>
                            </td>
                        </tr>
                        <tr>
                            <td>Weight</td>
                            <td><input type="number" name="pounds" id="pounds" value="160" class="inlongest inuipound" min="1" max="1000"><span class="inuipoundspan">pounds</span></td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- Metric Units -->
                <table width="620" id="metric-units" bgcolor="#eeeeee" style="display: block;">
                    <tbody>
                        <tr>
                            <td width="70">Height</td>
                            <td width="550"><input type="number" name="height_cm" id="height_cm" value="180" class="inlongest inuick" min="30" max="300" step="0.1"><span class="inuickspan">cm</span></td>
                        </tr>
                        <tr>
                            <td>Weight</td>
                            <td><input type="number" name="weight_kg" id="weight_kg" value="70" class="inlongest inuick" min="1" max="500" step="0.1"><span class="inuickspan">kg</span></td>
                        </tr>
                    </tbody>
                </table>
                
                <table width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">&nbsp;</td>
                            <td width="550">
                                <input type="submit" name="x" value="Calculate BMI" id="calculate-btn">
                                <input type="button" value="Clear" id="clear-btn" onclick="clearBMIForm();">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </form>
        </div>

        <ul style="margin-top:0px;margin-left:-20px;">
            <li class="smalltext"><b>BMI:</b> Body Mass Index is a measure of body fat based on height and weight.</li>
            <li class="smalltext"><b>Healthy BMI range:</b> 18.5 kg/m² to 25 kg/m² for most adults.</li>
            <li class="smalltext"><b>Limitations:</b> BMI may not be accurate for athletes, elderly, or children.</li>
        </ul>

        <!-- Results Display Area -->
        <div id="bmi-results" style="display: none; margin-top: 20px;">
            <div class="h2result" id="bmi-results-content"></div>
        </div>

        <br>
        <fieldset>
            <legend>Related</legend>
            <a href="#">Body Fat Calculator</a> | 
            <a href="#">Ideal Weight Calculator</a> | 
            <a href="#">Calorie Calculator</a>
        </fieldset>

        <br>
        <h3>What is BMI?</h3>
        <p>Body Mass Index (BMI) is a person's weight in kilograms divided by the square of height in meters. A high BMI can indicate high body fatness. BMI screens for weight categories that may lead to health problems, but it does not diagnose the body fatness or health of an individual.</p>

        <h3>BMI Categories</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">BMI</td>
                    <td class="cinfoHdL">Weight Status</td>
                    <td class="cinfoHdL">Health Risk</td>
                </tr>
                <tr><td><strong>Below 18.5</strong></td><td>Underweight</td><td>Increased</td></tr>
                <tr><td><strong>18.5 - 24.9</strong></td><td>Normal weight</td><td>Least</td></tr>
                <tr><td><strong>25.0 - 29.9</strong></td><td>Overweight</td><td>Increased</td></tr>
                <tr><td><strong>30.0 - 34.9</strong></td><td>Obesity Class I</td><td>High</td></tr>
                <tr><td><strong>35.0 - 39.9</strong></td><td>Obesity Class II</td><td>Very High</td></tr>
                <tr><td><strong>40.0 and Above</strong></td><td>Obesity Class III</td><td>Extremely High</td></tr>
            </tbody>
        </table>

        <h3>BMI Limitations</h3>
        <p>BMI is a useful indicator of health at the population level. However, the distribution of muscle and bone mass can vary between individuals of the same BMI. BMI also does not distinguish between muscle and fat mass. As a result, some people, such as heavily muscled athletes, may have a high BMI even though they don't have a high percentage of body fat. In others, such as elderly people, BMI may appear normal even though muscle has been lost with age.</p>

        <h3>Healthy Weight Tips</h3>
        <ul>
            <li>Maintain a balanced diet with plenty of fruits and vegetables</li>
            <li>Engage in regular physical activity (at least 150 minutes per week)</li>
            <li>Stay hydrated by drinking plenty of water</li>
            <li>Get adequate sleep (7-9 hours per night)</li>
            <li>Manage stress through relaxation techniques</li>
            <li>Consult healthcare professionals for personalized advice</li>
        </ul>
    </div>
    
    <div id="right">
        <div style="padding-top:10px; min-height:280px; text-align:center;">
            <div style="width: 300px; height: 250px; background: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; color: #666;">
                Advertisement Space
            </div>
        </div>
        
        <form name="calcSearchForm" onsubmit="return false;" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td><input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators..."></td>
                        <td><span id="bluebtn" onclick="performSearch();">Search</span></td>
                    </tr>
                    <tr>
                        <td colspan="2"><div id="calcSearchOut"></div></td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="#">Fitness and Health Calculators</a>
            </div>
            <div id="occontent">
                <a href="#">BMI Calculator</a>
                <a href="#">Body Fat Calculator</a>
                <a href="#">Calorie Calculator</a>
                <a href="#">BMR Calculator</a>
                <a href="#">Ideal Weight Calculator</a>
                <a href="#">Macro Calculator</a>
                <a href="#">Pace Calculator</a>
                <a href="#">Pregnancy Calculator</a>
                <a href="#">Heart Rate Calculator</a>
                <a href="#">More Fitness Calculators</a>
            </div>
            <div id="ocother">
                <a href="#">Financial</a> | 
                <a href="#">Fitness and Health</a> | 
                <a href="#">Math</a> | 
                <a href="#">Other</a>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="#">about us</a> | 
            <a href="#">sitemap</a> | 
            <a href="#">terms of use</a> | 
            <a href="#">privacy policy</a> &nbsp;  © 2008 - 2025 <a href="#">calculator.net</a>
        </div>
    </div>
</div>

<div class="topNavAbs">
    <a href="#">Financial</a>
    <a href="#" class="topNavOn">Fitness &amp; Health</a>
    <a href="#">Math</a>
    <a href="#">Other</a>
</div>

<script>
let currentUnits = 'metric';

function showUnits(unitType) {
    // Hide all unit tables
    document.getElementById('standard-units').style.display = 'none';
    document.getElementById('metric-units').style.display = 'none';
    
    // Show selected unit table
    if (unitType === 'standard') {
        document.getElementById('standard-units').style.display = 'block';
    } else if (unitType === 'metric') {
        document.getElementById('metric-units').style.display = 'block';
    }
    
    // Update menu
    const menuItems = document.querySelectorAll('#topmenu ul li');
    menuItems.forEach(item => {
        item.id = '';
        const link = item.querySelector('a');
        if (link && link.textContent.toLowerCase().includes(unitType === 'standard' ? 'us' : unitType)) {
            item.id = 'menuon';
        }
    });
    
    currentUnits = unitType;
    return false;
}

// BMI Calculator
document.getElementById('calculate-btn').addEventListener('click', function(e) {
    e.preventDefault();
    calculateBMI();
});

function calculateBMI() {
    let height, weight;
    
    if (currentUnits === 'metric') {
        height = parseFloat(document.getElementById('height_cm').value) / 100; // Convert cm to meters
        weight = parseFloat(document.getElementById('weight_kg').value);
    } else {
        const feet = parseFloat(document.getElementById('feet').value) || 0;
        const inches = parseFloat(document.getElementById('inches').value) || 0;
        const pounds = parseFloat(document.getElementById('pounds').value) || 0;
        
        height = ((feet * 12) + inches) * 0.0254; // Convert to meters
        weight = pounds * 0.453592; // Convert to kg
    }
    
    if (height <= 0 || weight <= 0) {
        alert('Please enter valid height and weight values');
        return;
    }
    
    const bmi = weight / (height * height);
    const category = getBMICategory(bmi);
    const healthRisk = getHealthRisk(bmi);
    
    const resultsDiv = document.getElementById('bmi-results-content');
    resultsDiv.innerHTML = `
        Your BMI is ${bmi.toFixed(1)} kg/m² - ${category}<br>
        Health Risk: ${healthRisk}<br>
        ${getBMIAdvice(bmi)}
    `;
    document.getElementById('bmi-results').style.display = 'block';
}

function getBMICategory(bmi) {
    if (bmi < 18.5) return 'Underweight';
    if (bmi < 25) return 'Normal weight';
    if (bmi < 30) return 'Overweight';
    if (bmi < 35) return 'Obesity Class I';
    if (bmi < 40) return 'Obesity Class II';
    return 'Obesity Class III';
}

function getHealthRisk(bmi) {
    if (bmi < 18.5 || bmi >= 25) return 'Increased';
    return 'Least';
}

function getBMIAdvice(bmi) {
    if (bmi < 18.5) {
        return 'Consider consulting a healthcare provider about healthy weight gain strategies.';
    } else if (bmi >= 18.5 && bmi < 25) {
        return 'Maintain your current weight through healthy diet and regular exercise.';
    } else if (bmi >= 25 && bmi < 30) {
        return 'Consider lifestyle changes to achieve a healthier weight.';
    } else {
        return 'Consult with a healthcare provider about weight management options.';
    }
}

function clearBMIForm() {
    document.getElementById('age').value = '25';
    document.getElementById('male').checked = true;
    
    if (currentUnits === 'metric') {
        document.getElementById('height_cm').value = '180';
        document.getElementById('weight_kg').value = '70';
    } else {
        document.getElementById('feet').value = '5';
        document.getElementById('inches').value = '10';
        document.getElementById('pounds').value = '160';
    }
    
    document.getElementById('bmi-results').style.display = 'none';
}

function performSearch() {
    const searchTerm = document.getElementById('calcSearchTerm').value;
    const searchOut = document.getElementById('calcSearchOut');
    
    if (searchTerm.trim() === '') {
        searchOut.innerHTML = '';
        return;
    }
    
    const calculators = [
        'BMI Calculator', 'Body Fat Calculator', 'Calorie Calculator', 
        'BMR Calculator', 'Ideal Weight Calculator', 'Macro Calculator'
    ];
    
    const results = calculators.filter(calc => 
        calc.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    if (results.length > 0) {
        searchOut.innerHTML = '<div>' + results.map(result => 
            `<a href="#" style="display: block; padding: 2px 0;">${result}</a>`
        ).join('') + '</div>';
    } else {
        searchOut.innerHTML = '<div>No calculators found</div>';
    }
}

// Initialize
showUnits('metric');
</script>

</body>
</html>
