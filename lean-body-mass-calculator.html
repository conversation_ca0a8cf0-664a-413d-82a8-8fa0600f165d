<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Lean Body Mass Calculator</title>
    <meta name="description" content="Calculate your lean body mass using multiple formulas including <PERSON>, <PERSON>, <PERSON>, and <PERSON> equations. Free LBM calculator with detailed analysis.">
    <link rel="stylesheet" href="./Carbohydrate Calculator_files/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Mobile responsive styles */
        @media screen and (max-width: 768px) {
            #contentout { width: 100% !important; padding: 0 10px; }
            #content { width: 100% !important; margin: 0; }
            #right { display: none; }
            .panel2 { width: 100% !important; padding: 5px !important; }
            table { width: 100% !important; font-size: 14px; }
            td { padding: 8px 4px !important; word-wrap: break-word; }
            input, select { width: 100% !important; max-width: 100% !important; box-sizing: border-box; font-size: 16px; }
            .inlongest, .innormal, .inlongesthalf { width: 100% !important; }
            h1 { font-size: 24px; }
            .cinfoT { font-size: 12px; overflow-x: auto; }
            .cinfoT td { padding: 4px 2px !important; }
            .cbcontainer { font-size: 12px; margin: 2px 0; }
            #topmenu ul li { display: inline-block; margin: 2px; font-size: 12px; }
        }
        @media screen and (max-width: 480px) {
            table td:first-child { width: 30% !important; }
            table td:last-child { width: 70% !important; }
            input[type="submit"], input[type="button"] {
                width: 100% !important; margin: 5px 0 !important; padding: 12px !important; font-size: 14px;
            }
            .inuifoot, .inuiinch { width: 45% !important; display: inline-block; }
            .cbcontainer { display: block; margin: 3px 0; }
        }
    </style>
    <link rel="apple-touch-icon" sizes="180x180" href="https://d26tpo4cm8sb6k.cloudfront.net/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-16x16.png">
    <link rel="manifest" href="https://www.calculator.net/manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="https://www.calculator.net/"><img src="./Carbohydrate Calculator_files/calculator-white.svg" width="208" height="22" alt="Calculator.net"></a></div>
        <div id="login"><a href="https://www.calculator.net/my-account/sign-in.php">sign in</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/" itemprop="item"><span itemprop="name">home</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/fitness-and-health-calculator.html" itemprop="item"><span itemprop="name">fitness &amp; health</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="#" itemprop="item"><span itemprop="name">lean body mass calculator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="window.print();return false;">Print</a></div>
        
        <h1>Lean Body Mass Calculator</h1>
        <p>The <i>Lean Body Mass Calculator</i> computes a person's estimated lean body mass (LBM) based on body weight, height, gender, and age. Lean body mass equals body weight minus body fat weight, or more simply, the weight of everything except body fat. The formulas used are based on scientific research and provide estimates for comparison.</p>
        
        <div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
        <span id="ucframe"></span>
        
        <div id="topmenu" class="topmenucenter">
            <ul>
                <li><a href="#" onclick="return showUnits('standard');">US Units</a></li>
                <li id="menuon"><a href="#" onclick="return showUnits('metric');">Metric Units</a></li>
                <li><a href="#" onclick="return showUnits('other');">Other Units</a></li>
            </ul>
        </div>

        <div class="panel2" style="padding:10px;">
            <form name="leanBodyMassForm" id="lbm-calculator-form">
                <table id="lbm-input-table" width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">Age</td>
                            <td width="550"><input type="number" name="age" id="age" value="25" class="inlongesthalf" min="18" max="80"> ages 18 - 80</td>
                        </tr>
                        <tr>
                            <td>Gender</td>
                            <td>
                                <label for="male" class="cbcontainer"><input type="radio" name="gender" id="male" value="male" checked=""><span class="rbmark"></span>male</label> &nbsp; 
                                <label for="female" class="cbcontainer"><input type="radio" name="gender" id="female" value="female"><span class="rbmark"></span>female</label>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- US Units -->
                <table width="620" id="standard-units" bgcolor="#eeeeee" style="display: none;">
                    <tbody>
                        <tr>
                            <td width="70">Height</td>
                            <td width="550">
                                <input type="number" name="feet" id="feet" value="5" class="inlongesthalf inuifoot" min="0" max="8"><span class="inuifootspan">feet</span>
                                &nbsp;&nbsp;
                                <input type="number" name="inches" id="inches" value="10" class="inlongesthalf inuiinch" min="0" max="11"><span class="inuiinchspan">inches</span>
                            </td>
                        </tr>
                        <tr>
                            <td>Weight</td>
                            <td><input type="number" name="pounds" id="pounds" value="160" class="inlongest inuipound" min="50" max="1000"><span class="inuipoundspan">pounds</span></td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- Metric Units -->
                <table width="620" id="metric-units" bgcolor="#eeeeee" style="display: block;">
                    <tbody>
                        <tr>
                            <td width="70">Height</td>
                            <td width="550"><input type="number" name="height_cm" id="height_cm" value="180" class="inlongest inuick" min="100" max="250" step="0.1"><span class="inuickspan">cm</span></td>
                        </tr>
                        <tr>
                            <td>Weight</td>
                            <td><input type="number" name="weight_kg" id="weight_kg" value="70" class="inlongest inuick" min="20" max="300" step="0.1"><span class="inuickspan">kg</span></td>
                        </tr>
                    </tbody>
                </table>
                
                <table width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">Body Fat %</td>
                            <td width="550">
                                <input type="number" name="bodyfat" id="bodyfat" value="" class="innormal" min="3" max="50" step="0.1" placeholder="Optional">
                                <span class="smalltext">(Optional - if known)</span>
                            </td>
                        </tr>
                        <tr>
                            <td width="70">&nbsp;</td>
                            <td width="550">
                                <input type="submit" name="x" value="Calculate Lean Body Mass" id="calculate-btn">
                                <input type="button" value="Clear" id="clear-btn" onclick="clearLBMForm();">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </form>
        </div>

        <ul style="margin-top:0px;margin-left:-20px;">
            <li class="smalltext"><b>LBM:</b> Weight of everything in your body except fat (muscle, bone, organs, water).</li>
            <li class="smalltext"><b>Multiple formulas:</b> Different equations provide estimates for comparison.</li>
            <li class="smalltext"><b>Body fat %:</b> If known, provides more accurate LBM calculation.</li>
        </ul>

        <!-- Results Display Area -->
        <div id="lbm-results" style="display: none; margin-top: 20px;">
            <div class="h2result" id="lbm-results-content"></div>
        </div>

        <br>
        <fieldset>
            <legend>Related</legend>
            <a href="#">Body Fat Calculator</a> | 
            <a href="#">BMI Calculator</a> | 
            <a href="#">BMR Calculator</a>
        </fieldset>

        <br>
        <h3>What is Lean Body Mass?</h3>
        <p>Lean body mass (LBM) is the weight of your body minus all the weight due to your fat mass. LBM includes the weight of your organs, skin, bones, body water, and muscles. Lean body mass usually ranges between 60-90% of body weight. Generally, men have a higher LBM than women. Athletes typically have a higher LBM than non-athletes.</p>

        <h3>Lean Body Mass Formulas</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Formula</td>
                    <td class="cinfoHdL">Men</td>
                    <td class="cinfoHdL">Women</td>
                    <td class="cinfoHdL">Notes</td>
                </tr>
                <tr>
                    <td><strong>Boer (1984)</strong></td>
                    <td>0.407×W + 0.267×H - 19.2</td>
                    <td>0.252×W + 0.473×H - 48.3</td>
                    <td>Most widely used formula</td>
                </tr>
                <tr>
                    <td><strong>James (1976)</strong></td>
                    <td>1.1×W - 128×(W/H)²</td>
                    <td>1.07×W - 148×(W/H)²</td>
                    <td>Based on BMI relationship</td>
                </tr>
                <tr>
                    <td><strong>Hume (1966)</strong></td>
                    <td>0.32810×W + 0.33929×H - 29.5336</td>
                    <td>0.29569×W + 0.41813×H - 43.2933</td>
                    <td>Early LBM formula</td>
                </tr>
                <tr>
                    <td><strong>Peters (2011)</strong></td>
                    <td colspan="2">3.8×(0.0215×W^0.6469×H^0.7236)</td>
                    <td>Recent allometric formula</td>
                </tr>
                <tr>
                    <td><strong>Body Fat Method</strong></td>
                    <td colspan="2">Weight × (1 - Body Fat %/100)</td>
                    <td>Most accurate if BF% known</td>
                </tr>
            </tbody>
        </table>
        <p class="smalltext">W = Weight (kg), H = Height (cm)</p>

        <h3>Factors Affecting Lean Body Mass</h3>
        <ul>
            <li><strong>Age:</strong> LBM typically decreases with age due to muscle loss</li>
            <li><strong>Gender:</strong> Men generally have higher LBM due to more muscle mass</li>
            <li><strong>Physical Activity:</strong> Regular exercise, especially resistance training, increases LBM</li>
            <li><strong>Nutrition:</strong> Adequate protein intake helps maintain muscle mass</li>
            <li><strong>Genetics:</strong> Natural body composition varies between individuals</li>
            <li><strong>Hormones:</strong> Testosterone and growth hormone affect muscle development</li>
        </ul>

        <h3>Importance of Lean Body Mass</h3>
        <ul>
            <li><strong>Metabolic Rate:</strong> Higher LBM increases resting metabolic rate</li>
            <li><strong>Strength and Function:</strong> More muscle mass improves physical performance</li>
            <li><strong>Health Benefits:</strong> Higher LBM associated with better health outcomes</li>
            <li><strong>Aging:</strong> Maintaining LBM helps prevent age-related decline</li>
            <li><strong>Weight Management:</strong> Muscle tissue burns more calories than fat</li>
        </ul>

        <h3>How to Increase Lean Body Mass</h3>
        <ul>
            <li>Engage in regular resistance training exercises</li>
            <li>Consume adequate protein (0.8-1.2g per kg body weight)</li>
            <li>Include compound exercises like squats, deadlifts, and pull-ups</li>
            <li>Get sufficient sleep for muscle recovery and growth</li>
            <li>Stay hydrated and maintain proper nutrition</li>
            <li>Consider progressive overload in your training program</li>
            <li>Allow adequate rest between intense training sessions</li>
        </ul>

        <h3>LBM vs. Fat-Free Mass</h3>
        <p>While often used interchangeably, lean body mass and fat-free mass are slightly different. Fat-free mass includes all body weight except fat, while lean body mass typically includes a small amount of essential fat. For practical purposes, the terms are often considered equivalent in fitness and health contexts.</p>
    </div>
    
    <div id="right">
        <div style="padding-top:10px; min-height:280px; text-align:center;">
            <div style="width: 300px; height: 250px; background: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; color: #666;">
                Advertisement Space
            </div>
        </div>
        
        <form name="calcSearchForm" onsubmit="return false;" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td><input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators..."></td>
                        <td><span id="bluebtn" onclick="performSearch();">Search</span></td>
                    </tr>
                    <tr>
                        <td colspan="2"><div id="calcSearchOut"></div></td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="#">Fitness and Health Calculators</a>
            </div>
            <div id="occontent">
                <a href="#">BMI Calculator</a>
                <a href="#">Body Fat Calculator</a>
                <a href="#">Calorie Calculator</a>
                <a href="#">BMR Calculator</a>
                <a href="#">Ideal Weight Calculator</a>
                <a href="#">Macro Calculator</a>
                <a href="#">Pace Calculator</a>
                <a href="#">Pregnancy Calculator</a>
                <a href="#">Heart Rate Calculator</a>
                <a href="#">More Fitness Calculators</a>
            </div>
            <div id="ocother">
                <a href="#">Financial</a> | 
                <a href="#">Fitness and Health</a> | 
                <a href="#">Math</a> | 
                <a href="#">Other</a>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="#">about us</a> | 
            <a href="#">sitemap</a> | 
            <a href="#">terms of use</a> | 
            <a href="#">privacy policy</a> &nbsp;  © 2008 - 2025 <a href="#">calculator.net</a>
        </div>
    </div>
</div>

<div class="topNavAbs">
    <a href="#">Financial</a>
    <a href="#" class="topNavOn">Fitness &amp; Health</a>
    <a href="#">Math</a>
    <a href="#">Other</a>
</div>

<script>
let currentUnits = 'metric';

function showUnits(unitType) {
    document.getElementById('standard-units').style.display = 'none';
    document.getElementById('metric-units').style.display = 'none';
    
    if (unitType === 'standard') {
        document.getElementById('standard-units').style.display = 'block';
    } else if (unitType === 'metric') {
        document.getElementById('metric-units').style.display = 'block';
    }
    
    const menuItems = document.querySelectorAll('#topmenu ul li');
    menuItems.forEach(item => {
        item.id = '';
        const link = item.querySelector('a');
        if (link && link.textContent.toLowerCase().includes(unitType === 'standard' ? 'us' : unitType)) {
            item.id = 'menuon';
        }
    });
    
    currentUnits = unitType;
    return false;
}

document.getElementById('calculate-btn').addEventListener('click', function(e) {
    e.preventDefault();
    calculateLBM();
});

function calculateLBM() {
    const age = parseInt(document.getElementById('age').value);
    const gender = document.querySelector('input[name="gender"]:checked').value;
    const bodyFatPercent = parseFloat(document.getElementById('bodyfat').value);
    
    let height, weight;
    
    if (currentUnits === 'metric') {
        height = parseFloat(document.getElementById('height_cm').value);
        weight = parseFloat(document.getElementById('weight_kg').value);
    } else {
        const feet = parseFloat(document.getElementById('feet').value) || 0;
        const inches = parseFloat(document.getElementById('inches').value) || 0;
        const pounds = parseFloat(document.getElementById('pounds').value) || 0;
        
        height = ((feet * 12) + inches) * 2.54; // Convert to cm
        weight = pounds * 0.453592; // Convert to kg
    }
    
    if (age <= 0 || height <= 0 || weight <= 0) {
        alert('Please enter valid values for age, height, and weight');
        return;
    }
    
    // Calculate LBM using different formulas
    const results = calculateAllLBMFormulas(weight, height, gender, bodyFatPercent);
    
    // Display results
    displayLBMResults(results, weight, bodyFatPercent);
}

function calculateAllLBMFormulas(weight, height, gender, bodyFatPercent) {
    const results = {};
    
    // Boer Formula (1984)
    if (gender === 'male') {
        results.boer = (0.407 * weight) + (0.267 * height) - 19.2;
    } else {
        results.boer = (0.252 * weight) + (0.473 * height) - 48.3;
    }
    
    // James Formula (1976)
    const bmi = weight / ((height / 100) * (height / 100));
    if (gender === 'male') {
        results.james = (1.1 * weight) - (128 * (weight / (height * height)) * 10000);
    } else {
        results.james = (1.07 * weight) - (148 * (weight / (height * height)) * 10000);
    }
    
    // Hume Formula (1966)
    if (gender === 'male') {
        results.hume = (0.32810 * weight) + (0.33929 * height) - 29.5336;
    } else {
        results.hume = (0.29569 * weight) + (0.41813 * height) - 43.2933;
    }
    
    // Peters Formula (2011) - Allometric
    results.peters = 3.8 * (0.0215 * Math.pow(weight, 0.6469) * Math.pow(height, 0.7236));
    
    // Body Fat Method (if body fat percentage is provided)
    if (bodyFatPercent && bodyFatPercent > 0) {
        results.bodyFat = weight * (1 - bodyFatPercent / 100);
    }
    
    return results;
}

function displayLBMResults(results, totalWeight, bodyFatPercent) {
    let resultsTable = '<table class="cinfoT" style="margin-top: 10px;"><tbody>';
    resultsTable += '<tr><td class="cinfoHd">Formula</td><td class="cinfoHd">Lean Body Mass</td><td class="cinfoHd">Fat Mass</td></tr>';
    
    resultsTable += `<tr><td><strong>Boer</strong></td><td>${results.boer.toFixed(1)} kg</td><td>${(totalWeight - results.boer).toFixed(1)} kg</td></tr>`;
    resultsTable += `<tr><td><strong>James</strong></td><td>${results.james.toFixed(1)} kg</td><td>${(totalWeight - results.james).toFixed(1)} kg</td></tr>`;
    resultsTable += `<tr><td><strong>Hume</strong></td><td>${results.hume.toFixed(1)} kg</td><td>${(totalWeight - results.hume).toFixed(1)} kg</td></tr>`;
    resultsTable += `<tr><td><strong>Peters</strong></td><td>${results.peters.toFixed(1)} kg</td><td>${(totalWeight - results.peters).toFixed(1)} kg</td></tr>`;
    
    if (results.bodyFat) {
        resultsTable += `<tr><td><strong>Body Fat Method</strong></td><td>${results.bodyFat.toFixed(1)} kg</td><td>${(totalWeight - results.bodyFat).toFixed(1)} kg</td></tr>`;
    }
    
    resultsTable += '</tbody></table>';
    
    // Calculate average of the four main formulas
    const average = (results.boer + results.james + results.hume + results.peters) / 4;
    const averageFatMass = totalWeight - average;
    const averageBodyFatPercent = (averageFatMass / totalWeight) * 100;
    
    let recommendedLBM = average;
    let recommendedMethod = 'Average of Formulas';
    
    if (results.bodyFat) {
        recommendedLBM = results.bodyFat;
        recommendedMethod = 'Body Fat Method (Most Accurate)';
    }
    
    const resultsDiv = document.getElementById('lbm-results-content');
    resultsDiv.innerHTML = `
        <strong>Lean Body Mass Calculations:</strong><br>
        ${resultsTable}<br>
        <strong>Recommended LBM:</strong> ${recommendedLBM.toFixed(1)} kg (${recommendedMethod})<br>
        <strong>Average Formula Result:</strong> ${average.toFixed(1)} kg<br>
        <strong>Estimated Body Fat:</strong> ${averageBodyFatPercent.toFixed(1)}%<br>
        <strong>LBM Percentage:</strong> ${((recommendedLBM / totalWeight) * 100).toFixed(1)}% of total body weight
    `;
    document.getElementById('lbm-results').style.display = 'block';
}

function clearLBMForm() {
    document.getElementById('age').value = '25';
    document.getElementById('male').checked = true;
    document.getElementById('bodyfat').value = '';
    
    if (currentUnits === 'metric') {
        document.getElementById('height_cm').value = '180';
        document.getElementById('weight_kg').value = '70';
    } else {
        document.getElementById('feet').value = '5';
        document.getElementById('inches').value = '10';
        document.getElementById('pounds').value = '160';
    }
    
    document.getElementById('lbm-results').style.display = 'none';
}

function performSearch() {
    const searchTerm = document.getElementById('calcSearchTerm').value;
    const searchOut = document.getElementById('calcSearchOut');
    
    if (searchTerm.trim() === '') {
        searchOut.innerHTML = '';
        return;
    }
    
    const calculators = [
        'BMI Calculator', 'Body Fat Calculator', 'Calorie Calculator', 
        'BMR Calculator', 'Ideal Weight Calculator', 'Lean Body Mass Calculator'
    ];
    
    const results = calculators.filter(calc => 
        calc.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    if (results.length > 0) {
        searchOut.innerHTML = '<div>' + results.map(result => 
            `<a href="#" style="display: block; padding: 2px 0;">${result}</a>`
        ).join('') + '</div>';
    } else {
        searchOut.innerHTML = '<div>No calculators found</div>';
    }
}

showUnits('metric');
</script>

</body>
</html>
