# Pace Calculator WordPress Theme

这是一个完整复刻原始配速计算器设计和功能的WordPress主题。

## 功能特性

### 完整复刻的功能：
1. **基础配速计算器** - 计算配速、时间和距离
2. **多点配速计算器** - 计算分段配速
3. **配速转换器** - 不同单位间的配速转换  
4. **完成时间计算器** - 基于当前进度预测完成时间
5. **世界纪录配速表** - 显示各项目的世界纪录配速
6. **训练指导内容** - 关于配速和心率训练的详细说明

### 设计特性：
- 100% 视觉保真度复刻原始设计
- 响应式布局，支持移动设备
- 完整的CSS样式匹配
- 交互式标签页切换
- AJAX驱动的计算功能
- 搜索功能
- 侧边栏小工具支持

## 安装说明

1. **上传主题文件**
   ```
   将 pace-calculator-theme 文件夹上传到 /wp-content/themes/ 目录
   ```

2. **激活主题**
   - 登录WordPress管理后台
   - 进入 外观 > 主题
   - 找到 "Pace Calculator Theme" 并激活

3. **创建配速计算器页面**
   - 创建新页面
   - 在页面属性中选择模板 "Pace Calculator"
   - 发布页面

4. **配置菜单**
   - 进入 外观 > 菜单
   - 创建主导航菜单并分配到 "Primary Menu" 位置
   - 创建顶部导航菜单并分配到 "Top Navigation" 位置

## 文件结构

```
pace-calculator-theme/
├── style.css                 # 主样式表
├── functions.php             # 主题功能和计算逻辑
├── header.php               # 头部模板
├── footer.php               # 底部模板
├── index.php                # 主模板
├── page-pace-calculator.php # 配速计算器页面模板
├── js/
│   └── calculator.js        # JavaScript交互功能
├── images/
│   ├── calculator-white.svg # Logo图片
│   └── heart-rate-vs-exercise.png # 心率图表
└── README.md               # 说明文档
```

## 技术实现

### 前端技术：
- HTML5 语义化标记
- CSS3 响应式设计
- jQuery JavaScript库
- AJAX异步计算

### 后端技术：
- PHP 7.4+ 兼容
- WordPress钩子和过滤器
- 自定义AJAX处理器
- 数学计算函数

### 计算功能：
- 配速计算（时间/距离）
- 时间计算（配速×距离）
- 距离计算（时间/配速）
- 多点分段配速分析
- 单位转换（英制/公制）
- 完成时间预测

## 自定义选项

主题支持以下自定义功能：

1. **小工具区域**
   - 右侧边栏小工具

2. **菜单位置**
   - 主导航菜单
   - 顶部导航菜单

3. **主题支持**
   - 文章缩略图
   - 自定义标题标签
   - HTML5标记

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- Internet Explorer 11+

## 性能优化

- 压缩的CSS和JavaScript
- 优化的图片资源
- 缓存友好的代码结构
- 最小化HTTP请求

## 维护和更新

定期检查以下项目：
- WordPress核心更新兼容性
- PHP版本兼容性
- 插件冲突检测
- 性能监控

## 技术支持

如需技术支持或发现问题，请检查：
1. WordPress错误日志
2. 浏览器控制台错误
3. 主题文件权限
4. 服务器PHP配置

## 版本历史

- v1.0 - 初始版本，完整复刻原始功能
