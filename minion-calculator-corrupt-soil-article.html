<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.minion-interface {
    background: linear-gradient(135deg, #6a1b9a 0%, #4a148c 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.minion-selector {
    background-color: #f3e5f5;
    border: 2px solid #6a1b9a;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.minion-card {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 15px;
    margin: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    width: 120px;
}
.minion-card:hover {
    border-color: #6a1b9a;
    transform: translateY(-3px);
}
.minion-card.selected {
    border-color: #6a1b9a;
    background-color: #f3e5f5;
}
.upgrade-section {
    background-color: #e8f5e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.production-result {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.corrupt-soil {
    background: linear-gradient(135deg, #424242 0%, #212121 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    color: white;
    text-align: center;
}
.tier-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    margin: 3px;
    color: white;
}
.tier-1 { background-color: #8bc34a; }
.tier-2 { background-color: #2196f3; }
.tier-3 { background-color: #9c27b0; }
.tier-4 { background-color: #ff9800; }
.tier-5 { background-color: #f44336; }
.tier-6 { background-color: #795548; }
.tier-7 { background-color: #607d8b; }
.tier-8 { background-color: #e91e63; }
.tier-9 { background-color: #3f51b5; }
.tier-10 { background-color: #ff5722; }
.tier-11 { background-color: #9e9e9e; }
.efficiency-meter {
    background-color: #e0e0e0;
    border-radius: 10px;
    height: 20px;
    margin: 10px 0;
    overflow: hidden;
}
.efficiency-fill {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #8bc34a);
    border-radius: 10px;
    transition: width 0.5s ease;
}
</style>

<h1>Minion Calculator with Corrupt Soil</h1>
<p>Calculate minion production rates and efficiency with corrupt soil upgrades in Hypixel SkyBlock. Optimize your minion setups, compare different configurations, and maximize your resource generation with our comprehensive minion calculator.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="minionform" id="minion-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Minion Calculator</a></li>
                <li><a href="#" data-tab="upgrades">Upgrade Guide</a></li>
                <li><a href="#" data-tab="efficiency">Efficiency Tips</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="minion-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">⛏️ SkyBlock Minion Calculator</h3>
                
                <div class="minion-selector">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🤖 Select Minion Type</h4>
                    <div style="text-align: center; margin-bottom: 20px;">
                        <div class="minion-card" data-minion="cobblestone">
                            <div style="font-size: 24px;">⛏️</div>
                            <div><strong>Cobblestone</strong></div>
                            <div style="font-size: 12px;">Mining</div>
                        </div>
                        <div class="minion-card" data-minion="wheat">
                            <div style="font-size: 24px;">🌾</div>
                            <div><strong>Wheat</strong></div>
                            <div style="font-size: 12px;">Farming</div>
                        </div>
                        <div class="minion-card" data-minion="cow">
                            <div style="font-size: 24px;">🐄</div>
                            <div><strong>Cow</strong></div>
                            <div style="font-size: 12px;">Combat</div>
                        </div>
                        <div class="minion-card" data-minion="oak">
                            <div style="font-size: 24px;">🌳</div>
                            <div><strong>Oak</strong></div>
                            <div style="font-size: 12px;">Foraging</div>
                        </div>
                        <div class="minion-card" data-minion="fishing">
                            <div style="font-size: 24px;">🎣</div>
                            <div><strong>Fishing</strong></div>
                            <div style="font-size: 12px;">Fishing</div>
                        </div>
                        <div class="minion-card" data-minion="clay">
                            <div style="font-size: 24px;">🧱</div>
                            <div><strong>Clay</strong></div>
                            <div style="font-size: 12px;">Mining</div>
                        </div>
                        <div class="minion-card" data-minion="snow">
                            <div style="font-size: 24px;">❄️</div>
                            <div><strong>Snow</strong></div>
                            <div style="font-size: 12px;">Mining</div>
                        </div>
                        <div class="minion-card" data-minion="obsidian">
                            <div style="font-size: 24px;">🖤</div>
                            <div><strong>Obsidian</strong></div>
                            <div style="font-size: 12px;">Mining</div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <label for="minion-tier"><strong>Minion Tier:</strong></label>
                        <select id="minion-tier" class="innormal" style="width: 150px; margin-left: 10px;">
                            <option value="1">Tier I</option>
                            <option value="2">Tier II</option>
                            <option value="3">Tier III</option>
                            <option value="4">Tier IV</option>
                            <option value="5">Tier V</option>
                            <option value="6">Tier VI</option>
                            <option value="7">Tier VII</option>
                            <option value="8">Tier VIII</option>
                            <option value="9">Tier IX</option>
                            <option value="10">Tier X</option>
                            <option value="11" selected>Tier XI</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div class="upgrade-section">
                <h4 style="color: #2c3e50; margin-bottom: 20px;">🔧 Minion Upgrades</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h5>Fuel Upgrades:</h5>
                        <select id="fuel-upgrade" class="innormal" style="width: 100%; margin-top: 5px;">
                            <option value="none">No Fuel</option>
                            <option value="coal">Coal (+5% speed)</option>
                            <option value="block_coal">Block of Coal (+10% speed)</option>
                            <option value="enchanted_coal">Enchanted Coal (+20% speed)</option>
                            <option value="enchanted_charcoal">Enchanted Charcoal (+25% speed)</option>
                            <option value="solar_panel">Solar Panel (+30% speed, day only)</option>
                            <option value="catalyst">Catalyst (+30% speed)</option>
                            <option value="hyper_catalyst">Hyper Catalyst (+35% speed)</option>
                        </select>
                        
                        <h5 style="margin-top: 15px;">Storage Upgrades:</h5>
                        <select id="storage-upgrade" class="innormal" style="width: 100%; margin-top: 5px;">
                            <option value="none">No Storage Upgrade</option>
                            <option value="small">Small Storage (+3 slots)</option>
                            <option value="medium">Medium Storage (+9 slots)</option>
                            <option value="large">Large Storage (+15 slots)</option>
                            <option value="xl">XL Storage (+21 slots)</option>
                            <option value="xxl">XXL Storage (+27 slots)</option>
                        </select>
                    </div>
                    
                    <div>
                        <h5>Special Upgrades:</h5>
                        <div style="margin: 10px 0;">
                            <label>
                                <input type="checkbox" id="corrupt-soil" style="margin-right: 8px;">
                                <strong>Corrupt Soil (+20% speed, -50% sell price)</strong>
                            </label>
                        </div>
                        
                        <div style="margin: 10px 0;">
                            <label>
                                <input type="checkbox" id="beacon" style="margin-right: 8px;">
                                <strong>Beacon (+10% speed)</strong>
                            </label>
                        </div>
                        
                        <div style="margin: 10px 0;">
                            <label>
                                <input type="checkbox" id="minion_expander" style="margin-right: 8px;">
                                <strong>Minion Expander (+5% speed)</strong>
                            </label>
                        </div>
                        
                        <h5 style="margin-top: 15px;">Automated Selling:</h5>
                        <select id="auto-smelter" class="innormal" style="width: 100%; margin-top: 5px;">
                            <option value="none">No Auto-Smelter</option>
                            <option value="auto_smelter">Auto Smelter</option>
                            <option value="dwarven_super_compactor">Dwarven Super Compactor</option>
                            <option value="super_compactor_3000">Super Compactor 3000</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Production" id="calculate-minion-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6a1b9a; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Calculator" id="clear-minion-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Calculations are based on Hypixel SkyBlock mechanics. Actual production may vary based on server performance and updates.
        </div>
        
        <div id="minion-results" style="display: none; margin-top: 30px;">
            <div class="production-result">
                <h3 style="margin-bottom: 20px;">⚡ Minion Production Results</h3>
                <div id="minion-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const minionCards = document.querySelectorAll('.minion-card');
    const calculateBtn = document.getElementById('calculate-minion-btn');
    const clearBtn = document.getElementById('clear-minion-btn');
    const resultsDiv = document.getElementById('minion-results');
    const resultsContent = document.getElementById('minion-results-content');
    
    let selectedMinion = null;
    
    // Minion data with base production times (in seconds)
    const minionData = {
        cobblestone: {
            name: 'Cobblestone Minion',
            emoji: '⛏️',
            category: 'Mining',
            baseTimes: [14, 14, 13, 13, 12, 12, 11, 11, 10, 10, 9],
            product: 'Cobblestone',
            sellPrice: 1
        },
        wheat: {
            name: 'Wheat Minion',
            emoji: '🌾',
            category: 'Farming',
            baseTimes: [13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8],
            product: 'Wheat',
            sellPrice: 3
        },
        cow: {
            name: 'Cow Minion',
            emoji: '🐄',
            category: 'Combat',
            baseTimes: [26, 26, 24, 24, 22, 22, 20, 20, 18, 18, 16],
            product: 'Raw Beef',
            sellPrice: 8
        },
        oak: {
            name: 'Oak Minion',
            emoji: '🌳',
            category: 'Foraging',
            baseTimes: [15, 15, 14, 14, 13, 13, 12, 12, 11, 11, 10],
            product: 'Oak Log',
            sellPrice: 2
        },
        fishing: {
            name: 'Fishing Minion',
            emoji: '🎣',
            category: 'Fishing',
            baseTimes: [30, 30, 28, 28, 26, 26, 24, 24, 22, 22, 20],
            product: 'Raw Fish',
            sellPrice: 6
        },
        clay: {
            name: 'Clay Minion',
            emoji: '🧱',
            category: 'Mining',
            baseTimes: [16, 16, 15, 15, 14, 14, 13, 13, 12, 12, 11],
            product: 'Clay',
            sellPrice: 3
        },
        snow: {
            name: 'Snow Minion',
            emoji: '❄️',
            category: 'Mining',
            baseTimes: [13, 13, 12, 12, 11, 11, 10, 10, 9, 9, 8],
            product: 'Snow Block',
            sellPrice: 2
        },
        obsidian: {
            name: 'Obsidian Minion',
            emoji: '🖤',
            category: 'Mining',
            baseTimes: [45, 45, 42, 42, 39, 39, 36, 36, 33, 33, 30],
            product: 'Obsidian',
            sellPrice: 50
        }
    };
    
    // Fuel upgrade data
    const fuelUpgrades = {
        none: { speedBonus: 0, name: 'No Fuel' },
        coal: { speedBonus: 0.05, name: 'Coal' },
        block_coal: { speedBonus: 0.10, name: 'Block of Coal' },
        enchanted_coal: { speedBonus: 0.20, name: 'Enchanted Coal' },
        enchanted_charcoal: { speedBonus: 0.25, name: 'Enchanted Charcoal' },
        solar_panel: { speedBonus: 0.30, name: 'Solar Panel' },
        catalyst: { speedBonus: 0.30, name: 'Catalyst' },
        hyper_catalyst: { speedBonus: 0.35, name: 'Hyper Catalyst' }
    };
    
    // Handle minion selection
    minionCards.forEach(card => {
        card.addEventListener('click', function() {
            minionCards.forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            selectedMinion = this.dataset.minion;
        });
    });
    
    calculateBtn.addEventListener('click', function() {
        if (!selectedMinion) {
            alert('Please select a minion type.');
            return;
        }
        
        const tier = parseInt(document.getElementById('minion-tier').value);
        const fuelUpgrade = document.getElementById('fuel-upgrade').value;
        const storageUpgrade = document.getElementById('storage-upgrade').value;
        const corruptSoil = document.getElementById('corrupt-soil').checked;
        const beacon = document.getElementById('beacon').checked;
        const minionExpander = document.getElementById('minion_expander').checked;
        const autoSmelter = document.getElementById('auto-smelter').value;
        
        const production = calculateMinionProduction(selectedMinion, tier, {
            fuelUpgrade,
            storageUpgrade,
            corruptSoil,
            beacon,
            minionExpander,
            autoSmelter
        });
        
        displayResults(production);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        selectedMinion = null;
        minionCards.forEach(c => c.classList.remove('selected'));
        document.getElementById('minion-calculator-form').reset();
        resultsDiv.style.display = 'none';
    });
    
    function calculateMinionProduction(minionType, tier, upgrades) {
        const minion = minionData[minionType];
        const baseTime = minion.baseTimes[tier - 1];
        
        // Calculate speed bonuses
        let speedMultiplier = 1;
        
        // Fuel upgrade
        const fuel = fuelUpgrades[upgrades.fuelUpgrade];
        speedMultiplier += fuel.speedBonus;
        
        // Corrupt soil
        if (upgrades.corruptSoil) {
            speedMultiplier += 0.20;
        }
        
        // Beacon
        if (upgrades.beacon) {
            speedMultiplier += 0.10;
        }
        
        // Minion expander
        if (upgrades.minionExpander) {
            speedMultiplier += 0.05;
        }
        
        // Calculate actual production time
        const actualTime = baseTime / speedMultiplier;
        
        // Calculate storage capacity
        let storageSlots = 15; // Base storage
        const storageUpgrades = {
            none: 0, small: 3, medium: 9, large: 15, xl: 21, xxl: 27
        };
        storageSlots += storageUpgrades[upgrades.storageUpgrade] || 0;
        
        // Calculate sell price
        let sellPrice = minion.sellPrice;
        if (upgrades.corruptSoil) {
            sellPrice *= 0.5; // 50% reduction
        }
        
        // Calculate production rates
        const itemsPerHour = 3600 / actualTime;
        const itemsPerDay = itemsPerHour * 24;
        const coinsPerHour = itemsPerHour * sellPrice;
        const coinsPerDay = coinsPerHour * 24;
        
        // Calculate time to fill storage
        const timeToFillStorage = (storageSlots * actualTime) / 3600; // in hours
        
        return {
            minion: minion,
            tier: tier,
            upgrades: upgrades,
            baseTime: baseTime,
            actualTime: actualTime,
            speedMultiplier: speedMultiplier,
            storageSlots: storageSlots,
            sellPrice: sellPrice,
            itemsPerHour: itemsPerHour,
            itemsPerDay: itemsPerDay,
            coinsPerHour: coinsPerHour,
            coinsPerDay: coinsPerDay,
            timeToFillStorage: timeToFillStorage,
            efficiency: Math.min(100, (speedMultiplier - 1) * 100 + 50)
        };
    }
    
    function displayResults(calc) {
        const tierClass = `tier-${calc.tier}`;
        
        let html = `
            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 25px;">
                <div style="font-size: 48px; margin-right: 20px;">${calc.minion.emoji}</div>
                <div>
                    <h3 style="margin: 0;">${calc.minion.name}</h3>
                    <div class="tier-badge ${tierClass}">Tier ${calc.tier}</div>
                    <div style="margin-top: 5px; color: rgba(255,255,255,0.8);">${calc.minion.category}</div>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Production Speed</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #6a1b9a;">${calc.actualTime.toFixed(1)}s</div>
                    <div style="font-size: 14px; color: #666;">per item</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Items per Hour</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #4caf50;">${Math.round(calc.itemsPerHour)}</div>
                    <div style="font-size: 14px; color: #666;">${calc.minion.product}</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Coins per Hour</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #ff9800;">${Math.round(calc.coinsPerHour).toLocaleString()}</div>
                    <div style="font-size: 14px; color: #666;">with selling</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Storage Capacity</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #2196f3;">${calc.storageSlots}</div>
                    <div style="font-size: 14px; color: #666;">slots</div>
                </div>
            </div>
        `;
        
        if (calc.upgrades.corruptSoil) {
            html += `
                <div class="corrupt-soil">
                    <h4>☠️ Corrupt Soil Active</h4>
                    <div style="margin: 10px 0;">
                        <strong>Speed Bonus:</strong> +20% | <strong>Sell Price:</strong> -50%
                    </div>
                    <div style="font-size: 14px; opacity: 0.8;">
                        Great for collection, reduced profit from selling
                    </div>
                </div>
            `;
        }
        
        html += `
            <div style="background: white; border-radius: 10px; padding: 20px; margin: 20px 0; color: #2c3e50;">
                <h4>📊 Production Analysis</h4>
                <div style="margin: 15px 0;">
                    <strong>Efficiency Rating:</strong>
                    <div class="efficiency-meter">
                        <div class="efficiency-fill" style="width: ${calc.efficiency}%"></div>
                    </div>
                    <div style="text-align: center; margin-top: 5px;">${calc.efficiency.toFixed(1)}%</div>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
                    <div>
                        <h5>Daily Production:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Items:</strong> ${Math.round(calc.itemsPerDay).toLocaleString()}</li>
                            <li><strong>Coins:</strong> ${Math.round(calc.coinsPerDay).toLocaleString()}</li>
                            <li><strong>Time to Fill:</strong> ${calc.timeToFillStorage.toFixed(1)} hours</li>
                        </ul>
                    </div>
                    <div>
                        <h5>Active Upgrades:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Fuel:</strong> ${fuelUpgrades[calc.upgrades.fuelUpgrade].name}</li>
                            <li><strong>Speed Bonus:</strong> +${((calc.speedMultiplier - 1) * 100).toFixed(1)}%</li>
                            <li><strong>Sell Price:</strong> ${calc.sellPrice} coins</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 Optimization Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Corrupt Soil:</strong> Use for collection goals, not profit maximization</li>
                    <li><strong>Storage:</strong> Upgrade storage to reduce collection frequency</li>
                    <li><strong>Fuel:</strong> Hyper Catalyst provides best speed boost</li>
                    <li><strong>Auto-Smelter:</strong> Use for automatic item processing</li>
                    <li><strong>Beacon:</strong> Place beacon nearby for additional speed boost</li>
                    <li><strong>Collection:</strong> Empty minions regularly to maintain production</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
