<?php
/*
Template Name: Carbohydrate Calculator
*/

get_header(); ?>

<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
</style>

<h1>Carbohydrate Calculator</h1>
<p>The <i>Carb Calculator</i> estimates the percentage of carbohydrates a person should consume each day.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>

<div id="topmenu" class="topmenucenter">
    <ul>
        <li><a href="#" onclick="return popMenu('standard',1);">US Units</a></li> 
        <li id="menuon"><a href="#" onclick="return popMenu('metric',1);">Metric Units</a></li> 
        <li><a href="#" onclick="return popMenu('other');">Other Units</a></li>
    </ul>
</div>

<div class="panel2" style="padding:10px;">
    <form name="calform">
        <table id="calinputtable" width="440" bgcolor="#eeeeee">
            <tbody>
                <tr>
                    <td width="70">Age</td>
                    <td width="370"><input type="text" name="cage" id="cage" value="25" onkeyup="iptfieldCheck(this, 'r', 'pn');" class="inlongesthalf"> ages 18 - 80</td>
                </tr>
                <tr>
                    <td>Gender</td>
                    <td>
                        <label for="csex1" class="cbcontainer">
                            <input type="radio" name="csex" id="csex1" value="m" checked="">
                            <span class="rbmark"></span>male
                        </label> &nbsp; 
                        <label for="csex2" class="cbcontainer">
                            <input type="radio" name="csex" id="csex2" value="f">
                            <span class="rbmark"></span>female
                        </label>
                    </td>
                </tr>
            </tbody>
        </table>
        
        <table width="440" id="standardheightweight" bgcolor="#eeeeee" style="display: none;">
            <tbody>
                <tr>
                    <td width="70">Height</td>
                    <td width="370">
                        <table border="0" cellpadding="0" cellspacing="0">
                            <tbody>
                                <tr>
                                    <td>
                                        <input type="text" name="cheightfeet" id="cheightfeet" value="5" onkeyup="iptfieldCheck(this, '', 'pzn');" class="inlongesthalf inuifoot">
                                        <span class="inuifootspan">feet</span>
                                    </td>
                                    <td>&nbsp;</td>
                                    <td>
                                        <input type="text" name="cheightinch" id="cheightinch" value="10" onkeyup="iptfieldCheck(this, '', 'pzn');" class="inlongesthalf inuiinch">
                                        <span class="inuiinchspan">inches</span>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </td>
                </tr>
                <tr>
                    <td>Weight</td>
                    <td><input type="text" name="cpound" id="cpound" value="160" onkeyup="iptfieldCheck(this, 'r', 'pn');" class="inlongest inuipound"><span class="inuipoundspan">pounds</span></td>
                </tr>
            </tbody>
        </table>
        
        <table width="440" id="metricheightweight" bgcolor="#eeeeee" style="display: block;">
            <tbody>
                <tr>
                    <td width="70">Height</td>
                    <td width="370"><input type="text" name="cheightmeter" id="cheightmeter" value="180" onkeyup="iptfieldCheck(this, 'r', 'pn');" class="inlongest inuick"><span class="inuickspan">cm</span></td>
                </tr>
                <tr id="metricweight">
                    <td>Weight</td>
                    <td><input type="text" name="ckg" id="ckg" value="60" onkeyup="iptfieldCheck(this, 'r', 'pn');" class="inlongest inuick"><span class="inuickspan">kg</span></td>
                </tr>
            </tbody>
        </table>
        
        <table width="620" bgcolor="#eeeeee">
            <tbody>
                <tr>
                    <td width="70">Activity</td>
                    <td width="550">
                        <select id="cactivity" name="cactivity">
                            <option value="1.2">Sedentary: little or no exercise</option>
                            <option value="1.375" selected="">Light: exercise 1-3 times/week</option>
                            <option value="1.465">Moderate: exercise 4-5 times/week</option>
                            <option value="1.55">Active: daily exercise or intense exercise 3-4 times/week</option>
                            <option value="1.725">Very Active: intense exercise 6-7 times/week</option>
                            <option value="1.9">Extra Active: very intense exercise daily, or physical job</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td width="70">&nbsp;</td>
                    <td width="550">
                        <div id="ccsettingtitle" style="margin: 10px 0px;"><b><a href="#" onclick="return cshmoreoption(1);">+ Settings</a></b></div>
                        <div id="ccsettingcontent" style="display: none;">
                            <input type="hidden" name="cmop" id="cmoreoption" value="0">
                            <div><b>BMR estimation formula:</b></div>
                            <div>
                                <div><label for="cformula1" class="cbcontainer"><input type="radio" name="cformula" id="cformula1" value="m" checked=""><span class="rbmark"></span> Mifflin St Jeor</label></div>
                                <div><label for="cformula2" class="cbcontainer"><input type="radio" name="cformula" id="cformula2" value="k"><span class="rbmark"></span> Katch-McArdle</label> &nbsp; <a href="<?php echo home_url('/body-fat-calculator/'); ?>">Body Fat:</a> <input type="text" name="cfatpct" value="20" class="in4char inpct" onkeyup="iptfieldCheck(this, '', 'pn');"></div>
                            </div>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>&nbsp;</td>
                    <td>
                        <input name="printit" value="0" type="hidden">
                        <input type="hidden" name="ctype" id="ctype" value="metric">
                        <input type="submit" name="x" value="Calculate">
                        <input type="button" value="Clear" onclick="clearForm(document.calform);">
                    </td>
                </tr>
            </tbody>
        </table>
    </form>
</div>

<ul style="margin-top:0px;margin-left:-20px;">
    <li class="smalltext"><b>Exercise:</b> 15-30 minutes of elevated heart rate activity.</li>
    <li class="smalltext"><b>Intense exercise:</b> 45-120 minutes of elevated heart rate activity.</li>
    <li class="smalltext"><b>Very intense exercise:</b> 2+ hours of elevated heart rate activity.</li>
</ul>

<script>
function popMenu(inval, insubmit){
    htmlVal = "";
    if (inval == "metric"){
        htmlVal = htmlVal + "<li><a href=\"#\" onclick=\"return popMenu('standard',1);\">US Units</a></li> <li id='menuon'><a href=\"#\" onclick=\"return popMenu('metric',1);\">Metric Units</a></li> <li><a href=\"#\" onclick=\"return popMenu('other');\">Other Units</a></li>";
        document.getElementById("ctype").value="metric";
        document.getElementById("standardheightweight").style.display = 'none';
        document.getElementById("metricheightweight").style.display = 'block';
        htmlVal = "<ul>" + htmlVal + "</ul>";
        if (insubmit==1) document.calform.submit();
    }else if (inval == "standard"){
        htmlVal = htmlVal + "<li id='menuon'><a href=\"#\" onclick=\"return popMenu('standard',1);\">US Units</a></li> <li><a href=\"#\" onclick=\"return popMenu('metric',1);\">Metric Units</a></li> <li><a href=\"#\" onclick=\"return popMenu('other');\">Other Units</a></li>";
        document.getElementById("ctype").value="standard";
        document.getElementById("standardheightweight").style.display = 'block';
        document.getElementById("metricheightweight").style.display = 'none';
        htmlVal = "<ul>" + htmlVal + "</ul>";
        if (insubmit==1) document.calform.submit();
    }
    document.getElementById("topmenu").innerHTML = htmlVal;
    return false;
}
popMenu("metric",0);

function cshmoreoption(optVar){
    if (optVar==1){
        document.getElementById("ccsettingcontent").style.display = 'block';
        document.getElementById("cmoreoption").value = "1";
        document.getElementById("ccsettingtitle").innerHTML = "<b><a href=\"#\" onclick=\"return cshmoreoption(0);\">- Settings</a></b>";
    }else{
        document.getElementById("ccsettingcontent").style.display = 'none';
        document.getElementById("cmoreoption").value = "0";
        document.getElementById("ccsettingtitle").innerHTML = "<b><a href=\"#\" onclick=\"return cshmoreoption(1);\">+ Settings</a></b>";
    }
    return false;
}
cshmoreoption(0);

// Input field validation function
function iptfieldCheck(field, type, validation) {
    // Basic validation logic
    var value = field.value;
    if (validation.includes('pn') && value < 0) {
        field.value = Math.abs(value);
    }
}

function clearForm(form) {
    form.reset();
    // Reset to default values
    document.getElementById('cage').value = '25';
    document.getElementById('cheightmeter').value = '180';
    document.getElementById('ckg').value = '60';
    document.getElementById('cheightfeet').value = '5';
    document.getElementById('cheightinch').value = '10';
    document.getElementById('cpound').value = '160';
    document.getElementById('cactivity').selectedIndex = 1;
}
</script>

<br>
<fieldset>
    <legend>Related</legend>
    <a href="<?php echo home_url('/calorie-calculator/'); ?>">Calorie Calculator</a> | 
    <a href="<?php echo home_url('/protein-calculator/'); ?>">Protein Calculator</a> | 
    <a href="<?php echo home_url('/fat-intake-calculator/'); ?>">Fat Intake Calculator</a>
</fieldset>

<br>
<h3>What are Carbohydrates?</h3>
<p>Carbohydrates (carbs) are one of three primary macronutrients that provide energy, along with fats and proteins. Carbohydrates are broken down in the body or converted into glucose, and serve as the body's main source of energy. They can also be stored as energy in the form of glycogen, or converted to fat (which can also be used as a source of energy).</p>

<h3>Types of Carbohydrates</h3>
<p>Carbohydrates are often classified as either simple (monosaccharides and disaccharides) or complex (polysaccharides or oligosaccharides), originally to create a distinction between sugars and other carbohydrates. However, there are many foods that contain multiple types of carbohydrates, such as fruits and vegetables, which can make the classification of certain foods ambiguous.</p>

<p>The three main types of carbohydrates are sugar, starch, and fiber:</p>
<ul>
    <li>Sugars are the simplest form of carbohydrates and can be found naturally in fruits, dairy, and vegetables; they can also be found in processed form in candy, cookies, cakes, and many beverages.</li>
    <li>Starches are complex carbohydrates that can be found naturally in many types of beans, vegetables, and grains.</li>
    <li>Fibers are complex carbohydrates that can be found in fruits, whole grains, vegetables, and many types of beans. Fibers are essential for digestion.</li>
</ul>

<h3>How Many Carbs Should I Eat?</h3>
<p>While this estimate varies depending on a number of factors, the Institute of Medicine recommends that a minimum of 130 grams of carbohydrates be consumed daily for adults. Other sources recommend that carbohydrates should comprise 40-75% of daily caloric intake.</p>

<p>Good carbs:</p>
<ul>
    <li>contain a low or moderate number of calories</li>
    <li>are high in nutrients</li>
    <li>do not contain refined sugars or grains</li>
    <li>are high in natural fibers</li>
    <li>are low in sodium and saturated fats</li>
    <li>are low in, or do not contain, cholesterol and trans fats</li>
</ul>

<p>Bad carbs essentially are the opposite of good carbs and:</p>
<ul>
    <li>are high in calories</li>
    <li>are low in many nutrients</li>
    <li>are full of refined sugars (ex. corn syrup, white sugar, honey, fruit juices)</li>
    <li>are low in fiber</li>
    <li>are high in sodium and may contain high levels of saturated fat</li>
    <li>may be high in cholesterol and trans fats</li>
    <li>are high in refined grains (ex. white flour)</li>
</ul>

<?php get_footer(); ?>
