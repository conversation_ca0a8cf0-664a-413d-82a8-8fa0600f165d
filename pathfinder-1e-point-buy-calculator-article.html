<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.pathfinder-interface {
    background: linear-gradient(135deg, #8e24aa 0%, #6a1b9a 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.ability-input {
    background-color: #f3e5f5;
    border: 2px solid #8e24aa;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.pathfinder-result {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.ability-score {
    display: grid;
    grid-template-columns: 100px 80px 80px 80px 80px;
    gap: 10px;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    background-color: #f5f5f5;
    border-radius: 8px;
}
.ability-score.header {
    background-color: #8e24aa;
    color: white;
    font-weight: bold;
}
.point-cost {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.race-bonus {
    background-color: #e8f5e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.point-display {
    font-size: 24px;
    font-weight: bold;
    padding: 10px 20px;
    border-radius: 10px;
    margin: 10px 0;
    text-align: center;
}
.points-remaining { background-color: #4caf50; color: white; }
.points-over { background-color: #f44336; color: white; }
.points-zero { background-color: #ff9800; color: white; }
</style>

<h1>Pathfinder 1e Point Buy Calculator</h1>
<p>Calculate ability scores for Pathfinder 1st Edition using the point buy system. Optimize your character build with our comprehensive calculator that includes racial bonuses, point costs, and ability modifiers for all Pathfinder 1e races and classes.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="pathfinderform" id="pathfinder-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Point Buy</a></li>
                <li><a href="#" data-tab="races">Races</a></li>
                <li><a href="#" data-tab="classes">Classes</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="pathfinder-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">🎲 Pathfinder 1e Point Buy Calculator</h3>
                
                <div class="ability-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">⚔️ Character Setup</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="point-buy-total"><strong>Point Buy Total:</strong></label>
                            <select id="point-buy-total" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="10">Low Fantasy (10 points)</option>
                                <option value="15">Standard Fantasy (15 points)</option>
                                <option value="20" selected>High Fantasy (20 points)</option>
                                <option value="25">Epic Fantasy (25 points)</option>
                                <option value="custom">Custom</option>
                            </select>
                            <input type="number" id="custom-points" value="20" min="0" max="50" class="innormal" style="width: 100%; margin-top: 5px; display: none;" placeholder="Custom points">
                        </div>
                        
                        <div>
                            <label for="character-race"><strong>Race:</strong></label>
                            <select id="character-race" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="human" selected>Human (+2 any)</option>
                                <option value="elf">Elf (+2 Dex, +2 Int, -2 Con)</option>
                                <option value="dwarf">Dwarf (+2 Con, +2 Wis, -2 Cha)</option>
                                <option value="halfling">Halfling (+2 Dex, +2 Cha, -2 Str)</option>
                                <option value="gnome">Gnome (+2 Con, +2 Cha, -2 Str)</option>
                                <option value="half-elf">Half-Elf (+2 any)</option>
                                <option value="half-orc">Half-Orc (+2 any)</option>
                                <option value="aasimar">Aasimar (+2 Wis, +2 Cha)</option>
                                <option value="tiefling">Tiefling (+2 Dex, +2 Int, -2 Cha)</option>
                                <option value="custom">Custom/Other</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="character-class"><strong>Intended Class:</strong></label>
                            <select id="character-class" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Select Class (Optional)</option>
                                <option value="fighter">Fighter</option>
                                <option value="wizard">Wizard</option>
                                <option value="cleric">Cleric</option>
                                <option value="rogue">Rogue</option>
                                <option value="ranger">Ranger</option>
                                <option value="paladin">Paladin</option>
                                <option value="barbarian">Barbarian</option>
                                <option value="bard">Bard</option>
                                <option value="druid">Druid</option>
                                <option value="monk">Monk</option>
                                <option value="sorcerer">Sorcerer</option>
                            </select>
                        </div>
                    </div>
                    
                    <div id="human-bonus" style="display: none; margin-top: 15px;">
                        <label for="human-bonus-ability"><strong>Human/Half-Elf/Half-Orc +2 Bonus to:</strong></label>
                        <select id="human-bonus-ability" class="innormal" style="width: 200px; margin-top: 5px;">
                            <option value="">Select Ability</option>
                            <option value="str">Strength</option>
                            <option value="dex">Dexterity</option>
                            <option value="con">Constitution</option>
                            <option value="int">Intelligence</option>
                            <option value="wis">Wisdom</option>
                            <option value="cha">Charisma</option>
                        </select>
                    </div>
                </div>
                
                <div style="background-color: #f3e5f5; border: 2px solid #8e24aa; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">📊 Ability Scores</h4>
                    
                    <div style="margin-bottom: 15px;">
                        <div class="ability-score header">
                            <div>Ability</div>
                            <div>Base</div>
                            <div>Racial</div>
                            <div>Total</div>
                            <div>Modifier</div>
                        </div>
                        
                        <div class="ability-score">
                            <div><strong>Strength</strong></div>
                            <div>
                                <button type="button" class="ability-btn" data-ability="str" data-action="decrease">-</button>
                                <span id="str-base" style="margin: 0 10px; font-weight: bold;">10</span>
                                <button type="button" class="ability-btn" data-ability="str" data-action="increase">+</button>
                            </div>
                            <div id="str-racial">+0</div>
                            <div id="str-total" style="font-weight: bold;">10</div>
                            <div id="str-modifier">+0</div>
                        </div>
                        
                        <div class="ability-score">
                            <div><strong>Dexterity</strong></div>
                            <div>
                                <button type="button" class="ability-btn" data-ability="dex" data-action="decrease">-</button>
                                <span id="dex-base" style="margin: 0 10px; font-weight: bold;">10</span>
                                <button type="button" class="ability-btn" data-ability="dex" data-action="increase">+</button>
                            </div>
                            <div id="dex-racial">+0</div>
                            <div id="dex-total" style="font-weight: bold;">10</div>
                            <div id="dex-modifier">+0</div>
                        </div>
                        
                        <div class="ability-score">
                            <div><strong>Constitution</strong></div>
                            <div>
                                <button type="button" class="ability-btn" data-ability="con" data-action="decrease">-</button>
                                <span id="con-base" style="margin: 0 10px; font-weight: bold;">10</span>
                                <button type="button" class="ability-btn" data-ability="con" data-action="increase">+</button>
                            </div>
                            <div id="con-racial">+0</div>
                            <div id="con-total" style="font-weight: bold;">10</div>
                            <div id="con-modifier">+0</div>
                        </div>
                        
                        <div class="ability-score">
                            <div><strong>Intelligence</strong></div>
                            <div>
                                <button type="button" class="ability-btn" data-ability="int" data-action="decrease">-</button>
                                <span id="int-base" style="margin: 0 10px; font-weight: bold;">10</span>
                                <button type="button" class="ability-btn" data-ability="int" data-action="increase">+</button>
                            </div>
                            <div id="int-racial">+0</div>
                            <div id="int-total" style="font-weight: bold;">10</div>
                            <div id="int-modifier">+0</div>
                        </div>
                        
                        <div class="ability-score">
                            <div><strong>Wisdom</strong></div>
                            <div>
                                <button type="button" class="ability-btn" data-ability="wis" data-action="decrease">-</button>
                                <span id="wis-base" style="margin: 0 10px; font-weight: bold;">10</span>
                                <button type="button" class="ability-btn" data-ability="wis" data-action="increase">+</button>
                            </div>
                            <div id="wis-racial">+0</div>
                            <div id="wis-total" style="font-weight: bold;">10</div>
                            <div id="wis-modifier">+0</div>
                        </div>
                        
                        <div class="ability-score">
                            <div><strong>Charisma</strong></div>
                            <div>
                                <button type="button" class="ability-btn" data-ability="cha" data-action="decrease">-</button>
                                <span id="cha-base" style="margin: 0 10px; font-weight: bold;">10</span>
                                <button type="button" class="ability-btn" data-ability="cha" data-action="increase">+</button>
                            </div>
                            <div id="cha-racial">+0</div>
                            <div id="cha-total" style="font-weight: bold;">10</div>
                            <div id="cha-modifier">+0</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <div id="points-display" class="point-display points-remaining">
                    Points Remaining: 20
                </div>
                
                <div style="margin-top: 15px;">
                    <input type="button" value="Reset to 10s" id="reset-abilities-btn" style="padding: 10px 20px; font-size: 16px; background-color: #ff9800; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                    <input type="button" value="Optimize for Class" id="optimize-class-btn" style="padding: 10px 20px; font-size: 16px; background-color: #2196f3; color: white; border: none; border-radius: 8px; cursor: pointer;">
                </div>
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Point costs: 7=−4pts, 8=−2pts, 9=−1pt, 10=0pts, 11=1pt, 12=2pts, 13=3pts, 14=5pts, 15=7pts, 16=10pts, 17=13pts, 18=17pts
        </div>
        
        <div id="pathfinder-results" style="display: block; margin-top: 30px;">
            <div class="point-cost">
                <h4 style="color: #8e24aa; margin-bottom: 20px;">📋 Character Summary</h4>
                <div id="character-summary"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const resetBtn = document.getElementById('reset-abilities-btn');
    const optimizeBtn = document.getElementById('optimize-class-btn');
    const pointBuySelect = document.getElementById('point-buy-total');
    const customPointsInput = document.getElementById('custom-points');
    const raceSelect = document.getElementById('character-race');
    const classSelect = document.getElementById('character-class');
    const humanBonusDiv = document.getElementById('human-bonus');
    const humanBonusSelect = document.getElementById('human-bonus-ability');
    const pointsDisplay = document.getElementById('points-display');
    const characterSummary = document.getElementById('character-summary');
    
    // Point buy costs for Pathfinder 1e
    const pointCosts = {
        7: -4, 8: -2, 9: -1, 10: 0, 11: 1, 12: 2, 13: 3, 14: 5, 15: 7, 16: 10, 17: 13, 18: 17
    };
    
    // Racial bonuses
    const racialBonuses = {
        human: { any: 2 },
        elf: { dex: 2, int: 2, con: -2 },
        dwarf: { con: 2, wis: 2, cha: -2 },
        halfling: { dex: 2, cha: 2, str: -2 },
        gnome: { con: 2, cha: 2, str: -2 },
        'half-elf': { any: 2 },
        'half-orc': { any: 2 },
        aasimar: { wis: 2, cha: 2 },
        tiefling: { dex: 2, int: 2, cha: -2 },
        custom: {}
    };
    
    // Class recommendations
    const classRecommendations = {
        fighter: { str: 16, con: 14, dex: 13 },
        wizard: { int: 16, dex: 14, con: 13 },
        cleric: { wis: 16, con: 14, str: 13 },
        rogue: { dex: 16, int: 14, con: 13 },
        ranger: { dex: 16, wis: 14, con: 13 },
        paladin: { str: 16, cha: 14, con: 13 },
        barbarian: { str: 16, con: 16, dex: 13 },
        bard: { cha: 16, dex: 14, con: 13 },
        druid: { wis: 16, con: 14, dex: 13 },
        monk: { wis: 16, dex: 14, con: 13 },
        sorcerer: { cha: 16, dex: 14, con: 13 }
    };
    
    let abilityScores = { str: 10, dex: 10, con: 10, int: 10, wis: 10, cha: 10 };
    let totalPoints = 20;
    
    // Handle point buy total change
    pointBuySelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customPointsInput.style.display = 'block';
            totalPoints = parseInt(customPointsInput.value) || 20;
        } else {
            customPointsInput.style.display = 'none';
            totalPoints = parseInt(this.value);
        }
        updateDisplay();
    });
    
    customPointsInput.addEventListener('input', function() {
        totalPoints = parseInt(this.value) || 20;
        updateDisplay();
    });
    
    // Handle race change
    raceSelect.addEventListener('change', function() {
        const race = this.value;
        if (race === 'human' || race === 'half-elf' || race === 'half-orc') {
            humanBonusDiv.style.display = 'block';
        } else {
            humanBonusDiv.style.display = 'none';
        }
        updateDisplay();
    });
    
    humanBonusSelect.addEventListener('change', function() {
        updateDisplay();
    });
    
    // Handle ability score changes
    document.querySelectorAll('.ability-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const ability = this.dataset.ability;
            const action = this.dataset.action;
            
            if (action === 'increase' && abilityScores[ability] < 18) {
                abilityScores[ability]++;
            } else if (action === 'decrease' && abilityScores[ability] > 7) {
                abilityScores[ability]--;
            }
            
            updateDisplay();
        });
    });
    
    resetBtn.addEventListener('click', function() {
        abilityScores = { str: 10, dex: 10, con: 10, int: 10, wis: 10, cha: 10 };
        updateDisplay();
    });
    
    optimizeBtn.addEventListener('click', function() {
        const selectedClass = classSelect.value;
        if (selectedClass && classRecommendations[selectedClass]) {
            optimizeForClass(selectedClass);
        } else {
            alert('Please select a class to optimize for.');
        }
    });
    
    function optimizeForClass(className) {
        const recommendations = classRecommendations[className];
        const race = raceSelect.value;
        
        // Reset to base
        abilityScores = { str: 10, dex: 10, con: 10, int: 10, wis: 10, cha: 10 };
        
        // Apply recommendations within point budget
        const sortedAbilities = Object.entries(recommendations).sort((a, b) => b[1] - a[1]);
        
        for (const [ability, targetScore] of sortedAbilities) {
            while (abilityScores[ability] < targetScore && getPointsUsed() < totalPoints) {
                if (abilityScores[ability] >= 18) break;
                
                const currentCost = pointCosts[abilityScores[ability]];
                const newCost = pointCosts[abilityScores[ability] + 1];
                
                if (getPointsUsed() + (newCost - currentCost) <= totalPoints) {
                    abilityScores[ability]++;
                } else {
                    break;
                }
            }
        }
        
        updateDisplay();
    }
    
    function getPointsUsed() {
        return Object.values(abilityScores).reduce((total, score) => total + pointCosts[score], 0);
    }
    
    function getRacialBonuses() {
        const race = raceSelect.value;
        const bonuses = { str: 0, dex: 0, con: 0, int: 0, wis: 0, cha: 0 };
        
        if (racialBonuses[race]) {
            Object.entries(racialBonuses[race]).forEach(([ability, bonus]) => {
                if (ability === 'any') {
                    const humanBonus = humanBonusSelect.value;
                    if (humanBonus) {
                        bonuses[humanBonus] += bonus;
                    }
                } else {
                    bonuses[ability] += bonus;
                }
            });
        }
        
        return bonuses;
    }
    
    function getAbilityModifier(score) {
        return Math.floor((score - 10) / 2);
    }
    
    function updateDisplay() {
        const racialBonuses = getRacialBonuses();
        const pointsUsed = getPointsUsed();
        const pointsRemaining = totalPoints - pointsUsed;
        
        // Update ability scores display
        ['str', 'dex', 'con', 'int', 'wis', 'cha'].forEach(ability => {
            const baseScore = abilityScores[ability];
            const racialBonus = racialBonuses[ability];
            const totalScore = baseScore + racialBonus;
            const modifier = getAbilityModifier(totalScore);
            
            document.getElementById(`${ability}-base`).textContent = baseScore;
            document.getElementById(`${ability}-racial`).textContent = racialBonus >= 0 ? `+${racialBonus}` : racialBonus;
            document.getElementById(`${ability}-total`).textContent = totalScore;
            document.getElementById(`${ability}-modifier`).textContent = modifier >= 0 ? `+${modifier}` : modifier;
        });
        
        // Update points display
        pointsDisplay.textContent = `Points Remaining: ${pointsRemaining}`;
        pointsDisplay.className = 'point-display ' + 
            (pointsRemaining > 0 ? 'points-remaining' : 
             pointsRemaining === 0 ? 'points-zero' : 'points-over');
        
        // Update character summary
        updateCharacterSummary(racialBonuses, pointsUsed, pointsRemaining);
    }
    
    function updateCharacterSummary(racialBonuses, pointsUsed, pointsRemaining) {
        const race = raceSelect.value;
        const characterClass = classSelect.value;
        
        const raceNames = {
            human: 'Human', elf: 'Elf', dwarf: 'Dwarf', halfling: 'Halfling',
            gnome: 'Gnome', 'half-elf': 'Half-Elf', 'half-orc': 'Half-Orc',
            aasimar: 'Aasimar', tiefling: 'Tiefling', custom: 'Custom'
        };
        
        const classNames = {
            fighter: 'Fighter', wizard: 'Wizard', cleric: 'Cleric', rogue: 'Rogue',
            ranger: 'Ranger', paladin: 'Paladin', barbarian: 'Barbarian', bard: 'Bard',
            druid: 'Druid', monk: 'Monk', sorcerer: 'Sorcerer'
        };
        
        let html = `
            <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                <div>
                    <h5>Character Build:</h5>
                    <ul style="margin: 10px 0;">
                        <li><strong>Race:</strong> ${raceNames[race] || 'Not selected'}</li>
                        <li><strong>Class:</strong> ${classNames[characterClass] || 'Not selected'}</li>
                        <li><strong>Point Buy:</strong> ${totalPoints} points</li>
                        <li><strong>Points Used:</strong> ${pointsUsed}</li>
                        <li><strong>Points Remaining:</strong> ${pointsRemaining}</li>
                    </ul>
                </div>
                
                <div>
                    <h5>Final Ability Scores:</h5>
                    <ul style="margin: 10px 0;">
        `;
        
        ['str', 'dex', 'con', 'int', 'wis', 'cha'].forEach(ability => {
            const abilityNames = {
                str: 'Strength', dex: 'Dexterity', con: 'Constitution',
                int: 'Intelligence', wis: 'Wisdom', cha: 'Charisma'
            };
            
            const baseScore = abilityScores[ability];
            const racialBonus = racialBonuses[ability];
            const totalScore = baseScore + racialBonus;
            const modifier = getAbilityModifier(totalScore);
            
            html += `<li><strong>${abilityNames[ability]}:</strong> ${totalScore} (${modifier >= 0 ? '+' : ''}${modifier})</li>`;
        });
        
        html += `
                    </ul>
                </div>
            </div>
            
            <div style="margin-top: 20px; padding: 15px; background-color: ${pointsRemaining < 0 ? '#ffebee' : '#f5f5f5'}; border-radius: 8px;">
                <h5 style="color: ${pointsRemaining < 0 ? '#f44336' : '#2c3e50'};">
                    ${pointsRemaining < 0 ? '⚠️ Over Budget' : pointsRemaining === 0 ? '✅ Perfect Build' : '💡 Build Status'}
                </h5>
                <p style="margin: 10px 0; color: #2c3e50;">
                    ${pointsRemaining < 0 ? 
                      `You are ${Math.abs(pointsRemaining)} points over budget. Reduce some ability scores.` :
                      pointsRemaining === 0 ?
                      'Perfect! You have used all available points efficiently.' :
                      `You have ${pointsRemaining} points remaining. Consider increasing ability scores.`}
                </p>
            </div>
        `;
        
        characterSummary.innerHTML = html;
    }
    
    // Initialize display
    updateDisplay();
});
</script>
