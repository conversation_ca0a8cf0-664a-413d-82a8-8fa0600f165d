<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.crafting-station {
    background: linear-gradient(135deg, #8b5a3c 0%, #6d4c41 100%);
    border-radius: 15px;
    padding: 20px;
    margin: 15px;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}
.crafting-station:hover {
    transform: translateY(-5px);
}
.recipe-card {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 12px;
    padding: 20px;
    margin: 15px 0;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    transition: border-color 0.3s ease;
}
.recipe-card:hover {
    border-color: #8b5a3c;
}
.material-list {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}
.material-item {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
}
.quantity-input {
    background-color: #fff3e0;
    border: 2px solid #ff9800;
    border-radius: 8px;
    padding: 15px;
    margin: 15px 0;
    text-align: center;
}
.total-materials {
    background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: #2c3e50;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.profession-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    margin: 3px;
    color: white;
}
.blacksmithing { background-color: #795548; }
.carpentry { background-color: #8bc34a; }
.cooking { background-color: #ff9800; }
.alchemy { background-color: #9c27b0; }
.tailoring { background-color: #e91e63; }
.masonry { background-color: #607d8b; }
.tier-1 { border-left: 4px solid #4caf50; }
.tier-2 { border-left: 4px solid #2196f3; }
.tier-3 { border-left: 4px solid #9c27b0; }
.tier-4 { border-left: 4px solid #ff9800; }
.tier-5 { border-left: 4px solid #f44336; }
</style>

<h1>Pax Dei Crafting Calculator</h1>
<p>Calculate the materials needed for crafting in Pax Dei, the medieval sandbox MMO. Plan your resource gathering, optimize your crafting chains, and manage your settlement's production efficiently.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="craftingform" id="crafting-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Crafting Calculator</a></li>
                <li><a href="#" data-tab="recipes">Recipe Database</a></li>
                <li><a href="#" data-tab="materials">Material Guide</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="quantity-input">
                <h3 style="color: #e65100; margin-bottom: 20px;">🏗️ Select Crafting Recipe</h3>
                <div style="margin-bottom: 20px;">
                    <label for="profession-select"><strong>Profession:</strong></label>
                    <select name="profession-select" id="profession-select" class="innormal" style="width: 200px; margin-left: 10px;">
                        <option value="">Select Profession</option>
                        <option value="blacksmithing">Blacksmithing</option>
                        <option value="carpentry">Carpentry</option>
                        <option value="cooking">Cooking</option>
                        <option value="alchemy">Alchemy</option>
                        <option value="tailoring">Tailoring</option>
                        <option value="masonry">Masonry</option>
                    </select>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <label for="recipe-select"><strong>Recipe:</strong></label>
                    <select name="recipe-select" id="recipe-select" class="innormal" style="width: 300px; margin-left: 10px;">
                        <option value="">Select Recipe</option>
                    </select>
                </div>
                
                <div>
                    <label for="quantity"><strong>Quantity to Craft:</strong></label>
                    <input type="number" name="quantity" id="quantity" value="1" min="1" max="1000" class="innormal" style="width: 100px; margin-left: 10px;">
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Materials" id="calculate-crafting-btn" style="padding: 15px 30px; font-size: 18px; background-color: #8b5a3c; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Clear Selection" id="clear-crafting-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Material requirements are based on Pax Dei crafting mechanics. Some recipes may require specific crafting stations or skill levels.
        </div>
        
        <div id="crafting-results" style="display: none; margin-top: 30px;">
            <div class="total-materials">
                <h3 style="margin-bottom: 20px;">📦 Required Materials</h3>
                <div id="crafting-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const professionSelect = document.getElementById('profession-select');
    const recipeSelect = document.getElementById('recipe-select');
    const calculateBtn = document.getElementById('calculate-crafting-btn');
    const clearBtn = document.getElementById('clear-crafting-btn');
    const resultsDiv = document.getElementById('crafting-results');
    const resultsContent = document.getElementById('crafting-results-content');
    
    // Recipe database
    const recipes = {
        blacksmithing: [
            {
                name: 'Iron Sword',
                tier: 1,
                materials: { 'Iron Ingot': 3, 'Wood': 1, 'Leather Strip': 1 },
                station: 'Forge',
                skill: 10
            },
            {
                name: 'Steel Armor',
                tier: 2,
                materials: { 'Steel Ingot': 8, 'Leather': 4, 'Iron Rivets': 12 },
                station: 'Advanced Forge',
                skill: 25
            },
            {
                name: 'Masterwork Blade',
                tier: 3,
                materials: { 'Damascus Steel': 5, 'Rare Wood': 2, 'Gold Wire': 1 },
                station: 'Master Forge',
                skill: 50
            }
        ],
        carpentry: [
            {
                name: 'Wooden Chest',
                tier: 1,
                materials: { 'Wood Planks': 6, 'Iron Nails': 8, 'Rope': 2 },
                station: 'Workbench',
                skill: 5
            },
            {
                name: 'Reinforced Door',
                tier: 2,
                materials: { 'Hardwood Planks': 10, 'Iron Bands': 4, 'Lock Mechanism': 1 },
                station: 'Advanced Workbench',
                skill: 20
            },
            {
                name: 'Ornate Furniture',
                tier: 3,
                materials: { 'Exotic Wood': 8, 'Gold Inlay': 3, 'Fine Cloth': 2 },
                station: 'Master Workshop',
                skill: 40
            }
        ],
        cooking: [
            {
                name: 'Bread',
                tier: 1,
                materials: { 'Flour': 2, 'Water': 1, 'Salt': 1 },
                station: 'Oven',
                skill: 1
            },
            {
                name: 'Meat Stew',
                tier: 2,
                materials: { 'Raw Meat': 3, 'Vegetables': 4, 'Herbs': 2, 'Water': 2 },
                station: 'Cooking Pot',
                skill: 15
            },
            {
                name: 'Royal Feast',
                tier: 3,
                materials: { 'Prime Meat': 5, 'Rare Spices': 3, 'Fine Wine': 2, 'Exotic Fruits': 4 },
                station: 'Grand Kitchen',
                skill: 35
            }
        ],
        alchemy: [
            {
                name: 'Health Potion',
                tier: 1,
                materials: { 'Red Herbs': 3, 'Pure Water': 1, 'Glass Vial': 1 },
                station: 'Alchemy Table',
                skill: 8
            },
            {
                name: 'Mana Elixir',
                tier: 2,
                materials: { 'Blue Crystals': 2, 'Moonwater': 2, 'Silver Dust': 1 },
                station: 'Advanced Laboratory',
                skill: 22
            },
            {
                name: 'Philosopher\'s Stone',
                tier: 4,
                materials: { 'Transmutation Circle': 1, 'Rare Elements': 10, 'Dragon Blood': 3 },
                station: 'Master Laboratory',
                skill: 75
            }
        ],
        tailoring: [
            {
                name: 'Linen Shirt',
                tier: 1,
                materials: { 'Linen Cloth': 4, 'Thread': 2, 'Buttons': 3 },
                station: 'Sewing Table',
                skill: 3
            },
            {
                name: 'Silk Robe',
                tier: 2,
                materials: { 'Silk Fabric': 6, 'Gold Thread': 3, 'Precious Gems': 2 },
                station: 'Advanced Loom',
                skill: 28
            },
            {
                name: 'Enchanted Cloak',
                tier: 3,
                materials: { 'Magical Fabric': 8, 'Mithril Thread': 4, 'Enchantment Runes': 6 },
                station: 'Enchanted Loom',
                skill: 45
            }
        ],
        masonry: [
            {
                name: 'Stone Blocks',
                tier: 1,
                materials: { 'Raw Stone': 4, 'Mortar': 2 },
                station: 'Stone Cutter',
                skill: 5
            },
            {
                name: 'Reinforced Wall',
                tier: 2,
                materials: { 'Cut Stone': 12, 'Iron Reinforcement': 6, 'Strong Mortar': 8 },
                station: 'Mason Workshop',
                skill: 25
            },
            {
                name: 'Cathedral Pillar',
                tier: 4,
                materials: { 'Marble': 20, 'Architectural Plans': 1, 'Master Mortar': 15 },
                station: 'Grand Mason Hall',
                skill: 60
            }
        ]
    };
    
    // Update recipe dropdown when profession changes
    professionSelect.addEventListener('change', function() {
        const profession = this.value;
        recipeSelect.innerHTML = '<option value="">Select Recipe</option>';
        
        if (profession && recipes[profession]) {
            recipes[profession].forEach((recipe, index) => {
                const option = document.createElement('option');
                option.value = `${profession}_${index}`;
                option.textContent = `${recipe.name} (Tier ${recipe.tier})`;
                recipeSelect.appendChild(option);
            });
        }
    });
    
    calculateBtn.addEventListener('click', function() {
        const profession = professionSelect.value;
        const recipeKey = recipeSelect.value;
        const quantity = parseInt(document.getElementById('quantity').value) || 1;
        
        if (!profession || !recipeKey) {
            alert('Please select both profession and recipe.');
            return;
        }
        
        const [prof, index] = recipeKey.split('_');
        const recipe = recipes[prof][parseInt(index)];
        
        if (!recipe) {
            alert('Recipe not found.');
            return;
        }
        
        const materials = calculateMaterials(recipe, quantity);
        displayResults(recipe, materials, quantity);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('crafting-calculator-form').reset();
        recipeSelect.innerHTML = '<option value="">Select Recipe</option>';
        resultsDiv.style.display = 'none';
    });
    
    function calculateMaterials(recipe, quantity) {
        const totalMaterials = {};
        
        Object.entries(recipe.materials).forEach(([material, amount]) => {
            totalMaterials[material] = amount * quantity;
        });
        
        return totalMaterials;
    }
    
    function displayResults(recipe, materials, quantity) {
        const professionClass = professionSelect.value;
        const tierClass = `tier-${recipe.tier}`;
        
        let html = `
            <div class="recipe-card ${tierClass}" style="margin-bottom: 25px;">
                <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px;">
                    <h3 style="color: #2c3e50; margin: 0;">${recipe.name}</h3>
                    <div>
                        <div class="profession-badge ${professionClass}">${professionSelect.value.toUpperCase()}</div>
                        <div style="font-size: 14px; color: #666; margin-top: 5px;">
                            Tier ${recipe.tier} • Skill ${recipe.skill} • ${recipe.station}
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center; margin: 15px 0;">
                    <strong style="font-size: 18px; color: #8b5a3c;">Crafting ${quantity}x ${recipe.name}</strong>
                </div>
            </div>
            
            <div class="material-list">
        `;
        
        Object.entries(materials).forEach(([material, amount]) => {
            html += `
                <div class="material-item">
                    <h4 style="color: #2c3e50; margin-bottom: 10px;">${material}</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #8b5a3c;">${amount}</div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        ${amount / quantity} per item
                    </div>
                </div>
            `;
        });
        
        html += `
            </div>
            
            <div style="background-color: rgba(139, 90, 60, 0.1); border-radius: 10px; padding: 20px; margin-top: 25px;">
                <h4 style="color: #8b5a3c;">📋 Crafting Information:</h4>
                <ul style="color: #2c3e50; text-align: left; margin: 10px 0;">
                    <li><strong>Required Station:</strong> ${recipe.station}</li>
                    <li><strong>Minimum Skill Level:</strong> ${recipe.skill}</li>
                    <li><strong>Recipe Tier:</strong> ${recipe.tier}</li>
                    <li><strong>Total Items to Craft:</strong> ${quantity}</li>
                </ul>
            </div>
            
            <div style="background-color: rgba(76, 175, 80, 0.1); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4 style="color: #4caf50;">💡 Crafting Tips:</h4>
                <ul style="color: #2c3e50; text-align: left; margin: 10px 0;">
                    <li>Gather materials in advance to avoid interrupting crafting sessions</li>
                    <li>Higher tier recipes require more advanced crafting stations</li>
                    <li>Some materials may need to be crafted from raw resources</li>
                    <li>Consider setting up supply chains for efficient production</li>
                    <li>Group similar recipes together to optimize station usage</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
