<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.persona-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 12px;
    padding: 15px;
    margin: 10px;
    color: white;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}
.persona-card:hover {
    transform: translateY(-5px);
}
.arcana-selector {
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}
.fusion-result {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.arcana-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 10px;
    margin: 15px 0;
}
.arcana-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    margin: 3px;
    color: white;
}
.fool { background-color: #e74c3c; }
.magician { background-color: #3498db; }
.priestess { background-color: #9b59b6; }
.empress { background-color: #e67e22; }
.emperor { background-color: #c0392b; }
.hierophant { background-color: #27ae60; }
.lovers { background-color: #f39c12; }
.chariot { background-color: #34495e; }
.justice { background-color: #16a085; }
.hermit { background-color: #8e44ad; }
.fortune { background-color: #2980b9; }
.strength { background-color: #d35400; }
.hanged { background-color: #7f8c8d; }
.death { background-color: #2c3e50; }
.temperance { background-color: #1abc9c; }
.devil { background-color: #8b4513; }
.tower { background-color: #95a5a6; }
.star { background-color: #f1c40f; }
.moon { background-color: #6c5ce7; }
.sun { background-color: #fdcb6e; }
.judgement { background-color: #a29bfe; }
.world { background-color: #00b894; }
</style>

<h1>Persona 3 Reload Fusion Calculator</h1>
<p>Calculate Persona fusion combinations in Persona 3 Reload with our comprehensive fusion calculator. Discover which Personas you can create by fusing different combinations and plan your optimal fusion strategies.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="fusionform" id="fusion-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Fusion Calculator</a></li>
                <li><a href="#" data-tab="arcana">Arcana Guide</a></li>
                <li><a href="#" data-tab="personas">Persona Database</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="arcana-selector">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">🎭 Select First Persona</h3>
                <div style="margin-bottom: 15px;">
                    <label for="persona1-arcana"><strong>Arcana:</strong></label>
                    <select name="persona1-arcana" id="persona1-arcana" class="innormal" style="width: 200px; margin-left: 10px;">
                        <option value="">Select Arcana</option>
                        <option value="fool">Fool</option>
                        <option value="magician">Magician</option>
                        <option value="priestess">Priestess</option>
                        <option value="empress">Empress</option>
                        <option value="emperor">Emperor</option>
                        <option value="hierophant">Hierophant</option>
                        <option value="lovers">Lovers</option>
                        <option value="chariot">Chariot</option>
                        <option value="justice">Justice</option>
                        <option value="hermit">Hermit</option>
                        <option value="fortune">Fortune</option>
                        <option value="strength">Strength</option>
                        <option value="hanged">Hanged Man</option>
                        <option value="death">Death</option>
                        <option value="temperance">Temperance</option>
                        <option value="devil">Devil</option>
                        <option value="tower">Tower</option>
                        <option value="star">Star</option>
                        <option value="moon">Moon</option>
                        <option value="sun">Sun</option>
                        <option value="judgement">Judgement</option>
                        <option value="world">World</option>
                    </select>
                </div>
                <div>
                    <label for="persona1-name"><strong>Persona:</strong></label>
                    <select name="persona1-name" id="persona1-name" class="innormal" style="width: 200px; margin-left: 10px;">
                        <option value="">Select Persona</option>
                    </select>
                </div>
            </div>

            <div class="arcana-selector">
                <h3 style="color: #2c3e50; margin-bottom: 15px;">🎭 Select Second Persona</h3>
                <div style="margin-bottom: 15px;">
                    <label for="persona2-arcana"><strong>Arcana:</strong></label>
                    <select name="persona2-arcana" id="persona2-arcana" class="innormal" style="width: 200px; margin-left: 10px;">
                        <option value="">Select Arcana</option>
                        <option value="fool">Fool</option>
                        <option value="magician">Magician</option>
                        <option value="priestess">Priestess</option>
                        <option value="empress">Empress</option>
                        <option value="emperor">Emperor</option>
                        <option value="hierophant">Hierophant</option>
                        <option value="lovers">Lovers</option>
                        <option value="chariot">Chariot</option>
                        <option value="justice">Justice</option>
                        <option value="hermit">Hermit</option>
                        <option value="fortune">Fortune</option>
                        <option value="strength">Strength</option>
                        <option value="hanged">Hanged Man</option>
                        <option value="death">Death</option>
                        <option value="temperance">Temperance</option>
                        <option value="devil">Devil</option>
                        <option value="tower">Tower</option>
                        <option value="star">Star</option>
                        <option value="moon">Moon</option>
                        <option value="sun">Sun</option>
                        <option value="judgement">Judgement</option>
                        <option value="world">World</option>
                    </select>
                </div>
                <div>
                    <label for="persona2-name"><strong>Persona:</strong></label>
                    <select name="persona2-name" id="persona2-name" class="innormal" style="width: 200px; margin-left: 10px;">
                        <option value="">Select Persona</option>
                    </select>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Fusion" id="calculate-fusion-btn" style="padding: 15px 30px; font-size: 18px; background-color: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Clear Selection" id="clear-fusion-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Fusion results are based on Persona 3 Reload mechanics. Some special fusion requirements may apply.
        </div>
        
        <div id="fusion-results" style="display: none; margin-top: 30px;">
            <div class="fusion-result">
                <h3 style="color: #2c3e50; margin-bottom: 20px;">✨ Fusion Result</h3>
                <div id="fusion-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-fusion-btn');
    const clearBtn = document.getElementById('clear-fusion-btn');
    const resultsDiv = document.getElementById('fusion-results');
    const resultsContent = document.getElementById('fusion-results-content');
    
    // Sample Persona data (simplified for demonstration)
    const personaData = {
        fool: [
            { name: 'Orpheus', level: 1, skills: ['Agi', 'Bash'] },
            { name: 'Slime', level: 3, skills: ['Bounce', 'Tarukaja'] },
            { name: 'Legion', level: 22, skills: ['Evil Touch', 'Curse Boost'] }
        ],
        magician: [
            { name: 'Apsaras', level: 3, skills: ['Agi', 'Media'] },
            { name: 'Unicorn', level: 11, skills: ['Garu', 'Diarama'] },
            { name: 'Surt', level: 52, skills: ['Ragnarok', 'Fire Amp'] }
        ],
        priestess: [
            { name: 'Pixie', level: 2, skills: ['Zio', 'Dia'] },
            { name: 'Angel', level: 4, skills: ['Garu', 'Bless'] },
            { name: 'Scathach', level: 45, skills: ['Vorpal Blade', 'Charge'] }
        ],
        empress: [
            { name: 'Leanan Sidhe', level: 6, skills: ['Agi', 'Rakukaja'] },
            { name: 'Hariti', level: 40, skills: ['Samarecarm', 'Mediarama'] },
            { name: 'Mother Harlot', level: 63, skills: ['Mabufudyne', 'Ice Amp'] }
        ],
        emperor: [
            { name: 'Forneus', level: 7, skills: ['Bufu', 'Tarukaja'] },
            { name: 'Oberon', level: 15, skills: ['Magaru', 'Sukukaja'] },
            { name: 'Odin', level: 57, skills: ['Thunder Reign', 'Elec Amp'] }
        ]
    };
    
    // Fusion chart (simplified)
    const fusionChart = {
        'fool+magician': 'priestess',
        'fool+priestess': 'magician',
        'magician+priestess': 'empress',
        'magician+empress': 'emperor',
        'priestess+empress': 'hierophant',
        'empress+emperor': 'lovers',
        'emperor+hierophant': 'chariot'
    };
    
    // Populate persona dropdowns when arcana is selected
    document.getElementById('persona1-arcana').addEventListener('change', function() {
        populatePersonaDropdown('persona1-name', this.value);
    });
    
    document.getElementById('persona2-arcana').addEventListener('change', function() {
        populatePersonaDropdown('persona2-name', this.value);
    });
    
    function populatePersonaDropdown(dropdownId, arcana) {
        const dropdown = document.getElementById(dropdownId);
        dropdown.innerHTML = '<option value="">Select Persona</option>';
        
        if (arcana && personaData[arcana]) {
            personaData[arcana].forEach(persona => {
                const option = document.createElement('option');
                option.value = persona.name.toLowerCase();
                option.textContent = `${persona.name} (Lv.${persona.level})`;
                dropdown.appendChild(option);
            });
        }
    }
    
    calculateBtn.addEventListener('click', function() {
        const arcana1 = document.getElementById('persona1-arcana').value;
        const arcana2 = document.getElementById('persona2-arcana').value;
        const persona1 = document.getElementById('persona1-name').value;
        const persona2 = document.getElementById('persona2-name').value;
        
        if (!arcana1 || !arcana2) {
            alert('Please select both Arcana types.');
            return;
        }
        
        if (!persona1 || !persona2) {
            alert('Please select both Personas.');
            return;
        }
        
        const fusionResult = calculateFusion(arcana1, arcana2, persona1, persona2);
        displayFusionResult(fusionResult, persona1, persona2);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('fusion-calculator-form').reset();
        document.getElementById('persona1-name').innerHTML = '<option value="">Select Persona</option>';
        document.getElementById('persona2-name').innerHTML = '<option value="">Select Persona</option>';
        resultsDiv.style.display = 'none';
    });
    
    function calculateFusion(arcana1, arcana2, persona1, persona2) {
        // Create fusion key
        const fusionKey1 = `${arcana1}+${arcana2}`;
        const fusionKey2 = `${arcana2}+${arcana1}`;
        
        let resultArcana = fusionChart[fusionKey1] || fusionChart[fusionKey2];
        
        if (!resultArcana) {
            // Default fusion logic
            const arcanaList = ['fool', 'magician', 'priestess', 'empress', 'emperor', 'hierophant', 'lovers', 'chariot'];
            const index1 = arcanaList.indexOf(arcana1);
            const index2 = arcanaList.indexOf(arcana2);
            
            if (index1 !== -1 && index2 !== -1) {
                const resultIndex = (index1 + index2) % arcanaList.length;
                resultArcana = arcanaList[resultIndex];
            } else {
                resultArcana = 'fool'; // Default
            }
        }
        
        // Get personas from result arcana
        const availablePersonas = personaData[resultArcana] || personaData.fool;
        const randomPersona = availablePersonas[Math.floor(Math.random() * availablePersonas.length)];
        
        return {
            arcana: resultArcana,
            persona: randomPersona,
            materials: [persona1, persona2]
        };
    }
    
    function displayFusionResult(result, persona1, persona2) {
        const arcanaClass = result.arcana;
        const capitalizedArcana = result.arcana.charAt(0).toUpperCase() + result.arcana.slice(1);
        const capitalizedPersona1 = persona1.charAt(0).toUpperCase() + persona1.slice(1);
        const capitalizedPersona2 = persona2.charAt(0).toUpperCase() + persona2.slice(1);
        
        let html = `
            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 20px;">
                <div class="persona-card" style="margin: 0 10px;">
                    <h4>${capitalizedPersona1}</h4>
                </div>
                <div style="font-size: 24px; margin: 0 15px;">+</div>
                <div class="persona-card" style="margin: 0 10px;">
                    <h4>${capitalizedPersona2}</h4>
                </div>
                <div style="font-size: 24px; margin: 0 15px;">=</div>
                <div class="persona-card" style="margin: 0 10px; background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%); color: #2c3e50;">
                    <h4>${result.persona.name}</h4>
                    <div class="arcana-badge ${arcanaClass}">${capitalizedArcana}</div>
                    <div style="margin-top: 10px;">Level ${result.persona.level}</div>
                </div>
            </div>
            
            <div style="background-color: white; border-radius: 10px; padding: 20px; margin: 20px 0; color: #2c3e50;">
                <h4>Inherited Skills:</h4>
                <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 10px;">
        `;
        
        result.persona.skills.forEach(skill => {
            html += `<span style="background-color: #667eea; color: white; padding: 5px 10px; border-radius: 15px; font-size: 14px;">${skill}</span>`;
        });
        
        html += `
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 15px; margin-top: 20px;">
                <h4 style="color: #2c3e50;">Fusion Tips:</h4>
                <ul style="color: #2c3e50; text-align: left;">
                    <li>Higher level Personas may inherit more skills</li>
                    <li>Some skills are exclusive to certain Arcana types</li>
                    <li>Special fusion requirements may apply for rare Personas</li>
                    <li>Check your Compendium for optimal fusion paths</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
