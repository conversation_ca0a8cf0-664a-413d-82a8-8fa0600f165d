<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.polytopia-interface {
    background: linear-gradient(135deg, #4a90e2 0%, #357abd 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.unit-selector {
    background-color: #f8f9fa;
    border: 2px solid #4a90e2;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.unit-card {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 15px;
    margin: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    width: 120px;
}
.unit-card:hover {
    border-color: #4a90e2;
    transform: translateY(-3px);
}
.unit-card.selected {
    border-color: #4a90e2;
    background-color: #e3f2fd;
}
.damage-result {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.terrain-modifier {
    background-color: #e8f5e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.tribe-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    margin: 3px;
    color: white;
}
.xinxi { background-color: #ff9800; }
.imperius { background-color: #2196f3; }
.bardur { background-color: #795548; }
.oumaji { background-color: #ffeb3b; color: #333; }
.kickoo { background-color: #00bcd4; }
.hoodrick { background-color: #4caf50; }
.luxidoor { background-color: #9c27b0; }
.vengir { background-color: #424242; }
.zebasi { background-color: #ff5722; }
.ai-mo { background-color: #e91e63; }
.quetzali { background-color: #8bc34a; }
.yadakk { background-color: #607d8b; }
.aquarion { background-color: #03a9f4; }
.elyrion { background-color: #673ab7; }
.polaris { background-color: #00e676; }
.cymanti { background-color: #ff1744; }
.health-bar {
    background-color: #e0e0e0;
    border-radius: 10px;
    height: 20px;
    margin: 10px 0;
    overflow: hidden;
}
.health-fill {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #8bc34a);
    border-radius: 10px;
    transition: width 0.5s ease;
}
.damage-breakdown {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
</style>

<h1>Polytopia Damage Calculator</h1>
<p>Calculate battle damage and outcomes in The Battle of Polytopia with our comprehensive damage calculator. Plan your attacks, predict battle results, and optimize your military strategies with accurate damage calculations for all units and terrain modifiers.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="polytopiaform" id="polytopia-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Damage Calculator</a></li>
                <li><a href="#" data-tab="units">Unit Guide</a></li>
                <li><a href="#" data-tab="strategy">Battle Strategy</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="polytopia-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">⚔️ Polytopia Battle Calculator</h3>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div class="unit-selector">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🗡️ Attacking Unit</h4>
                        <div style="text-align: center; margin-bottom: 15px;">
                            <div class="unit-card" data-unit="warrior">
                                <div style="font-size: 24px;">⚔️</div>
                                <div><strong>Warrior</strong></div>
                                <div style="font-size: 12px;">ATK: 2, DEF: 2</div>
                            </div>
                            <div class="unit-card" data-unit="rider">
                                <div style="font-size: 24px;">🐎</div>
                                <div><strong>Rider</strong></div>
                                <div style="font-size: 12px;">ATK: 2, DEF: 1</div>
                            </div>
                            <div class="unit-card" data-unit="archer">
                                <div style="font-size: 24px;">🏹</div>
                                <div><strong>Archer</strong></div>
                                <div style="font-size: 12px;">ATK: 2, DEF: 1</div>
                            </div>
                            <div class="unit-card" data-unit="swordsman">
                                <div style="font-size: 24px;">🗡️</div>
                                <div><strong>Swordsman</strong></div>
                                <div style="font-size: 12px;">ATK: 3, DEF: 3</div>
                            </div>
                            <div class="unit-card" data-unit="catapult">
                                <div style="font-size: 24px;">🎯</div>
                                <div><strong>Catapult</strong></div>
                                <div style="font-size: 12px;">ATK: 4, DEF: 0</div>
                            </div>
                            <div class="unit-card" data-unit="knight">
                                <div style="font-size: 24px;">🛡️</div>
                                <div><strong>Knight</strong></div>
                                <div style="font-size: 12px;">ATK: 3.5, DEF: 1</div>
                            </div>
                            <div class="unit-card" data-unit="giant">
                                <div style="font-size: 24px;">👹</div>
                                <div><strong>Giant</strong></div>
                                <div style="font-size: 12px;">ATK: 5, DEF: 4</div>
                            </div>
                            <div class="unit-card" data-unit="battleship">
                                <div style="font-size: 24px;">🚢</div>
                                <div><strong>Battleship</strong></div>
                                <div style="font-size: 12px;">ATK: 4, DEF: 3</div>
                            </div>
                        </div>
                        
                        <div>
                            <label for="attacker-health"><strong>Current Health:</strong></label>
                            <input type="number" id="attacker-health" value="10" min="1" max="40" class="innormal" style="width: 80px; margin-left: 10px;">
                            <span style="margin-left: 10px;">/ <span id="attacker-max-health">10</span></span>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="attacker-veteran">
                                <input type="checkbox" id="attacker-veteran" style="margin-right: 8px;">
                                <strong>Veteran (+50% ATK & DEF)</strong>
                            </label>
                        </div>
                    </div>
                    
                    <div class="unit-selector">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🛡️ Defending Unit</h4>
                        <div style="text-align: center; margin-bottom: 15px;">
                            <div class="unit-card" data-unit="warrior" data-side="defender">
                                <div style="font-size: 24px;">⚔️</div>
                                <div><strong>Warrior</strong></div>
                                <div style="font-size: 12px;">ATK: 2, DEF: 2</div>
                            </div>
                            <div class="unit-card" data-unit="rider" data-side="defender">
                                <div style="font-size: 24px;">🐎</div>
                                <div><strong>Rider</strong></div>
                                <div style="font-size: 12px;">ATK: 2, DEF: 1</div>
                            </div>
                            <div class="unit-card" data-unit="archer" data-side="defender">
                                <div style="font-size: 24px;">🏹</div>
                                <div><strong>Archer</strong></div>
                                <div style="font-size: 12px;">ATK: 2, DEF: 1</div>
                            </div>
                            <div class="unit-card" data-unit="swordsman" data-side="defender">
                                <div style="font-size: 24px;">🗡️</div>
                                <div><strong>Swordsman</strong></div>
                                <div style="font-size: 12px;">ATK: 3, DEF: 3</div>
                            </div>
                            <div class="unit-card" data-unit="catapult" data-side="defender">
                                <div style="font-size: 24px;">🎯</div>
                                <div><strong>Catapult</strong></div>
                                <div style="font-size: 12px;">ATK: 4, DEF: 0</div>
                            </div>
                            <div class="unit-card" data-unit="knight" data-side="defender">
                                <div style="font-size: 24px;">🛡️</div>
                                <div><strong>Knight</strong></div>
                                <div style="font-size: 12px;">ATK: 3.5, DEF: 1</div>
                            </div>
                            <div class="unit-card" data-unit="giant" data-side="defender">
                                <div style="font-size: 24px;">👹</div>
                                <div><strong>Giant</strong></div>
                                <div style="font-size: 12px;">ATK: 5, DEF: 4</div>
                            </div>
                            <div class="unit-card" data-unit="battleship" data-side="defender">
                                <div style="font-size: 24px;">🚢</div>
                                <div><strong>Battleship</strong></div>
                                <div style="font-size: 12px;">ATK: 4, DEF: 3</div>
                            </div>
                        </div>
                        
                        <div>
                            <label for="defender-health"><strong>Current Health:</strong></label>
                            <input type="number" id="defender-health" value="10" min="1" max="40" class="innormal" style="width: 80px; margin-left: 10px;">
                            <span style="margin-left: 10px;">/ <span id="defender-max-health">10</span></span>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="defender-veteran">
                                <input type="checkbox" id="defender-veteran" style="margin-right: 8px;">
                                <strong>Veteran (+50% ATK & DEF)</strong>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="terrain-modifier">
                <h4 style="color: #2c3e50; margin-bottom: 20px;">🌍 Battle Conditions</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                    <div>
                        <label for="defender-terrain"><strong>Defender's Terrain:</strong></label>
                        <select id="defender-terrain" class="innormal" style="width: 100%; margin-top: 5px;">
                            <option value="plain">Plain (No bonus)</option>
                            <option value="forest">Forest (+1 DEF)</option>
                            <option value="mountain">Mountain (+4 DEF)</option>
                            <option value="city">City (+4 DEF)</option>
                            <option value="village">Village (+2 DEF)</option>
                            <option value="water">Water (Naval only)</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="wall-bonus">
                            <input type="checkbox" id="wall-bonus" style="margin-right: 8px;">
                            <strong>City Wall (+2 DEF)</strong>
                        </label>
                        
                        <div style="margin-top: 15px;">
                            <label for="fortify-bonus">
                                <input type="checkbox" id="fortify-bonus" style="margin-right: 8px;">
                                <strong>Fortified (+1 DEF)</strong>
                            </label>
                        </div>
                    </div>
                    
                    <div>
                        <label for="retaliation">
                            <input type="checkbox" id="retaliation" checked style="margin-right: 8px;">
                            <strong>Defender Retaliates</strong>
                        </label>
                        
                        <div style="margin-top: 15px;">
                            <label for="first-strike">
                                <input type="checkbox" id="first-strike" style="margin-right: 8px;">
                                <strong>Attacker First Strike</strong>
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Battle" id="calculate-damage-btn" style="padding: 15px 30px; font-size: 18px; background-color: #4a90e2; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Battle" id="clear-damage-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Calculations are based on Polytopia battle mechanics. Results may vary due to game updates and special unit abilities.
        </div>
        
        <div id="damage-results" style="display: none; margin-top: 30px;">
            <div class="damage-result">
                <h3 style="margin-bottom: 20px;">⚡ Battle Results</h3>
                <div id="damage-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const unitCards = document.querySelectorAll('.unit-card');
    const calculateBtn = document.getElementById('calculate-damage-btn');
    const clearBtn = document.getElementById('clear-damage-btn');
    const resultsDiv = document.getElementById('damage-results');
    const resultsContent = document.getElementById('damage-results-content');
    
    let selectedAttacker = null;
    let selectedDefender = null;
    
    // Unit data
    const unitData = {
        warrior: { name: 'Warrior', attack: 2, defense: 2, health: 10, emoji: '⚔️' },
        rider: { name: 'Rider', attack: 2, defense: 1, health: 10, emoji: '🐎' },
        archer: { name: 'Archer', attack: 2, defense: 1, health: 10, emoji: '🏹' },
        swordsman: { name: 'Swordsman', attack: 3, defense: 3, health: 15, emoji: '🗡️' },
        catapult: { name: 'Catapult', attack: 4, defense: 0, health: 10, emoji: '🎯' },
        knight: { name: 'Knight', attack: 3.5, defense: 1, health: 15, emoji: '🛡️' },
        giant: { name: 'Giant', attack: 5, defense: 4, health: 40, emoji: '👹' },
        battleship: { name: 'Battleship', attack: 4, defense: 3, health: 20, emoji: '🚢' }
    };
    
    // Terrain bonuses
    const terrainBonuses = {
        plain: 0,
        forest: 1,
        mountain: 4,
        city: 4,
        village: 2,
        water: 0
    };
    
    // Handle unit selection
    unitCards.forEach(card => {
        card.addEventListener('click', function() {
            const unit = this.dataset.unit;
            const side = this.dataset.side;
            
            if (side === 'defender') {
                document.querySelectorAll('.unit-card[data-side="defender"]').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                selectedDefender = unit;
                document.getElementById('defender-health').value = unitData[unit].health;
                document.getElementById('defender-max-health').textContent = unitData[unit].health;
            } else {
                document.querySelectorAll('.unit-card:not([data-side="defender"])').forEach(c => c.classList.remove('selected'));
                this.classList.add('selected');
                selectedAttacker = unit;
                document.getElementById('attacker-health').value = unitData[unit].health;
                document.getElementById('attacker-max-health').textContent = unitData[unit].health;
            }
        });
    });
    
    calculateBtn.addEventListener('click', function() {
        if (!selectedAttacker || !selectedDefender) {
            alert('Please select both attacking and defending units.');
            return;
        }
        
        const battleResult = calculateBattle();
        displayResults(battleResult);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        selectedAttacker = null;
        selectedDefender = null;
        unitCards.forEach(c => c.classList.remove('selected'));
        document.getElementById('polytopia-calculator-form').reset();
        document.getElementById('retaliation').checked = true;
        resultsDiv.style.display = 'none';
    });
    
    function calculateBattle() {
        const attacker = unitData[selectedAttacker];
        const defender = unitData[selectedDefender];
        
        // Get current health
        const attackerHealth = parseInt(document.getElementById('attacker-health').value);
        const defenderHealth = parseInt(document.getElementById('defender-health').value);
        
        // Calculate effective stats
        let attackerAttack = attacker.attack;
        let attackerDefense = attacker.defense;
        let defenderAttack = defender.attack;
        let defenderDefense = defender.defense;
        
        // Veteran bonuses
        if (document.getElementById('attacker-veteran').checked) {
            attackerAttack *= 1.5;
            attackerDefense *= 1.5;
        }
        
        if (document.getElementById('defender-veteran').checked) {
            defenderAttack *= 1.5;
            defenderDefense *= 1.5;
        }
        
        // Terrain and fortification bonuses
        const terrain = document.getElementById('defender-terrain').value;
        defenderDefense += terrainBonuses[terrain];
        
        if (document.getElementById('wall-bonus').checked) {
            defenderDefense += 2;
        }
        
        if (document.getElementById('fortify-bonus').checked) {
            defenderDefense += 1;
        }
        
        // Calculate damage
        const attackerDamage = Math.max(0, Math.round((attackerAttack * attackerHealth / attacker.health) - defenderDefense));
        const defenderDamage = document.getElementById('retaliation').checked ? 
            Math.max(0, Math.round((defenderAttack * defenderHealth / defender.health) - attackerDefense)) : 0;
        
        // Calculate final health
        const defenderFinalHealth = Math.max(0, defenderHealth - attackerDamage);
        const attackerFinalHealth = Math.max(0, attackerHealth - defenderDamage);
        
        // Determine winner
        let winner = 'Draw';
        if (defenderFinalHealth === 0 && attackerFinalHealth > 0) {
            winner = 'Attacker';
        } else if (attackerFinalHealth === 0 && defenderFinalHealth > 0) {
            winner = 'Defender';
        } else if (defenderFinalHealth > 0 && attackerFinalHealth > 0) {
            winner = 'Both Survive';
        }
        
        return {
            attacker: { ...attacker, type: selectedAttacker, health: attackerHealth, finalHealth: attackerFinalHealth, damage: attackerDamage, effectiveAttack: attackerAttack, effectiveDefense: attackerDefense },
            defender: { ...defender, type: selectedDefender, health: defenderHealth, finalHealth: defenderFinalHealth, damage: defenderDamage, effectiveAttack: defenderAttack, effectiveDefense: defenderDefense },
            winner: winner,
            terrain: terrain,
            retaliation: document.getElementById('retaliation').checked
        };
    }
    
    function displayResults(battle) {
        let html = `
            <div style="display: flex; justify-content: space-around; align-items: center; margin-bottom: 25px;">
                <div style="text-align: center;">
                    <div style="font-size: 48px;">${battle.attacker.emoji}</div>
                    <h4>${battle.attacker.name}</h4>
                    <div style="font-size: 18px; margin: 10px 0;">
                        ${battle.attacker.health} → ${battle.attacker.finalHealth} HP
                    </div>
                    <div class="health-bar" style="width: 150px;">
                        <div class="health-fill" style="width: ${(battle.attacker.finalHealth / battle.attacker.health) * 100}%"></div>
                    </div>
                </div>
                
                <div style="text-align: center; font-size: 24px; font-weight: bold;">
                    VS
                </div>
                
                <div style="text-align: center;">
                    <div style="font-size: 48px;">${battle.defender.emoji}</div>
                    <h4>${battle.defender.name}</h4>
                    <div style="font-size: 18px; margin: 10px 0;">
                        ${battle.defender.health} → ${battle.defender.finalHealth} HP
                    </div>
                    <div class="health-bar" style="width: 150px;">
                        <div class="health-fill" style="width: ${(battle.defender.finalHealth / battle.defender.health) * 100}%"></div>
                    </div>
                </div>
            </div>
            
            <div style="font-size: 32px; font-weight: bold; margin: 20px 0; text-align: center;">
                Winner: ${battle.winner}
            </div>
            
            <div class="damage-breakdown">
                <h4 style="margin-bottom: 20px;">📊 Battle Breakdown</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h5>Attacker (${battle.attacker.name}):</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Effective Attack:</strong> ${battle.attacker.effectiveAttack.toFixed(1)}</li>
                            <li><strong>Effective Defense:</strong> ${battle.attacker.effectiveDefense.toFixed(1)}</li>
                            <li><strong>Damage Dealt:</strong> ${battle.attacker.damage}</li>
                            <li><strong>Damage Taken:</strong> ${battle.defender.damage}</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5>Defender (${battle.defender.name}):</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Effective Attack:</strong> ${battle.defender.effectiveAttack.toFixed(1)}</li>
                            <li><strong>Effective Defense:</strong> ${battle.defender.effectiveDefense.toFixed(1)}</li>
                            <li><strong>Damage Dealt:</strong> ${battle.defender.damage}</li>
                            <li><strong>Damage Taken:</strong> ${battle.attacker.damage}</li>
                            <li><strong>Terrain:</strong> ${battle.terrain.charAt(0).toUpperCase() + battle.terrain.slice(1)}</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 Battle Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Terrain Advantage:</strong> Use mountains and cities for maximum defense</li>
                    <li><strong>Veteran Units:</strong> 50% bonus to both attack and defense</li>
                    <li><strong>Health Scaling:</strong> Damage scales with current health percentage</li>
                    <li><strong>First Strike:</strong> Some units can attack without retaliation</li>
                    <li><strong>Unit Counters:</strong> Knights are effective against most units</li>
                    <li><strong>Positioning:</strong> Attack from advantageous terrain when possible</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
