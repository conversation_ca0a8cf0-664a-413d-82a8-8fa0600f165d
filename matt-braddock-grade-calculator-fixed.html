<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title><PERSON> Grade Calculator</title>
    <meta name="description" content="Calculate your final grades, GPA, and academic performance with <PERSON>'s comprehensive grade calculator. Track assignments, exams, and overall course performance.">
    <link rel="stylesheet" href="./Carbohydrate Calculator_files/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="apple-touch-icon" sizes="180x180" href="https://d26tpo4cm8sb6k.cloudfront.net/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-16x16.png">
    <link rel="manifest" href="https://www.calculator.net/manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="https://www.calculator.net/"><img src="./Carbohydrate Calculator_files/calculator-white.svg" width="208" height="22" alt="Calculator.net"></a></div>
        <div id="login"><a href="https://www.calculator.net/my-account/sign-in.php">sign in</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/" itemprop="item"><span itemprop="name">home</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/math-calculator.html" itemprop="item"><span itemprop="name">math</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="#" itemprop="item"><span itemprop="name">grade calculator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="window.print();return false;">Print</a></div>
        
        <h1>Matt Braddock Grade Calculator</h1>
        <p>Calculate your final grades, GPA, and academic performance with this comprehensive grade calculator. Track assignments, exams, quizzes, and overall course performance to determine your current standing and what you need to achieve your target grade.</p>
        
        <div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
        <span id="ucframe"></span>
        
        <div id="topmenu" class="topmenucenter">
            <ul>
                <li id="menuon"><a href="#" onclick="return showTab('final-grade');">Final Grade</a></li>
                <li><a href="#" onclick="return showTab('gpa');">GPA Calculator</a></li>
                <li><a href="#" onclick="return showTab('weighted');">Weighted Grade</a></li>
            </ul>
        </div>

        <div class="panel2" style="padding:10px;">
            <form name="gradeform" id="grade-calculator-form">
                <!-- Final Grade Calculator Tab -->
                <div id="final-grade-tab" style="display: block;">
                    <table id="grade-input-table" width="620" bgcolor="#eeeeee">
                        <tbody>
                            <tr>
                                <td width="120">Current Grade:</td>
                                <td width="500"><input type="number" name="current_grade" id="current_grade" value="85" class="innormal" min="0" max="100" step="0.1">%</td>
                            </tr>
                            <tr>
                                <td>Current Weight:</td>
                                <td><input type="number" name="current_weight" id="current_weight" value="70" class="innormal" min="0" max="100" step="1">%</td>
                            </tr>
                            <tr>
                                <td>Final Exam Weight:</td>
                                <td><input type="number" name="final_weight" id="final_weight" value="30" class="innormal" min="0" max="100" step="1">%</td>
                            </tr>
                            <tr>
                                <td>Desired Grade:</td>
                                <td><input type="number" name="desired_grade" id="desired_grade" value="90" class="innormal" min="0" max="100" step="0.1">%</td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td>
                                    <input type="submit" name="x" value="Calculate Grade" id="calculate-btn">
                                    <input type="button" value="Clear" id="clear-btn" onclick="clearGradeForm();">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- GPA Calculator Tab -->
                <div id="gpa-tab" style="display: none;">
                    <table id="gpa-input-table" width="620" bgcolor="#eeeeee">
                        <tbody>
                            <tr>
                                <td width="150"><strong>Course</strong></td>
                                <td width="100"><strong>Grade</strong></td>
                                <td width="100"><strong>Credits</strong></td>
                                <td width="100"><strong>Points</strong></td>
                                <td width="170"><strong>Action</strong></td>
                            </tr>
                            <tr id="course-row-1">
                                <td><input type="text" name="course1" value="Mathematics" class="inlonger"></td>
                                <td>
                                    <select name="coursegrade1" class="innormal" onchange="updateGPAPoints(1)">
                                        <option value="4.0">A</option>
                                        <option value="3.7">A-</option>
                                        <option value="3.3">B+</option>
                                        <option value="3.0" selected>B</option>
                                        <option value="2.7">B-</option>
                                        <option value="2.3">C+</option>
                                        <option value="2.0">C</option>
                                        <option value="1.7">C-</option>
                                        <option value="1.3">D+</option>
                                        <option value="1.0">D</option>
                                        <option value="0.0">F</option>
                                    </select>
                                </td>
                                <td><input type="number" name="credits1" value="3" class="innormal" min="0" max="10" step="0.5"></td>
                                <td><span id="gpa-points-1">3.0</span></td>
                                <td><input type="button" value="Remove" onclick="removeCourse(1)" style="font-size: 12px; padding: 3px 6px;"></td>
                            </tr>
                            <tr id="course-row-2">
                                <td><input type="text" name="course2" value="English" class="inlonger"></td>
                                <td>
                                    <select name="coursegrade2" class="innormal" onchange="updateGPAPoints(2)">
                                        <option value="4.0" selected>A</option>
                                        <option value="3.7">A-</option>
                                        <option value="3.3">B+</option>
                                        <option value="3.0">B</option>
                                        <option value="2.7">B-</option>
                                        <option value="2.3">C+</option>
                                        <option value="2.0">C</option>
                                        <option value="1.7">C-</option>
                                        <option value="1.3">D+</option>
                                        <option value="1.0">D</option>
                                        <option value="0.0">F</option>
                                    </select>
                                </td>
                                <td><input type="number" name="credits2" value="3" class="innormal" min="0" max="10" step="0.5"></td>
                                <td><span id="gpa-points-2">4.0</span></td>
                                <td><input type="button" value="Remove" onclick="removeCourse(2)" style="font-size: 12px; padding: 3px 6px;"></td>
                            </tr>
                            <tr id="course-row-3">
                                <td><input type="text" name="course3" value="Science" class="inlonger"></td>
                                <td>
                                    <select name="coursegrade3" class="innormal" onchange="updateGPAPoints(3)">
                                        <option value="4.0">A</option>
                                        <option value="3.7" selected>A-</option>
                                        <option value="3.3">B+</option>
                                        <option value="3.0">B</option>
                                        <option value="2.7">B-</option>
                                        <option value="2.3">C+</option>
                                        <option value="2.0">C</option>
                                        <option value="1.7">C-</option>
                                        <option value="1.3">D+</option>
                                        <option value="1.0">D</option>
                                        <option value="0.0">F</option>
                                    </select>
                                </td>
                                <td><input type="number" name="credits3" value="4" class="innormal" min="0" max="10" step="0.5"></td>
                                <td><span id="gpa-points-3">3.7</span></td>
                                <td><input type="button" value="Remove" onclick="removeCourse(3)" style="font-size: 12px; padding: 3px 6px;"></td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td colspan="4">
                                    <input type="button" value="Add Course" id="add-course-btn" onclick="addCourse();">
                                    <input type="button" value="Calculate GPA" id="calculate-gpa-btn" onclick="calculateGPA();">
                                    <input type="button" value="Clear All" onclick="clearGPAForm();">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Weighted Grade Calculator Tab -->
                <div id="weighted-tab" style="display: none;">
                    <table id="weighted-input-table" width="620" bgcolor="#eeeeee">
                        <tbody>
                            <tr>
                                <td width="150"><strong>Assignment</strong></td>
                                <td width="100"><strong>Grade (%)</strong></td>
                                <td width="100"><strong>Weight (%)</strong></td>
                                <td width="100"><strong>Category</strong></td>
                                <td width="170"><strong>Action</strong></td>
                            </tr>
                            <tr id="assignment-row-1">
                                <td><input type="text" name="assignment1" value="Midterm Exam" class="inlonger"></td>
                                <td><input type="number" name="grade1" value="88" class="innormal" min="0" max="100" step="0.1"></td>
                                <td><input type="number" name="weight1" value="25" class="innormal" min="0" max="100" step="1"></td>
                                <td>
                                    <select name="category1" class="inlong">
                                        <option value="exam" selected>Exam</option>
                                        <option value="quiz">Quiz</option>
                                        <option value="homework">Homework</option>
                                        <option value="project">Project</option>
                                        <option value="participation">Participation</option>
                                    </select>
                                </td>
                                <td><input type="button" value="Remove" onclick="removeAssignment(1)" style="font-size: 12px; padding: 3px 6px;"></td>
                            </tr>
                            <tr id="assignment-row-2">
                                <td><input type="text" name="assignment2" value="Homework Avg" class="inlonger"></td>
                                <td><input type="number" name="grade2" value="92" class="innormal" min="0" max="100" step="0.1"></td>
                                <td><input type="number" name="weight2" value="30" class="innormal" min="0" max="100" step="1"></td>
                                <td>
                                    <select name="category2" class="inlong">
                                        <option value="exam">Exam</option>
                                        <option value="quiz">Quiz</option>
                                        <option value="homework" selected>Homework</option>
                                        <option value="project">Project</option>
                                        <option value="participation">Participation</option>
                                    </select>
                                </td>
                                <td><input type="button" value="Remove" onclick="removeAssignment(2)" style="font-size: 12px; padding: 3px 6px;"></td>
                            </tr>
                            <tr id="assignment-row-3">
                                <td><input type="text" name="assignment3" value="Quiz Average" class="inlonger"></td>
                                <td><input type="number" name="grade3" value="85" class="innormal" min="0" max="100" step="0.1"></td>
                                <td><input type="number" name="weight3" value="15" class="innormal" min="0" max="100" step="1"></td>
                                <td>
                                    <select name="category3" class="inlong">
                                        <option value="exam">Exam</option>
                                        <option value="quiz" selected>Quiz</option>
                                        <option value="homework">Homework</option>
                                        <option value="project">Project</option>
                                        <option value="participation">Participation</option>
                                    </select>
                                </td>
                                <td><input type="button" value="Remove" onclick="removeAssignment(3)" style="font-size: 12px; padding: 3px 6px;"></td>
                            </tr>
                            <tr>
                                <td>&nbsp;</td>
                                <td colspan="4">
                                    <input type="button" value="Add Assignment" onclick="addAssignment();">
                                    <input type="button" value="Calculate Overall" onclick="calculateWeighted();">
                                    <input type="button" value="Clear All" onclick="clearWeightedForm();">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </form>
        </div>

        <ul style="margin-top:0px;margin-left:-20px;">
            <li class="smalltext"><b>Final Grade:</b> Calculate what you need on your final exam to achieve your desired grade.</li>
            <li class="smalltext"><b>GPA Calculator:</b> Calculate your Grade Point Average based on course grades and credit hours.</li>
            <li class="smalltext"><b>Weighted Grade:</b> Track multiple assignments with different weights and categories.</li>
        </ul>

        <!-- Results Display Area -->
        <div id="grade-results" style="display: none; margin-top: 20px;">
            <div class="h2result" id="grade-results-content"></div>
        </div>

        <br>
        <fieldset>
            <legend>Related</legend>
            <a href="#">Percentage Calculator</a> | 
            <a href="#">Average Calculator</a> | 
            <a href="#">Statistics Calculator</a>
        </fieldset>

        <br>
        <h3>Understanding Grade Calculations</h3>
        <p>Grade calculations involve different methods depending on your course structure. The <i>Final Grade Calculator</i> helps you determine what score you need on your final exam to achieve your desired overall grade, while the <i>GPA Calculator</i> converts letter grades to grade points and calculates your cumulative GPA.</p>

        <h3>Types of Grading Systems</h3>
        <p>Educational institutions use various grading systems to evaluate student performance:</p>
        <ul>
            <li><strong>Weighted Grading:</strong> Different assignments have different impacts on your final grade based on their importance.</li>
            <li><strong>Point-Based Systems:</strong> Each assignment is worth a certain number of points, and your grade is the percentage of total points earned.</li>
            <li><strong>Letter Grade Systems:</strong> Grades are assigned letters (A, B, C, D, F) which correspond to GPA points for cumulative calculations.</li>
        </ul>

        <h3>Grade Scale Reference</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Letter Grade</td>
                    <td class="cinfoHdL">Percentage Range</td>
                    <td class="cinfoHdL">GPA Points</td>
                    <td class="cinfoHdL">Description</td>
                </tr>
                <tr><td><strong>A</strong></td><td>93-100%</td><td>4.0</td><td>Excellent</td></tr>
                <tr><td><strong>A-</strong></td><td>90-92%</td><td>3.7</td><td>Very Good</td></tr>
                <tr><td><strong>B+</strong></td><td>87-89%</td><td>3.3</td><td>Good</td></tr>
                <tr><td><strong>B</strong></td><td>83-86%</td><td>3.0</td><td>Good</td></tr>
                <tr><td><strong>B-</strong></td><td>80-82%</td><td>2.7</td><td>Satisfactory</td></tr>
                <tr><td><strong>C+</strong></td><td>77-79%</td><td>2.3</td><td>Satisfactory</td></tr>
                <tr><td><strong>C</strong></td><td>73-76%</td><td>2.0</td><td>Satisfactory</td></tr>
                <tr><td><strong>C-</strong></td><td>70-72%</td><td>1.7</td><td>Below Average</td></tr>
                <tr><td><strong>D</strong></td><td>65-69%</td><td>1.0</td><td>Poor</td></tr>
                <tr><td><strong>F</strong></td><td>0-64%</td><td>0.0</td><td>Failing</td></tr>
            </tbody>
        </table>
    </div>
    
    <div id="right">
        <div style="padding-top:10px; min-height:280px; text-align:center;">
            <!-- Ad space placeholder -->
            <div style="width: 300px; height: 250px; background: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; color: #666;">
                Advertisement Space
            </div>
        </div>
        
        <form name="calcSearchForm" onsubmit="return false;" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td><input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators..."></td>
                        <td><span id="bluebtn" onclick="performSearch();">Search</span></td>
                    </tr>
                    <tr>
                        <td colspan="2"><div id="calcSearchOut"></div></td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="#">Math Calculators</a>
            </div>
            <div id="occontent">
                <a href="#">Grade Calculator</a>
                <a href="#">GPA Calculator</a>
                <a href="#">Percentage Calculator</a>
                <a href="#">Average Calculator</a>
                <a href="#">Fraction Calculator</a>
                <a href="#">Scientific Calculator</a>
                <a href="#">Statistics Calculator</a>
                <a href="#">Algebra Calculator</a>
                <a href="#">Geometry Calculator</a>
                <a href="#">More Math Calculators</a>
            </div>
            <div id="ocother">
                <a href="#">Financial</a> | 
                <a href="#">Fitness and Health</a> | 
                <a href="#">Math</a> | 
                <a href="#">Other</a>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="#">about us</a> | 
            <a href="#">sitemap</a> | 
            <a href="#">terms of use</a> | 
            <a href="#">privacy policy</a> &nbsp;  © 2008 - 2025 <a href="#">calculator.net</a>
        </div>
    </div>
</div>

<div class="topNavAbs">
    <a href="#">Financial</a>
    <a href="#">Fitness &amp; Health</a>
    <a href="#" class="topNavOn">Math</a>
    <a href="#">Other</a>
</div>

<script>
// Tab switching functionality
let currentTab = 'final-grade';
let courseCount = 3;
let assignmentCount = 3;

function showTab(tabName) {
    // Hide all tabs
    document.getElementById('final-grade-tab').style.display = 'none';
    document.getElementById('gpa-tab').style.display = 'none';
    document.getElementById('weighted-tab').style.display = 'none';

    // Show selected tab
    document.getElementById(tabName + '-tab').style.display = 'block';

    // Update menu
    const menuItems = document.querySelectorAll('#topmenu ul li');
    menuItems.forEach(item => {
        item.id = '';
        if (item.textContent.toLowerCase().includes(tabName.replace('-', ' '))) {
            item.id = 'menuon';
        }
    });

    currentTab = tabName;
    return false;
}

// Final Grade Calculator
document.getElementById('calculate-btn').addEventListener('click', function(e) {
    e.preventDefault();
    calculateFinalGrade();
});

function calculateFinalGrade() {
    const currentGrade = parseFloat(document.getElementById('current_grade').value) || 0;
    const currentWeight = parseFloat(document.getElementById('current_weight').value) || 0;
    const finalWeight = parseFloat(document.getElementById('final_weight').value) || 0;
    const desiredGrade = parseFloat(document.getElementById('desired_grade').value) || 0;

    // Validate inputs
    if (currentWeight + finalWeight !== 100) {
        alert('Current weight and final exam weight must add up to 100%');
        return;
    }

    // Calculate required final exam score
    const requiredFinal = (desiredGrade - (currentGrade * currentWeight / 100)) / (finalWeight / 100);
    const currentOverall = (currentGrade * currentWeight / 100);

    // Display results
    const resultsDiv = document.getElementById('grade-results-content');
    let resultText = `To achieve ${desiredGrade}% overall, you need ${requiredFinal.toFixed(1)}% on the final exam. `;
    resultText += `Your current grade contributes ${currentOverall.toFixed(1)} percentage points. `;
    resultText += requiredFinal <= 100 ? 'This goal is achievable!' : 'This may require extra credit.';

    resultsDiv.textContent = resultText;
    document.getElementById('grade-results').style.display = 'block';
}

function clearGradeForm() {
    document.getElementById('current_grade').value = '85';
    document.getElementById('current_weight').value = '70';
    document.getElementById('final_weight').value = '30';
    document.getElementById('desired_grade').value = '90';
    document.getElementById('grade-results').style.display = 'none';
}

// GPA Calculator Functions
function updateGPAPoints(courseNum) {
    const gradeSelect = document.querySelector(`select[name="coursegrade${courseNum}"]`);
    const pointsSpan = document.getElementById(`gpa-points-${courseNum}`);
    if (gradeSelect && pointsSpan) {
        pointsSpan.textContent = gradeSelect.value;
    }
}

function calculateGPA() {
    let totalPoints = 0;
    let totalCredits = 0;

    const rows = document.querySelectorAll('#gpa-input-table tbody tr');
    for (let i = 1; i < rows.length - 1; i++) { // Skip header and button rows
        const gradeSelect = rows[i].querySelector('select[name^="coursegrade"]');
        const creditsInput = rows[i].querySelector('input[name^="credits"]');

        if (gradeSelect && creditsInput && rows[i].style.display !== 'none') {
            const gradePoints = parseFloat(gradeSelect.value) || 0;
            const credits = parseFloat(creditsInput.value) || 0;

            totalPoints += gradePoints * credits;
            totalCredits += credits;
        }
    }

    if (totalCredits === 0) {
        alert('Please enter at least one course with credit hours');
        return;
    }

    const gpa = totalPoints / totalCredits;
    const gpaLevel = getGPALevel(gpa);

    const resultsDiv = document.getElementById('grade-results-content');
    resultsDiv.textContent = `Your GPA is ${gpa.toFixed(3)} based on ${totalCredits} credit hours. Academic Standing: ${gpaLevel}`;
    document.getElementById('grade-results').style.display = 'block';
}

function addCourse() {
    courseCount++;
    const table = document.getElementById('gpa-input-table').querySelector('tbody');
    const buttonRow = table.rows[table.rows.length - 1];
    const newRow = table.insertRow(table.rows.length - 1);
    newRow.id = 'course-row-' + courseCount;

    newRow.innerHTML = `
        <td><input type="text" name="course${courseCount}" class="inlonger" placeholder="Course name"></td>
        <td>
            <select name="coursegrade${courseCount}" class="innormal" onchange="updateGPAPoints(${courseCount})">
                <option value="4.0">A</option>
                <option value="3.7">A-</option>
                <option value="3.3">B+</option>
                <option value="3.0" selected>B</option>
                <option value="2.7">B-</option>
                <option value="2.3">C+</option>
                <option value="2.0">C</option>
                <option value="1.7">C-</option>
                <option value="1.3">D+</option>
                <option value="1.0">D</option>
                <option value="0.0">F</option>
            </select>
        </td>
        <td><input type="number" name="credits${courseCount}" value="3" class="innormal" min="0" max="10" step="0.5"></td>
        <td><span id="gpa-points-${courseCount}">3.0</span></td>
        <td><input type="button" value="Remove" onclick="removeCourse(${courseCount})" style="font-size: 12px; padding: 3px 6px;"></td>
    `;
}

function removeCourse(id) {
    const row = document.getElementById('course-row-' + id);
    if (row) {
        row.style.display = 'none';
    }
}

function clearGPAForm() {
    // Reset visible rows
    for (let i = 1; i <= 3; i++) {
        const row = document.getElementById('course-row-' + i);
        if (row) {
            row.style.display = '';
            row.querySelector('input[name^="course"]').value = i === 1 ? 'Mathematics' : i === 2 ? 'English' : 'Science';
            row.querySelector('select[name^="coursegrade"]').selectedIndex = i === 1 ? 3 : i === 2 ? 0 : 1;
            row.querySelector('input[name^="credits"]').value = i === 3 ? '4' : '3';
            updateGPAPoints(i);
        }
    }

    // Hide additional rows
    for (let i = 4; i <= courseCount; i++) {
        const row = document.getElementById('course-row-' + i);
        if (row) {
            row.style.display = 'none';
        }
    }

    document.getElementById('grade-results').style.display = 'none';
}

// Weighted Grade Calculator Functions
function calculateWeighted() {
    let totalWeightedScore = 0;
    let totalWeight = 0;

    const rows = document.querySelectorAll('#weighted-input-table tbody tr');
    for (let i = 1; i < rows.length - 1; i++) { // Skip header and button rows
        const gradeInput = rows[i].querySelector('input[name^="grade"]');
        const weightInput = rows[i].querySelector('input[name^="weight"]');

        if (gradeInput && weightInput && rows[i].style.display !== 'none') {
            const grade = parseFloat(gradeInput.value) || 0;
            const weight = parseFloat(weightInput.value) || 0;

            totalWeightedScore += grade * weight;
            totalWeight += weight;
        }
    }

    if (totalWeight === 0) {
        alert('Please enter at least one assignment with a weight greater than 0');
        return;
    }

    const overallGrade = totalWeightedScore / totalWeight;
    const letterGrade = getLetterGrade(overallGrade);

    const resultsDiv = document.getElementById('grade-results-content');
    resultsDiv.textContent = `Overall Grade: ${overallGrade.toFixed(2)}% (${letterGrade}) - Total Weight: ${totalWeight}%`;
    document.getElementById('grade-results').style.display = 'block';
}

function addAssignment() {
    assignmentCount++;
    const table = document.getElementById('weighted-input-table').querySelector('tbody');
    const buttonRow = table.rows[table.rows.length - 1];
    const newRow = table.insertRow(table.rows.length - 1);
    newRow.id = 'assignment-row-' + assignmentCount;

    newRow.innerHTML = `
        <td><input type="text" name="assignment${assignmentCount}" class="inlonger" placeholder="Assignment name"></td>
        <td><input type="number" name="grade${assignmentCount}" class="innormal" min="0" max="100" step="0.1" placeholder="Grade"></td>
        <td><input type="number" name="weight${assignmentCount}" class="innormal" min="0" max="100" step="1" placeholder="Weight"></td>
        <td>
            <select name="category${assignmentCount}" class="inlong">
                <option value="exam">Exam</option>
                <option value="quiz">Quiz</option>
                <option value="homework">Homework</option>
                <option value="project">Project</option>
                <option value="participation">Participation</option>
            </select>
        </td>
        <td><input type="button" value="Remove" onclick="removeAssignment(${assignmentCount})" style="font-size: 12px; padding: 3px 6px;"></td>
    `;
}

function removeAssignment(id) {
    const row = document.getElementById('assignment-row-' + id);
    if (row) {
        row.style.display = 'none';
    }
}

function clearWeightedForm() {
    // Reset visible rows
    for (let i = 1; i <= 3; i++) {
        const row = document.getElementById('assignment-row-' + i);
        if (row) {
            row.style.display = '';
            const assignments = ['Midterm Exam', 'Homework Avg', 'Quiz Average'];
            const grades = ['88', '92', '85'];
            const weights = ['25', '30', '15'];

            row.querySelector('input[name^="assignment"]').value = assignments[i-1];
            row.querySelector('input[name^="grade"]').value = grades[i-1];
            row.querySelector('input[name^="weight"]').value = weights[i-1];
            row.querySelector('select[name^="category"]').selectedIndex = i-1;
        }
    }

    // Hide additional rows
    for (let i = 4; i <= assignmentCount; i++) {
        const row = document.getElementById('assignment-row-' + i);
        if (row) {
            row.style.display = 'none';
        }
    }

    document.getElementById('grade-results').style.display = 'none';
}

// Utility functions
function getLetterGrade(percentage) {
    if (percentage >= 93) return 'A';
    if (percentage >= 90) return 'A-';
    if (percentage >= 87) return 'B+';
    if (percentage >= 83) return 'B';
    if (percentage >= 80) return 'B-';
    if (percentage >= 77) return 'C+';
    if (percentage >= 73) return 'C';
    if (percentage >= 70) return 'C-';
    if (percentage >= 65) return 'D';
    return 'F';
}

function getGPALevel(gpa) {
    if (gpa >= 3.8) return 'Summa Cum Laude';
    if (gpa >= 3.6) return 'Magna Cum Laude';
    if (gpa >= 3.4) return 'Cum Laude';
    if (gpa >= 3.0) return 'Good Standing';
    if (gpa >= 2.0) return 'Satisfactory';
    return 'Academic Probation';
}

function performSearch() {
    const searchTerm = document.getElementById('calcSearchTerm').value;
    const searchOut = document.getElementById('calcSearchOut');

    if (searchTerm.trim() === '') {
        searchOut.innerHTML = '';
        return;
    }

    // Simple search simulation
    const calculators = [
        'Grade Calculator', 'GPA Calculator', 'Percentage Calculator',
        'Average Calculator', 'Statistics Calculator', 'Scientific Calculator'
    ];

    const results = calculators.filter(calc =>
        calc.toLowerCase().includes(searchTerm.toLowerCase())
    );

    if (results.length > 0) {
        searchOut.innerHTML = '<div>' + results.map(result =>
            `<a href="#" style="display: block; padding: 2px 0;">${result}</a>`
        ).join('') + '</div>';
    } else {
        searchOut.innerHTML = '<div>No calculators found</div>';
    }
}

// Initialize
showTab('final-grade');
</script>

</body>
</html>
