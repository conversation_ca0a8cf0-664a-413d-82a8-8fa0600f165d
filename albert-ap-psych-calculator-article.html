<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.psych-interface {
    background: linear-gradient(135deg, #7b1fa2 0%, #4a148c 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.score-input {
    background-color: #f3e5f5;
    border: 2px solid #7b1fa2;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.ap-result {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.score-breakdown {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.ap-score-badge {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 16px;
    font-weight: bold;
    margin: 5px;
    color: white;
}
.score-5 { background-color: #4caf50; }
.score-4 { background-color: #8bc34a; }
.score-3 { background-color: #ff9800; }
.score-2 { background-color: #ff5722; }
.score-1 { background-color: #f44336; }
.topic-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
    margin: 20px 0;
}
.topic-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    color: #2c3e50;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.study-plan {
    background-color: #e8f5e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.progress-meter {
    background-color: #e0e0e0;
    border-radius: 10px;
    height: 20px;
    margin: 10px 0;
    overflow: hidden;
}
.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #f44336, #ff9800, #4caf50);
    border-radius: 10px;
    transition: width 0.5s ease;
}
.college-credit {
    background: linear-gradient(135deg, #ffd54f 0%, #ffb300 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    color: #2c3e50;
    text-align: center;
}
</style>

<h1>Albert AP Psychology Calculator</h1>
<p>Calculate your AP Psychology exam score and predict your performance with our comprehensive AP Psych calculator. Analyze your practice test results, track your progress across different psychology topics, and get personalized study recommendations for the AP Psychology exam.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="appsychform" id="ap-psych-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Score Calculator</a></li>
                <li><a href="#" data-tab="topics">Topic Analysis</a></li>
                <li><a href="#" data-tab="study">Study Plan</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="psych-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">🧠 AP Psychology Score Calculator</h3>
                
                <div class="score-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">📝 Practice Test Scores</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="mcq-correct"><strong>Multiple Choice Correct:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <input type="number" id="mcq-correct" value="75" min="0" max="100" class="innormal" style="width: 80px;">
                                <span style="margin-left: 10px;">out of 100 questions</span>
                            </div>
                        </div>
                        
                        <div>
                            <label for="frq-score"><strong>Free Response Score:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <input type="number" id="frq-score" value="12" min="0" max="14" class="innormal" style="width: 80px;">
                                <span style="margin-left: 10px;">out of 14 points</span>
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="test-date"><strong>Target Test Date:</strong></label>
                            <select id="test-date" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="may-2024">May 2024</option>
                                <option value="may-2025">May 2025</option>
                                <option value="practice">Practice Test</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="study-time"><strong>Weekly Study Hours:</strong></label>
                            <input type="number" id="study-time" value="5" min="1" max="20" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                    </div>
                </div>
                
                <div class="study-plan">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">📚 Topic Performance</h4>
                    
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px;">
                        <div>
                            <label for="biological-bases"><strong>Biological Bases (8-10%):</strong></label>
                            <select id="biological-bases" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="5">Excellent (90-100%)</option>
                                <option value="4">Good (80-89%)</option>
                                <option value="3" selected>Average (70-79%)</option>
                                <option value="2">Below Average (60-69%)</option>
                                <option value="1">Poor (Below 60%)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="sensation-perception"><strong>Sensation & Perception (6-8%):</strong></label>
                            <select id="sensation-perception" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="5">Excellent (90-100%)</option>
                                <option value="4">Good (80-89%)</option>
                                <option value="3" selected>Average (70-79%)</option>
                                <option value="2">Below Average (60-69%)</option>
                                <option value="1">Poor (Below 60%)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="learning"><strong>Learning (7-9%):</strong></label>
                            <select id="learning" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="5">Excellent (90-100%)</option>
                                <option value="4" selected>Good (80-89%)</option>
                                <option value="3">Average (70-79%)</option>
                                <option value="2">Below Average (60-69%)</option>
                                <option value="1">Poor (Below 60%)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="cognitive-psychology"><strong>Cognitive Psychology (13-17%):</strong></label>
                            <select id="cognitive-psychology" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="5">Excellent (90-100%)</option>
                                <option value="4">Good (80-89%)</option>
                                <option value="3" selected>Average (70-79%)</option>
                                <option value="2">Below Average (60-69%)</option>
                                <option value="1">Poor (Below 60%)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="developmental-psychology"><strong>Developmental Psychology (7-9%):</strong></label>
                            <select id="developmental-psychology" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="5">Excellent (90-100%)</option>
                                <option value="4">Good (80-89%)</option>
                                <option value="3" selected>Average (70-79%)</option>
                                <option value="2">Below Average (60-69%)</option>
                                <option value="1">Poor (Below 60%)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="personality"><strong>Personality (7-9%):</strong></label>
                            <select id="personality" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="5">Excellent (90-100%)</option>
                                <option value="4">Good (80-89%)</option>
                                <option value="3" selected>Average (70-79%)</option>
                                <option value="2">Below Average (60-69%)</option>
                                <option value="1">Poor (Below 60%)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="abnormal-psychology"><strong>Abnormal Psychology (7-9%):</strong></label>
                            <select id="abnormal-psychology" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="5">Excellent (90-100%)</option>
                                <option value="4">Good (80-89%)</option>
                                <option value="3" selected>Average (70-79%)</option>
                                <option value="2">Below Average (60-69%)</option>
                                <option value="1">Poor (Below 60%)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="treatment"><strong>Treatment (5-7%):</strong></label>
                            <select id="treatment" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="5">Excellent (90-100%)</option>
                                <option value="4">Good (80-89%)</option>
                                <option value="3" selected>Average (70-79%)</option>
                                <option value="2">Below Average (60-69%)</option>
                                <option value="1">Poor (Below 60%)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="social-psychology"><strong>Social Psychology (8-10%):</strong></label>
                            <select id="social-psychology" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="5">Excellent (90-100%)</option>
                                <option value="4">Good (80-89%)</option>
                                <option value="3" selected>Average (70-79%)</option>
                                <option value="2">Below Average (60-69%)</option>
                                <option value="1">Poor (Below 60%)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate AP Score" id="calculate-ap-btn" style="padding: 15px 30px; font-size: 18px; background-color: #7b1fa2; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Calculator" id="clear-ap-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Score predictions are based on historical AP Psychology scoring patterns. Actual exam results may vary.
        </div>
        
        <div id="ap-results" style="display: none; margin-top: 30px;">
            <div class="ap-result">
                <h3 style="margin-bottom: 20px;">📊 AP Psychology Score Prediction</h3>
                <div id="ap-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-ap-btn');
    const clearBtn = document.getElementById('clear-ap-btn');
    const resultsDiv = document.getElementById('ap-results');
    const resultsContent = document.getElementById('ap-results-content');
    
    // AP Psychology scoring data
    const apScoringData = {
        mcqWeight: 0.67, // Multiple choice is 67% of total score
        frqWeight: 0.33, // Free response is 33% of total score
        scoreThresholds: {
            5: 104, // Extremely well qualified
            4: 85,  // Well qualified
            3: 65,  // Qualified
            2: 50,  // Possibly qualified
            1: 0    // No recommendation
        }
    };
    
    // Topic weights for detailed analysis
    const topicWeights = {
        'biological-bases': 0.09,
        'sensation-perception': 0.07,
        'learning': 0.08,
        'cognitive-psychology': 0.15,
        'developmental-psychology': 0.08,
        'personality': 0.08,
        'abnormal-psychology': 0.08,
        'treatment': 0.06,
        'social-psychology': 0.09
    };
    
    calculateBtn.addEventListener('click', function() {
        const mcqCorrect = parseInt(document.getElementById('mcq-correct').value) || 0;
        const frqScore = parseInt(document.getElementById('frq-score').value) || 0;
        const testDate = document.getElementById('test-date').value;
        const studyTime = parseInt(document.getElementById('study-time').value) || 5;
        
        // Get topic performance scores
        const topicScores = {};
        Object.keys(topicWeights).forEach(topic => {
            topicScores[topic] = parseInt(document.getElementById(topic).value) || 3;
        });
        
        const apAnalysis = calculateAPScore(mcqCorrect, frqScore, topicScores, testDate, studyTime);
        displayResults(apAnalysis);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('ap-psych-calculator-form').reset();
        resultsDiv.style.display = 'none';
    });
    
    function calculateAPScore(mcqCorrect, frqScore, topicScores, testDate, studyTime) {
        // Calculate raw scores
        const mcqRawScore = mcqCorrect; // 1 point per correct answer
        const frqRawScore = frqScore; // Total FRQ points
        
        // Calculate composite score
        const mcqWeightedScore = mcqRawScore * apScoringData.mcqWeight;
        const frqWeightedScore = frqRawScore * (apScoringData.frqWeight * 100 / 14); // Scale FRQ to 100-point scale
        const compositeScore = mcqWeightedScore + frqWeightedScore;
        
        // Determine AP score
        let apScore = 1;
        for (let score = 5; score >= 1; score--) {
            if (compositeScore >= apScoringData.scoreThresholds[score]) {
                apScore = score;
                break;
            }
        }
        
        // Calculate topic analysis
        const topicAnalysis = analyzeTopicPerformance(topicScores);
        
        // Calculate improvement potential
        const improvementPotential = calculateImprovementPotential(mcqCorrect, frqScore, topicScores);
        
        // Generate study recommendations
        const studyRecommendations = generateStudyPlan(topicScores, studyTime, testDate);
        
        // Calculate college credit potential
        const collegeCreditInfo = getCollegeCreditInfo(apScore);
        
        return {
            mcqCorrect: mcqCorrect,
            frqScore: frqScore,
            compositeScore: compositeScore,
            apScore: apScore,
            mcqPercentage: (mcqCorrect / 100) * 100,
            frqPercentage: (frqScore / 14) * 100,
            topicAnalysis: topicAnalysis,
            improvementPotential: improvementPotential,
            studyRecommendations: studyRecommendations,
            collegeCreditInfo: collegeCreditInfo,
            testDate: testDate,
            studyTime: studyTime
        };
    }
    
    function analyzeTopicPerformance(topicScores) {
        const analysis = {
            strengths: [],
            weaknesses: [],
            averageScore: 0
        };
        
        let totalScore = 0;
        let totalWeight = 0;
        
        Object.entries(topicScores).forEach(([topic, score]) => {
            const weight = topicWeights[topic];
            totalScore += score * weight;
            totalWeight += weight;
            
            if (score >= 4) {
                analysis.strengths.push({
                    topic: topic.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                    score: score
                });
            } else if (score <= 2) {
                analysis.weaknesses.push({
                    topic: topic.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                    score: score
                });
            }
        });
        
        analysis.averageScore = totalScore / totalWeight;
        return analysis;
    }
    
    function calculateImprovementPotential(mcqCorrect, frqScore, topicScores) {
        const currentComposite = (mcqCorrect * apScoringData.mcqWeight) + 
                                (frqScore * (apScoringData.frqWeight * 100 / 14));
        
        // Calculate potential if weak areas improved
        const weakTopics = Object.entries(topicScores).filter(([topic, score]) => score <= 2);
        const improvementPoints = weakTopics.length * 5; // Estimate 5 points improvement per weak topic
        
        const potentialComposite = Math.min(150, currentComposite + improvementPoints);
        
        let potentialAPScore = 1;
        for (let score = 5; score >= 1; score--) {
            if (potentialComposite >= apScoringData.scoreThresholds[score]) {
                potentialAPScore = score;
                break;
            }
        }
        
        return {
            currentComposite: currentComposite,
            potentialComposite: potentialComposite,
            potentialAPScore: potentialAPScore,
            improvementNeeded: Math.max(0, apScoringData.scoreThresholds[Math.min(5, potentialAPScore + 1)] - currentComposite)
        };
    }
    
    function generateStudyPlan(topicScores, studyTime, testDate) {
        const plan = {
            priorityTopics: [],
            weeklySchedule: {},
            timeAllocation: {}
        };
        
        // Identify priority topics (lowest scores)
        const sortedTopics = Object.entries(topicScores)
            .sort(([,a], [,b]) => a - b)
            .slice(0, 3);
        
        sortedTopics.forEach(([topic, score]) => {
            plan.priorityTopics.push({
                topic: topic.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase()),
                score: score,
                timeNeeded: (5 - score) * 2 // Hours needed based on score gap
            });
        });
        
        // Allocate study time
        const totalPriorityTime = plan.priorityTopics.reduce((sum, topic) => sum + topic.timeNeeded, 0);
        const reviewTime = Math.max(2, studyTime - totalPriorityTime);
        
        plan.timeAllocation = {
            priorityTopics: totalPriorityTime,
            review: reviewTime,
            practiceTests: Math.max(1, Math.floor(studyTime * 0.2))
        };
        
        return plan;
    }
    
    function getCollegeCreditInfo(apScore) {
        const creditInfo = {
            score: apScore,
            creditEligible: apScore >= 3,
            typicalCredits: 0,
            description: ''
        };
        
        switch (apScore) {
            case 5:
                creditInfo.typicalCredits = 6;
                creditInfo.description = 'Excellent - Most colleges award full credit';
                break;
            case 4:
                creditInfo.typicalCredits = 3;
                creditInfo.description = 'Good - Many colleges award credit';
                break;
            case 3:
                creditInfo.typicalCredits = 3;
                creditInfo.description = 'Qualified - Some colleges award credit';
                break;
            case 2:
                creditInfo.typicalCredits = 0;
                creditInfo.description = 'Possibly qualified - Limited credit opportunities';
                break;
            case 1:
                creditInfo.typicalCredits = 0;
                creditInfo.description = 'No recommendation - No college credit';
                break;
        }
        
        return creditInfo;
    }
    
    function displayResults(analysis) {
        const scoreClass = `score-${analysis.apScore}`;
        
        let html = `
            <div style="font-size: 48px; font-weight: bold; margin-bottom: 20px;">
                AP Score: ${analysis.apScore}
            </div>
            
            <div class="ap-score-badge ${scoreClass}">
                ${analysis.apScore === 5 ? 'Extremely Well Qualified' :
                  analysis.apScore === 4 ? 'Well Qualified' :
                  analysis.apScore === 3 ? 'Qualified' :
                  analysis.apScore === 2 ? 'Possibly Qualified' : 'No Recommendation'}
            </div>
            
            <div style="margin: 20px 0; font-size: 16px;">
                Composite Score: ${analysis.compositeScore.toFixed(1)} / 150
            </div>
            
            <div class="score-breakdown">
                <h4 style="color: #7b1fa2; margin-bottom: 20px;">📈 Score Breakdown</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h5>Section Performance:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Multiple Choice:</strong> ${analysis.mcqCorrect}/100 (${analysis.mcqPercentage.toFixed(1)}%)</li>
                            <li><strong>Free Response:</strong> ${analysis.frqScore}/14 (${analysis.frqPercentage.toFixed(1)}%)</li>
                            <li><strong>Overall Performance:</strong> ${((analysis.compositeScore / 150) * 100).toFixed(1)}%</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5>Improvement Potential:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Current Score:</strong> ${analysis.apScore}</li>
                            <li><strong>Potential Score:</strong> ${analysis.improvementPotential.potentialAPScore}</li>
                            <li><strong>Points Needed:</strong> ${analysis.improvementPotential.improvementNeeded.toFixed(1)}</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="topic-grid">
        `;
        
        // Display topic strengths
        if (analysis.topicAnalysis.strengths.length > 0) {
            html += `
                <div class="topic-card">
                    <h4 style="color: #4caf50;">💪 Strengths</h4>
            `;
            analysis.topicAnalysis.strengths.forEach(strength => {
                html += `<div style="margin: 5px 0;">${strength.topic}</div>`;
            });
            html += `</div>`;
        }
        
        // Display topic weaknesses
        if (analysis.topicAnalysis.weaknesses.length > 0) {
            html += `
                <div class="topic-card">
                    <h4 style="color: #f44336;">📚 Areas to Improve</h4>
            `;
            analysis.topicAnalysis.weaknesses.forEach(weakness => {
                html += `<div style="margin: 5px 0;">${weakness.topic}</div>`;
            });
            html += `</div>`;
        }
        
        // Display college credit info
        html += `
            <div class="topic-card">
                <h4 style="color: #ff9800;">🎓 College Credit</h4>
                <div style="margin: 10px 0;">
                    <strong>${analysis.collegeCreditInfo.typicalCredits} Credits</strong>
                </div>
                <div style="font-size: 14px;">${analysis.collegeCreditInfo.description}</div>
            </div>
        `;
        
        html += `</div>`;
        
        // Study recommendations
        if (analysis.studyRecommendations.priorityTopics.length > 0) {
            html += `
                <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                    <h4>📅 Personalized Study Plan (${analysis.studyTime} hours/week):</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 15px;">
                        <div>
                            <h5>Priority Topics:</h5>
                            <ul style="text-align: left; margin: 10px 0;">
            `;
            
            analysis.studyRecommendations.priorityTopics.forEach(topic => {
                html += `<li><strong>${topic.topic}:</strong> ${topic.timeNeeded} hours/week</li>`;
            });
            
            html += `
                            </ul>
                        </div>
                        <div>
                            <h5>Time Allocation:</h5>
                            <ul style="text-align: left; margin: 10px 0;">
                                <li><strong>Weak Areas:</strong> ${analysis.studyRecommendations.timeAllocation.priorityTopics} hours</li>
                                <li><strong>Review:</strong> ${analysis.studyRecommendations.timeAllocation.review} hours</li>
                                <li><strong>Practice Tests:</strong> ${analysis.studyRecommendations.timeAllocation.practiceTests} hours</li>
                            </ul>
                        </div>
                    </div>
                </div>
            `;
        }
        
        // Study tips
        html += `
            <div style="background-color: rgba(255,255,255,0.1); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 Study Tips for AP Psychology:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Vocabulary:</strong> Master key terms and concepts for each unit</li>
                    <li><strong>FRQ Practice:</strong> Practice writing clear, detailed responses</li>
                    <li><strong>Research Studies:</strong> Know famous experiments and their findings</li>
                    <li><strong>Application:</strong> Practice applying concepts to real-world scenarios</li>
                    <li><strong>Review:</strong> Use spaced repetition for long-term retention</li>
                    <li><strong>Practice Tests:</strong> Take full-length practice exams regularly</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
