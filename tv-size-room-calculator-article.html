<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.tv-interface {
    background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.room-input {
    background-color: #e3f2fd;
    border: 2px solid #1976d2;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.tv-recommendation {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.size-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}
.size-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    color: #2c3e50;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.viewing-guide {
    background-color: #f1f8e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.resolution-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    margin: 3px;
    color: white;
}
.hd { background-color: #ff9800; }
.full-hd { background-color: #2196f3; }
.qhd { background-color: #9c27b0; }
.uhd-4k { background-color: #4caf50; }
.uhd-8k { background-color: #f44336; }
.distance-meter {
    background-color: #e0e0e0;
    border-radius: 10px;
    height: 20px;
    margin: 10px 0;
    overflow: hidden;
}
.distance-fill {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #2196f3);
    border-radius: 10px;
    transition: width 0.5s ease;
}
.room-layout {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.tv-preview {
    background: linear-gradient(135deg, #424242 0%, #212121 100%);
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
    color: white;
    text-align: center;
}
</style>

<h1>TV Size Room Calculator</h1>
<p>Calculate the optimal TV size for your room with our comprehensive TV size calculator. Find the perfect screen size based on viewing distance, room dimensions, resolution, and seating arrangement for the best viewing experience.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="tvform" id="tv-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">TV Size Calculator</a></li>
                <li><a href="#" data-tab="guide">Viewing Guide</a></li>
                <li><a href="#" data-tab="comparison">Size Comparison</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="tv-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">📺 TV Size Calculator</h3>
                
                <div class="room-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🏠 Room Dimensions</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="room-length"><strong>Room Length:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <input type="number" id="room-length" value="12" min="6" max="50" step="0.5" class="innormal" style="width: 100px;">
                                <select id="length-unit" class="innormal" style="margin-left: 10px;">
                                    <option value="feet">feet</option>
                                    <option value="meters">meters</option>
                                </select>
                            </div>
                        </div>
                        
                        <div>
                            <label for="room-width"><strong>Room Width:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <input type="number" id="room-width" value="10" min="6" max="50" step="0.5" class="innormal" style="width: 100px;">
                                <select id="width-unit" class="innormal" style="margin-left: 10px;">
                                    <option value="feet">feet</option>
                                    <option value="meters">meters</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="viewing-distance"><strong>Viewing Distance:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <input type="number" id="viewing-distance" value="8" min="3" max="25" step="0.5" class="innormal" style="width: 100px;">
                                <select id="distance-unit" class="innormal" style="margin-left: 10px;">
                                    <option value="feet">feet</option>
                                    <option value="meters">meters</option>
                                </select>
                            </div>
                        </div>
                        
                        <div>
                            <label for="ceiling-height"><strong>Ceiling Height:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <input type="number" id="ceiling-height" value="9" min="7" max="20" step="0.5" class="innormal" style="width: 100px;">
                                <select id="height-unit" class="innormal" style="margin-left: 10px;">
                                    <option value="feet">feet</option>
                                    <option value="meters">meters</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="viewing-guide">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🎯 Viewing Preferences</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="resolution"><strong>Preferred Resolution:</strong></label>
                            <select id="resolution" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="hd">HD (720p)</option>
                                <option value="full-hd">Full HD (1080p)</option>
                                <option value="qhd">QHD (1440p)</option>
                                <option value="uhd-4k" selected>4K UHD (2160p)</option>
                                <option value="uhd-8k">8K UHD (4320p)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="usage-type"><strong>Primary Usage:</strong></label>
                            <select id="usage-type" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="general">General Viewing</option>
                                <option value="movies">Movies & Cinema</option>
                                <option value="gaming">Gaming</option>
                                <option value="sports">Sports</option>
                                <option value="mixed">Mixed Usage</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="seating-arrangement"><strong>Seating:</strong></label>
                            <select id="seating-arrangement" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="single">Single Row</option>
                                <option value="multiple">Multiple Rows</option>
                                <option value="sectional">Sectional Sofa</option>
                                <option value="theater">Theater Style</option>
                            </select>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <label for="budget-range"><strong>Budget Range:</strong></label>
                        <select id="budget-range" class="innormal" style="width: 200px; margin-left: 10px;">
                            <option value="budget">Budget ($300-800)</option>
                            <option value="mid-range">Mid-Range ($800-1500)</option>
                            <option value="premium">Premium ($1500-3000)</option>
                            <option value="luxury">Luxury ($3000+)</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Optimal TV Size" id="calculate-tv-btn" style="padding: 15px 30px; font-size: 18px; background-color: #1976d2; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Calculator" id="clear-tv-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Recommendations are based on industry standards and optimal viewing angles. Personal preferences may vary.
        </div>
        
        <div id="tv-results" style="display: none; margin-top: 30px;">
            <div class="tv-recommendation">
                <h3 style="margin-bottom: 20px;">📺 TV Size Recommendations</h3>
                <div id="tv-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-tv-btn');
    const clearBtn = document.getElementById('clear-tv-btn');
    const resultsDiv = document.getElementById('tv-results');
    const resultsContent = document.getElementById('tv-results-content');
    
    // TV size data with dimensions
    const tvSizes = {
        32: { width: 27.9, height: 15.7, diagonal: 32 },
        40: { width: 34.9, height: 19.6, diagonal: 40 },
        43: { width: 37.5, height: 21.1, diagonal: 43 },
        50: { width: 43.6, height: 24.5, diagonal: 50 },
        55: { width: 48.0, height: 27.0, diagonal: 55 },
        65: { width: 56.7, height: 31.9, diagonal: 65 },
        75: { width: 65.4, height: 36.8, diagonal: 75 },
        85: { width: 74.1, height: 41.7, diagonal: 85 },
        98: { width: 85.5, height: 48.1, diagonal: 98 }
    };
    
    // Resolution viewing distance multipliers
    const resolutionMultipliers = {
        'hd': 3.0,
        'full-hd': 2.5,
        'qhd': 2.0,
        'uhd-4k': 1.5,
        'uhd-8k': 1.0
    };
    
    calculateBtn.addEventListener('click', function() {
        const roomLength = parseFloat(document.getElementById('room-length').value) || 12;
        const roomWidth = parseFloat(document.getElementById('room-width').value) || 10;
        const viewingDistance = parseFloat(document.getElementById('viewing-distance').value) || 8;
        const ceilingHeight = parseFloat(document.getElementById('ceiling-height').value) || 9;
        
        const lengthUnit = document.getElementById('length-unit').value;
        const widthUnit = document.getElementById('width-unit').value;
        const distanceUnit = document.getElementById('distance-unit').value;
        const heightUnit = document.getElementById('height-unit').value;
        
        const resolution = document.getElementById('resolution').value;
        const usageType = document.getElementById('usage-type').value;
        const seatingArrangement = document.getElementById('seating-arrangement').value;
        const budgetRange = document.getElementById('budget-range').value;
        
        // Convert all measurements to inches for calculation
        const roomLengthInches = convertToInches(roomLength, lengthUnit);
        const roomWidthInches = convertToInches(roomWidth, widthUnit);
        const viewingDistanceInches = convertToInches(viewingDistance, distanceUnit);
        const ceilingHeightInches = convertToInches(ceilingHeight, heightUnit);
        
        const recommendations = calculateTVSize(roomLengthInches, roomWidthInches, 
            viewingDistanceInches, ceilingHeightInches, resolution, usageType, 
            seatingArrangement, budgetRange);
        
        displayResults(recommendations);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('tv-calculator-form').reset();
        resultsDiv.style.display = 'none';
    });
    
    function convertToInches(value, unit) {
        if (unit === 'meters') {
            return value * 39.3701; // meters to inches
        } else {
            return value * 12; // feet to inches
        }
    }
    
    function calculateTVSize(roomLength, roomWidth, viewingDistance, ceilingHeight, 
                           resolution, usageType, seatingArrangement, budgetRange) {
        
        // Calculate optimal size based on viewing distance and resolution
        const resolutionMultiplier = resolutionMultipliers[resolution];
        const optimalDiagonal = viewingDistance / resolutionMultiplier;
        
        // Adjust for usage type
        let usageMultiplier = 1.0;
        switch (usageType) {
            case 'movies': usageMultiplier = 1.2; break;
            case 'gaming': usageMultiplier = 1.1; break;
            case 'sports': usageMultiplier = 1.15; break;
            case 'mixed': usageMultiplier = 1.05; break;
        }
        
        const adjustedDiagonal = optimalDiagonal * usageMultiplier;
        
        // Find the closest TV sizes
        const availableSizes = Object.keys(tvSizes).map(Number).sort((a, b) => a - b);
        const recommendedSize = findClosestSize(adjustedDiagonal, availableSizes);
        
        // Calculate alternative sizes
        const minSize = Math.max(32, recommendedSize - 10);
        const maxSize = Math.min(98, recommendedSize + 15);
        
        const alternatives = availableSizes.filter(size => 
            size >= minSize && size <= maxSize && size !== recommendedSize
        );
        
        // Check room constraints
        const roomConstraints = checkRoomConstraints(roomLength, roomWidth, ceilingHeight);
        
        // Calculate viewing angles
        const viewingAngles = calculateViewingAngles(recommendedSize, viewingDistance);
        
        return {
            recommended: recommendedSize,
            alternatives: alternatives,
            optimalDistance: adjustedDiagonal * resolutionMultiplier,
            roomConstraints: roomConstraints,
            viewingAngles: viewingAngles,
            resolution: resolution,
            usageType: usageType,
            seatingArrangement: seatingArrangement,
            budgetRange: budgetRange,
            roomDimensions: {
                length: roomLength,
                width: roomWidth,
                height: ceilingHeight
            }
        };
    }
    
    function findClosestSize(targetDiagonal, availableSizes) {
        let closest = availableSizes[0];
        let minDiff = Math.abs(targetDiagonal - closest);
        
        for (let size of availableSizes) {
            const diff = Math.abs(targetDiagonal - size);
            if (diff < minDiff) {
                minDiff = diff;
                closest = size;
            }
        }
        
        return closest;
    }
    
    function checkRoomConstraints(roomLength, roomWidth, ceilingHeight) {
        const constraints = [];
        
        if (roomLength < 120) { // Less than 10 feet
            constraints.push('Small room - consider 32"-50" TVs');
        } else if (roomLength > 240) { // More than 20 feet
            constraints.push('Large room - 65"+ TVs recommended');
        }
        
        if (ceilingHeight < 96) { // Less than 8 feet
            constraints.push('Low ceiling - avoid very large TVs');
        }
        
        if (roomWidth < 120) { // Less than 10 feet
            constraints.push('Narrow room - consider wall mounting');
        }
        
        return constraints;
    }
    
    function calculateViewingAngles(tvSize, viewingDistance) {
        const tvData = tvSizes[tvSize];
        const horizontalAngle = 2 * Math.atan((tvData.width / 2) / viewingDistance) * (180 / Math.PI);
        const verticalAngle = 2 * Math.atan((tvData.height / 2) / viewingDistance) * (180 / Math.PI);
        
        return {
            horizontal: horizontalAngle,
            vertical: verticalAngle
        };
    }
    
    function displayResults(recommendations) {
        const recommendedTV = tvSizes[recommendations.recommended];
        const resolutionClass = recommendations.resolution.replace('-', '-');
        
        let html = `
            <div style="font-size: 48px; font-weight: bold; margin-bottom: 20px;">
                ${recommendations.recommended}" TV
            </div>
            
            <div style="font-size: 18px; margin-bottom: 25px;">
                Optimal size for your room and viewing preferences
            </div>
            
            <div class="tv-preview">
                <h4>📐 TV Dimensions</h4>
                <div style="margin: 15px 0;">
                    <strong>Width:</strong> ${recommendedTV.width.toFixed(1)}" | 
                    <strong>Height:</strong> ${recommendedTV.height.toFixed(1)}" | 
                    <strong>Diagonal:</strong> ${recommendedTV.diagonal}"
                </div>
                <div class="resolution-badge ${resolutionClass}">${recommendations.resolution.toUpperCase()}</div>
            </div>
            
            <div class="size-grid">
                <div class="size-card">
                    <h4>Recommended Size</h4>
                    <div style="font-size: 32px; font-weight: bold; color: #1976d2;">${recommendations.recommended}"</div>
                    <div style="margin-top: 10px;">Perfect for your setup</div>
                </div>
                
                <div class="size-card">
                    <h4>Viewing Distance</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #4caf50;">${(recommendations.optimalDistance / 12).toFixed(1)} ft</div>
                    <div style="margin-top: 10px;">Optimal distance</div>
                </div>
                
                <div class="size-card">
                    <h4>Viewing Angle</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #ff9800;">${recommendations.viewingAngles.horizontal.toFixed(1)}°</div>
                    <div style="margin-top: 10px;">Horizontal FOV</div>
                </div>
                
                <div class="size-card">
                    <h4>Usage Type</h4>
                    <div style="font-size: 18px; font-weight: bold; color: #9c27b0;">${recommendations.usageType.charAt(0).toUpperCase() + recommendations.usageType.slice(1)}</div>
                    <div style="margin-top: 10px;">Optimized for</div>
                </div>
            </div>
            
            <div class="room-layout">
                <h4 style="color: #1976d2; margin-bottom: 15px;">🏠 Room Analysis</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h5>Alternative Sizes:</h5>
                        <ul style="margin: 10px 0;">
        `;
        
        recommendations.alternatives.forEach(size => {
            html += `<li><strong>${size}":</strong> ${size < recommendations.recommended ? 'Smaller option' : 'Larger option'}</li>`;
        });
        
        html += `
                        </ul>
                    </div>
                    
                    <div>
                        <h5>Room Considerations:</h5>
                        <ul style="margin: 10px 0;">
        `;
        
        if (recommendations.roomConstraints.length > 0) {
            recommendations.roomConstraints.forEach(constraint => {
                html += `<li>${constraint}</li>`;
            });
        } else {
            html += `<li>Room size is ideal for recommended TV</li>`;
        }
        
        html += `
                        </ul>
                    </div>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 Setup Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Mounting Height:</strong> Center of TV should be at eye level when seated</li>
                    <li><strong>Lighting:</strong> Avoid placing TV opposite windows to reduce glare</li>
                    <li><strong>Sound:</strong> Consider soundbar or surround sound for larger TVs</li>
                    <li><strong>Cables:</strong> Plan cable management for clean installation</li>
                    <li><strong>Viewing Angle:</strong> Ensure all seats have good viewing angles</li>
                    <li><strong>Future-Proofing:</strong> Consider 4K/8K content availability</li>
                </ul>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.1); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>🛒 Shopping Recommendations (${recommendations.budgetRange}):</h4>
                <ul style="text-align: left; margin: 10px 0;">
        `;
        
        if (recommendations.budgetRange === 'budget') {
            html += `
                <li>Look for sales on previous year models</li>
                <li>Consider TCL, Hisense, or Roku TV brands</li>
                <li>Focus on essential features like 4K and HDR</li>
            `;
        } else if (recommendations.budgetRange === 'mid-range') {
            html += `
                <li>Samsung, LG, or Sony mid-tier models</li>
                <li>Look for QLED or OLED technology</li>
                <li>Gaming features if applicable</li>
            `;
        } else if (recommendations.budgetRange === 'premium') {
            html += `
                <li>High-end OLED or QLED displays</li>
                <li>Advanced HDR support (Dolby Vision)</li>
                <li>Premium smart TV features</li>
            `;
        } else {
            html += `
                <li>Flagship OLED or 8K models</li>
                <li>Professional calibration recommended</li>
                <li>Premium audio integration</li>
            `;
        }
        
        html += `
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
