<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.genetic-interface {
    background: linear-gradient(135deg, #8bc34a 0%, #689f38 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.parent-section {
    background-color: #f1f8e9;
    border: 2px solid #8bc34a;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.gene-selector {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 15px;
    margin: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    width: 160px;
}
.gene-selector:hover {
    border-color: #8bc34a;
    transform: translateY(-3px);
}
.gene-selector.selected {
    border-color: #8bc34a;
    background-color: #f1f8e9;
}
.offspring-result {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.gene-type-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: bold;
    margin: 3px;
    color: white;
}
.dominant { background-color: #f44336; }
.recessive { background-color: #2196f3; }
.codominant { background-color: #ff9800; }
.incomplete-dominant { background-color: #9c27b0; }
.sex-linked { background-color: #e91e63; }
.probability-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}
.probability-card {
    background: white;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    color: #2c3e50;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.punnett-square {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.genetic-breakdown {
    background-color: #e8f5e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.morph-preview {
    background: linear-gradient(135deg, #ffd54f 0%, #ffb300 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    color: #2c3e50;
    text-align: center;
}
</style>

<h1>Ball Python Genetic Calculator</h1>
<p>Calculate ball python breeding genetics and predict offspring outcomes with our advanced genetic calculator. Understand inheritance patterns, calculate probabilities for different morphs, and plan your breeding projects with scientific precision.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="geneticform" id="genetic-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Genetic Calculator</a></li>
                <li><a href="#" data-tab="genetics">Genetics Guide</a></li>
                <li><a href="#" data-tab="breeding">Breeding Plans</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="genetic-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">🧬 Ball Python Genetic Calculator</h3>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div class="parent-section">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🐍 Male Parent (Sire)</h4>
                        <div id="male-genes">
                            <div class="gene-selector" data-gene="albino" data-parent="male">
                                <div><strong>Albino</strong></div>
                                <div class="gene-type-badge recessive">Recessive</div>
                                <div style="margin-top: 8px;">
                                    <select class="gene-state" style="width: 100%;">
                                        <option value="normal">Normal (++)</option>
                                        <option value="het">Het (A+)</option>
                                        <option value="visual">Visual (AA)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="gene-selector" data-gene="piebald" data-parent="male">
                                <div><strong>Piebald</strong></div>
                                <div class="gene-type-badge recessive">Recessive</div>
                                <div style="margin-top: 8px;">
                                    <select class="gene-state" style="width: 100%;">
                                        <option value="normal">Normal (++)</option>
                                        <option value="het">Het (P+)</option>
                                        <option value="visual">Visual (PP)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="gene-selector" data-gene="pastel" data-parent="male">
                                <div><strong>Pastel</strong></div>
                                <div class="gene-type-badge dominant">Dominant</div>
                                <div style="margin-top: 8px;">
                                    <select class="gene-state" style="width: 100%;">
                                        <option value="normal">Normal (++)</option>
                                        <option value="visual">Visual (Pa+)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="gene-selector" data-gene="spider" data-parent="male">
                                <div><strong>Spider</strong></div>
                                <div class="gene-type-badge dominant">Dominant</div>
                                <div style="margin-top: 8px;">
                                    <select class="gene-state" style="width: 100%;">
                                        <option value="normal">Normal (++)</option>
                                        <option value="visual">Visual (Sp+)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="gene-selector" data-gene="fire" data-parent="male">
                                <div><strong>Fire</strong></div>
                                <div class="gene-type-badge codominant">Co-dominant</div>
                                <div style="margin-top: 8px;">
                                    <select class="gene-state" style="width: 100%;">
                                        <option value="normal">Normal (++)</option>
                                        <option value="het">Het (Fi+)</option>
                                        <option value="super">Super (FiFi)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="gene-selector" data-gene="mojave" data-parent="male">
                                <div><strong>Mojave</strong></div>
                                <div class="gene-type-badge codominant">Co-dominant</div>
                                <div style="margin-top: 8px;">
                                    <select class="gene-state" style="width: 100%;">
                                        <option value="normal">Normal (++)</option>
                                        <option value="het">Het (Mo+)</option>
                                        <option value="super">Super (MoMo)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="parent-section">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🐍 Female Parent (Dam)</h4>
                        <div id="female-genes">
                            <div class="gene-selector" data-gene="albino" data-parent="female">
                                <div><strong>Albino</strong></div>
                                <div class="gene-type-badge recessive">Recessive</div>
                                <div style="margin-top: 8px;">
                                    <select class="gene-state" style="width: 100%;">
                                        <option value="normal">Normal (++)</option>
                                        <option value="het">Het (A+)</option>
                                        <option value="visual">Visual (AA)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="gene-selector" data-gene="piebald" data-parent="female">
                                <div><strong>Piebald</strong></div>
                                <div class="gene-type-badge recessive">Recessive</div>
                                <div style="margin-top: 8px;">
                                    <select class="gene-state" style="width: 100%;">
                                        <option value="normal">Normal (++)</option>
                                        <option value="het">Het (P+)</option>
                                        <option value="visual">Visual (PP)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="gene-selector" data-gene="pastel" data-parent="female">
                                <div><strong>Pastel</strong></div>
                                <div class="gene-type-badge dominant">Dominant</div>
                                <div style="margin-top: 8px;">
                                    <select class="gene-state" style="width: 100%;">
                                        <option value="normal">Normal (++)</option>
                                        <option value="visual">Visual (Pa+)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="gene-selector" data-gene="spider" data-parent="female">
                                <div><strong>Spider</strong></div>
                                <div class="gene-type-badge dominant">Dominant</div>
                                <div style="margin-top: 8px;">
                                    <select class="gene-state" style="width: 100%;">
                                        <option value="normal">Normal (++)</option>
                                        <option value="visual">Visual (Sp+)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="gene-selector" data-gene="fire" data-parent="female">
                                <div><strong>Fire</strong></div>
                                <div class="gene-type-badge codominant">Co-dominant</div>
                                <div style="margin-top: 8px;">
                                    <select class="gene-state" style="width: 100%;">
                                        <option value="normal">Normal (++)</option>
                                        <option value="het">Het (Fi+)</option>
                                        <option value="super">Super (FiFi)</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="gene-selector" data-gene="mojave" data-parent="female">
                                <div><strong>Mojave</strong></div>
                                <div class="gene-type-badge codominant">Co-dominant</div>
                                <div style="margin-top: 8px;">
                                    <select class="gene-state" style="width: 100%;">
                                        <option value="normal">Normal (++)</option>
                                        <option value="het">Het (Mo+)</option>
                                        <option value="super">Super (MoMo)</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="genetic-breakdown">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🔬 Breeding Parameters</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="clutch-size"><strong>Expected Clutch Size:</strong></label>
                            <input type="number" id="clutch-size" value="6" min="1" max="15" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                        
                        <div>
                            <label for="breeding-season"><strong>Breeding Season:</strong></label>
                            <select id="breeding-season" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="2024">2024 Season</option>
                                <option value="2025">2025 Season</option>
                                <option value="2026">2026 Season</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="market-focus"><strong>Market Focus:</strong></label>
                            <select id="market-focus" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="pet">Pet Market</option>
                                <option value="breeder">Breeder Market</option>
                                <option value="collection">Collection Goals</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Genetics" id="calculate-genetic-btn" style="padding: 15px 30px; font-size: 18px; background-color: #8bc34a; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Genetics" id="clear-genetic-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Genetic calculations are based on Mendelian inheritance patterns. Actual breeding results may vary due to environmental factors and genetic complexity.
        </div>
        
        <div id="genetic-results" style="display: none; margin-top: 30px;">
            <div class="offspring-result">
                <h3 style="margin-bottom: 20px;">🥚 Genetic Breeding Results</h3>
                <div id="genetic-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-genetic-btn');
    const clearBtn = document.getElementById('clear-genetic-btn');
    const resultsDiv = document.getElementById('genetic-results');
    const resultsContent = document.getElementById('genetic-results-content');
    
    // Gene data with inheritance patterns
    const geneData = {
        albino: { type: 'recessive', symbol: 'A', name: 'Albino' },
        piebald: { type: 'recessive', symbol: 'P', name: 'Piebald' },
        pastel: { type: 'dominant', symbol: 'Pa', name: 'Pastel' },
        spider: { type: 'dominant', symbol: 'Sp', name: 'Spider' },
        fire: { type: 'codominant', symbol: 'Fi', name: 'Fire' },
        mojave: { type: 'codominant', symbol: 'Mo', name: 'Mojave' }
    };
    
    calculateBtn.addEventListener('click', function() {
        const maleGenetics = getParentGenetics('male');
        const femaleGenetics = getParentGenetics('female');
        const clutchSize = parseInt(document.getElementById('clutch-size').value) || 6;
        const breedingSeason = document.getElementById('breeding-season').value;
        const marketFocus = document.getElementById('market-focus').value;
        
        const genetics = calculateGenetics(maleGenetics, femaleGenetics, clutchSize, breedingSeason, marketFocus);
        displayResults(genetics);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('genetic-calculator-form').reset();
        resultsDiv.style.display = 'none';
    });
    
    function getParentGenetics(parent) {
        const genetics = {};
        const geneSelectors = document.querySelectorAll(`[data-parent="${parent}"]`);
        
        geneSelectors.forEach(selector => {
            const gene = selector.dataset.gene;
            const stateSelect = selector.querySelector('.gene-state');
            genetics[gene] = stateSelect.value;
        });
        
        return genetics;
    }
    
    function calculateGenetics(maleGenetics, femaleGenetics, clutchSize, breedingSeason, marketFocus) {
        const outcomes = new Map();
        const totalCombinations = 16; // 4x4 Punnett square for multiple genes
        
        // Calculate all possible offspring combinations
        Object.keys(geneData).forEach(gene => {
            const maleState = maleGenetics[gene] || 'normal';
            const femaleState = femaleGenetics[gene] || 'normal';
            const geneInfo = geneData[gene];
            
            const offspringProbabilities = calculateGeneOutcomes(maleState, femaleState, geneInfo);
            
            offspringProbabilities.forEach((probability, outcome) => {
                const key = `${gene}_${outcome}`;
                outcomes.set(key, (outcomes.get(key) || 0) + probability);
            });
        });
        
        // Calculate combination morphs
        const combinationMorphs = calculateCombinationMorphs(maleGenetics, femaleGenetics);
        
        // Calculate expected numbers in clutch
        const expectedNumbers = new Map();
        outcomes.forEach((probability, morph) => {
            const expected = Math.round((probability / 100) * clutchSize);
            if (expected > 0) {
                expectedNumbers.set(morph, expected);
            }
        });
        
        return {
            outcomes: outcomes,
            expectedNumbers: expectedNumbers,
            combinationMorphs: combinationMorphs,
            clutchSize: clutchSize,
            breedingSeason: breedingSeason,
            marketFocus: marketFocus,
            parentGenetics: { male: maleGenetics, female: femaleGenetics }
        };
    }
    
    function calculateGeneOutcomes(maleState, femaleState, geneInfo) {
        const outcomes = new Map();
        
        if (geneInfo.type === 'recessive') {
            if (maleState === 'normal' && femaleState === 'normal') {
                outcomes.set('normal', 100);
            } else if ((maleState === 'visual' && femaleState === 'normal') || 
                       (maleState === 'normal' && femaleState === 'visual')) {
                outcomes.set('het', 100);
            } else if (maleState === 'het' && femaleState === 'het') {
                outcomes.set('normal', 25);
                outcomes.set('het', 50);
                outcomes.set('visual', 25);
            } else if ((maleState === 'visual' && femaleState === 'het') || 
                       (maleState === 'het' && femaleState === 'visual')) {
                outcomes.set('het', 50);
                outcomes.set('visual', 50);
            } else if (maleState === 'visual' && femaleState === 'visual') {
                outcomes.set('visual', 100);
            } else if ((maleState === 'het' && femaleState === 'normal') || 
                       (maleState === 'normal' && femaleState === 'het')) {
                outcomes.set('normal', 50);
                outcomes.set('het', 50);
            }
        } else if (geneInfo.type === 'dominant') {
            if (maleState === 'normal' && femaleState === 'normal') {
                outcomes.set('normal', 100);
            } else if ((maleState === 'visual' && femaleState === 'normal') || 
                       (maleState === 'normal' && femaleState === 'visual')) {
                outcomes.set('normal', 50);
                outcomes.set('visual', 50);
            } else if (maleState === 'visual' && femaleState === 'visual') {
                outcomes.set('visual', 100);
            }
        } else if (geneInfo.type === 'codominant') {
            if (maleState === 'normal' && femaleState === 'normal') {
                outcomes.set('normal', 100);
            } else if ((maleState === 'het' && femaleState === 'normal') || 
                       (maleState === 'normal' && femaleState === 'het')) {
                outcomes.set('normal', 50);
                outcomes.set('het', 50);
            } else if (maleState === 'het' && femaleState === 'het') {
                outcomes.set('normal', 25);
                outcomes.set('het', 50);
                outcomes.set('super', 25);
            } else if ((maleState === 'super' && femaleState === 'normal') || 
                       (maleState === 'normal' && femaleState === 'super')) {
                outcomes.set('het', 100);
            } else if ((maleState === 'super' && femaleState === 'het') || 
                       (maleState === 'het' && femaleState === 'super')) {
                outcomes.set('het', 50);
                outcomes.set('super', 50);
            } else if (maleState === 'super' && femaleState === 'super') {
                outcomes.set('super', 100);
            }
        }
        
        return outcomes;
    }
    
    function calculateCombinationMorphs(maleGenetics, femaleGenetics) {
        const combinations = [];
        
        // Check for popular combinations
        const maleGenes = Object.keys(maleGenetics).filter(gene => maleGenetics[gene] !== 'normal');
        const femaleGenes = Object.keys(femaleGenetics).filter(gene => femaleGenetics[gene] !== 'normal');
        
        if (maleGenes.includes('pastel') && femaleGenes.includes('fire')) {
            combinations.push({ name: 'Sunset', probability: 25, description: 'Pastel + Fire combination' });
        }
        
        if (maleGenes.includes('spider') && femaleGenes.includes('pastel')) {
            combinations.push({ name: 'Bumblebee', probability: 25, description: 'Spider + Pastel combination' });
        }
        
        if (maleGenes.includes('albino') && femaleGenes.includes('piebald')) {
            combinations.push({ name: 'Dreamsicle', probability: 6.25, description: 'Albino + Piebald combination' });
        }
        
        if (maleGenes.includes('mojave') && femaleGenes.includes('fire')) {
            combinations.push({ name: 'Mystic', probability: 25, description: 'Mojave + Fire combination' });
        }
        
        return combinations;
    }
    
    function displayResults(genetics) {
        let html = `
            <div style="margin-bottom: 25px;">
                <h4>📊 Breeding Outcome Probabilities</h4>
                <div style="font-size: 16px; margin: 10px 0;">
                    Expected clutch size: ${genetics.clutchSize} eggs | Season: ${genetics.breedingSeason} | Focus: ${genetics.marketFocus}
                </div>
            </div>
            
            <div class="probability-grid">
        `;
        
        // Group outcomes by gene
        const geneOutcomes = {};
        genetics.outcomes.forEach((probability, key) => {
            const [gene, outcome] = key.split('_');
            if (!geneOutcomes[gene]) geneOutcomes[gene] = {};
            geneOutcomes[gene][outcome] = probability;
        });
        
        Object.keys(geneOutcomes).forEach(gene => {
            const geneInfo = geneData[gene];
            const outcomes = geneOutcomes[gene];
            
            html += `
                <div class="probability-card">
                    <h5>${geneInfo.name}</h5>
                    <div class="gene-type-badge ${geneInfo.type}">${geneInfo.type.toUpperCase()}</div>
            `;
            
            Object.keys(outcomes).forEach(outcome => {
                const probability = outcomes[outcome];
                const expected = Math.round((probability / 100) * genetics.clutchSize);
                
                html += `
                    <div style="margin: 8px 0; padding: 8px; background-color: #f5f5f5; border-radius: 5px;">
                        <strong>${outcome.charAt(0).toUpperCase() + outcome.slice(1)}:</strong> ${probability.toFixed(1)}%
                        <br><small>${expected} out of ${genetics.clutchSize} eggs</small>
                    </div>
                `;
            });
            
            html += `</div>`;
        });
        
        html += `</div>`;
        
        // Display combination morphs
        if (genetics.combinationMorphs.length > 0) {
            html += `
                <div class="punnett-square">
                    <h4 style="color: #8bc34a;">🌟 Potential Designer Morphs</h4>
            `;
            
            genetics.combinationMorphs.forEach(combo => {
                const expected = Math.round((combo.probability / 100) * genetics.clutchSize);
                html += `
                    <div class="morph-preview">
                        <h5>${combo.name}</h5>
                        <div><strong>Probability:</strong> ${combo.probability}% (${expected} eggs)</div>
                        <div style="font-size: 14px; margin-top: 5px;">${combo.description}</div>
                    </div>
                `;
            });
            
            html += `</div>`;
        }
        
        // Market analysis
        html += `
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💰 Market Analysis (${genetics.marketFocus}):</h4>
                <ul style="text-align: left; margin: 10px 0;">
        `;
        
        if (genetics.marketFocus === 'pet') {
            html += `
                <li>Normal morphs: High demand, lower prices ($50-150)</li>
                <li>Single gene morphs: Moderate demand, good prices ($200-500)</li>
                <li>Designer combos: Lower demand, premium prices ($800+)</li>
            `;
        } else if (genetics.marketFocus === 'breeder') {
            html += `
                <li>Het animals: High breeder value for future projects</li>
                <li>Proven genetics: Essential for breeding programs</li>
                <li>Rare combinations: High investment potential</li>
            `;
        } else {
            html += `
                <li>Focus on personal collection goals</li>
                <li>Unique combinations for display</li>
                <li>Genetic diversity for future breeding</li>
            `;
        }
        
        html += `
                </ul>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.1); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>🧬 Breeding Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Record Keeping:</strong> Document all breeding outcomes for future reference</li>
                    <li><strong>Health First:</strong> Always prioritize animal health over genetics</li>
                    <li><strong>Market Research:</strong> Stay updated on current morph values and trends</li>
                    <li><strong>Genetic Diversity:</strong> Avoid excessive inbreeding in your lines</li>
                    <li><strong>Patience:</strong> Some projects take multiple generations to achieve</li>
                    <li><strong>Ethics:</strong> Consider the welfare implications of certain morphs</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
