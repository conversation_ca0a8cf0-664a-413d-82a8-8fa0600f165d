<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.foundry-interface {
    background: linear-gradient(135deg, #8d6e63 0%, #5d4037 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.metal-selector {
    background-color: #f5f5f5;
    border: 2px solid #8d6e63;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.metal-bar {
    display: inline-block;
    padding: 10px 15px;
    margin: 5px;
    border-radius: 8px;
    color: white;
    font-weight: bold;
    cursor: pointer;
    transition: transform 0.2s ease;
}
.metal-bar:hover {
    transform: scale(1.05);
}
.bronze { background-color: #cd7f32; }
.iron { background-color: #c0c0c0; }
.steel { background-color: #71797e; }
.mithril { background-color: #4a90e2; }
.adamant { background-color: #228b22; }
.rune { background-color: #4169e1; }
.dragon { background-color: #dc143c; }
.sword-design {
    background: white;
    border: 3px solid #8d6e63;
    border-radius: 12px;
    padding: 20px;
    margin: 20px 0;
    text-align: center;
}
.progress-bar {
    background-color: #e0e0e0;
    border-radius: 10px;
    height: 25px;
    margin: 10px 0;
    overflow: hidden;
}
.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4caf50, #8bc34a);
    border-radius: 10px;
    transition: width 0.5s ease;
}
.reward-display {
    background: linear-gradient(135deg, #ffd700 0%, #ffb300 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: #2c3e50;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.quality-indicator {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-weight: bold;
    margin: 5px;
}
.quality-low { background-color: #f44336; color: white; }
.quality-medium { background-color: #ff9800; color: white; }
.quality-high { background-color: #4caf50; color: white; }
.quality-perfect { background-color: #9c27b0; color: white; }
</style>

<h1>OSRS Giants' Foundry Calculator</h1>
<p>Calculate optimal metal combinations and expected rewards for the Giants' Foundry minigame in Old School RuneScape. Plan your smithing training and maximize your experience and profit per hour.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="foundryform" id="foundry-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Foundry Calculator</a></li>
                <li><a href="#" data-tab="rewards">Reward Guide</a></li>
                <li><a href="#" data-tab="strategies">Strategies</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="foundry-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">🔥 Giants' Foundry Interface</h3>
                
                <div class="metal-selector">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">Select Metal Bars (Choose 2 types)</h4>
                    <div style="text-align: center;">
                        <div class="metal-bar bronze" data-metal="bronze">Bronze Bars</div>
                        <div class="metal-bar iron" data-metal="iron">Iron Bars</div>
                        <div class="metal-bar steel" data-metal="steel">Steel Bars</div>
                        <div class="metal-bar mithril" data-metal="mithril">Mithril Bars</div>
                        <div class="metal-bar adamant" data-metal="adamant">Adamant Bars</div>
                        <div class="metal-bar rune" data-metal="rune">Rune Bars</div>
                        <div class="metal-bar dragon" data-metal="dragon">Dragon Bars</div>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <label for="metal1-quantity"><strong>First Metal Quantity:</strong></label>
                                <input type="number" id="metal1-quantity" value="14" min="1" max="28" class="innormal" style="width: 100%; margin-top: 5px;">
                                <div id="metal1-display" style="margin-top: 10px; font-weight: bold; color: #8d6e63;">No metal selected</div>
                            </div>
                            <div>
                                <label for="metal2-quantity"><strong>Second Metal Quantity:</strong></label>
                                <input type="number" id="metal2-quantity" value="14" min="1" max="28" class="innormal" style="width: 100%; margin-top: 5px;">
                                <div id="metal2-display" style="margin-top: 10px; font-weight: bold; color: #8d6e63;">No metal selected</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="sword-design">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">⚔️ Sword Design Requirements</h4>
                    <div style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 15px; text-align: center;">
                        <div>
                            <strong>Blade Type:</strong>
                            <select id="blade-type" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="light">Light Blade</option>
                                <option value="medium">Medium Blade</option>
                                <option value="heavy">Heavy Blade</option>
                            </select>
                        </div>
                        <div>
                            <strong>Forte Type:</strong>
                            <select id="forte-type" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="light">Light Forte</option>
                                <option value="medium">Medium Forte</option>
                                <option value="heavy">Heavy Forte</option>
                            </select>
                        </div>
                        <div>
                            <strong>Tip Type:</strong>
                            <select id="tip-type" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="light">Light Tip</option>
                                <option value="medium">Medium Tip</option>
                                <option value="heavy">Heavy Tip</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div style="text-align: center; margin: 25px 0;">
                    <input type="button" value="Calculate Foundry Results" id="calculate-foundry-btn" style="padding: 15px 30px; font-size: 18px; background-color: #ff6f00; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                    <input type="button" value="Reset Foundry" id="clear-foundry-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
                </div>
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Calculations are based on OSRS Giants' Foundry mechanics. Actual results may vary based on your smithing level and performance.
        </div>
        
        <div id="foundry-results" style="display: none; margin-top: 30px;">
            <div class="reward-display">
                <h3 style="margin-bottom: 20px;">⚒️ Foundry Results</h3>
                <div id="foundry-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const metalBars = document.querySelectorAll('.metal-bar');
    const calculateBtn = document.getElementById('calculate-foundry-btn');
    const clearBtn = document.getElementById('clear-foundry-btn');
    const resultsDiv = document.getElementById('foundry-results');
    const resultsContent = document.getElementById('foundry-results-content');
    
    let selectedMetals = [];
    
    // Metal data with experience and properties
    const metalData = {
        bronze: { xp: 12.5, level: 1, weight: 'light', cost: 50 },
        iron: { xp: 25, level: 15, weight: 'light', cost: 150 },
        steel: { xp: 37.5, level: 30, weight: 'medium', cost: 400 },
        mithril: { xp: 50, level: 50, weight: 'medium', cost: 800 },
        adamant: { xp: 62.5, level: 70, weight: 'heavy', cost: 2000 },
        rune: { xp: 75, level: 85, weight: 'heavy', cost: 12000 },
        dragon: { xp: 87.5, level: 90, weight: 'heavy', cost: 25000 }
    };
    
    // Handle metal selection
    metalBars.forEach(bar => {
        bar.addEventListener('click', function() {
            const metal = this.dataset.metal;
            
            if (selectedMetals.includes(metal)) {
                // Deselect metal
                selectedMetals = selectedMetals.filter(m => m !== metal);
                this.style.opacity = '1';
                this.style.border = 'none';
            } else if (selectedMetals.length < 2) {
                // Select metal
                selectedMetals.push(metal);
                this.style.opacity = '0.7';
                this.style.border = '3px solid #fff';
            } else {
                alert('You can only select 2 metal types.');
                return;
            }
            
            updateMetalDisplays();
        });
    });
    
    function updateMetalDisplays() {
        const metal1Display = document.getElementById('metal1-display');
        const metal2Display = document.getElementById('metal2-display');
        
        if (selectedMetals.length >= 1) {
            metal1Display.textContent = `${selectedMetals[0].charAt(0).toUpperCase() + selectedMetals[0].slice(1)} Bars`;
            metal1Display.style.color = '#4caf50';
        } else {
            metal1Display.textContent = 'No metal selected';
            metal1Display.style.color = '#8d6e63';
        }
        
        if (selectedMetals.length >= 2) {
            metal2Display.textContent = `${selectedMetals[1].charAt(0).toUpperCase() + selectedMetals[1].slice(1)} Bars`;
            metal2Display.style.color = '#4caf50';
        } else {
            metal2Display.textContent = 'No metal selected';
            metal2Display.style.color = '#8d6e63';
        }
    }
    
    calculateBtn.addEventListener('click', function() {
        if (selectedMetals.length !== 2) {
            alert('Please select exactly 2 metal types.');
            return;
        }
        
        const metal1Qty = parseInt(document.getElementById('metal1-quantity').value) || 14;
        const metal2Qty = parseInt(document.getElementById('metal2-quantity').value) || 14;
        const bladeType = document.getElementById('blade-type').value;
        const forteType = document.getElementById('forte-type').value;
        const tipType = document.getElementById('tip-type').value;
        
        const results = calculateFoundryResults(selectedMetals, [metal1Qty, metal2Qty], [bladeType, forteType, tipType]);
        displayResults(results);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        selectedMetals = [];
        metalBars.forEach(bar => {
            bar.style.opacity = '1';
            bar.style.border = 'none';
        });
        updateMetalDisplays();
        document.getElementById('foundry-calculator-form').reset();
        resultsDiv.style.display = 'none';
    });
    
    function calculateFoundryResults(metals, quantities, design) {
        const metal1 = metalData[metals[0]];
        const metal2 = metalData[metals[1]];
        
        // Calculate base experience
        const baseXp = (metal1.xp * quantities[0] + metal2.xp * quantities[1]);
        
        // Calculate quality based on metal combination and design
        const metalCompatibility = calculateMetalCompatibility(metals);
        const designBonus = calculateDesignBonus(metals, design);
        
        const qualityScore = (metalCompatibility + designBonus) / 2;
        let quality, qualityMultiplier;
        
        if (qualityScore >= 90) {
            quality = 'Perfect';
            qualityMultiplier = 1.5;
        } else if (qualityScore >= 75) {
            quality = 'High';
            qualityMultiplier = 1.3;
        } else if (qualityScore >= 50) {
            quality = 'Medium';
            qualityMultiplier = 1.1;
        } else {
            quality = 'Low';
            qualityMultiplier = 0.9;
        }
        
        const totalXp = Math.round(baseXp * qualityMultiplier);
        const totalCost = metal1.cost * quantities[0] + metal2.cost * quantities[1];
        
        // Calculate rewards
        const foundryReputation = Math.round(totalXp / 10);
        const moulds = Math.floor(foundryReputation / 100);
        const coalBag = foundryReputation >= 500;
        
        return {
            metals: metals,
            quantities: quantities,
            design: design,
            baseXp: baseXp,
            totalXp: totalXp,
            quality: quality,
            qualityScore: qualityScore,
            totalCost: totalCost,
            foundryReputation: foundryReputation,
            moulds: moulds,
            coalBag: coalBag,
            metalCompatibility: metalCompatibility,
            designBonus: designBonus
        };
    }
    
    function calculateMetalCompatibility(metals) {
        const metal1Level = metalData[metals[0]].level;
        const metal2Level = metalData[metals[1]].level;
        const levelDifference = Math.abs(metal1Level - metal2Level);
        
        // Better compatibility with similar level metals
        if (levelDifference <= 15) return 90;
        if (levelDifference <= 30) return 75;
        if (levelDifference <= 50) return 60;
        return 40;
    }
    
    function calculateDesignBonus(metals, design) {
        const metal1Weight = metalData[metals[0]].weight;
        const metal2Weight = metalData[metals[1]].weight;
        
        let matches = 0;
        const weights = [metal1Weight, metal2Weight];
        
        // Check how many design parts match the metal weights
        if (weights.includes(design[0])) matches++;
        if (weights.includes(design[1])) matches++;
        if (weights.includes(design[2])) matches++;
        
        return (matches / 3) * 100;
    }
    
    function displayResults(results) {
        const qualityClass = `quality-${results.quality.toLowerCase()}`;
        
        let html = `
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center;">
                    <h4 style="color: #2c3e50;">Total Experience</h4>
                    <div style="font-size: 28px; font-weight: bold; color: #4caf50;">${results.totalXp.toLocaleString()}</div>
                    <div style="font-size: 14px; color: #666;">Base: ${results.baseXp.toLocaleString()}</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center;">
                    <h4 style="color: #2c3e50;">Quality</h4>
                    <div class="quality-indicator ${qualityClass}">${results.quality}</div>
                    <div style="font-size: 14px; color: #666; margin-top: 5px;">${results.qualityScore.toFixed(1)}% Score</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center;">
                    <h4 style="color: #2c3e50;">Total Cost</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #ff9800;">${results.totalCost.toLocaleString()} GP</div>
                    <div style="font-size: 14px; color: #666;">Material cost</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center;">
                    <h4 style="color: #2c3e50;">Foundry Reputation</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #9c27b0;">${results.foundryReputation}</div>
                    <div style="font-size: 14px; color: #666;">Reputation points</div>
                </div>
            </div>
            
            <div style="background: white; border-radius: 10px; padding: 20px; margin: 20px 0;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">📊 Performance Breakdown</h4>
                <div style="margin: 10px 0;">
                    <strong>Metal Compatibility:</strong> ${results.metalCompatibility}%
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${results.metalCompatibility}%"></div>
                    </div>
                </div>
                <div style="margin: 10px 0;">
                    <strong>Design Match:</strong> ${results.designBonus.toFixed(1)}%
                    <div class="progress-bar">
                        <div class="progress-fill" style="width: ${results.designBonus}%"></div>
                    </div>
                </div>
            </div>
            
            <div style="background: white; border-radius: 10px; padding: 20px; margin: 20px 0;">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">🎁 Potential Rewards</h4>
                <ul style="text-align: left; color: #2c3e50;">
                    <li><strong>Moulds Available:</strong> ${results.moulds} (based on reputation)</li>
                    <li><strong>Coal Bag Upgrade:</strong> ${results.coalBag ? 'Available' : 'Need more reputation'}</li>
                    <li><strong>XP per GP:</strong> ${(results.totalXp / results.totalCost).toFixed(2)}</li>
                    <li><strong>Reputation per Hour:</strong> ~${Math.round(results.foundryReputation * 6)} (estimated)</li>
                </ul>
            </div>
            
            <div style="background: rgba(139, 90, 60, 0.1); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4 style="color: #8d6e63;">💡 Optimization Tips:</h4>
                <ul style="color: #2c3e50; text-align: left;">
                    <li>Use metals with similar levels for better compatibility</li>
                    <li>Match sword design requirements to your metal weights</li>
                    <li>Higher tier metals give more XP but cost significantly more</li>
                    <li>Perfect quality gives 50% bonus XP - aim for 90%+ score</li>
                    <li>Consider profit vs XP based on your goals</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
