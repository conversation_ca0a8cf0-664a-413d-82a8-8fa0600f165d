<?php
/*
Template Name: Pace Calculator
*/

get_header(); ?>

<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
</style>

<h1>Pace Calculator</h1>
<p>Use the following calculator to estimate the pace for a variety of activities, including running, walking, and biking. The calculator can also be used to estimate the time taken or distance traveled with a given pace and time or distance.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="calform" id="pace-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="pace">Pace</a></li>
                <li><a href="#" data-tab="time">Time</a></li>
                <li><a href="#" data-tab="distance">Distance</a></li>
            </ul>
        </div>

        <div class="panel2" style="padding: 8px 5px;">
            <table id="calinputtime" style="display: block;">
                <tbody>
                    <tr>
                        <td width="75">Time</td>
                        <td><input type="text" name="ctime" id="ctime" value="00:50:25" class="inlong"></td>
                        <td>hh:mm:ss</td>
                    </tr>
                </tbody>
            </table>

            <table id="calinputdistance" style="display: block;">
                <tbody>
                    <tr>
                        <td width="75">Distance</td>
                        <td><input type="text" name="cdistance" id="cdistance" value="5" class="inlong"></td>
                        <td>
                            <select name="cdistanceunit" id="cdistanceunit">
                                <option value="Miles">Miles</option>
                                <option value="Kilometers" selected="">Kilometers</option>
                                <option value="Meters">Meters</option>
                                <option value="Yards">Yards</option>
                            </select>
                            &nbsp; &nbsp;
                            <select name="cpickevent" id="cpickevent">
                                <option value="">-- Or pick an event --</option>
                                <option value="M">Marathon</option>
                                <option value="HM">Half-Marathon</option>
                                <option value="1K">1K</option>
                                <option value="5K">5K</option>
                                <option value="10K">10K</option>
                                <option value="1M">1 Miles</option>
                                <option value="5M">5 Miles</option>
                                <option value="10M">10 Miles</option>
                                <option value="800m">800 meters</option>
                                <option value="1500m">1500 meters</option>
                            </select>
                        </td>
                    </tr>
                </tbody>
            </table>

            <table id="calinputpace" style="display: none;">
                <tbody>
                    <tr valign="top">
                        <td width="75">Pace</td>
                        <td>
                            <input type="text" name="cpace" id="cpace" value="00:08:10" class="inlong">
                            <div id="cpaceunithint" style="text-align:right;">hh:mm:ss</div>
                        </td>
                        <td>
                            <select name="cpaceunit" id="cpaceunit">
                                <option value="tpm" selected="">Per Mile</option>
                                <option value="tpk">Per Kilometer</option>
                                <option value="mph">Miles Per Hour</option>
                                <option value="kph">Kilometers Per Hour</option>
                                <option value="mpm">Meters Per Minute</option>
                                <option value="mps">Meters Per Second</option>
                                <option value="ypm">Yards Per Minute</option>
                                <option value="yps">Yards Per Second</option>
                            </select>
                        </td>
                    </tr>
                </tbody>
            </table>

            <table width="310" style="padding-top: 10px;">
                <tbody>
                    <tr>
                        <td align="center">
                            <input type="submit" name="x" value="Calculate" id="calculate-btn">
                            <input type="button" value="Clear" id="clear-btn">
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <div class="smalltext">Note that placeholder zeros do not need to be entered in the "Time" or "Pace" field. For example, the time 5 minutes 3 seconds does not need to be entered as 00:05:03, and can be entered as 5:3.</div>

        <div id="calculation-results" style="display: none; margin-top: 20px; padding: 15px; background-color: #f0f8ff; border: 1px solid #ccc; border-radius: 5px;">
            <h3>Results:</h3>
            <div id="results-content"></div>
        </div>
    </form>
</div>

<a name="multipoint"></a>
<br><h2>Multipoint Pace Calculator</h2>
<p>The following calculator can determine the pace of segments of a run (or other activity) for those with access to the time at intermittent points during the run. For example, if a person runs from point A to point B, then to point C, records the time at each point, and subsequently determines the distance between those points (using many available websites, applications, or maps), the multipoint calculator can determine how fast the person traveled between each pair of points, allowing use for training purposes; a person can run the same route (or distance) repeatedly and track pace over that given route, enabling comparison of times between each segment (or lap) to identify areas for potential improvement.</p>

<form id="multipoint-form" name="calform2">
    <table class="panel">
        <tbody>
            <tr>
                <td bgcolor="#E0F0FE">&nbsp;</td>
                <td bgcolor="#E0F0FE" colspan="2">Distance</td>
                <td bgcolor="#E0F0FE">Time (hh:mm:ss)</td>
            </tr>
            <?php for ($i = 1; $i <= 12; $i++) : ?>
            <tr>
                <td><?php echo $i; ?>.</td>
                <td>
                    <input type="text" name="cmpdistance<?php echo $i; ?>" value="<?php echo $i <= 5 ? $i : ''; ?>" class="innormal">
                </td>
                <td>
                    <select name="cmpdistanceunit<?php echo $i; ?>">
                        <option value="Kilometers">Kilometers</option>
                        <option value="Miles">Miles</option>
                        <option value="Meters">Meters</option>
                        <option value="Yards">Yards</option>
                    </select>
                </td>
                <td>
                    <input type="text" name="cmptime<?php echo $i; ?>" value="<?php 
                        $times = array('', '3:25', '6:55', '10:25', '14:01', '17:25');
                        echo isset($times[$i]) ? $times[$i] : '';
                    ?>" class="inlong">
                </td>
            </tr>
            <?php endfor; ?>
            <tr>
                <td colspan="4" align="center">
                    Your Preferred Pace Unit
                    <select name="cmppaceunit">
                        <option value="tpm">Per Mile</option>
                        <option value="tpk">Per Kilometer</option>
                        <option value="mph">Miles Per Hour</option>
                        <option value="kph">Kilometers Per Hour</option>
                        <option value="mpm">Meters Per Minute</option>
                        <option value="mps">Meters Per Second</option>
                        <option value="ypm">Yards Per Minute</option>
                        <option value="yps">Yards Per Second</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td colspan="4" align="center" style="padding-top:10px;">
                    <input type="submit" name="x" value="Calculate" id="multipoint-calculate">
                    <input type="button" value="Clear" id="multipoint-clear">
                </td>
            </tr>
        </tbody>
    </table>
    
    <!-- Multipoint Results -->
    <div id="multipoint-results" style="display: none; margin-top: 20px; padding: 15px; background-color: #f0f8ff; border: 1px solid #ccc; border-radius: 5px;">
        <h3>Segment Pace Results:</h3>
        <div id="multipoint-results-content"></div>
    </div>
</form>

<a name="paceconverter"></a>
<br><h2>Pace Converter</h2>
<form id="pace-converter-form" name="calform3">
    <table class="panel">
        <tbody>
            <tr>
                <td valign="top" align="center">
                    <input type="text" name="ccfrom" id="ccfrom" value="5:30" class="inhalf"><br>
                    <span id="ccpaceunithint">hh:mm:ss</span>
                </td>
                <td valign="top">
                    <select name="ccpaceunitfrom" id="ccpaceunitfrom">
                        <option value="tpm" selected="">Per Mile</option>
                        <option value="tpk">Per Kilometer</option>
                        <option value="mph">Miles Per Hour</option>
                        <option value="kph">Kilometers Per Hour</option>
                        <option value="mpm">Meters Per Minute</option>
                        <option value="mps">Meters Per Second</option>
                        <option value="ypm">Yards Per Minute</option>
                        <option value="yps">Yards Per Second</option>
                    </select>
                </td>
                <td class="verybigtext" valign="top">&nbsp;<b>=</b></td>
                <td valign="top">
                    <b>?</b>
                    <select name="ccpaceunitto" id="ccpaceunitto">
                        <option value="tpm">Per Mile</option>
                        <option value="tpk" selected="">Per Kilometer</option>
                        <option value="mph">Miles Per Hour</option>
                        <option value="kph">Kilometers Per Hour</option>
                        <option value="mpm">Meters Per Minute</option>
                        <option value="mps">Meters Per Second</option>
                        <option value="ypm">Yards Per Minute</option>
                        <option value="yps">Yards Per Second</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td align="center" colspan="4" style="padding-top:10px;">
                    <input type="submit" name="x" value="Calculate" id="converter-calculate">
                    <input type="button" value="Clear" id="converter-clear">
                </td>
            </tr>
        </tbody>
    </table>
    
    <!-- Converter Results -->
    <div id="converter-results" style="display: none; margin-top: 20px; padding: 15px; background-color: #f0f8ff; border: 1px solid #ccc; border-radius: 5px;">
        <h3>Conversion Result:</h3>
        <div id="converter-results-content"></div>
    </div>
</form>

<a name="finishtime"></a>
<br><h2>Finish Time Calculator</h2>
<p>The following calculator can be used to estimate a person's finish time based on the time and distance covered in a race at the point the calculator is used.</p>

<form id="finish-time-form" name="calform4">
    <table class="panel">
        <tbody>
            <tr>
                <td>Current Distance Traveled</td>
                <td>
                    <input type="text" name="cftdistancenow" id="cftdistancenow" value="1" class="inhalf">
                    <select name="cftdistancenowunit">
                        <option value="Miles">Miles</option>
                        <option value="Kilometers" selected="">Kilometers</option>
                        <option value="Meters">Meters</option>
                        <option value="Yards">Yards</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>Elapsed Time</td>
                <td><input type="text" name="cfttime" id="cfttime" value="6:15" class="inhalf"> hh:mm:ss</td>
            </tr>
            <tr>
                <td>Full Distance</td>
                <td>
                    <input type="text" name="cftdistancefull" id="cftdistancefull" value="5" class="inhalf">
                    <select name="cftdistancefullunit">
                        <option value="Miles">Miles</option>
                        <option value="Kilometers" selected="">Kilometers</option>
                        <option value="Meters">Meters</option>
                        <option value="Yards">Yards</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td align="center" colspan="2" style="padding-top:10px;">
                    <input type="submit" name="x" value="Calculate" id="finish-time-calculate">
                    <input type="button" value="Clear" id="finish-time-clear">
                </td>
            </tr>
        </tbody>
    </table>
    
    <!-- Finish Time Results -->
    <div id="finish-time-results" style="display: none; margin-top: 20px; padding: 15px; background-color: #f0f8ff; border: 1px solid #ccc; border-radius: 5px;">
        <h3>Finish Time Prediction:</h3>
        <div id="finish-time-results-content"></div>
    </div>
</form>

<h3>Typical Races and World Record Paces</h3>
<p>The following table shows the world record paces for various running distances. These represent the absolute fastest times ever recorded and serve as benchmarks for elite performance. Understanding these paces can help runners set realistic goals and appreciate the incredible speed of world-class athletes.</p>

<table class="cinfoT">
    <tbody>
        <tr>
            <td class="cinfoHd">Category</td>
            <td class="cinfoHdL">Men's World Record Pace</td>
            <td class="cinfoHdL">Women's World Record Pace</td>
        </tr>
        <tr>
            <td>100 meters</td>
            <td class="cinfoBodL">2:35/mile or 1:36/km</td>
            <td class="cinfoBodL">2:49/mile or 1:45/km</td>
        </tr>
        <tr>
            <td>200 meters</td>
            <td class="cinfoBodL">2:35/mile or 1:36/km</td>
            <td class="cinfoBodL">2:52/mile or 1:47/km</td>
        </tr>
        <tr>
            <td>400 meters</td>
            <td class="cinfoBodL">2:54/mile or 1:48/km</td>
            <td class="cinfoBodL">3:12/mile or 1:59/km</td>
        </tr>
        <tr>
            <td>800 meters</td>
            <td class="cinfoBodL">3:23/mile or 2:06/km</td>
            <td class="cinfoBodL">3:48/mile or 2:21/km</td>
        </tr>
        <tr>
            <td>1,500 meters</td>
            <td class="cinfoBodL">3:41/mile or 2:17/km</td>
            <td class="cinfoBodL">4:07/mile or 2:34/km</td>
        </tr>
        <tr>
            <td>1 mile</td>
            <td class="cinfoBodL">3:43/mile or 2:19/km</td>
            <td class="cinfoBodL">4:13/mile or 2:37/km</td>
        </tr>
        <tr>
            <td>5K</td>
            <td class="cinfoBodL">4:04/mile or 2:31/km</td>
            <td class="cinfoBodL">4:34/mile or 2:50/km</td>
        </tr>
        <tr>
            <td>10K</td>
            <td class="cinfoBodL">4:14/mile or 2:38/km</td>
            <td class="cinfoBodL">4:45/mile or 2:57/km</td>
        </tr>
        <tr>
            <td>Half Marathon<br>(13.11 miles / 21.098 km)</td>
            <td class="cinfoBodL">4:27/mile or 2:46/km</td>
            <td class="cinfoBodL">4:58/mile or 3:05/km</td>
        </tr>
        <tr>
            <td>Marathon<br>(26.22 miles / 42.195 km)</td>
            <td class="cinfoBodL">4:41/mile or 2:55/km</td>
            <td class="cinfoBodL">5:10/mile or 3:13/km</td>
        </tr>
    </tbody>
</table>

<p><strong>Note:</strong> These world record paces demonstrate the incredible speed and endurance of elite athletes. For recreational runners, typical paces are significantly slower. A good 5K pace for recreational runners might range from 7:00-12:00 per mile, while marathon paces often range from 8:00-15:00 per mile depending on fitness level and experience.</p>

<h3>Pace Comparison Chart by Distance</h3>
<p>The following chart illustrates how pace changes across different distances, showing the relationship between speed and endurance:</p>

<div style="text-align:center; margin: 20px 0;">
    <svg width="600" height="400" viewBox="0 0 600 400" style="border: 1px solid #ccc; background: #f9f9f9;">
        <!-- Chart Title -->
        <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#003366">World Record Pace by Distance</text>

        <!-- Y-axis (Pace) -->
        <line x1="60" y1="50" x2="60" y2="350" stroke="#333" stroke-width="2"/>
        <text x="25" y="200" text-anchor="middle" font-size="12" fill="#333" transform="rotate(-90 25 200)">Pace (min/mile)</text>

        <!-- X-axis (Distance) -->
        <line x1="60" y1="350" x2="550" y2="350" stroke="#333" stroke-width="2"/>
        <text x="305" y="385" text-anchor="middle" font-size="12" fill="#333">Distance</text>

        <!-- Y-axis labels -->
        <text x="55" y="55" text-anchor="end" font-size="10" fill="#666">2:30</text>
        <text x="55" y="105" text-anchor="end" font-size="10" fill="#666">3:00</text>
        <text x="55" y="155" text-anchor="end" font-size="10" fill="#666">3:30</text>
        <text x="55" y="205" text-anchor="end" font-size="10" fill="#666">4:00</text>
        <text x="55" y="255" text-anchor="end" font-size="10" fill="#666">4:30</text>
        <text x="55" y="305" text-anchor="end" font-size="10" fill="#666">5:00</text>

        <!-- X-axis labels -->
        <text x="100" y="365" text-anchor="middle" font-size="10" fill="#666">100m</text>
        <text x="150" y="365" text-anchor="middle" font-size="10" fill="#666">400m</text>
        <text x="200" y="365" text-anchor="middle" font-size="10" fill="#666">800m</text>
        <text x="250" y="365" text-anchor="middle" font-size="10" fill="#666">1500m</text>
        <text x="300" y="365" text-anchor="middle" font-size="10" fill="#666">5K</text>
        <text x="350" y="365" text-anchor="middle" font-size="10" fill="#666">10K</text>
        <text x="400" y="365" text-anchor="middle" font-size="10" fill="#666">Half</text>
        <text x="450" y="365" text-anchor="middle" font-size="10" fill="#666">Marathon</text>

        <!-- Men's line -->
        <polyline points="100,65 150,75 200,95 250,115 300,145 350,155 400,175 450,195"
                  fill="none" stroke="#0066cc" stroke-width="3"/>

        <!-- Women's line -->
        <polyline points="100,85 150,105 200,125 250,145 300,175 350,185 400,205 450,225"
                  fill="none" stroke="#cc0066" stroke-width="3"/>

        <!-- Legend -->
        <rect x="450" y="60" width="120" height="50" fill="white" stroke="#ccc"/>
        <line x1="460" y1="75" x2="480" y2="75" stroke="#0066cc" stroke-width="3"/>
        <text x="485" y="79" font-size="12" fill="#333">Men's WR</text>
        <line x1="460" y1="95" x2="480" y2="95" stroke="#cc0066" stroke-width="3"/>
        <text x="485" y="99" font-size="12" fill="#333">Women's WR</text>
    </svg>
</div>

<p>This chart clearly shows how pace slows as distance increases, reflecting the trade-off between speed and endurance. Sprint events like the 100m and 400m are run at incredibly fast paces that cannot be sustained over longer distances.</p>

<br><h2>Training Through Pace and Heart Rate</h2>

<p>Pace is a rate of activity or movement, while heart rate is measured as the number of times that a person's heart contracts over a minute. Pace and heart rate have a positive correlation; higher pace corresponds to higher heart rate. The use of both in training can help a person improve performance, avoid over-training, as well as track progress and fitness over time.</p>

<h3>Measuring and Estimating Heart Rate and Heart Rate Zones:</h3>

<p>Heart rate can be measured in different ways, from using devices such as heart rate monitors, to simply looking at a watch while measuring pulse at some peripheral point such as the wrist or neck. Some of the more notable measurements of heart rate include resting heart rate and maximum heart rate, which are often used to estimate specific target heart rate zones to determine different levels of exercise.</p>

<p>Typical adult resting heart rates (RHR) are commonly cited to range from 60-100 beats per minute (bpm), though there is some argument that normal RHRs actually fall within the range of 50-90 bpm. Generally, a lower RHR indicates more efficient heart function, though RHRs that are lower than 50 bpm can be a sign of an underlying heart condition or disease. The same is true of RHRs above 90 bpm.</p>

<p>Maximum heart rate (MHR) is most accurately measured using a cardiac stress test, which involves measuring a person's heart function (including heart rate) at periodically increasing levels of exercise. These tests typically range from ten to twenty minutes in duration, which can be inconvenient. As such, there are many estimates for MHR based on age, which is strongly correlated with heart rate, though there is little consensus regarding which formula should be used. The most commonly cited formula for calculating MHR is:</p>

<p class="bigtext" align="center">MHR = 220 – age</p>

<p>Although it is the most commonly cited formula, and is often used to determine heart rate training zones, it does not have a reference to any standard deviation, and is not considered a good predictor of MHR by reputable health and fitness professionals. Furthermore, MHRs vary significantly between individuals, even those with highly similar training and age within the same sport. Nevertheless, MHR determined using the above formula is often used to prescribe exercise training heart rate ranges, and can be beneficial as a reference. Note that an exercise intensity level of 60-70% of maximum heart rate is considered the ideal range for burning fat. Refer to the figure below for further detail.</p>

<h3>Heart Rate Training Zones</h3>
<p>Understanding different heart rate zones is crucial for effective training. Each zone targets different physiological adaptations and energy systems:</p>

<table class="cinfoT" style="margin: 20px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">Zone</td>
            <td class="cinfoHd">% of Max HR</td>
            <td class="cinfoHd">Purpose</td>
            <td class="cinfoHd">Benefits</td>
            <td class="cinfoHd">Duration</td>
        </tr>
        <tr>
            <td><strong>Zone 1: Recovery</strong></td>
            <td class="cinfoBodL">50-60%</td>
            <td>Active recovery, warm-up</td>
            <td>Promotes blood flow, aids recovery</td>
            <td>20-90 minutes</td>
        </tr>
        <tr>
            <td><strong>Zone 2: Aerobic Base</strong></td>
            <td class="cinfoBodL">60-70%</td>
            <td>Fat burning, base building</td>
            <td>Improves fat oxidation, builds aerobic capacity</td>
            <td>30-180 minutes</td>
        </tr>
        <tr>
            <td><strong>Zone 3: Aerobic</strong></td>
            <td class="cinfoBodL">70-80%</td>
            <td>Aerobic development</td>
            <td>Improves cardiovascular efficiency</td>
            <td>20-60 minutes</td>
        </tr>
        <tr>
            <td><strong>Zone 4: Threshold</strong></td>
            <td class="cinfoBodL">80-90%</td>
            <td>Lactate threshold training</td>
            <td>Increases lactate buffering capacity</td>
            <td>8-40 minutes</td>
        </tr>
        <tr>
            <td><strong>Zone 5: VO2 Max</strong></td>
            <td class="cinfoBodL">90-100%</td>
            <td>Maximum oxygen uptake</td>
            <td>Improves maximum aerobic power</td>
            <td>3-8 minutes</td>
        </tr>
    </tbody>
</table>

<div style="text-align:center;"><b>Exercise intensity levels and typical heart rates associated with said levels based on age</b></div>

<div style="text-align:center; margin: 20px 0;">
    <svg width="700" height="450" viewBox="0 0 700 450" style="border: 1px solid #ccc; background: #f9f9f9;">
        <!-- Chart Title -->
        <text x="350" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#003366">Heart Rate Zones by Age</text>

        <!-- Y-axis (Heart Rate) -->
        <line x1="60" y1="50" x2="60" y2="400" stroke="#333" stroke-width="2"/>
        <text x="25" y="225" text-anchor="middle" font-size="12" fill="#333" transform="rotate(-90 25 225)">Heart Rate (BPM)</text>

        <!-- X-axis (Age) -->
        <line x1="60" y1="400" x2="650" y2="400" stroke="#333" stroke-width="2"/>
        <text x="355" y="435" text-anchor="middle" font-size="12" fill="#333">Age (years)</text>

        <!-- Y-axis labels -->
        <text x="55" y="55" text-anchor="end" font-size="10" fill="#666">200</text>
        <text x="55" y="105" text-anchor="end" font-size="10" fill="#666">180</text>
        <text x="55" y="155" text-anchor="end" font-size="10" fill="#666">160</text>
        <text x="55" y="205" text-anchor="end" font-size="10" fill="#666">140</text>
        <text x="55" y="255" text-anchor="end" font-size="10" fill="#666">120</text>
        <text x="55" y="305" text-anchor="end" font-size="10" fill="#666">100</text>
        <text x="55" y="355" text-anchor="end" font-size="10" fill="#666">80</text>

        <!-- X-axis labels -->
        <text x="110" y="415" text-anchor="middle" font-size="10" fill="#666">20</text>
        <text x="210" y="415" text-anchor="middle" font-size="10" fill="#666">30</text>
        <text x="310" y="415" text-anchor="middle" font-size="10" fill="#666">40</text>
        <text x="410" y="415" text-anchor="middle" font-size="10" fill="#666">50</text>
        <text x="510" y="415" text-anchor="middle" font-size="10" fill="#666">60</text>
        <text x="610" y="415" text-anchor="middle" font-size="10" fill="#666">70</text>

        <!-- Zone 5 (90-100% MHR) -->
        <polygon points="110,50 610,90 610,70 110,30" fill="#ff4444" opacity="0.3"/>
        <text x="360" y="45" text-anchor="middle" font-size="11" fill="#cc0000" font-weight="bold">Zone 5: VO2 Max (90-100%)</text>

        <!-- Zone 4 (80-90% MHR) -->
        <polygon points="110,70 610,110 610,130 110,90" fill="#ff8800" opacity="0.3"/>
        <text x="360" y="85" text-anchor="middle" font-size="11" fill="#cc4400" font-weight="bold">Zone 4: Threshold (80-90%)</text>

        <!-- Zone 3 (70-80% MHR) -->
        <polygon points="110,90 610,130 610,170 110,130" fill="#ffcc00" opacity="0.3"/>
        <text x="360" y="125" text-anchor="middle" font-size="11" fill="#996600" font-weight="bold">Zone 3: Aerobic (70-80%)</text>

        <!-- Zone 2 (60-70% MHR) -->
        <polygon points="110,130 610,170 610,210 110,170" fill="#88cc00" opacity="0.3"/>
        <text x="360" y="165" text-anchor="middle" font-size="11" fill="#446600" font-weight="bold">Zone 2: Fat Burn (60-70%)</text>

        <!-- Zone 1 (50-60% MHR) -->
        <polygon points="110,170 610,210 610,250 110,210" fill="#44cc88" opacity="0.3"/>
        <text x="360" y="205" text-anchor="middle" font-size="11" fill="#226644" font-weight="bold">Zone 1: Recovery (50-60%)</text>

        <!-- Max HR line (220-age) -->
        <line x1="110" y1="50" x2="610" y2="90" stroke="#000" stroke-width="3"/>
        <text x="500" y="75" font-size="12" fill="#000" font-weight="bold">Max HR = 220 - Age</text>

        <!-- Grid lines -->
        <g stroke="#ddd" stroke-width="1" opacity="0.5">
            <line x1="110" y1="50" x2="110" y2="400"/>
            <line x1="210" y1="50" x2="210" y2="400"/>
            <line x1="310" y1="50" x2="310" y2="400"/>
            <line x1="410" y1="50" x2="410" y2="400"/>
            <line x1="510" y1="50" x2="510" y2="400"/>
            <line x1="610" y1="50" x2="610" y2="400"/>
        </g>
    </svg>
</div>

<h3>Age-Based Heart Rate Calculator</h3>
<p>Use the following calculator to determine your personal heart rate zones based on your age:</p>

<div class="panel2" style="padding: 15px; margin: 20px 0;">
    <table>
        <tr>
            <td width="100">Your Age:</td>
            <td><input type="number" id="age-input" value="30" class="innormal" min="15" max="80"></td>
            <td><input type="button" value="Calculate Zones" id="hr-calculate" style="margin-left: 10px;"></td>
        </tr>
    </table>

    <div id="hr-results" style="display: none; margin-top: 15px;">
        <h4>Your Heart Rate Zones:</h4>
        <table class="cinfoT" style="margin-top: 10px;">
            <tbody>
                <tr>
                    <td class="cinfoHd">Zone</td>
                    <td class="cinfoHd">Heart Rate Range</td>
                    <td class="cinfoHd">Training Focus</td>
                </tr>
                <tr>
                    <td><strong>Zone 1: Recovery</strong></td>
                    <td class="cinfoBodL" id="zone1-range">-</td>
                    <td>Easy pace, recovery runs</td>
                </tr>
                <tr>
                    <td><strong>Zone 2: Aerobic Base</strong></td>
                    <td class="cinfoBodL" id="zone2-range">-</td>
                    <td>Long runs, base building</td>
                </tr>
                <tr>
                    <td><strong>Zone 3: Aerobic</strong></td>
                    <td class="cinfoBodL" id="zone3-range">-</td>
                    <td>Tempo runs, steady efforts</td>
                </tr>
                <tr>
                    <td><strong>Zone 4: Threshold</strong></td>
                    <td class="cinfoBodL" id="zone4-range">-</td>
                    <td>Lactate threshold, race pace</td>
                </tr>
                <tr>
                    <td><strong>Zone 5: VO2 Max</strong></td>
                    <td class="cinfoBodL" id="zone5-range">-</td>
                    <td>Intervals, maximum effort</td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<script>
document.getElementById('hr-calculate').addEventListener('click', function() {
    const age = parseInt(document.getElementById('age-input').value);
    if (age >= 15 && age <= 80) {
        const maxHR = 220 - age;

        document.getElementById('zone1-range').textContent = Math.round(maxHR * 0.5) + ' - ' + Math.round(maxHR * 0.6) + ' BPM';
        document.getElementById('zone2-range').textContent = Math.round(maxHR * 0.6) + ' - ' + Math.round(maxHR * 0.7) + ' BPM';
        document.getElementById('zone3-range').textContent = Math.round(maxHR * 0.7) + ' - ' + Math.round(maxHR * 0.8) + ' BPM';
        document.getElementById('zone4-range').textContent = Math.round(maxHR * 0.8) + ' - ' + Math.round(maxHR * 0.9) + ' BPM';
        document.getElementById('zone5-range').textContent = Math.round(maxHR * 0.9) + ' - ' + Math.round(maxHR * 1.0) + ' BPM';

        document.getElementById('hr-results').style.display = 'block';
    } else {
        alert('Please enter an age between 15 and 80 years.');
    }
});
</script>

<br>
<h3>Aerobic vs. Anaerobic Exercise:</h3>

<p>Aerobic and anaerobic exercise are often mentioned in the context of endurance training and running. These types of exercise mainly differ based on the duration and the intensity of muscular contractions and the manner in which energy is generated within the muscle. Generally, anaerobic exercises (~80-90% MHR) involve short, intense bursts of activity while aerobic exercises (~70-80% MHR) involve light activity sustained over a long period of time. An exercise intensity level of 55-85% of MHR for 20-30 minutes is generally recommended to attain the best results from aerobic exercise.</p>

<p>In solely aerobic exercise, there is sufficient oxygen for a person's muscles to produce all the necessary energy for the exercise. In contrast, in anaerobic exercise, the cardiovascular system cannot supply muscles with oxygen quickly enough, and muscles break down sugar to supply the necessary energy, resulting in excess of lactate (a byproduct of glucose metabolism). Excess lactate causes the burning sensation in muscles typical of anaerobic exercises and eventually makes the continuation of exercise not possible if excess lactate is not allowed sufficient time to be removed from the bloodstream. Note that although lactate is also produced in aerobic conditions, it is used almost as quickly as it is formed at low levels of exercise, and only trace amounts leak into the bloodstream from the muscles.</p>

<p>Understanding aerobic exercise is particularly important when training for a long-distance activity such as a marathon. Determining a pace that can be maintained while using energy primarily derived through aerobic means, referred to as an "aerobic threshold pace," helps maintain a balance between fat and carbohydrate utilization. This pace requires a relatively low level of intensity, and is usually maintainable for a few hours. Increasing aerobic threshold pace allows for a faster sustainable pace and is a large aspect of many marathon training programs.</p>

<p>An anaerobic threshold pace is defined by some as the threshold at which glycogen, rather than oxygen, becomes the primary source of energy for the body. Note that while anaerobic training will result in a person becoming more fit overall, it is not necessarily ideal training for a marathon, since an anaerobic pace is not sustainable for long periods of time. This is not to say that a person should not perform any anaerobic training, as training at or slightly above their anaerobic threshold (the level of exercise intensity at which lactic acid builds up more quickly than it can be removed from the bloodstream) can also be beneficial.</p>

<p>Similarly to heart rate, the most accurate way to determine these thresholds is through testing within a lab setting. However, both aerobic and anaerobic thresholds can also be estimated using a number of different methods, some of which involve the use of a heart rate monitor. According to a 2005 study, the most accurate way to determine anaerobic threshold (outside of blood work in a lab) is a 30-minute time trial in which heart rate is monitored. In this time trial, a person must run at maximum effort, averaging their heart rate over the last 20 minutes of the run. The average heart rate over the last 20 minutes is an estimation of the person's anaerobic threshold heart rate, also known as lactate threshold heart rate (LTHR). It is important that the time trial be performed alone. If it is done in a group setting, the duration must be increased to 60 minutes rather than 30 minutes. Aerobic threshold heart rate can be estimated by subtracting 30 beats per minute from the anaerobic threshold heart rate.</p>

<p>Essentially, threshold training involves training to postpone the point at which lactate starts to build up in the bloodstream, which effectively postpones the point of fatigue, potentially allowing a person to run farther and faster.</p>

<h3>Training Pace Guidelines</h3>
<p>Different types of training runs should be performed at specific pace ranges to maximize their effectiveness. Here's a comprehensive guide to training paces:</p>

<table class="cinfoT" style="margin: 20px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">Training Type</td>
            <td class="cinfoHd">Pace Range</td>
            <td class="cinfoHd">Heart Rate Zone</td>
            <td class="cinfoHd">Purpose</td>
            <td class="cinfoHd">Example Workouts</td>
        </tr>
        <tr>
            <td><strong>Recovery Runs</strong></td>
            <td class="cinfoBodL">1-2 min/mile slower than easy pace</td>
            <td class="cinfoBodL">Zone 1 (50-60%)</td>
            <td>Active recovery, blood flow</td>
            <td>20-45 min easy jog</td>
        </tr>
        <tr>
            <td><strong>Easy/Base Runs</strong></td>
            <td class="cinfoBodL">Conversational pace</td>
            <td class="cinfoBodL">Zone 2 (60-70%)</td>
            <td>Aerobic base building</td>
            <td>30-120 min steady runs</td>
        </tr>
        <tr>
            <td><strong>Long Runs</strong></td>
            <td class="cinfoBodL">30-90 sec/mile slower than marathon pace</td>
            <td class="cinfoBodL">Zone 2-3 (60-80%)</td>
            <td>Endurance, fat adaptation</td>
            <td>90-180 min continuous runs</td>
        </tr>
        <tr>
            <td><strong>Tempo Runs</strong></td>
            <td class="cinfoBodL">15-30 sec/mile slower than 10K pace</td>
            <td class="cinfoBodL">Zone 3-4 (70-85%)</td>
            <td>Lactate threshold improvement</td>
            <td>20-40 min sustained efforts</td>
        </tr>
        <tr>
            <td><strong>Threshold Intervals</strong></td>
            <td class="cinfoBodL">10K to half marathon pace</td>
            <td class="cinfoBodL">Zone 4 (80-90%)</td>
            <td>Lactate buffering capacity</td>
            <td>4-8 x 5-8 min with 2-3 min rest</td>
        </tr>
        <tr>
            <td><strong>VO2 Max Intervals</strong></td>
            <td class="cinfoBodL">3K to 5K race pace</td>
            <td class="cinfoBodL">Zone 5 (90-100%)</td>
            <td>Maximum oxygen uptake</td>
            <td>5-8 x 3-5 min with equal rest</td>
        </tr>
        <tr>
            <td><strong>Speed/Neuromuscular</strong></td>
            <td class="cinfoBodL">800m to mile race pace</td>
            <td class="cinfoBodL">Zone 5 (95-100%)</td>
            <td>Running economy, speed</td>
            <td>6-12 x 30 sec to 2 min, full recovery</td>
        </tr>
    </tbody>
</table>

<h3>Pace Prediction and Race Equivalency</h3>
<p>Understanding the relationship between different race distances can help predict performance and set realistic goals. The following table shows equivalent performances across common race distances:</p>

<table class="cinfoT" style="margin: 20px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">5K Time</td>
            <td class="cinfoHd">10K Time</td>
            <td class="cinfoHd">Half Marathon</td>
            <td class="cinfoHd">Marathon</td>
            <td class="cinfoHd">Fitness Level</td>
        </tr>
        <tr>
            <td class="cinfoBodL">15:00</td>
            <td class="cinfoBodL">31:30</td>
            <td class="cinfoBodL">1:10:00</td>
            <td class="cinfoBodL">2:28:00</td>
            <td>Elite</td>
        </tr>
        <tr>
            <td class="cinfoBodL">18:00</td>
            <td class="cinfoBodL">37:30</td>
            <td class="cinfoBodL">1:22:00</td>
            <td class="cinfoBodL">2:52:00</td>
            <td>Competitive</td>
        </tr>
        <tr>
            <td class="cinfoBodL">20:00</td>
            <td class="cinfoBodL">41:30</td>
            <td class="cinfoBodL">1:30:00</td>
            <td class="cinfoBodL">3:10:00</td>
            <td>Advanced</td>
        </tr>
        <tr>
            <td class="cinfoBodL">22:00</td>
            <td class="cinfoBodL">45:30</td>
            <td class="cinfoBodL">1:38:00</td>
            <td class="cinfoBodL">3:28:00</td>
            <td>Good</td>
        </tr>
        <tr>
            <td class="cinfoBodL">25:00</td>
            <td class="cinfoBodL">52:00</td>
            <td class="cinfoBodL">1:52:00</td>
            <td class="cinfoBodL">4:00:00</td>
            <td>Average</td>
        </tr>
        <tr>
            <td class="cinfoBodL">30:00</td>
            <td class="cinfoBodL">62:30</td>
            <td class="cinfoBodL">2:15:00</td>
            <td class="cinfoBodL">4:45:00</td>
            <td>Recreational</td>
        </tr>
        <tr>
            <td class="cinfoBodL">35:00</td>
            <td class="cinfoBodL">73:00</td>
            <td class="cinfoBodL">2:38:00</td>
            <td class="cinfoBodL">5:30:00</td>
            <td>Beginner</td>
        </tr>
    </tbody>
</table>

<h3>Environmental Factors Affecting Pace</h3>
<p>Various environmental conditions can significantly impact running pace and performance. Understanding these factors helps in adjusting expectations and training accordingly:</p>

<h4>Temperature Effects</h4>
<table class="cinfoT" style="margin: 15px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">Temperature Range</td>
            <td class="cinfoHd">Pace Adjustment</td>
            <td class="cinfoHd">Recommendations</td>
        </tr>
        <tr>
            <td>Below 32°F (0°C)</td>
            <td class="cinfoBodL">+10-20 sec/mile</td>
            <td>Longer warm-up, layer clothing, watch for ice</td>
        </tr>
        <tr>
            <td>32-50°F (0-10°C)</td>
            <td class="cinfoBodL">Optimal</td>
            <td>Ideal racing conditions</td>
        </tr>
        <tr>
            <td>50-60°F (10-15°C)</td>
            <td class="cinfoBodL">+0-5 sec/mile</td>
            <td>Very good conditions</td>
        </tr>
        <tr>
            <td>60-70°F (15-21°C)</td>
            <td class="cinfoBodL">+5-15 sec/mile</td>
            <td>Good conditions, stay hydrated</td>
        </tr>
        <tr>
            <td>70-80°F (21-27°C)</td>
            <td class="cinfoBodL">+15-30 sec/mile</td>
            <td>Increase hydration, consider early morning runs</td>
        </tr>
        <tr>
            <td>Above 80°F (27°C)</td>
            <td class="cinfoBodL">+30-60 sec/mile</td>
            <td>High risk, frequent water breaks, avoid midday</td>
        </tr>
    </tbody>
</table>

<h4>Altitude and Terrain Effects</h4>
<ul style="margin: 15px 0; padding-left: 30px;">
    <li><strong>Altitude:</strong> Expect 2-3% slower pace per 1,000 feet above 3,000 feet elevation</li>
    <li><strong>Hills:</strong> Add 15-30 seconds per mile for every 1% grade increase</li>
    <li><strong>Trail running:</strong> Generally 1-2 minutes per mile slower than road pace</li>
    <li><strong>Wind:</strong> Headwinds can slow pace by 5-20 seconds per mile depending on strength</li>
    <li><strong>Surface:</strong> Soft surfaces (sand, snow) can slow pace by 30-60 seconds per mile</li>
</ul>

<h3>Practical Training Tips</h3>
<div style="background-color: #f0f8ff; padding: 15px; margin: 20px 0; border-left: 4px solid #0066cc;">
    <h4>Weekly Training Structure</h4>
    <p><strong>80/20 Rule:</strong> Spend 80% of your training time at easy/moderate intensity (Zones 1-2) and 20% at moderate-hard to hard intensity (Zones 3-5).</p>

    <h4>Progression Guidelines</h4>
    <ul>
        <li>Increase weekly mileage by no more than 10% per week</li>
        <li>Include a recovery week every 3-4 weeks with 20-30% reduced volume</li>
        <li>Build base fitness for 8-12 weeks before adding intense workouts</li>
        <li>Allow 48-72 hours between hard workout sessions</li>
    </ul>

    <h4>Pace Monitoring Tools</h4>
    <ul>
        <li><strong>GPS watches:</strong> Real-time pace feedback and heart rate monitoring</li>
        <li><strong>Smartphone apps:</strong> Convenient tracking with audio cues</li>
        <li><strong>Heart rate monitors:</strong> More accurate intensity measurement than pace alone</li>
        <li><strong>Perceived exertion:</strong> Learn to gauge effort level without devices</li>
    </ul>
</div>

<h3>Common Pacing Mistakes</h3>
<ol style="margin: 15px 0; padding-left: 30px;">
    <li><strong>Starting too fast:</strong> Most common race mistake, leads to significant slowdown</li>
    <li><strong>Ignoring environmental conditions:</strong> Not adjusting pace for weather or terrain</li>
    <li><strong>Training too hard on easy days:</strong> Prevents proper recovery and adaptation</li>
    <li><strong>Inconsistent pacing:</strong> Large pace variations waste energy and reduce efficiency</li>
    <li><strong>Neglecting warm-up:</strong> Starting workouts or races without proper preparation</li>
    <li><strong>Comparing to others:</strong> Everyone has different fitness levels and goals</li>
</ol>

<p><strong>Remember:</strong> Pace is just one tool in your training arsenal. Listen to your body, consider external factors, and adjust your expectations accordingly. Consistent, patient training at appropriate intensities will lead to long-term improvement and enjoyment in running.</p>

<br>

<?php get_footer(); ?>
