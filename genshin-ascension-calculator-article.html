<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.character-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    padding: 20px;
    margin: 15px;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}
.character-card:hover {
    transform: translateY(-5px);
}
.element-badge {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    margin: 5px;
    color: white;
}
.pyro { background-color: #ff6b35; }
.hydro { background-color: #4fc3f7; }
.anemo { background-color: #4dd0e1; }
.electro { background-color: #ab47bc; }
.dendro { background-color: #8bc34a; }
.cryo { background-color: #81c784; }
.geo { background-color: #ffb74d; }
.rarity-5 { border: 3px solid #ffd700; }
.rarity-4 { border: 3px solid #9c27b0; }
.material-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 20px 0;
}
.material-card {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.ascension-level {
    background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    text-align: center;
    color: #2c3e50;
    font-weight: bold;
}
.total-cost {
    background: linear-gradient(135deg, #a8e6cf 0%, #88d8a3 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    text-align: center;
    color: #2c3e50;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.level-selector {
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
</style>

<h1>Genshin Impact Ascension Calculator</h1>
<p>Calculate the materials and Mora needed to ascend your Genshin Impact characters from any level to your target level. Plan your farming routes and track your progress with our comprehensive ascension calculator.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="ascensionform" id="ascension-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Ascension Calculator</a></li>
                <li><a href="#" data-tab="materials">Material Guide</a></li>
                <li><a href="#" data-tab="characters">Character Database</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="level-selector">
                <h3 style="color: #2c3e50; margin-bottom: 20px;">🌟 Character Selection</h3>
                <div style="margin-bottom: 20px;">
                    <label for="character-select"><strong>Character:</strong></label>
                    <select name="character-select" id="character-select" class="innormal" style="width: 250px; margin-left: 10px;">
                        <option value="">Select Character</option>
                        <option value="albedo">Albedo (5⭐ Geo)</option>
                        <option value="amber">Amber (4⭐ Pyro)</option>
                        <option value="ayaka">Ayaka (5⭐ Cryo)</option>
                        <option value="ayato">Ayato (5⭐ Hydro)</option>
                        <option value="barbara">Barbara (4⭐ Hydro)</option>
                        <option value="beidou">Beidou (4⭐ Electro)</option>
                        <option value="bennett">Bennett (4⭐ Pyro)</option>
                        <option value="childe">Childe (5⭐ Hydro)</option>
                        <option value="chongyun">Chongyun (4⭐ Cryo)</option>
                        <option value="diluc">Diluc (5⭐ Pyro)</option>
                        <option value="diona">Diona (4⭐ Cryo)</option>
                        <option value="eula">Eula (5⭐ Cryo)</option>
                        <option value="fischl">Fischl (4⭐ Electro)</option>
                        <option value="ganyu">Ganyu (5⭐ Cryo)</option>
                        <option value="hutao">Hu Tao (5⭐ Pyro)</option>
                        <option value="jean">Jean (5⭐ Anemo)</option>
                        <option value="kazuha">Kazuha (5⭐ Anemo)</option>
                        <option value="keqing">Keqing (5⭐ Electro)</option>
                        <option value="klee">Klee (5⭐ Pyro)</option>
                        <option value="kokomi">Kokomi (5⭐ Hydro)</option>
                        <option value="lisa">Lisa (4⭐ Electro)</option>
                        <option value="mona">Mona (5⭐ Hydro)</option>
                        <option value="ningguang">Ningguang (4⭐ Geo)</option>
                        <option value="noelle">Noelle (4⭐ Geo)</option>
                        <option value="qiqi">Qiqi (5⭐ Cryo)</option>
                        <option value="raiden">Raiden Shogun (5⭐ Electro)</option>
                        <option value="razor">Razor (4⭐ Electro)</option>
                        <option value="rosaria">Rosaria (4⭐ Cryo)</option>
                        <option value="sucrose">Sucrose (4⭐ Anemo)</option>
                        <option value="tartaglia">Tartaglia (5⭐ Hydro)</option>
                        <option value="venti">Venti (5⭐ Anemo)</option>
                        <option value="xiangling">Xiangling (4⭐ Pyro)</option>
                        <option value="xiao">Xiao (5⭐ Anemo)</option>
                        <option value="xingqiu">Xingqiu (4⭐ Hydro)</option>
                        <option value="xinyan">Xinyan (4⭐ Pyro)</option>
                        <option value="yae">Yae Miko (5⭐ Electro)</option>
                        <option value="yanfei">Yanfei (4⭐ Pyro)</option>
                        <option value="zhongli">Zhongli (5⭐ Geo)</option>
                    </select>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-top: 20px;">
                    <div>
                        <label for="current-level"><strong>Current Level:</strong></label>
                        <select name="current-level" id="current-level" class="innormal" style="width: 100%; margin-top: 5px;">
                            <option value="1">Level 1</option>
                            <option value="20">Level 20</option>
                            <option value="20+">Level 20+ (Ascended)</option>
                            <option value="40">Level 40</option>
                            <option value="40+">Level 40+ (Ascended)</option>
                            <option value="50">Level 50</option>
                            <option value="50+">Level 50+ (Ascended)</option>
                            <option value="60">Level 60</option>
                            <option value="60+">Level 60+ (Ascended)</option>
                            <option value="70">Level 70</option>
                            <option value="70+">Level 70+ (Ascended)</option>
                            <option value="80">Level 80</option>
                            <option value="80+">Level 80+ (Ascended)</option>
                            <option value="90">Level 90</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="target-level"><strong>Target Level:</strong></label>
                        <select name="target-level" id="target-level" class="innormal" style="width: 100%; margin-top: 5px;">
                            <option value="20">Level 20</option>
                            <option value="20+">Level 20+ (Ascended)</option>
                            <option value="40">Level 40</option>
                            <option value="40+">Level 40+ (Ascended)</option>
                            <option value="50">Level 50</option>
                            <option value="50+">Level 50+ (Ascended)</option>
                            <option value="60">Level 60</option>
                            <option value="60+">Level 60+ (Ascended)</option>
                            <option value="70">Level 70</option>
                            <option value="70+">Level 70+ (Ascended)</option>
                            <option value="80">Level 80</option>
                            <option value="80+">Level 80+ (Ascended)</option>
                            <option value="90" selected>Level 90</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Materials" id="calculate-ascension-btn" style="padding: 15px 30px; font-size: 18px; background-color: #667eea; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Clear Selection" id="clear-ascension-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Material requirements are based on official Genshin Impact data. Boss materials and local specialties may vary by character.
        </div>
        
        <div id="ascension-results" style="display: none; margin-top: 30px;">
            <div class="total-cost">
                <h3 style="margin-bottom: 20px;">📊 Total Materials Required</h3>
                <div id="ascension-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-ascension-btn');
    const clearBtn = document.getElementById('clear-ascension-btn');
    const resultsDiv = document.getElementById('ascension-results');
    const resultsContent = document.getElementById('ascension-results-content');
    
    // Character data with ascension materials
    const characterData = {
        diluc: {
            name: 'Diluc',
            rarity: 5,
            element: 'pyro',
            materials: {
                gem: 'Agnidus Agate',
                boss: 'Everflame Seed',
                local: 'Small Lamp Grass',
                common: 'Recruit\'s Insignia'
            }
        },
        ganyu: {
            name: 'Ganyu',
            rarity: 5,
            element: 'cryo',
            materials: {
                gem: 'Shivada Jade',
                boss: 'Hoarfrost Core',
                local: 'Qingxin',
                common: 'Whopperflower Nectar'
            }
        },
        zhongli: {
            name: 'Zhongli',
            rarity: 5,
            element: 'geo',
            materials: {
                gem: 'Prithiva Topaz',
                boss: 'Basalt Pillar',
                local: 'Cor Lapis',
                common: 'Slime Condensate'
            }
        },
        bennett: {
            name: 'Bennett',
            rarity: 4,
            element: 'pyro',
            materials: {
                gem: 'Agnidus Agate',
                boss: 'Everflame Seed',
                local: 'Windwheel Aster',
                common: 'Treasure Hoarder Insignia'
            }
        }
    };
    
    // Ascension costs by level and rarity
    const ascensionCosts = {
        4: {
            '20+': { mora: 20000, gem_sliver: 1, gem_fragment: 0, gem_chunk: 0, gem_gemstone: 0, boss: 0, local: 3, common1: 3 },
            '40+': { mora: 40000, gem_sliver: 0, gem_fragment: 3, gem_chunk: 0, gem_gemstone: 0, boss: 2, local: 10, common2: 15 },
            '50+': { mora: 60000, gem_sliver: 0, gem_fragment: 0, gem_chunk: 3, gem_gemstone: 0, boss: 4, local: 20, common2: 12 },
            '60+': { mora: 80000, gem_sliver: 0, gem_fragment: 0, gem_chunk: 6, gem_gemstone: 0, boss: 8, local: 30, common3: 18 },
            '70+': { mora: 100000, gem_sliver: 0, gem_fragment: 0, gem_chunk: 0, gem_gemstone: 3, boss: 12, local: 45, common3: 12 },
            '80+': { mora: 120000, gem_sliver: 0, gem_fragment: 0, gem_chunk: 0, gem_gemstone: 6, boss: 20, local: 60, common4: 24 }
        },
        5: {
            '20+': { mora: 20000, gem_sliver: 1, gem_fragment: 0, gem_chunk: 0, gem_gemstone: 0, boss: 0, local: 3, common1: 3 },
            '40+': { mora: 40000, gem_sliver: 0, gem_fragment: 3, gem_chunk: 0, gem_gemstone: 0, boss: 2, local: 10, common2: 15 },
            '50+': { mora: 60000, gem_sliver: 0, gem_fragment: 0, gem_chunk: 3, gem_gemstone: 0, boss: 4, local: 20, common2: 12 },
            '60+': { mora: 80000, gem_sliver: 0, gem_fragment: 0, gem_chunk: 6, gem_gemstone: 0, boss: 8, local: 30, common3: 18 },
            '70+': { mora: 100000, gem_sliver: 0, gem_fragment: 0, gem_chunk: 0, gem_gemstone: 3, boss: 12, local: 45, common3: 12 },
            '80+': { mora: 120000, gem_sliver: 0, gem_fragment: 0, gem_chunk: 0, gem_gemstone: 6, boss: 20, local: 60, common4: 24 }
        }
    };
    
    calculateBtn.addEventListener('click', function() {
        const character = document.getElementById('character-select').value;
        const currentLevel = document.getElementById('current-level').value;
        const targetLevel = document.getElementById('target-level').value;
        
        if (!character) {
            alert('Please select a character.');
            return;
        }
        
        if (!currentLevel || !targetLevel) {
            alert('Please select both current and target levels.');
            return;
        }
        
        const materials = calculateMaterials(character, currentLevel, targetLevel);
        displayResults(materials, character, currentLevel, targetLevel);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('ascension-calculator-form').reset();
        resultsDiv.style.display = 'none';
    });
    
    function calculateMaterials(characterKey, current, target) {
        const char = characterData[characterKey] || characterData.diluc;
        const rarity = char.rarity;
        
        // Define ascension levels
        const levels = ['20+', '40+', '50+', '60+', '70+', '80+'];
        const currentIndex = levels.indexOf(current);
        const targetIndex = levels.indexOf(target);
        
        let totalMaterials = {
            mora: 0,
            gem_sliver: 0,
            gem_fragment: 0,
            gem_chunk: 0,
            gem_gemstone: 0,
            boss: 0,
            local: 0,
            common1: 0,
            common2: 0,
            common3: 0,
            common4: 0
        };
        
        // Calculate materials needed for each ascension
        for (let i = Math.max(0, currentIndex + 1); i <= targetIndex; i++) {
            const levelCost = ascensionCosts[rarity][levels[i]];
            if (levelCost) {
                Object.keys(levelCost).forEach(material => {
                    totalMaterials[material] += levelCost[material];
                });
            }
        }
        
        return {
            character: char,
            materials: totalMaterials,
            levels: { current, target }
        };
    }
    
    function displayResults(result, characterKey, current, target) {
        const char = result.character;
        const materials = result.materials;
        
        let html = `
            <div class="character-card rarity-${char.rarity}" style="margin-bottom: 25px;">
                <h3>${char.name}</h3>
                <div class="element-badge ${char.element}">${char.element.toUpperCase()}</div>
                <div style="margin-top: 10px;">
                    <strong>${current} → ${target}</strong>
                </div>
            </div>
            
            <div class="material-grid">
                <div class="material-card">
                    <h4 style="color: #f39c12;">💰 Mora</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #f39c12;">${materials.mora.toLocaleString()}</div>
                </div>
        `;
        
        if (materials.gem_sliver > 0) {
            html += `
                <div class="material-card">
                    <h4 style="color: #3498db;">💎 ${char.materials.gem} Sliver</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #3498db;">${materials.gem_sliver}</div>
                </div>
            `;
        }
        
        if (materials.gem_fragment > 0) {
            html += `
                <div class="material-card">
                    <h4 style="color: #9b59b6;">💎 ${char.materials.gem} Fragment</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #9b59b6;">${materials.gem_fragment}</div>
                </div>
            `;
        }
        
        if (materials.gem_chunk > 0) {
            html += `
                <div class="material-card">
                    <h4 style="color: #e74c3c;">💎 ${char.materials.gem} Chunk</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #e74c3c;">${materials.gem_chunk}</div>
                </div>
            `;
        }
        
        if (materials.gem_gemstone > 0) {
            html += `
                <div class="material-card">
                    <h4 style="color: #f1c40f;">💎 ${char.materials.gem} Gemstone</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #f1c40f;">${materials.gem_gemstone}</div>
                </div>
            `;
        }
        
        if (materials.boss > 0) {
            html += `
                <div class="material-card">
                    <h4 style="color: #e67e22;">👹 ${char.materials.boss}</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #e67e22;">${materials.boss}</div>
                </div>
            `;
        }
        
        if (materials.local > 0) {
            html += `
                <div class="material-card">
                    <h4 style="color: #27ae60;">🌿 ${char.materials.local}</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #27ae60;">${materials.local}</div>
                </div>
            `;
        }
        
        const commonTotal = materials.common1 + materials.common2 + materials.common3 + materials.common4;
        if (commonTotal > 0) {
            html += `
                <div class="material-card">
                    <h4 style="color: #95a5a6;">⚔️ ${char.materials.common}</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #95a5a6;">${commonTotal}</div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        (Various tiers)
                    </div>
                </div>
            `;
        }
        
        html += `
            </div>
            
            <div style="background-color: rgba(255,255,255,0.9); border-radius: 10px; padding: 20px; margin-top: 25px; color: #2c3e50;">
                <h4>📝 Farming Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Boss Materials:</strong> Farm weekly bosses for guaranteed drops</li>
                    <li><strong>Gems:</strong> Convert lower tier gems using Dust of Azoth</li>
                    <li><strong>Local Specialties:</strong> Respawn every 48 hours in the overworld</li>
                    <li><strong>Common Materials:</strong> Farm from enemies or buy from shops</li>
                    <li><strong>Mora:</strong> Complete daily commissions and use Ley Line Outcrops</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
