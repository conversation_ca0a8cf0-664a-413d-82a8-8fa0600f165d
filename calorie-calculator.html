<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Calorie Calculator</title>
    <meta name="description" content="Calculate your daily calorie needs based on age, gender, height, weight, and activity level. Free calorie calculator for weight loss, maintenance, and gain.">
    <link rel="stylesheet" href="./Carbohydrate Calculator_files/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Mobile responsive styles */
        @media screen and (max-width: 768px) {
            #contentout { width: 100% !important; padding: 0 10px; }
            #content { width: 100% !important; margin: 0; }
            #right { display: none; }
            .panel2 { width: 100% !important; padding: 5px !important; }
            table { width: 100% !important; font-size: 14px; }
            td { padding: 8px 4px !important; word-wrap: break-word; }
            input, select { width: 100% !important; max-width: 100% !important; box-sizing: border-box; font-size: 16px; }
            .inlongest, .innormal, .inlongesthalf { width: 100% !important; }
            h1 { font-size: 24px; }
            .cinfoT { font-size: 12px; overflow-x: auto; }
            .cinfoT td { padding: 4px 2px !important; }
            .cbcontainer { font-size: 12px; margin: 2px 0; }
        }
        @media screen and (max-width: 480px) {
            table td:first-child { width: 30% !important; }
            table td:last-child { width: 70% !important; }
            input[type="submit"], input[type="button"] {
                width: 100% !important; margin: 5px 0 !important; padding: 12px !important; font-size: 14px;
            }
            .inuifoot, .inuiinch { width: 45% !important; display: inline-block; }
            .cbcontainer { display: block; margin: 3px 0; }
        }
    </style>
    <link rel="apple-touch-icon" sizes="180x180" href="https://d26tpo4cm8sb6k.cloudfront.net/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-16x16.png">
    <link rel="manifest" href="https://www.calculator.net/manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="https://www.calculator.net/"><img src="./Carbohydrate Calculator_files/calculator-white.svg" width="208" height="22" alt="Calculator.net"></a></div>
        <div id="login"><a href="https://www.calculator.net/my-account/sign-in.php">sign in</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/" itemprop="item"><span itemprop="name">home</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/fitness-and-health-calculator.html" itemprop="item"><span itemprop="name">fitness &amp; health</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="#" itemprop="item"><span itemprop="name">calorie calculator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="window.print();return false;">Print</a></div>
        
        <h1>Calorie Calculator</h1>
        <p>The <i>Calorie Calculator</i> can be used to estimate the number of calories a person needs to consume each day. This calculator can also provide some simple guidelines for gaining or losing weight.</p>
        
        <div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
        <span id="ucframe"></span>
        
        <div id="topmenu" class="topmenucenter">
            <ul>
                <li><a href="#" onclick="return showUnits('standard');">US Units</a></li>
                <li id="menuon"><a href="#" onclick="return showUnits('metric');">Metric Units</a></li>
                <li><a href="#" onclick="return showUnits('other');">Other Units</a></li>
            </ul>
        </div>

        <div class="panel2" style="padding:10px;">
            <form name="calorieForm" id="calorie-calculator-form">
                <table id="calorie-input-table" width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">Age</td>
                            <td width="550"><input type="number" name="age" id="age" value="25" class="inlongesthalf" min="15" max="80"> ages 15 - 80</td>
                        </tr>
                        <tr>
                            <td>Gender</td>
                            <td>
                                <label for="male" class="cbcontainer"><input type="radio" name="gender" id="male" value="male" checked=""><span class="rbmark"></span>male</label> &nbsp; 
                                <label for="female" class="cbcontainer"><input type="radio" name="gender" id="female" value="female"><span class="rbmark"></span>female</label>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- US Units -->
                <table width="620" id="standard-units" bgcolor="#eeeeee" style="display: none;">
                    <tbody>
                        <tr>
                            <td width="70">Height</td>
                            <td width="550">
                                <input type="number" name="feet" id="feet" value="5" class="inlongesthalf inuifoot" min="0" max="8"><span class="inuifootspan">feet</span>
                                &nbsp;&nbsp;
                                <input type="number" name="inches" id="inches" value="10" class="inlongesthalf inuiinch" min="0" max="11"><span class="inuiinchspan">inches</span>
                            </td>
                        </tr>
                        <tr>
                            <td>Weight</td>
                            <td><input type="number" name="pounds" id="pounds" value="160" class="inlongest inuipound" min="50" max="1000"><span class="inuipoundspan">pounds</span></td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- Metric Units -->
                <table width="620" id="metric-units" bgcolor="#eeeeee" style="display: block;">
                    <tbody>
                        <tr>
                            <td width="70">Height</td>
                            <td width="550"><input type="number" name="height_cm" id="height_cm" value="180" class="inlongest inuick" min="100" max="250" step="0.1"><span class="inuickspan">cm</span></td>
                        </tr>
                        <tr>
                            <td>Weight</td>
                            <td><input type="number" name="weight_kg" id="weight_kg" value="70" class="inlongest inuick" min="20" max="300" step="0.1"><span class="inuickspan">kg</span></td>
                        </tr>
                    </tbody>
                </table>
                
                <table width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">Activity</td>
                            <td width="550">
                                <select id="activity" name="activity" class="inlongest">
                                    <option value="1.2">Sedentary: little or no exercise</option>
                                    <option value="1.375" selected="">Light: exercise 1-3 times/week</option>
                                    <option value="1.465">Moderate: exercise 4-5 times/week</option>
                                    <option value="1.55">Active: daily exercise or intense exercise 3-4 times/week</option>
                                    <option value="1.725">Very Active: intense exercise 6-7 times/week</option>
                                    <option value="1.9">Extra Active: very intense exercise daily, or physical job</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td width="70">Goal</td>
                            <td width="550">
                                <select id="goal" name="goal" class="inlongest">
                                    <option value="maintain" selected="">Maintain current weight</option>
                                    <option value="mild_loss">Mild weight loss (0.25 kg/week)</option>
                                    <option value="loss">Weight loss (0.5 kg/week)</option>
                                    <option value="extreme_loss">Extreme weight loss (1 kg/week)</option>
                                    <option value="mild_gain">Mild weight gain (0.25 kg/week)</option>
                                    <option value="gain">Weight gain (0.5 kg/week)</option>
                                    <option value="extreme_gain">Extreme weight gain (1 kg/week)</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td width="70">&nbsp;</td>
                            <td width="550">
                                <input type="submit" name="x" value="Calculate Calories" id="calculate-btn">
                                <input type="button" value="Clear" id="clear-btn" onclick="clearCalorieForm();">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </form>
        </div>

        <ul style="margin-top:0px;margin-left:-20px;">
            <li class="smalltext"><b>Exercise:</b> 15-30 minutes of elevated heart rate activity.</li>
            <li class="smalltext"><b>Intense exercise:</b> 45-120 minutes of elevated heart rate activity.</li>
            <li class="smalltext"><b>Very intense exercise:</b> 2+ hours of elevated heart rate activity.</li>
        </ul>

        <!-- Results Display Area -->
        <div id="calorie-results" style="display: none; margin-top: 20px;">
            <div class="h2result" id="calorie-results-content"></div>
        </div>

        <br>
        <fieldset>
            <legend>Related</legend>
            <a href="#">BMR Calculator</a> | 
            <a href="#">BMI Calculator</a> | 
            <a href="#">Macro Calculator</a>
        </fieldset>

        <br>
        <h3>What are Calories?</h3>
        <p>A calorie is a unit of energy. In nutrition, calories refer to the energy people get from the food and drink they consume, and the energy they use in physical activity. Calories are listed in the nutritional information on all food packaging. Many weight loss programs center around reducing the intake of calories.</p>

        <h3>Calorie Requirements by Age and Gender</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Age Group</td>
                    <td class="cinfoHdL">Male (calories/day)</td>
                    <td class="cinfoHdL">Female (calories/day)</td>
                </tr>
                <tr><td><strong>19-30 years</strong></td><td>2,400-3,000</td><td>1,800-2,400</td></tr>
                <tr><td><strong>31-50 years</strong></td><td>2,200-3,000</td><td>1,800-2,200</td></tr>
                <tr><td><strong>51+ years</strong></td><td>2,000-2,800</td><td>1,600-2,200</td></tr>
            </tbody>
        </table>

        <h3>Factors Affecting Calorie Needs</h3>
        <ul>
            <li><strong>Age:</strong> Calorie needs generally decrease with age due to slower metabolism</li>
            <li><strong>Gender:</strong> Men typically require more calories than women due to larger body size</li>
            <li><strong>Height and Weight:</strong> Larger bodies require more energy to maintain</li>
            <li><strong>Activity Level:</strong> More active individuals need more calories</li>
            <li><strong>Muscle Mass:</strong> Muscle tissue burns more calories than fat tissue</li>
            <li><strong>Health Conditions:</strong> Some medical conditions can affect metabolism</li>
        </ul>

        <h3>Weight Management Guidelines</h3>
        <p>To lose 1 pound (0.45 kg) per week, you need to create a calorie deficit of approximately 3,500 calories, or 500 calories per day. This can be achieved through a combination of diet and exercise. For healthy weight loss, it's generally recommended to lose 1-2 pounds per week.</p>

        <h3>Tips for Healthy Eating</h3>
        <ul>
            <li>Focus on nutrient-dense foods like fruits, vegetables, lean proteins, and whole grains</li>
            <li>Control portion sizes to manage calorie intake</li>
            <li>Stay hydrated by drinking plenty of water</li>
            <li>Limit processed foods, sugary drinks, and excessive fats</li>
            <li>Eat regular meals to maintain stable energy levels</li>
            <li>Consider consulting a registered dietitian for personalized advice</li>
        </ul>
    </div>
    
    <div id="right">
        <div style="padding-top:10px; min-height:280px; text-align:center;">
            <div style="width: 300px; height: 250px; background: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; color: #666;">
                Advertisement Space
            </div>
        </div>
        
        <form name="calcSearchForm" onsubmit="return false;" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td><input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators..."></td>
                        <td><span id="bluebtn" onclick="performSearch();">Search</span></td>
                    </tr>
                    <tr>
                        <td colspan="2"><div id="calcSearchOut"></div></td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="#">Fitness and Health Calculators</a>
            </div>
            <div id="occontent">
                <a href="#">BMI Calculator</a>
                <a href="#">Body Fat Calculator</a>
                <a href="#">Calorie Calculator</a>
                <a href="#">BMR Calculator</a>
                <a href="#">Ideal Weight Calculator</a>
                <a href="#">Macro Calculator</a>
                <a href="#">Pace Calculator</a>
                <a href="#">Pregnancy Calculator</a>
                <a href="#">Heart Rate Calculator</a>
                <a href="#">More Fitness Calculators</a>
            </div>
            <div id="ocother">
                <a href="#">Financial</a> | 
                <a href="#">Fitness and Health</a> | 
                <a href="#">Math</a> | 
                <a href="#">Other</a>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="#">about us</a> | 
            <a href="#">sitemap</a> | 
            <a href="#">terms of use</a> | 
            <a href="#">privacy policy</a> &nbsp;  © 2008 - 2025 <a href="#">calculator.net</a>
        </div>
    </div>
</div>

<div class="topNavAbs">
    <a href="#">Financial</a>
    <a href="#" class="topNavOn">Fitness &amp; Health</a>
    <a href="#">Math</a>
    <a href="#">Other</a>
</div>

<script>
let currentUnits = 'metric';

function showUnits(unitType) {
    document.getElementById('standard-units').style.display = 'none';
    document.getElementById('metric-units').style.display = 'none';
    
    if (unitType === 'standard') {
        document.getElementById('standard-units').style.display = 'block';
    } else if (unitType === 'metric') {
        document.getElementById('metric-units').style.display = 'block';
    }
    
    const menuItems = document.querySelectorAll('#topmenu ul li');
    menuItems.forEach(item => {
        item.id = '';
        const link = item.querySelector('a');
        if (link && link.textContent.toLowerCase().includes(unitType === 'standard' ? 'us' : unitType)) {
            item.id = 'menuon';
        }
    });
    
    currentUnits = unitType;
    return false;
}

document.getElementById('calculate-btn').addEventListener('click', function(e) {
    e.preventDefault();
    calculateCalories();
});

function calculateCalories() {
    const age = parseInt(document.getElementById('age').value);
    const gender = document.querySelector('input[name="gender"]:checked').value;
    const activity = parseFloat(document.getElementById('activity').value);
    const goal = document.getElementById('goal').value;
    
    let height, weight;
    
    if (currentUnits === 'metric') {
        height = parseFloat(document.getElementById('height_cm').value);
        weight = parseFloat(document.getElementById('weight_kg').value);
    } else {
        const feet = parseFloat(document.getElementById('feet').value) || 0;
        const inches = parseFloat(document.getElementById('inches').value) || 0;
        const pounds = parseFloat(document.getElementById('pounds').value) || 0;
        
        height = ((feet * 12) + inches) * 2.54; // Convert to cm
        weight = pounds * 0.453592; // Convert to kg
    }
    
    if (age <= 0 || height <= 0 || weight <= 0) {
        alert('Please enter valid values for all fields');
        return;
    }
    
    // Calculate BMR using Mifflin-St Jeor Equation
    let bmr;
    if (gender === 'male') {
        bmr = 10 * weight + 6.25 * height - 5 * age + 5;
    } else {
        bmr = 10 * weight + 6.25 * height - 5 * age - 161;
    }
    
    // Calculate TDEE (Total Daily Energy Expenditure)
    const tdee = bmr * activity;
    
    // Calculate calories based on goal
    const goalCalories = getGoalCalories(tdee, goal);
    
    const resultsDiv = document.getElementById('calorie-results-content');
    resultsDiv.innerHTML = `
        Maintenance Calories: ${Math.round(tdee)} calories/day<br>
        ${getGoalText(goal)}: ${Math.round(goalCalories)} calories/day<br>
        BMR: ${Math.round(bmr)} calories/day
    `;
    document.getElementById('calorie-results').style.display = 'block';
}

function getGoalCalories(tdee, goal) {
    const adjustments = {
        'maintain': 0,
        'mild_loss': -275,
        'loss': -550,
        'extreme_loss': -1100,
        'mild_gain': 275,
        'gain': 550,
        'extreme_gain': 1100
    };
    
    return tdee + (adjustments[goal] || 0);
}

function getGoalText(goal) {
    const texts = {
        'maintain': 'Maintain Weight',
        'mild_loss': 'Mild Weight Loss',
        'loss': 'Weight Loss',
        'extreme_loss': 'Extreme Weight Loss',
        'mild_gain': 'Mild Weight Gain',
        'gain': 'Weight Gain',
        'extreme_gain': 'Extreme Weight Gain'
    };
    
    return texts[goal] || 'Goal';
}

function clearCalorieForm() {
    document.getElementById('age').value = '25';
    document.getElementById('male').checked = true;
    document.getElementById('activity').selectedIndex = 1;
    document.getElementById('goal').selectedIndex = 0;
    
    if (currentUnits === 'metric') {
        document.getElementById('height_cm').value = '180';
        document.getElementById('weight_kg').value = '70';
    } else {
        document.getElementById('feet').value = '5';
        document.getElementById('inches').value = '10';
        document.getElementById('pounds').value = '160';
    }
    
    document.getElementById('calorie-results').style.display = 'none';
}

function performSearch() {
    const searchTerm = document.getElementById('calcSearchTerm').value;
    const searchOut = document.getElementById('calcSearchOut');
    
    if (searchTerm.trim() === '') {
        searchOut.innerHTML = '';
        return;
    }
    
    const calculators = [
        'BMI Calculator', 'Body Fat Calculator', 'Calorie Calculator', 
        'BMR Calculator', 'Ideal Weight Calculator', 'Macro Calculator'
    ];
    
    const results = calculators.filter(calc => 
        calc.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    if (results.length > 0) {
        searchOut.innerHTML = '<div>' + results.map(result => 
            `<a href="#" style="display: block; padding: 2px 0;">${result}</a>`
        ).join('') + '</div>';
    } else {
        searchOut.innerHTML = '<div>No calculators found</div>';
    }
}

showUnits('metric');
</script>

</body>
</html>
