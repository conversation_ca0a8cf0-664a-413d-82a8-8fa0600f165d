<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.morph-selector {
    background-color: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}
.morph-selector.selected {
    border-color: #007bff;
    background-color: #e7f3ff;
}
.parent-section {
    background-color: #f1f3f4;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
    border-left: 5px solid #4285f4;
}
.offspring-result {
    background-color: #e8f5e8;
    border: 2px solid #28a745;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}
.morph-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    margin: 15px 0;
}
.morph-card {
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.probability-bar {
    background-color: #e9ecef;
    border-radius: 10px;
    height: 20px;
    margin: 10px 0;
    overflow: hidden;
}
.probability-fill {
    height: 100%;
    background: linear-gradient(90deg, #28a745, #20c997);
    border-radius: 10px;
    transition: width 0.3s ease;
}
.gene-type {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    margin: 2px;
}
.dominant { background-color: #dc3545; color: white; }
.recessive { background-color: #007bff; color: white; }
.codominant { background-color: #ffc107; color: black; }
.incomplete-dominant { background-color: #6f42c1; color: white; }
</style>

<h1>Ball Python Morph Calculator</h1>
<p>Calculate ball python breeding outcomes and predict offspring morphs with our comprehensive genetic calculator. This tool helps breeders determine the probability of different morphs when breeding ball pythons with various genetic traits.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="morphform" id="morph-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Morph Calculator</a></li>
                <li><a href="#" data-tab="genetics">Genetics Guide</a></li>
                <li><a href="#" data-tab="morphs">Morph Database</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="parent-section">
                <h3 style="color: #4285f4; margin-bottom: 15px;">🐍 Male Parent (Sire)</h3>
                <div id="male-morphs">
                    <div class="morph-grid">
                        <div class="morph-card">
                            <label><input type="checkbox" name="male_albino" value="albino"> Albino</label>
                            <div class="gene-type recessive">Recessive</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="male_piebald" value="piebald"> Piebald</label>
                            <div class="gene-type recessive">Recessive</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="male_pastel" value="pastel"> Pastel</label>
                            <div class="gene-type dominant">Dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="male_spider" value="spider"> Spider</label>
                            <div class="gene-type dominant">Dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="male_champagne" value="champagne"> Champagne</label>
                            <div class="gene-type codominant">Co-dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="male_banana" value="banana"> Banana</label>
                            <div class="gene-type codominant">Co-dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="male_fire" value="fire"> Fire</label>
                            <div class="gene-type codominant">Co-dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="male_mojave" value="mojave"> Mojave</label>
                            <div class="gene-type codominant">Co-dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="male_lesser" value="lesser"> Lesser</label>
                            <div class="gene-type codominant">Co-dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="male_cinnamon" value="cinnamon"> Cinnamon</label>
                            <div class="gene-type codominant">Co-dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="male_enchi" value="enchi"> Enchi</label>
                            <div class="gene-type dominant">Dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="male_pinstripe" value="pinstripe"> Pinstripe</label>
                            <div class="gene-type dominant">Dominant</div>
                        </div>
                    </div>
                </div>
            </div>

            <div class="parent-section">
                <h3 style="color: #ea4335; margin-bottom: 15px;">🐍 Female Parent (Dam)</h3>
                <div id="female-morphs">
                    <div class="morph-grid">
                        <div class="morph-card">
                            <label><input type="checkbox" name="female_albino" value="albino"> Albino</label>
                            <div class="gene-type recessive">Recessive</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="female_piebald" value="piebald"> Piebald</label>
                            <div class="gene-type recessive">Recessive</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="female_pastel" value="pastel"> Pastel</label>
                            <div class="gene-type dominant">Dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="female_spider" value="spider"> Spider</label>
                            <div class="gene-type dominant">Dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="female_champagne" value="champagne"> Champagne</label>
                            <div class="gene-type codominant">Co-dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="female_banana" value="banana"> Banana</label>
                            <div class="gene-type codominant">Co-dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="female_fire" value="fire"> Fire</label>
                            <div class="gene-type codominant">Co-dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="female_mojave" value="mojave"> Mojave</label>
                            <div class="gene-type codominant">Co-dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="female_lesser" value="lesser"> Lesser</label>
                            <div class="gene-type codominant">Co-dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="female_cinnamon" value="cinnamon"> Cinnamon</label>
                            <div class="gene-type codominant">Co-dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="female_enchi" value="enchi"> Enchi</label>
                            <div class="gene-type dominant">Dominant</div>
                        </div>
                        <div class="morph-card">
                            <label><input type="checkbox" name="female_pinstripe" value="pinstripe"> Pinstripe</label>
                            <div class="gene-type dominant">Dominant</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Breeding Outcomes" id="calculate-morph-btn" style="padding: 15px 30px; font-size: 18px; background-color: #28a745; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Clear All" id="clear-morph-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> This calculator provides theoretical breeding outcomes based on Mendelian genetics. Actual results may vary due to environmental factors and genetic complexity.
        </div>
        
        <div id="morph-results" style="display: none; margin-top: 30px;">
            <div class="offspring-result">
                <h3 style="text-align: center; color: #28a745; margin-bottom: 25px;">🥚 Predicted Offspring Results</h3>
                <div id="morph-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-morph-btn');
    const clearBtn = document.getElementById('clear-morph-btn');
    const resultsDiv = document.getElementById('morph-results');
    const resultsContent = document.getElementById('morph-results-content');
    
    // Morph genetics data
    const morphGenetics = {
        albino: { type: 'recessive', symbol: 'a' },
        piebald: { type: 'recessive', symbol: 'p' },
        pastel: { type: 'dominant', symbol: 'Pa' },
        spider: { type: 'dominant', symbol: 'Sp' },
        champagne: { type: 'codominant', symbol: 'Ch' },
        banana: { type: 'codominant', symbol: 'Ba' },
        fire: { type: 'codominant', symbol: 'Fi' },
        mojave: { type: 'codominant', symbol: 'Mo' },
        lesser: { type: 'codominant', symbol: 'Le' },
        cinnamon: { type: 'codominant', symbol: 'Ci' },
        enchi: { type: 'dominant', symbol: 'En' },
        pinstripe: { type: 'dominant', symbol: 'Pi' }
    };
    
    calculateBtn.addEventListener('click', function() {
        const maleTraits = getSelectedTraits('male');
        const femaleTraits = getSelectedTraits('female');
        
        if (maleTraits.length === 0 && femaleTraits.length === 0) {
            alert('Please select at least one morph for either parent.');
            return;
        }
        
        const outcomes = calculateBreedingOutcomes(maleTraits, femaleTraits);
        displayResults(outcomes);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.querySelectorAll('input[type="checkbox"]').forEach(cb => cb.checked = false);
        resultsDiv.style.display = 'none';
    });
    
    function getSelectedTraits(parent) {
        const traits = [];
        document.querySelectorAll(`input[name^="${parent}_"]:checked`).forEach(input => {
            traits.push(input.value);
        });
        return traits;
    }
    
    function calculateBreedingOutcomes(maleTraits, femaleTraits) {
        const outcomes = new Map();
        
        // Simple calculation for demonstration
        // In reality, this would be much more complex with proper Punnett squares
        
        if (maleTraits.length === 0 && femaleTraits.length === 0) {
            outcomes.set('Normal', 100);
            return outcomes;
        }
        
        // Calculate basic outcomes
        const allTraits = [...new Set([...maleTraits, ...femaleTraits])];
        
        // Normal offspring (no morphs)
        let normalChance = 25;
        
        // Single morph offspring
        allTraits.forEach(trait => {
            const genetics = morphGenetics[trait];
            let chance;
            
            if (genetics.type === 'recessive') {
                if (maleTraits.includes(trait) && femaleTraits.includes(trait)) {
                    chance = 25; // Both parents have the gene
                } else {
                    chance = 0; // Need both parents for recessive
                }
            } else if (genetics.type === 'dominant') {
                if (maleTraits.includes(trait) || femaleTraits.includes(trait)) {
                    chance = 50; // One parent has the gene
                } else {
                    chance = 0;
                }
            } else if (genetics.type === 'codominant') {
                if (maleTraits.includes(trait) && femaleTraits.includes(trait)) {
                    chance = 25; // Super form
                    outcomes.set(`Super ${trait.charAt(0).toUpperCase() + trait.slice(1)}`, chance);
                    chance = 50; // Normal form
                } else if (maleTraits.includes(trait) || femaleTraits.includes(trait)) {
                    chance = 50; // Heterozygous
                } else {
                    chance = 0;
                }
            }
            
            if (chance > 0) {
                outcomes.set(trait.charAt(0).toUpperCase() + trait.slice(1), chance);
                normalChance -= chance * 0.3; // Reduce normal chance
            }
        });
        
        // Combination morphs (simplified)
        if (allTraits.length > 1) {
            const combinations = [];
            for (let i = 0; i < allTraits.length; i++) {
                for (let j = i + 1; j < allTraits.length; j++) {
                    const combo = `${allTraits[i].charAt(0).toUpperCase() + allTraits[i].slice(1)} ${allTraits[j].charAt(0).toUpperCase() + allTraits[j].slice(1)}`;
                    combinations.push(combo);
                }
            }
            
            combinations.forEach(combo => {
                outcomes.set(combo, 12.5);
                normalChance -= 5;
            });
        }
        
        if (normalChance > 0) {
            outcomes.set('Normal', Math.max(normalChance, 5));
        }
        
        return outcomes;
    }
    
    function displayResults(outcomes) {
        let html = '<div class="morph-grid">';
        
        outcomes.forEach((percentage, morph) => {
            const roundedPercentage = Math.round(percentage);
            html += `
                <div class="morph-card">
                    <h4 style="color: #333; margin-bottom: 10px;">${morph}</h4>
                    <div class="probability-bar">
                        <div class="probability-fill" style="width: ${roundedPercentage}%"></div>
                    </div>
                    <div style="font-weight: bold; color: #28a745;">${roundedPercentage}%</div>
                    <div style="font-size: 12px; color: #666; margin-top: 5px;">
                        ${Math.round(roundedPercentage * 0.1)} out of 10 eggs
                    </div>
                </div>
            `;
        });
        
        html += '</div>';
        
        html += `
            <div style="background-color: #fff3cd; border: 1px solid #ffeaa7; border-radius: 8px; padding: 15px; margin-top: 20px;">
                <h4 style="color: #856404;">Important Notes:</h4>
                <ul style="color: #856404; margin: 10px 0;">
                    <li>These are theoretical probabilities based on simple Mendelian genetics</li>
                    <li>Actual breeding results may vary due to genetic complexity</li>
                    <li>Some morphs may have additional health considerations</li>
                    <li>Always research compatibility and health implications before breeding</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>

<a name="genetics-guide"></a>
<br><h2>Ball Python Genetics Guide</h2>
<p>Understanding ball python genetics is essential for successful breeding programs. Ball pythons exhibit various inheritance patterns that determine the appearance of offspring.</p>

<h3>Types of Genetic Inheritance</h3>
<table class="cinfoT">
    <tbody>
        <tr>
            <td class="cinfoHd">Inheritance Type</td>
            <td class="cinfoHd">Description</td>
            <td class="cinfoHd">Examples</td>
            <td class="cinfoHd">Breeding Ratio</td>
        </tr>
        <tr>
            <td><strong>Recessive</strong></td>
            <td>Requires two copies of the gene to be visible</td>
            <td>Albino, Piebald, Clown, Axanthic</td>
            <td class="cinfoBodL">25% visual, 50% het, 25% normal</td>
        </tr>
        <tr>
            <td><strong>Dominant</strong></td>
            <td>Only needs one copy to be visible</td>
            <td>Pastel, Spider, Pinstripe, Enchi</td>
            <td class="cinfoBodL">50% visual, 50% normal</td>
        </tr>
        <tr>
            <td><strong>Co-dominant</strong></td>
            <td>One copy shows effect, two copies show super form</td>
            <td>Fire, Mojave, Lesser, Champagne</td>
            <td class="cinfoBodL">25% super, 50% het, 25% normal</td>
        </tr>
        <tr>
            <td><strong>Incomplete Dominant</strong></td>
            <td>Similar to co-dominant but with blended traits</td>
            <td>Cinnamon, Black Pastel</td>
            <td class="cinfoBodL">25% super, 50% het, 25% normal</td>
        </tr>
    </tbody>
</table>

<h3>Popular Ball Python Morphs</h3>
<div style="background-color: #f8f9fa; padding: 20px; border-radius: 10px; margin: 20px 0;">
    <h4>Recessive Morphs</h4>
    <div class="morph-grid">
        <div class="morph-card">
            <h5>Albino</h5>
            <p style="font-size: 14px;">Lacks melanin, resulting in yellow and white coloration with red eyes.</p>
            <div class="gene-type recessive">Recessive</div>
        </div>
        <div class="morph-card">
            <h5>Piebald</h5>
            <p style="font-size: 14px;">Irregular white patches with normal colored areas.</p>
            <div class="gene-type recessive">Recessive</div>
        </div>
        <div class="morph-card">
            <h5>Clown</h5>
            <p style="font-size: 14px;">Distinctive pattern with reduced markings and bright colors.</p>
            <div class="gene-type recessive">Recessive</div>
        </div>
        <div class="morph-card">
            <h5>Axanthic</h5>
            <p style="font-size: 14px;">Lacks yellow pigment, appearing in black, white, and gray.</p>
            <div class="gene-type recessive">Recessive</div>
        </div>
    </div>

    <h4>Dominant Morphs</h4>
    <div class="morph-grid">
        <div class="morph-card">
            <h5>Pastel</h5>
            <p style="font-size: 14px;">Enhanced colors with increased yellow and reduced dark pigment.</p>
            <div class="gene-type dominant">Dominant</div>
        </div>
        <div class="morph-card">
            <h5>Spider</h5>
            <p style="font-size: 14px;">Thin, web-like pattern with head wobble neurological trait.</p>
            <div class="gene-type dominant">Dominant</div>
        </div>
        <div class="morph-card">
            <h5>Pinstripe</h5>
            <p style="font-size: 14px;">Thin dorsal stripe with reduced side patterns.</p>
            <div class="gene-type dominant">Dominant</div>
        </div>
        <div class="morph-card">
            <h5>Enchi</h5>
            <p style="font-size: 14px;">Enhanced pattern with increased orange and yellow tones.</p>
            <div class="gene-type dominant">Dominant</div>
        </div>
    </div>

    <h4>Co-dominant Morphs</h4>
    <div class="morph-grid">
        <div class="morph-card">
            <h5>Fire</h5>
            <p style="font-size: 14px;">Bright golden colors with clean sides. Super form is very light.</p>
            <div class="gene-type codominant">Co-dominant</div>
        </div>
        <div class="morph-card">
            <h5>Mojave</h5>
            <p style="font-size: 14px;">Blue-gray base with flame-like patterns. Super is leucistic.</p>
            <div class="gene-type codominant">Co-dominant</div>
        </div>
        <div class="morph-card">
            <h5>Lesser</h5>
            <p style="font-size: 14px;">Similar to Mojave with subtle differences. Super is blue-eyed leucistic.</p>
            <div class="gene-type codominant">Co-dominant</div>
        </div>
        <div class="morph-card">
            <h5>Champagne</h5>
            <p style="font-size: 14px;">Tan and lavender coloration. Super form has unique patterns.</p>
            <div class="gene-type codominant">Co-dominant</div>
        </div>
    </div>
</div>

<h3>Breeding Calculations and Punnett Squares</h3>
<p>Understanding how to calculate breeding outcomes is crucial for ball python breeders. Here are the basic principles:</p>

<table class="cinfoT" style="margin: 20px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">Cross Type</td>
            <td class="cinfoHd">Parents</td>
            <td class="cinfoHd">Expected Offspring</td>
            <td class="cinfoHd">Probability</td>
        </tr>
        <tr>
            <td><strong>Recessive x Normal</strong></td>
            <td>Albino x Normal</td>
            <td>100% Het Albino</td>
            <td class="cinfoBodL">All carriers</td>
        </tr>
        <tr>
            <td><strong>Het x Het</strong></td>
            <td>Het Albino x Het Albino</td>
            <td>25% Albino, 50% Het, 25% Normal</td>
            <td class="cinfoBodL">1:2:1 ratio</td>
        </tr>
        <tr>
            <td><strong>Dominant x Normal</strong></td>
            <td>Pastel x Normal</td>
            <td>50% Pastel, 50% Normal</td>
            <td class="cinfoBodL">1:1 ratio</td>
        </tr>
        <tr>
            <td><strong>Co-dom x Normal</strong></td>
            <td>Fire x Normal</td>
            <td>50% Fire, 50% Normal</td>
            <td class="cinfoBodL">1:1 ratio</td>
        </tr>
        <tr>
            <td><strong>Co-dom x Co-dom</strong></td>
            <td>Fire x Fire</td>
            <td>25% Super Fire, 50% Fire, 25% Normal</td>
            <td class="cinfoBodL">1:2:1 ratio</td>
        </tr>
    </tbody>
</table>

<h3>Designer Morphs and Combinations</h3>
<p>Many of the most valuable ball pythons are combinations of multiple genes. Here are some popular designer morphs:</p>

<table class="cinfoT" style="margin: 20px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">Designer Morph</td>
            <td class="cinfoHd">Genetic Combination</td>
            <td class="cinfoHd">Description</td>
            <td class="cinfoHd">Market Value</td>
        </tr>
        <tr>
            <td><strong>Sunset</strong></td>
            <td>Fire + Pastel</td>
            <td>Bright orange and yellow with clean sides</td>
            <td class="cinfoBodL">High</td>
        </tr>
        <tr>
            <td><strong>Bumblebee</strong></td>
            <td>Spider + Pastel</td>
            <td>Bright yellow with spider pattern</td>
            <td class="cinfoBodL">Medium</td>
        </tr>
        <tr>
            <td><strong>Dreamsicle</strong></td>
            <td>Albino + Piebald</td>
            <td>White patches on albino background</td>
            <td class="cinfoBodL">Very High</td>
        </tr>
        <tr>
            <td><strong>Blue-Eyed Leucistic</strong></td>
            <td>Lesser + Mojave (or other combinations)</td>
            <td>Pure white with blue eyes</td>
            <td class="cinfoBodL">Very High</td>
        </tr>
        <tr>
            <td><strong>Pied Pastel</strong></td>
            <td>Piebald + Pastel</td>
            <td>Enhanced colors on piebald pattern</td>
            <td class="cinfoBodL">High</td>
        </tr>
        <tr>
            <td><strong>Champagne Albino</strong></td>
            <td>Champagne + Albino</td>
            <td>Lavender and cream coloration</td>
            <td class="cinfoBodL">High</td>
        </tr>
    </tbody>
</table>
