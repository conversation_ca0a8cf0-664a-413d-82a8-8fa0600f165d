<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.warmup-interface {
    background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.timer-input {
    background-color: #fce4ec;
    border: 2px solid #e91e63;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.warmup-result {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.countdown-timer {
    background: white;
    border-radius: 20px;
    padding: 30px;
    margin: 20px 0;
    color: #2c3e50;
    text-align: center;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
}
.timer-display {
    font-size: 48px;
    font-weight: bold;
    color: #e91e63;
    margin: 20px 0;
    font-family: 'Courier New', monospace;
}
.progress-ring {
    width: 120px;
    height: 120px;
    margin: 20px auto;
}
.progress-ring-circle {
    stroke: #e91e63;
    stroke-width: 8;
    fill: transparent;
    stroke-dasharray: 314;
    stroke-dashoffset: 314;
    transform: rotate(-90deg);
    transform-origin: 50% 50%;
    transition: stroke-dashoffset 1s ease;
}
.warmup-status {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    margin: 5px;
    color: white;
}
.warming { background-color: #ff9800; }
.ready { background-color: #4caf50; }
.pending { background-color: #9e9e9e; }
</style>

<h1>Dexcom G7 Warm Up Time Calculator</h1>
<p>Calculate and track your Dexcom G7 sensor warm-up time with our precise countdown timer. Monitor the 30-minute initialization period and get notified when your sensor is ready for accurate glucose readings.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="warmupform" id="warmup-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Warm-up Timer</a></li>
                <li><a href="#" data-tab="tracking">Session Tracking</a></li>
                <li><a href="#" data-tab="tips">Optimization Tips</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="warmup-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">⏱️ G7 Warm-up Timer</h3>
                
                <div class="timer-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">📱 Sensor Insertion Details</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="insertion-datetime"><strong>Insertion Date & Time:</strong></label>
                            <input type="datetime-local" id="insertion-datetime" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                        
                        <div>
                            <label for="warmup-duration"><strong>Warm-up Duration:</strong></label>
                            <select id="warmup-duration" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="30" selected>30 minutes (Standard G7)</option>
                                <option value="60">60 minutes (Extended warm-up)</option>
                                <option value="15">15 minutes (Quick start - testing)</option>
                                <option value="custom">Custom duration</option>
                            </select>
                            <input type="number" id="custom-duration" value="30" min="1" max="120" class="innormal" style="width: 100%; margin-top: 5px; display: none;" placeholder="Minutes">
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="sensor-generation"><strong>Sensor Type:</strong></label>
                            <select id="sensor-generation" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="g7" selected>Dexcom G7</option>
                                <option value="g6">Dexcom G6 (2 hours)</option>
                                <option value="g5">Dexcom G5 (2 hours)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="notification-preference"><strong>Notification Preference:</strong></label>
                            <select id="notification-preference" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="completion" selected>At completion only</option>
                                <option value="intervals">Every 10 minutes</option>
                                <option value="frequent">Every 5 minutes</option>
                                <option value="silent">Silent mode</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #f1f8e9; border: 2px solid #4caf50; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🎯 Quick Actions</h4>
                    
                    <div style="text-align: center;">
                        <button type="button" id="start-now-btn" style="padding: 12px 24px; margin: 5px; background-color: #4caf50; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;">
                            🚀 Start Warm-up Now
                        </button>
                        
                        <button type="button" id="schedule-btn" style="padding: 12px 24px; margin: 5px; background-color: #2196f3; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;">
                            📅 Schedule for Later
                        </button>
                        
                        <button type="button" id="reset-timer-btn" style="padding: 12px 24px; margin: 5px; background-color: #ff9800; color: white; border: none; border-radius: 8px; cursor: pointer; font-size: 16px;">
                            🔄 Reset Timer
                        </button>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Warm-up Time" id="calculate-warmup-btn" style="padding: 15px 30px; font-size: 18px; background-color: #e91e63; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Clear All" id="clear-warmup-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> G7 sensors typically require 30 minutes to warm up. Do not calibrate or rely on readings during this period.
        </div>
        
        <div id="warmup-results" style="display: none; margin-top: 30px;">
            <div class="warmup-result">
                <h3 style="margin-bottom: 20px;">⏰ Warm-up Status</h3>
                <div id="warmup-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-warmup-btn');
    const clearBtn = document.getElementById('clear-warmup-btn');
    const startNowBtn = document.getElementById('start-now-btn');
    const scheduleBtn = document.getElementById('schedule-btn');
    const resetTimerBtn = document.getElementById('reset-timer-btn');
    const resultsDiv = document.getElementById('warmup-results');
    const resultsContent = document.getElementById('warmup-results-content');
    const warmupDurationSelect = document.getElementById('warmup-duration');
    const customDurationInput = document.getElementById('custom-duration');
    const sensorGenerationSelect = document.getElementById('sensor-generation');
    
    let countdownInterval = null;
    let warmupStartTime = null;
    let warmupEndTime = null;
    
    // Set default insertion time to now
    const now = new Date();
    const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
    document.getElementById('insertion-datetime').value = localDateTime;
    
    // Handle custom duration input
    warmupDurationSelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customDurationInput.style.display = 'block';
        } else {
            customDurationInput.style.display = 'none';
        }
    });
    
    // Handle sensor generation change
    sensorGenerationSelect.addEventListener('change', function() {
        if (this.value === 'g6' || this.value === 'g5') {
            warmupDurationSelect.value = '120';
            warmupDurationSelect.disabled = true;
        } else {
            warmupDurationSelect.value = '30';
            warmupDurationSelect.disabled = false;
        }
    });
    
    startNowBtn.addEventListener('click', function() {
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
        document.getElementById('insertion-datetime').value = localDateTime;
        calculateBtn.click();
    });
    
    scheduleBtn.addEventListener('click', function() {
        calculateBtn.click();
    });
    
    resetTimerBtn.addEventListener('click', function() {
        if (countdownInterval) {
            clearInterval(countdownInterval);
            countdownInterval = null;
        }
        resultsDiv.style.display = 'none';
    });
    
    calculateBtn.addEventListener('click', function() {
        const insertionDateTime = document.getElementById('insertion-datetime').value;
        const warmupDurationValue = warmupDurationSelect.value;
        const customDuration = parseInt(customDurationInput.value) || 30;
        const sensorGeneration = sensorGenerationSelect.value;
        const notificationPreference = document.getElementById('notification-preference').value;
        
        if (!insertionDateTime) {
            alert('Please select the sensor insertion date and time.');
            return;
        }
        
        let warmupMinutes = 30; // Default G7
        
        if (sensorGeneration === 'g6' || sensorGeneration === 'g5') {
            warmupMinutes = 120; // 2 hours for G6/G5
        } else if (warmupDurationValue === 'custom') {
            warmupMinutes = customDuration;
        } else {
            warmupMinutes = parseInt(warmupDurationValue);
        }
        
        const warmupData = calculateWarmupTime(insertionDateTime, warmupMinutes, 
            sensorGeneration, notificationPreference);
        
        displayWarmupResults(warmupData);
        startCountdown(warmupData);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('warmup-calculator-form').reset();
        if (countdownInterval) {
            clearInterval(countdownInterval);
            countdownInterval = null;
        }
        const now = new Date();
        const localDateTime = new Date(now.getTime() - now.getTimezoneOffset() * 60000).toISOString().slice(0, 16);
        document.getElementById('insertion-datetime').value = localDateTime;
        resultsDiv.style.display = 'none';
    });
    
    function calculateWarmupTime(insertionDateTime, warmupMinutes, sensorGeneration, notificationPreference) {
        const insertionTime = new Date(insertionDateTime);
        const currentTime = new Date();
        const warmupEndTime = new Date(insertionTime.getTime() + (warmupMinutes * 60 * 1000));
        
        const timeElapsed = Math.max(0, currentTime - insertionTime);
        const timeRemaining = Math.max(0, warmupEndTime - currentTime);
        
        const minutesElapsed = Math.floor(timeElapsed / (1000 * 60));
        const minutesRemaining = Math.ceil(timeRemaining / (1000 * 60));
        
        const progressPercentage = Math.min(100, (minutesElapsed / warmupMinutes) * 100);
        
        let status = 'pending';
        if (currentTime >= insertionTime && currentTime < warmupEndTime) {
            status = 'warming';
        } else if (currentTime >= warmupEndTime) {
            status = 'ready';
        }
        
        return {
            insertionTime: insertionTime,
            warmupEndTime: warmupEndTime,
            warmupMinutes: warmupMinutes,
            minutesElapsed: minutesElapsed,
            minutesRemaining: minutesRemaining,
            progressPercentage: progressPercentage,
            status: status,
            sensorGeneration: sensorGeneration,
            notificationPreference: notificationPreference,
            timeElapsed: timeElapsed,
            timeRemaining: timeRemaining
        };
    }
    
    function displayWarmupResults(data) {
        const sensorNames = {
            g7: 'Dexcom G7',
            g6: 'Dexcom G6',
            g5: 'Dexcom G5'
        };
        
        const statusText = {
            pending: 'Scheduled',
            warming: 'Warming Up',
            ready: 'Ready for Use'
        };
        
        const statusClass = data.status;
        
        let html = `
            <div style="margin-bottom: 25px;">
                <div class="warmup-status ${statusClass}">
                    ${statusText[data.status]}
                </div>
                <div style="font-size: 18px; margin-top: 10px;">
                    ${sensorNames[data.sensorGeneration]} - ${data.warmupMinutes} minute warm-up
                </div>
            </div>
            
            <div class="countdown-timer">
                <div style="display: flex; justify-content: center; align-items: center;">
                    <svg class="progress-ring" width="120" height="120">
                        <circle class="progress-ring-circle" cx="60" cy="60" r="50" 
                                style="stroke-dashoffset: ${314 - (314 * data.progressPercentage / 100)};"></circle>
                    </svg>
                </div>
                
                <div class="timer-display" id="countdown-display">
                    ${data.status === 'ready' ? 'READY!' : formatTime(data.timeRemaining)}
                </div>
                
                <div style="font-size: 16px; color: #666; margin-bottom: 20px;">
                    ${data.status === 'ready' ? 'Sensor is ready for glucose readings' : 
                      data.status === 'warming' ? 'Sensor is warming up...' : 
                      'Warm-up will begin at insertion time'}
                </div>
                
                <div style="background-color: #f5f5f5; border-radius: 8px; padding: 15px; margin: 20px 0;">
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; text-align: left;">
                        <div>
                            <strong>Insertion Time:</strong><br>
                            ${data.insertionTime.toLocaleString()}
                        </div>
                        <div>
                            <strong>Ready Time:</strong><br>
                            ${data.warmupEndTime.toLocaleString()}
                        </div>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 15px; margin-top: 20px;">
                    <div style="background-color: #e3f2fd; padding: 15px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #1976d2;">${data.minutesElapsed}</div>
                        <div style="font-size: 12px; color: #666;">Minutes Elapsed</div>
                    </div>
                    
                    <div style="background-color: #fff3e0; padding: 15px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #f57c00;">${data.minutesRemaining}</div>
                        <div style="font-size: 12px; color: #666;">Minutes Remaining</div>
                    </div>
                    
                    <div style="background-color: #e8f5e9; padding: 15px; border-radius: 8px; text-align: center;">
                        <div style="font-size: 24px; font-weight: bold; color: #388e3c;">${data.progressPercentage.toFixed(0)}%</div>
                        <div style="font-size: 12px; color: #666;">Progress</div>
                    </div>
                </div>
            </div>
            
            <div style="background: white; border-radius: 10px; padding: 20px; margin: 20px 0; color: #2c3e50;">
                <h4 style="color: #e91e63; margin-bottom: 15px;">📋 Warm-up Guidelines</h4>
                
                <div style="background-color: ${data.status === 'warming' ? '#fff3e0' : '#f5f5f5'}; border-radius: 8px; padding: 15px; margin: 15px 0;">
                    <h5 style="color: ${data.status === 'warming' ? '#f57c00' : '#2c3e50'};">
                        ${data.status === 'warming' ? '⚠️ During Warm-up Period' : 
                          data.status === 'ready' ? '✅ Sensor Ready' : '📅 Scheduled Warm-up'}
                    </h5>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        ${data.status === 'warming' ? `
                            <li>Do not calibrate the sensor</li>
                            <li>Readings may be inaccurate or unavailable</li>
                            <li>Keep sensor dry and avoid excessive movement</li>
                            <li>Wait for completion notification</li>
                        ` : data.status === 'ready' ? `
                            <li>Sensor is ready for accurate glucose readings</li>
                            <li>You can now rely on glucose values</li>
                            <li>Calibration may be performed if needed</li>
                            <li>Monitor for any adhesion issues</li>
                        ` : `
                            <li>Warm-up will begin automatically at insertion time</li>
                            <li>Ensure proper sensor placement</li>
                            <li>Have your receiver/phone ready</li>
                            <li>Plan for the warm-up period</li>
                        `}
                    </ul>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 Warm-up Optimization Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Timing:</strong> Insert sensor when you have 30+ minutes available</li>
                    <li><strong>Placement:</strong> Ensure proper insertion depth and angle</li>
                    <li><strong>Environment:</strong> Avoid extreme temperatures during warm-up</li>
                    <li><strong>Activity:</strong> Limit vigorous activity during initial period</li>
                    <li><strong>Hydration:</strong> Stay well-hydrated for optimal sensor performance</li>
                    <li><strong>Patience:</strong> Allow full warm-up time for best accuracy</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
    
    function startCountdown(data) {
        if (countdownInterval) {
            clearInterval(countdownInterval);
        }
        
        if (data.status === 'ready') {
            return; // No countdown needed
        }
        
        countdownInterval = setInterval(function() {
            const currentTime = new Date();
            const timeRemaining = Math.max(0, data.warmupEndTime - currentTime);
            
            if (timeRemaining <= 0) {
                clearInterval(countdownInterval);
                document.getElementById('countdown-display').textContent = 'READY!';
                
                // Show notification if enabled
                if (data.notificationPreference !== 'silent' && 'Notification' in window) {
                    new Notification('Dexcom G7 Ready!', {
                        body: 'Your sensor warm-up is complete. Readings are now available.',
                        icon: '/favicon.ico'
                    });
                }
                
                // Refresh the display
                calculateBtn.click();
                return;
            }
            
            const countdownElement = document.getElementById('countdown-display');
            if (countdownElement) {
                countdownElement.textContent = formatTime(timeRemaining);
            }
            
            // Update progress ring
            const totalTime = data.warmupMinutes * 60 * 1000;
            const elapsed = totalTime - timeRemaining;
            const progressPercentage = (elapsed / totalTime) * 100;
            
            const progressRing = document.querySelector('.progress-ring-circle');
            if (progressRing) {
                const offset = 314 - (314 * progressPercentage / 100);
                progressRing.style.strokeDashoffset = offset;
            }
            
        }, 1000);
    }
    
    function formatTime(milliseconds) {
        const totalSeconds = Math.floor(milliseconds / 1000);
        const hours = Math.floor(totalSeconds / 3600);
        const minutes = Math.floor((totalSeconds % 3600) / 60);
        const seconds = totalSeconds % 60;
        
        if (hours > 0) {
            return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        } else {
            return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
        }
    }
    
    // Request notification permission
    if ('Notification' in window && Notification.permission === 'default') {
        Notification.requestPermission();
    }
});
</script>
