<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.trend-interface {
    background: linear-gradient(135deg, #9c27b0 0%, #7b1fa2 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.glucose-input {
    background-color: #f3e5f5;
    border: 2px solid #9c27b0;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.trend-result {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.trend-arrow {
    font-size: 48px;
    margin: 20px 0;
    display: inline-block;
}
.rising-fast { color: #f44336; }
.rising { color: #ff9800; }
.stable { color: #4caf50; }
.falling { color: #ff9800; }
.falling-fast { color: #f44336; }
.glucose-chart {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.reading-entry {
    display: grid;
    grid-template-columns: 1fr 1fr 80px;
    gap: 10px;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    background-color: #f5f5f5;
    border-radius: 8px;
}
.prediction-box {
    background-color: #e8f5e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
</style>

<h1>Dexcom G7 Glucose Trend Calculator</h1>
<p>Calculate and analyze glucose trends from your Dexcom G7 readings. Track rate of change, predict future glucose levels, and get actionable insights for diabetes management with our comprehensive trend analysis tool.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="trendform" id="trend-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Trend Calculator</a></li>
                <li><a href="#" data-tab="analysis">Trend Analysis</a></li>
                <li><a href="#" data-tab="predictions">Predictions</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="trend-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">📈 G7 Glucose Trend Calculator</h3>
                
                <div class="glucose-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">📊 Glucose Readings</h4>
                    
                    <div style="margin-bottom: 15px;">
                        <div class="reading-entry" style="background-color: #9c27b0; color: white; font-weight: bold;">
                            <div>Glucose Reading (mg/dL)</div>
                            <div>Time</div>
                            <div>Action</div>
                        </div>
                        
                        <div id="readings-container">
                            <div class="reading-entry">
                                <input type="number" class="glucose-reading innormal" value="120" min="40" max="400" placeholder="mg/dL" style="width: 100%;">
                                <input type="time" class="reading-time innormal" style="width: 100%;">
                                <button type="button" class="remove-reading" style="background: #f44336; color: white; border: none; border-radius: 4px; padding: 5px;">❌</button>
                            </div>
                        </div>
                        
                        <div style="text-align: center; margin-top: 15px;">
                            <button type="button" id="add-reading-btn" style="padding: 10px 20px; background-color: #4caf50; color: white; border: none; border-radius: 8px; cursor: pointer;">
                                ➕ Add Reading
                            </button>
                            <button type="button" id="auto-populate-btn" style="padding: 10px 20px; background-color: #2196f3; color: white; border: none; border-radius: 8px; cursor: pointer; margin-left: 10px;">
                                🔄 Auto-populate Sample Data
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="prediction-box">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🎯 Analysis Settings</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="trend-period"><strong>Trend Analysis Period:</strong></label>
                            <select id="trend-period" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="15">Last 15 minutes</option>
                                <option value="30" selected>Last 30 minutes</option>
                                <option value="60">Last 1 hour</option>
                                <option value="120">Last 2 hours</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="prediction-time"><strong>Prediction Timeframe:</strong></label>
                            <select id="prediction-time" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="15">15 minutes ahead</option>
                                <option value="30" selected>30 minutes ahead</option>
                                <option value="60">1 hour ahead</option>
                                <option value="90">1.5 hours ahead</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="target-range"><strong>Target Range:</strong></label>
                            <select id="target-range" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="70-180" selected>70-180 mg/dL (Standard)</option>
                                <option value="70-140">70-140 mg/dL (Tight)</option>
                                <option value="80-180">80-180 mg/dL (Conservative)</option>
                                <option value="custom">Custom Range</option>
                            </select>
                        </div>
                    </div>
                    
                    <div id="custom-range" style="display: none; margin-top: 15px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <label for="custom-low"><strong>Low Target:</strong></label>
                                <input type="number" id="custom-low" value="70" min="40" max="200" class="innormal" style="width: 100%; margin-top: 5px;">
                            </div>
                            <div>
                                <label for="custom-high"><strong>High Target:</strong></label>
                                <input type="number" id="custom-high" value="180" min="100" max="400" class="innormal" style="width: 100%; margin-top: 5px;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Glucose Trend" id="calculate-trend-btn" style="padding: 15px 30px; font-size: 18px; background-color: #9c27b0; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Clear All Readings" id="clear-trend-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Trend calculations are for educational purposes. Always consult healthcare providers for medical decisions.
        </div>
        
        <div id="trend-results" style="display: none; margin-top: 30px;">
            <div class="trend-result">
                <h3 style="margin-bottom: 20px;">📈 Glucose Trend Analysis</h3>
                <div id="trend-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-trend-btn');
    const clearBtn = document.getElementById('clear-trend-btn');
    const addReadingBtn = document.getElementById('add-reading-btn');
    const autoPopulateBtn = document.getElementById('auto-populate-btn');
    const resultsDiv = document.getElementById('trend-results');
    const resultsContent = document.getElementById('trend-results-content');
    const readingsContainer = document.getElementById('readings-container');
    const targetRangeSelect = document.getElementById('target-range');
    const customRangeDiv = document.getElementById('custom-range');
    
    // Set default time for first reading
    const now = new Date();
    const timeString = now.toTimeString().slice(0, 5);
    document.querySelector('.reading-time').value = timeString;
    
    targetRangeSelect.addEventListener('change', function() {
        if (this.value === 'custom') {
            customRangeDiv.style.display = 'block';
        } else {
            customRangeDiv.style.display = 'none';
        }
    });
    
    addReadingBtn.addEventListener('click', function() {
        addReadingEntry();
    });
    
    autoPopulateBtn.addEventListener('click', function() {
        populateSampleData();
    });
    
    calculateBtn.addEventListener('click', function() {
        const readings = collectReadings();
        const trendPeriod = parseInt(document.getElementById('trend-period').value) || 30;
        const predictionTime = parseInt(document.getElementById('prediction-time').value) || 30;
        const targetRange = getTargetRange();
        
        if (readings.length < 2) {
            alert('Please enter at least 2 glucose readings for trend analysis.');
            return;
        }
        
        const trendAnalysis = calculateGlucoseTrend(readings, trendPeriod, predictionTime, targetRange);
        displayTrendResults(trendAnalysis);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        readingsContainer.innerHTML = '';
        addReadingEntry();
        resultsDiv.style.display = 'none';
    });
    
    function addReadingEntry() {
        const entryDiv = document.createElement('div');
        entryDiv.className = 'reading-entry';
        
        const now = new Date();
        const timeString = now.toTimeString().slice(0, 5);
        
        entryDiv.innerHTML = `
            <input type="number" class="glucose-reading innormal" value="" min="40" max="400" placeholder="mg/dL" style="width: 100%;">
            <input type="time" class="reading-time innormal" value="${timeString}" style="width: 100%;">
            <button type="button" class="remove-reading" style="background: #f44336; color: white; border: none; border-radius: 4px; padding: 5px;">❌</button>
        `;
        
        readingsContainer.appendChild(entryDiv);
        
        entryDiv.querySelector('.remove-reading').addEventListener('click', function() {
            if (readingsContainer.children.length > 1) {
                entryDiv.remove();
            }
        });
    }
    
    function populateSampleData() {
        readingsContainer.innerHTML = '';
        
        const sampleData = [
            { glucose: 95, time: '08:00' },
            { glucose: 110, time: '08:15' },
            { glucose: 135, time: '08:30' },
            { glucose: 165, time: '08:45' },
            { glucose: 180, time: '09:00' },
            { glucose: 175, time: '09:15' }
        ];
        
        sampleData.forEach(data => {
            const entryDiv = document.createElement('div');
            entryDiv.className = 'reading-entry';
            
            entryDiv.innerHTML = `
                <input type="number" class="glucose-reading innormal" value="${data.glucose}" min="40" max="400" style="width: 100%;">
                <input type="time" class="reading-time innormal" value="${data.time}" style="width: 100%;">
                <button type="button" class="remove-reading" style="background: #f44336; color: white; border: none; border-radius: 4px; padding: 5px;">❌</button>
            `;
            
            readingsContainer.appendChild(entryDiv);
            
            entryDiv.querySelector('.remove-reading').addEventListener('click', function() {
                if (readingsContainer.children.length > 1) {
                    entryDiv.remove();
                }
            });
        });
    }
    
    function collectReadings() {
        const readings = [];
        const entries = readingsContainer.querySelectorAll('.reading-entry');
        
        entries.forEach(entry => {
            const glucose = parseFloat(entry.querySelector('.glucose-reading').value);
            const time = entry.querySelector('.reading-time').value;
            
            if (glucose && time) {
                const [hours, minutes] = time.split(':').map(Number);
                const timeInMinutes = hours * 60 + minutes;
                
                readings.push({
                    glucose: glucose,
                    time: time,
                    timeInMinutes: timeInMinutes
                });
            }
        });
        
        // Sort by time
        readings.sort((a, b) => a.timeInMinutes - b.timeInMinutes);
        return readings;
    }
    
    function getTargetRange() {
        const targetRangeValue = targetRangeSelect.value;
        
        if (targetRangeValue === 'custom') {
            const low = parseInt(document.getElementById('custom-low').value) || 70;
            const high = parseInt(document.getElementById('custom-high').value) || 180;
            return { low, high };
        }
        
        const ranges = {
            '70-180': { low: 70, high: 180 },
            '70-140': { low: 70, high: 140 },
            '80-180': { low: 80, high: 180 }
        };
        
        return ranges[targetRangeValue] || { low: 70, high: 180 };
    }
    
    function calculateGlucoseTrend(readings, trendPeriod, predictionTime, targetRange) {
        const currentReading = readings[readings.length - 1];
        const currentGlucose = currentReading.glucose;
        
        // Calculate rate of change (mg/dL per minute)
        let rateOfChange = 0;
        if (readings.length >= 2) {
            const timeDiff = readings[readings.length - 1].timeInMinutes - readings[0].timeInMinutes;
            const glucoseDiff = readings[readings.length - 1].glucose - readings[0].glucose;
            rateOfChange = timeDiff > 0 ? glucoseDiff / timeDiff : 0;
        }
        
        // Determine trend direction and speed
        let trendDirection = 'stable';
        let trendArrow = '→';
        let trendClass = 'stable';
        
        const ratePerMinute = Math.abs(rateOfChange);
        
        if (rateOfChange > 2) {
            trendDirection = 'rising fast';
            trendArrow = '↗↗';
            trendClass = 'rising-fast';
        } else if (rateOfChange > 1) {
            trendDirection = 'rising';
            trendArrow = '↗';
            trendClass = 'rising';
        } else if (rateOfChange < -2) {
            trendDirection = 'falling fast';
            trendArrow = '↘↘';
            trendClass = 'falling-fast';
        } else if (rateOfChange < -1) {
            trendDirection = 'falling';
            trendArrow = '↘';
            trendClass = 'falling';
        }
        
        // Predict future glucose level
        const predictedGlucose = currentGlucose + (rateOfChange * predictionTime);
        
        // Analyze time in range
        const inRange = readings.filter(r => r.glucose >= targetRange.low && r.glucose <= targetRange.high).length;
        const timeInRange = (inRange / readings.length) * 100;
        
        // Calculate statistics
        const glucoseValues = readings.map(r => r.glucose);
        const avgGlucose = glucoseValues.reduce((sum, val) => sum + val, 0) / glucoseValues.length;
        const minGlucose = Math.min(...glucoseValues);
        const maxGlucose = Math.max(...glucoseValues);
        
        // Generate recommendations
        const recommendations = generateRecommendations(currentGlucose, rateOfChange, predictedGlucose, targetRange);
        
        // Calculate variability (coefficient of variation)
        const stdDev = Math.sqrt(glucoseValues.reduce((sum, val) => sum + Math.pow(val - avgGlucose, 2), 0) / glucoseValues.length);
        const coefficientOfVariation = (stdDev / avgGlucose) * 100;
        
        return {
            currentGlucose: currentGlucose,
            rateOfChange: rateOfChange,
            trendDirection: trendDirection,
            trendArrow: trendArrow,
            trendClass: trendClass,
            predictedGlucose: predictedGlucose,
            predictionTime: predictionTime,
            timeInRange: timeInRange,
            avgGlucose: avgGlucose,
            minGlucose: minGlucose,
            maxGlucose: maxGlucose,
            coefficientOfVariation: coefficientOfVariation,
            targetRange: targetRange,
            recommendations: recommendations,
            readings: readings
        };
    }
    
    function generateRecommendations(currentGlucose, rateOfChange, predictedGlucose, targetRange) {
        const recommendations = [];
        
        if (currentGlucose < targetRange.low) {
            recommendations.push('Current glucose is below target range - consider treating low');
            if (rateOfChange < -1) {
                recommendations.push('Glucose is falling rapidly - treat immediately');
            }
        } else if (currentGlucose > targetRange.high) {
            recommendations.push('Current glucose is above target range - consider correction');
            if (rateOfChange > 1) {
                recommendations.push('Glucose is rising rapidly - consider immediate action');
            }
        }
        
        if (predictedGlucose < targetRange.low) {
            recommendations.push('Predicted glucose will be low - consider preventive treatment');
        } else if (predictedGlucose > targetRange.high) {
            recommendations.push('Predicted glucose will be high - consider preventive correction');
        }
        
        if (Math.abs(rateOfChange) > 3) {
            recommendations.push('Very rapid glucose change detected - monitor closely');
        }
        
        if (rateOfChange > 0 && currentGlucose > 250) {
            recommendations.push('High glucose with rising trend - check for ketones');
        }
        
        if (recommendations.length === 0) {
            recommendations.push('Glucose trend looks stable - continue current management');
        }
        
        return recommendations;
    }
    
    function displayTrendResults(data) {
        let html = `
            <div style="margin-bottom: 25px;">
                <div class="trend-arrow ${data.trendClass}">
                    ${data.trendArrow}
                </div>
                <div style="font-size: 36px; font-weight: bold; margin: 20px 0;">
                    ${data.currentGlucose} mg/dL
                </div>
                <div style="font-size: 18px; margin-bottom: 15px;">
                    Current glucose is ${data.trendDirection}
                </div>
                <div style="font-size: 16px;">
                    Rate: ${data.rateOfChange > 0 ? '+' : ''}${data.rateOfChange.toFixed(1)} mg/dL per minute
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Predicted Glucose</h4>
                    <div style="font-size: 24px; font-weight: bold; color: ${data.predictedGlucose < data.targetRange.low ? '#f44336' : data.predictedGlucose > data.targetRange.high ? '#ff9800' : '#4caf50'};">
                        ${Math.round(data.predictedGlucose)}
                    </div>
                    <div style="font-size: 12px; color: #666;">in ${data.predictionTime} minutes</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Time in Range</h4>
                    <div style="font-size: 24px; font-weight: bold; color: ${data.timeInRange >= 70 ? '#4caf50' : data.timeInRange >= 50 ? '#ff9800' : '#f44336'};">
                        ${data.timeInRange.toFixed(0)}%
                    </div>
                    <div style="font-size: 12px; color: #666;">${data.targetRange.low}-${data.targetRange.high} mg/dL</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Average Glucose</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #9c27b0;">
                        ${Math.round(data.avgGlucose)}
                    </div>
                    <div style="font-size: 12px; color: #666;">mg/dL</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Glucose Range</h4>
                    <div style="font-size: 18px; font-weight: bold; color: #2196f3;">
                        ${data.minGlucose} - ${data.maxGlucose}
                    </div>
                    <div style="font-size: 12px; color: #666;">min - max mg/dL</div>
                </div>
            </div>
            
            <div class="glucose-chart">
                <h4 style="color: #9c27b0; margin-bottom: 20px;">📊 Trend Analysis Details</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <h5>Trend Metrics:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Direction:</strong> ${data.trendDirection}</li>
                            <li><strong>Rate of Change:</strong> ${data.rateOfChange.toFixed(2)} mg/dL/min</li>
                            <li><strong>Variability (CV):</strong> ${data.coefficientOfVariation.toFixed(1)}%</li>
                            <li><strong>Readings Analyzed:</strong> ${data.readings.length}</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5>Target Range Analysis:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Target Range:</strong> ${data.targetRange.low}-${data.targetRange.high} mg/dL</li>
                            <li><strong>Time in Range:</strong> ${data.timeInRange.toFixed(1)}%</li>
                            <li><strong>Current Status:</strong> ${data.currentGlucose < data.targetRange.low ? 'Below' : data.currentGlucose > data.targetRange.high ? 'Above' : 'In'} range</li>
                            <li><strong>Predicted Status:</strong> ${data.predictedGlucose < data.targetRange.low ? 'Below' : data.predictedGlucose > data.targetRange.high ? 'Above' : 'In'} range</li>
                        </ul>
                    </div>
                </div>
                
                <div style="background-color: ${data.recommendations.some(r => r.includes('immediately') || r.includes('rapidly')) ? '#ffebee' : '#f5f5f5'}; border-radius: 8px; padding: 15px; margin: 15px 0;">
                    <h5 style="color: ${data.recommendations.some(r => r.includes('immediately') || r.includes('rapidly')) ? '#f44336' : '#2c3e50'};">
                        💡 Recommendations:
                    </h5>
                    <ul style="margin: 10px 0; color: #2c3e50;">
                        ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
                
                <div style="margin-top: 20px;">
                    <h5>Glucose Reading Timeline:</h5>
                    <div style="max-height: 200px; overflow-y: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background-color: #f5f5f5;">
                                    <th style="padding: 8px; border: 1px solid #ddd;">Time</th>
                                    <th style="padding: 8px; border: 1px solid #ddd;">Glucose (mg/dL)</th>
                                    <th style="padding: 8px; border: 1px solid #ddd;">Status</th>
                                </tr>
                            </thead>
                            <tbody>
        `;
        
        data.readings.forEach((reading, index) => {
            const status = reading.glucose < data.targetRange.low ? 'Low' : 
                          reading.glucose > data.targetRange.high ? 'High' : 'In Range';
            const statusColor = status === 'Low' ? '#f44336' : status === 'High' ? '#ff9800' : '#4caf50';
            
            html += `
                <tr>
                    <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${reading.time}</td>
                    <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${reading.glucose}</td>
                    <td style="padding: 6px; border: 1px solid #ddd; text-align: center; color: ${statusColor};">${status}</td>
                </tr>
            `;
        });
        
        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>📈 Understanding Glucose Trends:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>↗↗ Rising Fast:</strong> >2 mg/dL per minute - Consider immediate action</li>
                    <li><strong>↗ Rising:</strong> 1-2 mg/dL per minute - Monitor and consider correction</li>
                    <li><strong>→ Stable:</strong> <1 mg/dL per minute - Continue current management</li>
                    <li><strong>↘ Falling:</strong> 1-2 mg/dL per minute - Monitor for low glucose</li>
                    <li><strong>↘↘ Falling Fast:</strong> >2 mg/dL per minute - Treat immediately</li>
                    <li><strong>Time in Range:</strong> Target >70% for optimal diabetes management</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
    
    // Initialize with one reading entry
    addReadingEntry();
});
</script>
