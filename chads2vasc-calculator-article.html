<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.risk-factor-row {
    border-bottom: 1px solid #eee;
    padding: 8px 0;
}
.score-display {
    font-size: 24px;
    font-weight: bold;
    color: white;
    text-align: center;
    padding: 15px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 10px;
    margin: 10px 0;
}
.risk-level {
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
    font-weight: bold;
    text-align: center;
}
.risk-low { background-color: #d4edda; color: #155724; border: 2px solid #c3e6cb; }
.risk-moderate { background-color: #fff3cd; color: #856404; border: 2px solid #ffeaa7; }
.risk-high { background-color: #f8d7da; color: #721c24; border: 2px solid #f5c6cb; }
.recommendation {
    background-color: #e7f3ff;
    border-left: 4px solid #0066cc;
    padding: 15px;
    margin: 15px 0;
}
</style>

<h1>CHA2DS2-VASc Calculator</h1>
<p>The CHA2DS2-VASc Calculator is a clinical prediction tool used to assess stroke risk in patients with atrial fibrillation. This evidence-based scoring system helps healthcare providers determine the need for anticoagulation therapy to prevent thromboembolic events in non-valvular atrial fibrillation patients.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="chadsform" id="chads-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">CHA2DS2-VASc Calculator</a></li>
                <li><a href="#" data-tab="interpretation">Score Interpretation</a></li>
                <li><a href="#" data-tab="guidelines">Clinical Guidelines</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 15px;">
            <table id="chads-input" style="width: 100%; margin-bottom: 20px;">
                <tbody>
                    <tr style="background-color: #E0F0FE;">
                        <td colspan="3" style="padding: 12px; font-weight: bold; text-align: center; font-size: 16px;">Patient Demographics & Risk Factors</td>
                    </tr>
                    <tr class="risk-factor-row">
                        <td width="50%"><strong>Age Category:</strong></td>
                        <td width="30%">
                            <select name="age" id="age" class="innormal" style="width: 100%; padding: 5px;">
                                <option value="0">Under 65 years</option>
                                <option value="1">65-74 years</option>
                                <option value="2">75 years or older</option>
                            </select>
                        </td>
                        <td width="20%" style="text-align: center; font-weight: bold; color: #0066cc;">
                            <span id="age-points">0</span> pts
                        </td>
                    </tr>
                    <tr class="risk-factor-row">
                        <td><strong>Sex (Female):</strong></td>
                        <td>
                            <select name="sex" id="sex" class="innormal" style="width: 100%; padding: 5px;">
                                <option value="0">Male</option>
                                <option value="1">Female</option>
                            </select>
                        </td>
                        <td style="text-align: center; font-weight: bold; color: #0066cc;">
                            <span id="sex-points">0</span> pts
                        </td>
                    </tr>
                    <tr class="risk-factor-row">
                        <td>
                            <input type="checkbox" name="chf" id="chf" value="1" style="margin-right: 8px;">
                            <label for="chf"><strong>Congestive Heart Failure</strong></label>
                            <br><small style="color: #666; margin-left: 20px;">History of heart failure or reduced ejection fraction</small>
                        </td>
                        <td style="text-align: center; color: #666;">1 point if present</td>
                        <td style="text-align: center; font-weight: bold; color: #0066cc;">
                            <span id="chf-points">0</span> pts
                        </td>
                    </tr>
                    <tr class="risk-factor-row">
                        <td>
                            <input type="checkbox" name="hypertension" id="hypertension" value="1" style="margin-right: 8px;">
                            <label for="hypertension"><strong>Hypertension</strong></label>
                            <br><small style="color: #666; margin-left: 20px;">History of hypertension or on antihypertensive treatment</small>
                        </td>
                        <td style="text-align: center; color: #666;">1 point if present</td>
                        <td style="text-align: center; font-weight: bold; color: #0066cc;">
                            <span id="hypertension-points">0</span> pts
                        </td>
                    </tr>
                    <tr class="risk-factor-row">
                        <td>
                            <input type="checkbox" name="diabetes" id="diabetes" value="1" style="margin-right: 8px;">
                            <label for="diabetes"><strong>Diabetes Mellitus</strong></label>
                            <br><small style="color: #666; margin-left: 20px;">Fasting glucose >125 mg/dL or on antidiabetic treatment</small>
                        </td>
                        <td style="text-align: center; color: #666;">1 point if present</td>
                        <td style="text-align: center; font-weight: bold; color: #0066cc;">
                            <span id="diabetes-points">0</span> pts
                        </td>
                    </tr>
                    <tr class="risk-factor-row">
                        <td>
                            <input type="checkbox" name="stroke" id="stroke" value="2" style="margin-right: 8px;">
                            <label for="stroke"><strong>Previous Stroke/TIA/Thromboembolism</strong></label>
                            <br><small style="color: #666; margin-left: 20px;">History of stroke, TIA, or systemic embolism</small>
                        </td>
                        <td style="text-align: center; color: #666;">2 points if present</td>
                        <td style="text-align: center; font-weight: bold; color: #0066cc;">
                            <span id="stroke-points">0</span> pts
                        </td>
                    </tr>
                    <tr class="risk-factor-row">
                        <td>
                            <input type="checkbox" name="vascular" id="vascular" value="1" style="margin-right: 8px;">
                            <label for="vascular"><strong>Vascular Disease</strong></label>
                            <br><small style="color: #666; margin-left: 20px;">Prior MI, peripheral artery disease, or aortic plaque</small>
                        </td>
                        <td style="text-align: center; color: #666;">1 point if present</td>
                        <td style="text-align: center; font-weight: bold; color: #0066cc;">
                            <span id="vascular-points">0</span> pts
                        </td>
                    </tr>
                </tbody>
            </table>
            
            <div style="text-align: center; margin: 20px 0;">
                <input type="button" value="Calculate CHA2DS2-VASc Score" id="calculate-chads-btn" style="padding: 12px 25px; font-size: 16px; background-color: #0066cc; color: white; border: none; border-radius: 5px; cursor: pointer; margin-right: 10px;">
                <input type="button" value="Clear All" id="clear-chads-btn" style="padding: 12px 25px; font-size: 16px; background-color: #666; color: white; border: none; border-radius: 5px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 15px 0;">
            <strong>Medical Disclaimer:</strong> This calculator is for educational purposes only and should not replace clinical judgment. Always consult with a healthcare professional for medical decisions.
        </div>
        
        <div id="chads-results" style="display: none; margin-top: 25px; padding: 20px; background-color: #f8f9fa; border: 2px solid #0066cc; border-radius: 10px;">
            <h3 style="text-align: center; color: #0066cc; margin-bottom: 20px;">CHA2DS2-VASc Score Results</h3>
            <div id="chads-results-content"></div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const form = document.getElementById('chads-calculator-form');
    const calculateBtn = document.getElementById('calculate-chads-btn');
    const clearBtn = document.getElementById('clear-chads-btn');
    const resultsDiv = document.getElementById('chads-results');
    const resultsContent = document.getElementById('chads-results-content');
    
    // Update points display when inputs change
    function updatePointsDisplay() {
        const age = parseInt(document.getElementById('age').value) || 0;
        const sex = parseInt(document.getElementById('sex').value) || 0;
        const chf = document.getElementById('chf').checked ? 1 : 0;
        const hypertension = document.getElementById('hypertension').checked ? 1 : 0;
        const diabetes = document.getElementById('diabetes').checked ? 1 : 0;
        const stroke = document.getElementById('stroke').checked ? 2 : 0;
        const vascular = document.getElementById('vascular').checked ? 1 : 0;
        
        document.getElementById('age-points').textContent = age;
        document.getElementById('sex-points').textContent = sex;
        document.getElementById('chf-points').textContent = chf;
        document.getElementById('hypertension-points').textContent = hypertension;
        document.getElementById('diabetes-points').textContent = diabetes;
        document.getElementById('stroke-points').textContent = stroke;
        document.getElementById('vascular-points').textContent = vascular;
    }
    
    // Add event listeners for real-time updates
    document.getElementById('age').addEventListener('change', updatePointsDisplay);
    document.getElementById('sex').addEventListener('change', updatePointsDisplay);
    document.getElementById('chf').addEventListener('change', updatePointsDisplay);
    document.getElementById('hypertension').addEventListener('change', updatePointsDisplay);
    document.getElementById('diabetes').addEventListener('change', updatePointsDisplay);
    document.getElementById('stroke').addEventListener('change', updatePointsDisplay);
    document.getElementById('vascular').addEventListener('change', updatePointsDisplay);
    
    calculateBtn.addEventListener('click', function() {
        const age = parseInt(document.getElementById('age').value) || 0;
        const sex = parseInt(document.getElementById('sex').value) || 0;
        const chf = document.getElementById('chf').checked ? 1 : 0;
        const hypertension = document.getElementById('hypertension').checked ? 1 : 0;
        const diabetes = document.getElementById('diabetes').checked ? 1 : 0;
        const stroke = document.getElementById('stroke').checked ? 2 : 0;
        const vascular = document.getElementById('vascular').checked ? 1 : 0;
        
        const totalScore = age + sex + chf + hypertension + diabetes + stroke + vascular;
        
        let riskLevel, riskClass, strokeRisk, recommendation;
        
        if (totalScore === 0) {
            riskLevel = 'Very Low Risk';
            riskClass = 'risk-low';
            strokeRisk = '0%';
            recommendation = 'No anticoagulation recommended. Consider aspirin or no antithrombotic therapy.';
        } else if (totalScore === 1) {
            riskLevel = 'Low Risk';
            riskClass = 'risk-low';
            strokeRisk = '1.3%';
            recommendation = 'Consider anticoagulation. May use aspirin or oral anticoagulant based on bleeding risk and patient preference.';
        } else if (totalScore === 2) {
            riskLevel = 'Moderate Risk';
            riskClass = 'risk-moderate';
            strokeRisk = '2.2%';
            recommendation = 'Oral anticoagulation recommended unless contraindicated.';
        } else {
            riskLevel = 'High Risk';
            riskClass = 'risk-high';
            strokeRisk = totalScore <= 3 ? '3.2%' : totalScore <= 4 ? '4.0%' : totalScore <= 5 ? '6.7%' : totalScore <= 6 ? '9.8%' : '9.6-15.2%';
            recommendation = 'Oral anticoagulation strongly recommended unless contraindicated.';
        }
        
        resultsContent.innerHTML = `
            <div class="score-display">
                CHA2DS2-VASc Score: ${totalScore}
            </div>
            <div class="risk-level ${riskClass}">
                ${riskLevel}<br>
                <small>Annual Stroke Risk: ${strokeRisk}</small>
            </div>
            <div class="recommendation">
                <h4>Clinical Recommendation:</h4>
                <p>${recommendation}</p>
            </div>
        `;
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        form.reset();
        updatePointsDisplay();
        resultsDiv.style.display = 'none';
    });
    
    // Initialize points display
    updatePointsDisplay();
});
</script>

<a name="score-interpretation"></a>
<br><h2>Understanding CHA2DS2-VASc Score Components</h2>
<p>The CHA2DS2-VASc score is an acronym that represents different risk factors for stroke in patients with atrial fibrillation. Each letter corresponds to a specific clinical condition or demographic factor:</p>

<table class="cinfoT">
    <tbody>
        <tr>
            <td class="cinfoHd">Component</td>
            <td class="cinfoHd">Risk Factor</td>
            <td class="cinfoHd">Points</td>
            <td class="cinfoHd">Clinical Definition</td>
        </tr>
        <tr>
            <td><strong>C</strong></td>
            <td>Congestive Heart Failure</td>
            <td class="cinfoBodL">1</td>
            <td>History of heart failure or reduced ejection fraction (&lt;40%)</td>
        </tr>
        <tr>
            <td><strong>H</strong></td>
            <td>Hypertension</td>
            <td class="cinfoBodL">1</td>
            <td>History of hypertension or currently on antihypertensive treatment</td>
        </tr>
        <tr>
            <td><strong>A₂</strong></td>
            <td>Age ≥75 years</td>
            <td class="cinfoBodL">2</td>
            <td>Age 75 years or older (doubled points due to higher risk)</td>
        </tr>
        <tr>
            <td><strong>D</strong></td>
            <td>Diabetes Mellitus</td>
            <td class="cinfoBodL">1</td>
            <td>Fasting glucose &gt;125 mg/dL or on antidiabetic medication</td>
        </tr>
        <tr>
            <td><strong>S₂</strong></td>
            <td>Prior Stroke/TIA</td>
            <td class="cinfoBodL">2</td>
            <td>History of stroke, TIA, or systemic thromboembolism</td>
        </tr>
        <tr>
            <td><strong>V</strong></td>
            <td>Vascular Disease</td>
            <td class="cinfoBodL">1</td>
            <td>Prior MI, peripheral artery disease, or aortic plaque</td>
        </tr>
        <tr>
            <td><strong>A</strong></td>
            <td>Age 65-74 years</td>
            <td class="cinfoBodL">1</td>
            <td>Age between 65-74 years</td>
        </tr>
        <tr>
            <td><strong>Sc</strong></td>
            <td>Sex Category (Female)</td>
            <td class="cinfoBodL">1</td>
            <td>Female gender (additional risk factor)</td>
        </tr>
    </tbody>
</table>

<h3>Score Interpretation and Risk Stratification</h3>
<p>The total CHA2DS2-VASc score ranges from 0 to 9, with higher scores indicating greater stroke risk. Here's how to interpret the results:</p>

<table class="cinfoT" style="margin: 20px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">Score</td>
            <td class="cinfoHd">Risk Level</td>
            <td class="cinfoHd">Annual Stroke Risk</td>
            <td class="cinfoHd">Recommended Treatment</td>
        </tr>
        <tr>
            <td class="cinfoBodL"><strong>0</strong></td>
            <td>Very Low</td>
            <td class="cinfoBodL">0%</td>
            <td>No anticoagulation; consider aspirin or no therapy</td>
        </tr>
        <tr>
            <td class="cinfoBodL"><strong>1</strong></td>
            <td>Low</td>
            <td class="cinfoBodL">1.3%</td>
            <td>Consider anticoagulation based on bleeding risk</td>
        </tr>
        <tr>
            <td class="cinfoBodL"><strong>2</strong></td>
            <td>Moderate</td>
            <td class="cinfoBodL">2.2%</td>
            <td>Oral anticoagulation recommended</td>
        </tr>
        <tr>
            <td class="cinfoBodL"><strong>3</strong></td>
            <td>Moderate-High</td>
            <td class="cinfoBodL">3.2%</td>
            <td>Oral anticoagulation recommended</td>
        </tr>
        <tr>
            <td class="cinfoBodL"><strong>4</strong></td>
            <td>High</td>
            <td class="cinfoBodL">4.0%</td>
            <td>Oral anticoagulation strongly recommended</td>
        </tr>
        <tr>
            <td class="cinfoBodL"><strong>5</strong></td>
            <td>High</td>
            <td class="cinfoBodL">6.7%</td>
            <td>Oral anticoagulation strongly recommended</td>
        </tr>
        <tr>
            <td class="cinfoBodL"><strong>6</strong></td>
            <td>Very High</td>
            <td class="cinfoBodL">9.8%</td>
            <td>Oral anticoagulation strongly recommended</td>
        </tr>
        <tr>
            <td class="cinfoBodL"><strong>7-9</strong></td>
            <td>Very High</td>
            <td class="cinfoBodL">9.6-15.2%</td>
            <td>Oral anticoagulation strongly recommended</td>
        </tr>
    </tbody>
</table>

<h3>Clinical Decision Making</h3>
<div style="background-color: #e7f3ff; border-left: 4px solid #0066cc; padding: 15px; margin: 20px 0;">
    <h4>Key Clinical Considerations:</h4>
    <ul>
        <li><strong>Score 0 (Men) or 1 (Women):</strong> Very low stroke risk. No anticoagulation typically needed.</li>
        <li><strong>Score 1 (Men) or 2 (Women):</strong> Consider individual patient factors, bleeding risk, and patient preference.</li>
        <li><strong>Score ≥2 (Men) or ≥3 (Women):</strong> Oral anticoagulation recommended unless contraindicated.</li>
        <li><strong>Bleeding Risk Assessment:</strong> Always consider HAS-BLED score alongside CHA2DS2-VASc.</li>
        <li><strong>Patient Preference:</strong> Shared decision-making is essential, especially for borderline scores.</li>
    </ul>
</div>

<h3>Anticoagulation Options</h3>
<p>When anticoagulation is recommended, several options are available:</p>

<table class="cinfoT" style="margin: 20px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">Medication Class</td>
            <td class="cinfoHd">Examples</td>
            <td class="cinfoHd">Advantages</td>
            <td class="cinfoHd">Considerations</td>
        </tr>
        <tr>
            <td><strong>Vitamin K Antagonists</strong></td>
            <td>Warfarin</td>
            <td>Long track record, reversible, cost-effective</td>
            <td>Requires INR monitoring, drug interactions, dietary restrictions</td>
        </tr>
        <tr>
            <td><strong>Direct Oral Anticoagulants (DOACs)</strong></td>
            <td>Apixaban, Rivaroxaban, Dabigatran, Edoxaban</td>
            <td>No routine monitoring, fewer interactions, rapid onset</td>
            <td>Higher cost, limited reversal agents, renal considerations</td>
        </tr>
        <tr>
            <td><strong>Antiplatelet Therapy</strong></td>
            <td>Aspirin, Clopidogrel</td>
            <td>Lower bleeding risk, widely available</td>
            <td>Less effective for stroke prevention in AF</td>
        </tr>
    </tbody>
</table>

<a name="clinical-guidelines"></a>
<br><h2>Clinical Guidelines and Evidence Base</h2>
<p>The CHA2DS2-VASc score is endorsed by major international cardiology societies and is based on extensive clinical research. Here's what the guidelines recommend:</p>

<h3>Major Society Recommendations</h3>
<table class="cinfoT" style="margin: 20px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">Organization</td>
            <td class="cinfoHd">Recommendation</td>
            <td class="cinfoHd">Class of Evidence</td>
        </tr>
        <tr>
            <td><strong>European Society of Cardiology (ESC)</strong></td>
            <td>CHA2DS2-VASc preferred over CHADS2 for stroke risk assessment</td>
            <td class="cinfoBodL">Class I, Level A</td>
        </tr>
        <tr>
            <td><strong>American Heart Association (AHA)</strong></td>
            <td>CHA2DS2-VASc recommended for initial stroke risk assessment</td>
            <td class="cinfoBodL">Class I, Level B</td>
        </tr>
        <tr>
            <td><strong>Canadian Cardiovascular Society</strong></td>
            <td>CHA2DS2-VASc score should guide anticoagulation decisions</td>
            <td class="cinfoBodL">Strong Recommendation</td>
        </tr>
        <tr>
            <td><strong>NICE (UK)</strong></td>
            <td>Use CHA2DS2-VASc to assess stroke risk in AF patients</td>
            <td class="cinfoBodL">Grade A</td>
        </tr>
    </tbody>
</table>

<h3>Evidence Base and Validation Studies</h3>
<div style="background-color: #f8f9fa; border: 1px solid #dee2e6; padding: 15px; margin: 20px 0; border-radius: 5px;">
    <h4>Key Clinical Studies:</h4>
    <ul>
        <li><strong>Original Validation Study (2010):</strong> Demonstrated superior predictive ability compared to CHADS2 score</li>
        <li><strong>Euro Heart Survey:</strong> Validated in over 1,000 AF patients across Europe</li>
        <li><strong>Danish National Registry:</strong> Large-scale validation in 47,576 patients</li>
        <li><strong>Real-World Evidence:</strong> Multiple studies confirm clinical utility across diverse populations</li>
    </ul>

    <h4>Performance Characteristics:</h4>
    <ul>
        <li><strong>C-statistic:</strong> 0.606 (moderate discriminative ability)</li>
        <li><strong>Sensitivity:</strong> Better identification of low-risk patients compared to CHADS2</li>
        <li><strong>Specificity:</strong> Improved classification of patients who may not need anticoagulation</li>
    </ul>
</div>

<h3>Special Populations and Considerations</h3>
<table class="cinfoT" style="margin: 20px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">Population</td>
            <td class="cinfoHd">Special Considerations</td>
            <td class="cinfoHd">Recommendations</td>
        </tr>
        <tr>
            <td><strong>Elderly Patients (≥80 years)</strong></td>
            <td>Higher bleeding risk, frailty considerations</td>
            <td>Careful risk-benefit assessment; consider DOACs over warfarin</td>
        </tr>
        <tr>
            <td><strong>Chronic Kidney Disease</strong></td>
            <td>Altered drug metabolism, increased bleeding risk</td>
            <td>Dose adjustment required; avoid dabigatran if CrCl &lt;30</td>
        </tr>
        <tr>
            <td><strong>Women of Childbearing Age</strong></td>
            <td>Pregnancy considerations, teratogenicity</td>
            <td>Avoid warfarin in pregnancy; consider heparin-based therapy</td>
        </tr>
        <tr>
            <td><strong>Patients with Cancer</strong></td>
            <td>Increased thrombotic and bleeding risk</td>
            <td>Individual assessment; may require LMWH over oral agents</td>
        </tr>
    </tbody>
</table>

<h3>Limitations and Considerations</h3>
<div style="background-color: #fff3cd; border-left: 4px solid #ffc107; padding: 15px; margin: 20px 0;">
    <h4>Important Limitations:</h4>
    <ul>
        <li><strong>Moderate Predictive Ability:</strong> C-statistic of 0.606 indicates room for improvement</li>
        <li><strong>Population-Based:</strong> Derived from European populations; may not apply to all ethnicities</li>
        <li><strong>Static Assessment:</strong> Doesn't account for changing risk factors over time</li>
        <li><strong>Bleeding Risk:</strong> Doesn't incorporate bleeding risk assessment (use HAS-BLED separately)</li>
        <li><strong>Individual Variation:</strong> Some patients may have unique risk factors not captured</li>
    </ul>
</div>

<h3>Frequently Asked Questions</h3>

<div style="margin: 20px 0;">
    <h4 style="color: #0066cc;">Q: How often should CHA2DS2-VASc score be recalculated?</h4>
    <p><strong>A:</strong> The score should be reassessed annually or when clinical status changes significantly. Age-related changes automatically increase the score over time, and new comorbidities may alter risk stratification.</p>

    <h4 style="color: #0066cc;">Q: Can CHA2DS2-VASc be used in patients with valvular atrial fibrillation?</h4>
    <p><strong>A:</strong> No, CHA2DS2-VASc is specifically validated for non-valvular atrial fibrillation. Patients with moderate-to-severe mitral stenosis or mechanical heart valves require different anticoagulation approaches.</p>

    <h4 style="color: #0066cc;">Q: What if a patient has a CHA2DS2-VASc score of 1 but high bleeding risk?</h4>
    <p><strong>A:</strong> This requires individualized decision-making. Consider the HAS-BLED score, patient preferences, and potentially modifiable bleeding risk factors. Shared decision-making is crucial.</p>

    <h4 style="color: #0066cc;">Q: Are there any emerging alternatives to CHA2DS2-VASc?</h4>
    <p><strong>A:</strong> Several newer scores are being developed, including biomarker-based and imaging-enhanced risk scores. However, CHA2DS2-VASc remains the gold standard in current guidelines.</p>

    <h4 style="color: #0066cc;">Q: How does paroxysmal vs. persistent AF affect the score?</h4>
    <p><strong>A:</strong> The CHA2DS2-VASc score applies equally to all types of AF (paroxysmal, persistent, or permanent). The stroke risk is similar regardless of AF pattern.</p>
</div>

<h3>Clinical Implementation Tips</h3>
<div style="background-color: #d1ecf1; border: 1px solid #bee5eb; padding: 15px; margin: 20px 0; border-radius: 5px;">
    <h4>Best Practices for Clinical Use:</h4>
    <ul>
        <li><strong>Documentation:</strong> Clearly document the score and rationale for treatment decisions</li>
        <li><strong>Patient Education:</strong> Explain the score and its implications to patients</li>
        <li><strong>Regular Review:</strong> Reassess annually and with clinical changes</li>
        <li><strong>Bleeding Assessment:</strong> Always consider HAS-BLED score alongside CHA2DS2-VASc</li>
        <li><strong>Shared Decision-Making:</strong> Involve patients in treatment decisions, especially for borderline scores</li>
        <li><strong>Quality Improvement:</strong> Use the score as part of AF quality metrics</li>
    </ul>
</div>

<p><strong>References and Further Reading:</strong></p>
<ul style="font-size: 14px; color: #666;">
    <li>Lip GY, et al. Refining clinical risk stratification for predicting stroke and thromboembolism in atrial fibrillation using a novel risk factor-based approach. Chest. 2010;137(2):263-272.</li>
    <li>January CT, et al. 2019 AHA/ACC/HRS Focused Update of the 2014 AHA/ACC/HRS Guideline for the Management of Patients with Atrial Fibrillation. Circulation. 2019;140(2):e125-e151.</li>
    <li>Hindricks G, et al. 2020 ESC Guidelines for the diagnosis and management of atrial fibrillation. Eur Heart J. 2021;42(5):373-498.</li>
</ul>

<p style="text-align: center; margin-top: 30px; padding: 20px; background-color: #f8f9fa; border-radius: 5px;">
    <strong>Remember:</strong> This calculator is a clinical decision support tool. Always use clinical judgment and consider individual patient factors when making treatment decisions. Consult current guidelines and consider specialist referral for complex cases.
</p>
