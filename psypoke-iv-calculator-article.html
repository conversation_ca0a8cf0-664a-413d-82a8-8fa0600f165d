<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.psypoke-interface {
    background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.pokemon-input {
    background-color: #fce4ec;
    border: 2px solid #e91e63;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.psypoke-result {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.iv-display {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 10px;
    margin: 20px 0;
}
.iv-stat {
    background: white;
    border-radius: 10px;
    padding: 15px;
    text-align: center;
    color: #2c3e50;
}
.iv-perfect { background: linear-gradient(135deg, #4caf50, #8bc34a); color: white; }
.iv-good { background: linear-gradient(135deg, #ff9800, #ffc107); color: white; }
.iv-average { background: linear-gradient(135deg, #9e9e9e, #757575); color: white; }
.iv-poor { background: linear-gradient(135deg, #f44336, #ff5722); color: white; }
.pokemon-selector {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.stat-input {
    display: grid;
    grid-template-columns: 100px 1fr;
    gap: 10px;
    align-items: center;
    margin: 10px 0;
}
</style>

<h1>PsyPoke IV Calculator</h1>
<p>Calculate Individual Values (IVs) for your Pokémon with PsyPoke's comprehensive IV calculator. Determine hidden stats, optimize your team, and discover your Pokémon's true potential with accurate IV calculations for all generations.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="psypokeform" id="psypoke-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">IV Calculator</a></li>
                <li><a href="#" data-tab="pokemon">Pokémon Database</a></li>
                <li><a href="#" data-tab="analysis">IV Analysis</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="psypoke-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">🔍 PsyPoke IV Calculator</h3>
                
                <div class="pokemon-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🎮 Pokémon Information</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="pokemon-name"><strong>Pokémon:</strong></label>
                            <select id="pokemon-name" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="pikachu" selected>Pikachu</option>
                                <option value="charizard">Charizard</option>
                                <option value="blastoise">Blastoise</option>
                                <option value="venusaur">Venusaur</option>
                                <option value="mewtwo">Mewtwo</option>
                                <option value="mew">Mew</option>
                                <option value="dragonite">Dragonite</option>
                                <option value="alakazam">Alakazam</option>
                                <option value="machamp">Machamp</option>
                                <option value="gengar">Gengar</option>
                                <option value="custom">Custom Pokémon</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="pokemon-level"><strong>Level:</strong></label>
                            <input type="number" id="pokemon-level" value="50" min="1" max="100" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                        
                        <div>
                            <label for="pokemon-nature"><strong>Nature:</strong></label>
                            <select id="pokemon-nature" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="hardy" selected>Hardy (Neutral)</option>
                                <option value="adamant">Adamant (+Atk, -SpA)</option>
                                <option value="jolly">Jolly (+Spe, -SpA)</option>
                                <option value="modest">Modest (+SpA, -Atk)</option>
                                <option value="timid">Timid (+Spe, -Atk)</option>
                                <option value="bold">Bold (+Def, -Atk)</option>
                                <option value="calm">Calm (+SpD, -Atk)</option>
                                <option value="impish">Impish (+Def, -SpA)</option>
                                <option value="careful">Careful (+SpD, -SpA)</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #fce4ec; border: 2px solid #e91e63; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">📊 Current Stats</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <div class="stat-input">
                                <label><strong>HP:</strong></label>
                                <input type="number" id="stat-hp" value="140" min="1" max="999" class="innormal" style="width: 100%;">
                            </div>
                            
                            <div class="stat-input">
                                <label><strong>Attack:</strong></label>
                                <input type="number" id="stat-attack" value="85" min="1" max="999" class="innormal" style="width: 100%;">
                            </div>
                            
                            <div class="stat-input">
                                <label><strong>Defense:</strong></label>
                                <input type="number" id="stat-defense" value="75" min="1" max="999" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <div class="stat-input">
                                <label><strong>Sp. Attack:</strong></label>
                                <input type="number" id="stat-spatk" value="90" min="1" max="999" class="innormal" style="width: 100%;">
                            </div>
                            
                            <div class="stat-input">
                                <label><strong>Sp. Defense:</strong></label>
                                <input type="number" id="stat-spdef" value="80" min="1" max="999" class="innormal" style="width: 100%;">
                            </div>
                            
                            <div class="stat-input">
                                <label><strong>Speed:</strong></label>
                                <input type="number" id="stat-speed" value="110" min="1" max="999" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #f1f8e9; border: 2px solid #4caf50; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">⚙️ Advanced Options</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="generation"><strong>Generation:</strong></label>
                            <select id="generation" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="1">Generation I (RBY)</option>
                                <option value="2">Generation II (GSC)</option>
                                <option value="3" selected>Generation III+ (RSE+)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="iv-calculation-method"><strong>Calculation Method:</strong></label>
                            <select id="iv-calculation-method" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="exact" selected>Exact Calculation</option>
                                <option value="range">Range Estimation</option>
                                <option value="probability">Probability Analysis</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="hidden-power"><strong>Hidden Power Type:</strong></label>
                            <select id="hidden-power" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Unknown</option>
                                <option value="fighting">Fighting</option>
                                <option value="flying">Flying</option>
                                <option value="poison">Poison</option>
                                <option value="ground">Ground</option>
                                <option value="rock">Rock</option>
                                <option value="bug">Bug</option>
                                <option value="ghost">Ghost</option>
                                <option value="steel">Steel</option>
                                <option value="fire">Fire</option>
                                <option value="water">Water</option>
                                <option value="grass">Grass</option>
                                <option value="electric">Electric</option>
                                <option value="psychic">Psychic</option>
                                <option value="ice">Ice</option>
                                <option value="dragon">Dragon</option>
                                <option value="dark">Dark</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate IVs" id="calculate-iv-btn" style="padding: 15px 30px; font-size: 18px; background-color: #e91e63; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Calculator" id="clear-iv-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> IV calculations are based on visible stats. For most accurate results, use Pokémon at higher levels or with known EVs.
        </div>
        
        <div id="psypoke-results" style="display: none; margin-top: 30px;">
            <div class="psypoke-result">
                <h3 style="margin-bottom: 20px;">🔍 IV Calculation Results</h3>
                <div id="psypoke-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-iv-btn');
    const clearBtn = document.getElementById('clear-iv-btn');
    const resultsDiv = document.getElementById('psypoke-results');
    const resultsContent = document.getElementById('psypoke-results-content');
    
    // Pokémon base stats database (simplified)
    const pokemonBaseStats = {
        pikachu: { hp: 35, attack: 55, defense: 40, spatk: 50, spdef: 50, speed: 90 },
        charizard: { hp: 78, attack: 84, defense: 78, spatk: 109, spdef: 85, speed: 100 },
        blastoise: { hp: 79, attack: 83, defense: 100, spatk: 85, spdef: 105, speed: 78 },
        venusaur: { hp: 80, attack: 82, defense: 83, spatk: 100, spdef: 100, speed: 80 },
        mewtwo: { hp: 106, attack: 110, defense: 90, spatk: 154, spdef: 90, speed: 130 },
        mew: { hp: 100, attack: 100, defense: 100, spatk: 100, spdef: 100, speed: 100 },
        dragonite: { hp: 91, attack: 134, defense: 95, spatk: 100, spdef: 100, speed: 80 },
        alakazam: { hp: 55, attack: 50, defense: 45, spatk: 135, spdef: 95, speed: 120 },
        machamp: { hp: 90, attack: 130, defense: 80, spatk: 65, spdef: 85, speed: 55 },
        gengar: { hp: 60, attack: 65, defense: 60, spatk: 130, spdef: 75, speed: 110 }
    };
    
    // Nature modifiers
    const natureModifiers = {
        hardy: { attack: 1.0, defense: 1.0, spatk: 1.0, spdef: 1.0, speed: 1.0 },
        adamant: { attack: 1.1, defense: 1.0, spatk: 0.9, spdef: 1.0, speed: 1.0 },
        jolly: { attack: 1.0, defense: 1.0, spatk: 0.9, spdef: 1.0, speed: 1.1 },
        modest: { attack: 0.9, defense: 1.0, spatk: 1.1, spdef: 1.0, speed: 1.0 },
        timid: { attack: 0.9, defense: 1.0, spatk: 1.0, spdef: 1.0, speed: 1.1 },
        bold: { attack: 0.9, defense: 1.1, spatk: 1.0, spdef: 1.0, speed: 1.0 },
        calm: { attack: 0.9, defense: 1.0, spatk: 1.0, spdef: 1.1, speed: 1.0 },
        impish: { attack: 1.0, defense: 1.1, spatk: 0.9, spdef: 1.0, speed: 1.0 },
        careful: { attack: 1.0, defense: 1.0, spatk: 0.9, spdef: 1.1, speed: 1.0 }
    };
    
    calculateBtn.addEventListener('click', function() {
        const pokemonName = document.getElementById('pokemon-name').value;
        const level = parseInt(document.getElementById('pokemon-level').value) || 50;
        const nature = document.getElementById('pokemon-nature').value;
        const generation = parseInt(document.getElementById('generation').value) || 3;
        
        const currentStats = {
            hp: parseInt(document.getElementById('stat-hp').value) || 0,
            attack: parseInt(document.getElementById('stat-attack').value) || 0,
            defense: parseInt(document.getElementById('stat-defense').value) || 0,
            spatk: parseInt(document.getElementById('stat-spatk').value) || 0,
            spdef: parseInt(document.getElementById('stat-spdef').value) || 0,
            speed: parseInt(document.getElementById('stat-speed').value) || 0
        };
        
        if (!pokemonBaseStats[pokemonName]) {
            alert('Please select a valid Pokémon or implement custom Pokémon stats.');
            return;
        }
        
        const ivCalculation = calculateIVs(pokemonName, level, nature, currentStats, generation);
        displayIVResults(ivCalculation);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('psypoke-calculator-form').reset();
        resultsDiv.style.display = 'none';
    });
    
    function calculateIVs(pokemonName, level, nature, currentStats, generation) {
        const baseStats = pokemonBaseStats[pokemonName];
        const natureModifier = natureModifiers[nature] || natureModifiers.hardy;
        
        const ivs = {};
        const statNames = ['hp', 'attack', 'defense', 'spatk', 'spdef', 'speed'];
        
        // Calculate IVs for each stat
        statNames.forEach(stat => {
            if (stat === 'hp') {
                // HP IV calculation
                const iv = currentStats[stat] - Math.floor(((2 * baseStats[stat] + level) * level) / 100) - level - 10;
                ivs[stat] = Math.max(0, Math.min(31, iv));
            } else {
                // Other stats IV calculation
                const modifier = natureModifier[stat] || 1.0;
                const baseStat = Math.floor(((2 * baseStats[stat] + level) * level) / 100) + 5;
                const modifiedStat = Math.floor(baseStat * modifier);
                const iv = currentStats[stat] - modifiedStat;
                ivs[stat] = Math.max(0, Math.min(31, Math.round(iv)));
            }
        });
        
        // Calculate IV percentage and rating
        const totalIVs = Object.values(ivs).reduce((sum, iv) => sum + iv, 0);
        const maxIVs = 31 * 6;
        const ivPercentage = (totalIVs / maxIVs) * 100;
        
        // Calculate Hidden Power type and power
        const hiddenPower = calculateHiddenPower(ivs);
        
        // Generate IV rating
        const ivRating = getIVRating(ivPercentage);
        
        return {
            pokemonName: pokemonName,
            level: level,
            nature: nature,
            generation: generation,
            baseStats: baseStats,
            currentStats: currentStats,
            ivs: ivs,
            totalIVs: totalIVs,
            ivPercentage: ivPercentage,
            ivRating: ivRating,
            hiddenPower: hiddenPower
        };
    }
    
    function calculateHiddenPower(ivs) {
        // Hidden Power calculation for Gen 3+
        const hpType = Math.floor(((ivs.hp % 2) + 2 * (ivs.attack % 2) + 4 * (ivs.defense % 2) + 
                                  8 * (ivs.speed % 2) + 16 * (ivs.spatk % 2) + 32 * (ivs.spdef % 2)) * 15 / 63);
        
        const hpPower = Math.floor(((Math.floor(ivs.hp / 2) % 2) + 2 * (Math.floor(ivs.attack / 2) % 2) + 
                                   4 * (Math.floor(ivs.defense / 2) % 2) + 8 * (Math.floor(ivs.speed / 2) % 2) + 
                                   16 * (Math.floor(ivs.spatk / 2) % 2) + 32 * (Math.floor(ivs.spdef / 2) % 2)) * 40 / 63) + 30;
        
        const types = ['Fighting', 'Flying', 'Poison', 'Ground', 'Rock', 'Bug', 'Ghost', 'Steel',
                      'Fire', 'Water', 'Grass', 'Electric', 'Psychic', 'Ice', 'Dragon', 'Dark'];
        
        return {
            type: types[hpType] || 'Fighting',
            power: Math.round(hpPower)
        };
    }
    
    function getIVRating(percentage) {
        if (percentage >= 90) return { rating: 'Perfect', class: 'iv-perfect' };
        if (percentage >= 75) return { rating: 'Excellent', class: 'iv-good' };
        if (percentage >= 50) return { rating: 'Good', class: 'iv-average' };
        return { rating: 'Poor', class: 'iv-poor' };
    }
    
    function getStatRating(iv) {
        if (iv >= 30) return 'iv-perfect';
        if (iv >= 25) return 'iv-good';
        if (iv >= 15) return 'iv-average';
        return 'iv-poor';
    }
    
    function displayIVResults(calc) {
        const statNames = {
            hp: 'HP', attack: 'Attack', defense: 'Defense',
            spatk: 'Sp. Attack', spdef: 'Sp. Defense', speed: 'Speed'
        };
        
        let html = `
            <div style="margin-bottom: 25px;">
                <div style="font-size: 36px; font-weight: bold; margin-bottom: 10px;">
                    ${calc.ivPercentage.toFixed(1)}%
                </div>
                <div style="font-size: 18px; margin-bottom: 15px;">
                    Overall IV Rating: ${calc.ivRating.rating}
                </div>
                <div style="font-size: 16px;">
                    Total IVs: ${calc.totalIVs}/186
                </div>
            </div>
            
            <div class="iv-display">
        `;
        
        Object.entries(calc.ivs).forEach(([stat, iv]) => {
            const statClass = getStatRating(iv);
            html += `
                <div class="iv-stat ${statClass}">
                    <div style="font-weight: bold; margin-bottom: 5px;">${statNames[stat]}</div>
                    <div style="font-size: 24px; font-weight: bold;">${iv}</div>
                    <div style="font-size: 12px;">/ 31</div>
                </div>
            `;
        });
        
        html += `
            </div>
            
            <div class="pokemon-selector">
                <h4 style="color: #e91e63; margin-bottom: 20px;">📊 Detailed Analysis</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <h5>Pokémon Information:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Pokémon:</strong> ${calc.pokemonName.charAt(0).toUpperCase() + calc.pokemonName.slice(1)}</li>
                            <li><strong>Level:</strong> ${calc.level}</li>
                            <li><strong>Nature:</strong> ${calc.nature.charAt(0).toUpperCase() + calc.nature.slice(1)}</li>
                            <li><strong>Generation:</strong> ${calc.generation}</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5>Hidden Power:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Type:</strong> ${calc.hiddenPower.type}</li>
                            <li><strong>Power:</strong> ${calc.hiddenPower.power}</li>
                            <li><strong>IV Rating:</strong> ${calc.ivRating.rating}</li>
                            <li><strong>Perfect IVs:</strong> ${Object.values(calc.ivs).filter(iv => iv === 31).length}/6</li>
                        </ul>
                    </div>
                </div>
                
                <div style="background-color: #f5f5f5; border-radius: 8px; padding: 15px; margin: 15px 0;">
                    <h5>Stat Breakdown:</h5>
                    <table style="width: 100%; border-collapse: collapse;">
                        <thead>
                            <tr style="background-color: #e91e63; color: white;">
                                <th style="padding: 8px; border: 1px solid #ddd;">Stat</th>
                                <th style="padding: 8px; border: 1px solid #ddd;">Base</th>
                                <th style="padding: 8px; border: 1px solid #ddd;">Current</th>
                                <th style="padding: 8px; border: 1px solid #ddd;">IV</th>
                                <th style="padding: 8px; border: 1px solid #ddd;">Rating</th>
                            </tr>
                        </thead>
                        <tbody>
        `;
        
        Object.entries(calc.ivs).forEach(([stat, iv]) => {
            const rating = iv >= 30 ? 'Perfect' : iv >= 25 ? 'Excellent' : iv >= 15 ? 'Good' : 'Poor';
            const ratingColor = iv >= 30 ? '#4caf50' : iv >= 25 ? '#ff9800' : iv >= 15 ? '#9e9e9e' : '#f44336';
            
            html += `
                <tr>
                    <td style="padding: 6px; border: 1px solid #ddd; font-weight: bold;">${statNames[stat]}</td>
                    <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${calc.baseStats[stat]}</td>
                    <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${calc.currentStats[stat]}</td>
                    <td style="padding: 6px; border: 1px solid #ddd; text-align: center; font-weight: bold;">${iv}</td>
                    <td style="padding: 6px; border: 1px solid #ddd; text-align: center; color: ${ratingColor}; font-weight: bold;">${rating}</td>
                </tr>
            `;
        });
        
        html += `
                        </tbody>
                    </table>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 IV Analysis Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Perfect IVs:</strong> 31 in all stats is extremely rare (1 in 1 billion chance)</li>
                    <li><strong>Competitive Viability:</strong> 25+ IVs in important stats are usually sufficient</li>
                    <li><strong>Hidden Power:</strong> Specific IV combinations determine Hidden Power type</li>
                    <li><strong>Nature Impact:</strong> Natures provide 10% boost/reduction to specific stats</li>
                    <li><strong>Level Accuracy:</strong> Higher level Pokémon provide more accurate IV calculations</li>
                    <li><strong>EV Consideration:</strong> This calculator assumes 0 EVs in all stats</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
