<!DOCTYPE html>
<!-- saved from url=(0070)https://securepubads.g.doubleclick.net/static/topics/topics_frame.html -->
<html><head><meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    
    <title>Topics Frame</title>
    <meta http-equiv="origin-trial" content="Avh5Ny0XEFCyQ7+oNieXskUrqY8edUzL5/XrwKlGjARQHW4TFRK+jVd5HnDIpY20n5OLHfgU4ku7x48N3uhG/A0AAABxeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiUHJpdmFjeVNhbmRib3hBZHNBUElzIiwiZXhwaXJ5IjoxNjk1MTY3OTk5LCJpc1N1YmRvbWFpbiI6dHJ1ZX0=">
    <script>
      
/*

 Copyright 2022 Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var m,aa,ba=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ca=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},da=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");},ea=da(this),fa=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",q={},ia={},w=function(a,b,c){if(!c||a!=null){c=ia[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}},y=function(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],g;!a&&e in q?g=q:g=ea;for(e=0;e<d.length-1;e++){var f=d[e];if(!(f in g))break a;g=g[f]}d=d[d.length-1];c=fa&&c==="es6"?g[d]:null;b=b(c);b!=null&&(a?ca(q,d,{configurable:!0,writable:!0,value:b}):b!==c&&(ia[d]===void 0&&(a=Math.random()*1E9>>>0,ia[d]=fa?ea.Symbol(d):"$jscp$"+a+"$"+d),ca(g,ia[d],{configurable:!0,writable:!0,value:b})))}};y("Symbol",function(a){if(a)return a;var b=function(g,f){this.g=g;ca(this,"description",{configurable:!0,writable:!0,value:f})};b.prototype.toString=function(){return this.g};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(g){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(g||"")+"_"+d++,g)};return e},"es6");y("Symbol.iterator",function(a){if(a)return a;a=(0,q.Symbol)("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=ea[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ca(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ja(ba(this))}})}return a},"es6");var ja=function(a){a={next:a};a[w(q.Symbol,"iterator")]=function(){return this};return a},ka=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},la;if(fa&&typeof w(Object,"setPrototypeOf")=="function")la=w(Object,"setPrototypeOf");else{var ma;a:{var na={a:!0},oa={};try{oa.__proto__=na;ma=oa.a;break a}catch(a){}ma=!1}la=ma?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var pa=la,z=function(a,b){a.prototype=ka(b.prototype);a.prototype.constructor=a;if(pa)pa(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Ma=b.prototype},A=function(a){var b=typeof q.Symbol!="undefined"&&w(q.Symbol,"iterator")&&a[w(q.Symbol,"iterator")];if(b)return b.call(a);if(typeof a.length=="number")return{next:ba(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},qa=function(a){if(!(a instanceof Array)){a=A(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a},ua=function(a){return ta(a,a)},ta=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},va=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},wa=fa&&typeof w(Object,"assign")=="function"?w(Object,"assign"):function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)va(d,e)&&(a[e]=d[e])}return a};y("Object.assign",function(a){return a||wa},"es6");var xa=function(){this.D=!1;this.i=null;this.g=void 0;this.h=1;this.U=this.s=0;this.l=null},ya=function(a){if(a.D)throw new TypeError("Generator is already running");a.D=!0};xa.prototype.R=function(a){this.g=a};var za=function(a,b){a.l={ja:b,Ba:!0};a.h=a.s||a.U};xa.prototype.return=function(a){this.l={return:a};this.h=this.U};var B=function(a,b,c){a.h=c;return{value:b}},Aa=function(a){a.s=0;var b=a.l.ja;a.l=null;return b},Ba=function(a){this.g=new xa;this.h=a},Fa=function(a,b){ya(a.g);var c=a.g.i;if(c)return Ca(a,"return"in c?c["return"]:function(d){return{value:d,done:!0}},b,a.g.return);a.g.return(b);return Da(a)},Ca=function(a,b,c,d){try{var e=b.call(a.g.i,c);if(!(e instanceof Object))throw new TypeError("Iterator result "+e+" is not an object");if(!e.done)return a.g.D=!1,e;var g=e.value}catch(f){return a.g.i=null,za(a.g,f),Da(a)}a.g.i=null;d.call(a.g,g);return Da(a)},Da=function(a){for(;a.g.h;)try{var b=a.h(a.g);if(b)return a.g.D=!1,{value:b.value,done:!1}}catch(c){a.g.g=void 0,za(a.g,c)}a.g.D=!1;if(a.g.l){b=a.g.l;a.g.l=null;if(b.Ba)throw b.ja;return{value:b.return,done:!0}}return{value:void 0,done:!0}},Ga=function(a){this.next=function(b){ya(a.g);a.g.i?b=Ca(a,a.g.i.next,b,a.g.R):(a.g.R(b),b=Da(a));return b};this.throw=function(b){ya(a.g);a.g.i?b=Ca(a,a.g.i["throw"],b,a.g.R):(za(a.g,b),b=Da(a));return b};this.return=function(b){return Fa(a,b)};this[w(q.Symbol,"iterator")]=function(){return this}},Ha=function(a){function b(d){return a.next(d)}function c(d){return a.throw(d)}return new q.Promise(function(d,e){function g(f){f.done?d(f.value):q.Promise.resolve(f.value).then(b,c).then(g,e)}g(a.next())})},C=function(a){return Ha(new Ga(new Ba(a)))},Ia=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};y("globalThis",function(a){return a||ea},"es_2020");y("Reflect.setPrototypeOf",function(a){return a?a:pa?function(b,c){try{return pa(b,c),!0}catch(d){return!1}}:null},"es6");y("Promise",function(a){function b(){this.g=null}function c(f){return f instanceof e?f:new e(function(h){h(f)})}if(a)return a;b.prototype.h=function(f){if(this.g==null){this.g=[];var h=this;this.i(function(){h.s()})}this.g.push(f)};var d=ea.setTimeout;b.prototype.i=function(f){d(f,0)};b.prototype.s=function(){for(;this.g&&this.g.length;){var f=this.g;this.g=[];for(var h=0;h<f.length;++h){var k=f[h];f[h]=null;try{k()}catch(l){this.l(l)}}}this.g=null};b.prototype.l=function(f){this.i(function(){throw f;})};var e=function(f){this.h=0;this.i=void 0;this.g=[];this.R=!1;var h=this.l();try{f(h.resolve,h.reject)}catch(k){h.reject(k)}};e.prototype.l=function(){function f(l){return function(n){k||(k=!0,l.call(h,n))}}var h=this,k=!1;return{resolve:f(this.qa),reject:f(this.s)}};e.prototype.qa=function(f){if(f===this)this.s(new TypeError("A Promise cannot resolve to itself"));else if(f instanceof e)this.sa(f);else{a:switch(typeof f){case "object":var h=f!=null;break a;case "function":h=!0;break a;default:h=!1}h?this.pa(f):this.D(f)}};e.prototype.pa=function(f){var h=void 0;try{h=f.then}catch(k){this.s(k);return}typeof h=="function"?this.ta(h,f):this.D(f)};e.prototype.s=function(f){this.U(2,f)};e.prototype.D=function(f){this.U(1,f)};e.prototype.U=function(f,h){if(this.h!=0)throw Error("Cannot settle("+f+", "+h+"): Promise already settled in state"+this.h);this.h=f;this.i=h;this.h===2&&this.ra();this.na()};e.prototype.ra=function(){var f=this;d(function(){if(f.oa()){var h=ea.console;typeof h!=="undefined"&&h.error(f.i)}},1)};e.prototype.oa=function(){if(this.R)return!1;var f=ea.CustomEvent,h=ea.Event,k=ea.dispatchEvent;if(typeof k==="undefined")return!0;typeof f==="function"?f=new f("unhandledrejection",{cancelable:!0}):typeof h==="function"?f=new h("unhandledrejection",{cancelable:!0}):(f=ea.document.createEvent("CustomEvent"),f.initCustomEvent("unhandledrejection",!1,!0,f));f.promise=this;f.reason=this.i;return k(f)};e.prototype.na=function(){if(this.g!=null){for(var f=0;f<this.g.length;++f)g.h(this.g[f]);this.g=null}};var g=new b;e.prototype.sa=function(f){var h=this.l();f.X(h.resolve,h.reject)};e.prototype.ta=function(f,h){var k=this.l();try{f.call(h,k.resolve,k.reject)}catch(l){k.reject(l)}};e.prototype.then=function(f,h){function k(r,t){return typeof r=="function"?function(u){try{l(r(u))}catch(v){n(v)}}:t}var l,n,p=new e(function(r,t){l=r;n=t});this.X(k(f,l),k(h,n));return p};e.prototype.catch=function(f){return this.then(void 0,f)};e.prototype.X=function(f,h){function k(){switch(l.h){case 1:f(l.i);break;case 2:h(l.i);break;default:throw Error("Unexpected state: "+l.h);}}var l=this;this.g==null?g.h(k):this.g.push(k);this.R=!0};e.resolve=c;e.reject=function(f){return new e(function(h,k){k(f)})};e.race=function(f){return new e(function(h,k){for(var l=A(f),n=l.next();!n.done;n=l.next())c(n.value).X(h,k)})};e.all=function(f){var h=A(f),k=h.next();return k.done?c([]):new e(function(l,n){function p(u){return function(v){r[u]=v;t--;t==0&&l(r)}}var r=[],t=0;do r.push(void 0),t++,c(k.value).X(p(r.length-1),n),k=h.next();while(!k.done)})};return e},"es6");y("Object.setPrototypeOf",function(a){return a||pa},"es6");y("WeakMap",function(a){function b(){}function c(f){var h=typeof f;return h==="object"&&f!==null||h==="function"}if(function(){if(!a||!Object.seal)return!1;try{var f=Object.seal({}),h=Object.seal({}),k=new a([[f,2],[h,3]]);if(k.get(f)!=2||k.get(h)!=3)return!1;k.delete(f);k.set(h,4);return!k.has(f)&&k.get(h)==4}catch(l){return!1}}())return a;var d="$jscomp_hidden_"+Math.random(),e=0,g=function(f){this.g=(e+=Math.random()+1).toString();if(f){f=A(f);for(var h;!(h=f.next()).done;)h=h.value,this.set(h[0],h[1])}};g.prototype.set=function(f,h){if(!c(f))throw Error("Invalid WeakMap key");if(!va(f,d)){var k=new b;ca(f,d,{value:k})}if(!va(f,d))throw Error("WeakMap key fail: "+f);f[d][this.g]=h;return this};g.prototype.get=function(f){return c(f)&&va(f,d)?f[d][this.g]:void 0};g.prototype.has=function(f){return c(f)&&va(f,d)&&va(f[d],this.g)};g.prototype.delete=function(f){return c(f)&&va(f,d)&&va(f[d],this.g)?delete f[d][this.g]:!1};return g},"es6");y("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),k=new a(A([[h,"s"]]));if(k.get(h)!="s"||k.size!=1||k.get({x:4})||k.set({x:4},"t")!=k||k.size!=2)return!1;var l=k.entries(),n=l.next();if(n.done||n.value[0]!=h||n.value[1]!="s")return!1;n=l.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!l.next().done?!1:!0}catch(p){return!1}}())return a;var b=new q.WeakMap,c=function(h){this[0]={};this[1]=g();this.size=0;if(h){h=A(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=h===0?0:h;var l=d(this,h);l.list||(l.list=this[0][l.id]=[]);l.entry?l.entry.value=k:(l.entry={next:this[1],G:this[1].G,head:this[1],key:h,value:k},l.list.push(l.entry),this[1].G.next=l.entry,this[1].G=l.entry,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.entry&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.entry.G.next=h.entry.next,h.entry.next.G=h.entry.G,h.entry.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].G=g();this.size=0};c.prototype.has=function(h){return!!d(this,h).entry};c.prototype.get=function(h){return(h=d(this,h).entry)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=function(h,k){for(var l=this.entries(),n;!(n=l.next()).done;)n=n.value,h.call(k,n[1],n[0],this)};c.prototype[w(q.Symbol,"iterator")]=c.prototype.entries;var d=function(h,k){var l=k&&typeof k;l=="object"||l=="function"?b.has(k)?l=b.get(k):(l=""+ ++f,b.set(k,l)):l="p_"+k;var n=h[0][l];if(n&&va(h[0],l))for(h=0;h<n.length;h++){var p=n[h];if(k!==k&&p.key!==p.key||k===p.key)return{id:l,list:n,index:h,entry:p}}return{id:l,list:n,index:-1,entry:void 0}},e=function(h,k){var l=h[1];return ja(function(){if(l){for(;l.head!=h[1];)l=l.G;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};l=null}return{done:!0,value:void 0}})},g=function(){var h={};return h.G=h.next=h.head=h},f=0;return c},"es6");y("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}},"es6");y("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var g=d[c];if(g===b||w(Object,"is").call(Object,g,b))return!0}return!1}},"es7");y("String.prototype.includes",function(a){return a?a:function(b,c){if(this==null)throw new TypeError("The 'this' value for String.prototype.includes must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype.includes must not be a regular expression");return this.indexOf(b,c||0)!==-1}},"es6");y("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}},"es6");y("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991},"es6");y("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991},"es6");y("Number.isInteger",function(a){return a?a:function(b){return w(Number,"isFinite").call(Number,b)?b===Math.floor(b):!1}},"es6");y("Number.isSafeInteger",function(a){return a?a:function(b){return w(Number,"isInteger").call(Number,b)&&Math.abs(b)<=w(Number,"MAX_SAFE_INTEGER")}},"es6");y("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}},"es6");y("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}},"es6");/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var D=this||self,Ja=function(a){return a};function Ka(){throw Error("Invalid UTF8");}function La(a,b){b=String.fromCharCode.apply(null,b);return a==null?b:a+b}var Ma=void 0,Na,Oa=typeof TextDecoder!=="undefined",Pa,Qa=typeof String.prototype.isWellFormed==="function",Ra=typeof TextEncoder!=="undefined";function Sa(a){D.setTimeout(function(){throw a;},0)};var Ta,Ua;a:{for(var Va=["CLOSURE_FLAGS"],Wa=D,Xa=0;Xa<Va.length;Xa++)if(Wa=Wa[Va[Xa]],Wa==null){Ua=null;break a}Ua=Wa}var Ya=Ua&&Ua[610401301];Ta=Ya!=null?Ya:!1;function Za(){var a=D.navigator;return a&&(a=a.userAgent)?a:""}var $a,ab=D.navigator;$a=ab?ab.userAgentData||null:null;function bb(a){return Za().indexOf(a)!=-1};var cb=function(a){cb[" "](a);return a};cb[" "]=function(){};var db=bb("Gecko")&&!(Za().toLowerCase().indexOf("webkit")!=-1&&!bb("Edge"))&&!(bb("Trident")||bb("MSIE"))&&!bb("Edge"),eb=Za().toLowerCase().indexOf("webkit")!=-1&&!bb("Edge");var fb={},gb=null,ib=function(a,b){b===void 0&&(b=0);hb();b=fb[b];for(var c=Array(Math.floor(a.length/3)),d=b[64]||"",e=0,g=0;e<a.length-2;e+=3){var f=a[e],h=a[e+1],k=a[e+2],l=b[f>>2];f=b[(f&3)<<4|h>>4];h=b[(h&15)<<2|k>>6];k=b[k&63];c[g++]=l+f+h+k}l=0;k=d;switch(a.length-e){case 2:l=a[e+1],k=b[(l&15)<<2]||d;case 1:a=a[e],c[g]=b[a>>2]+b[(a&3)<<4|l>>4]+k+d}return c.join("")},kb=function(a){var b=a.length,c=b*3/4;c%3?c=Math.floor(c):"=.".indexOf(a[b-1])!=-1&&(c="=.".indexOf(a[b-2])!=-1?c-2:c-1);var d=new Uint8Array(c),e=0;jb(a,function(g){d[e++]=g});return e!==c?d.subarray(0,e):d},jb=function(a,b){function c(k){for(;d<a.length;){var l=a.charAt(d++),n=gb[l];if(n!=null)return n;if(!/^[\s\xa0]*$/.test(l))throw Error("Unknown base64 encoding at char: "+l);}return k}hb();for(var d=0;;){var e=c(-1),g=c(0),f=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|g>>4);f!=64&&(b(g<<4&240|f>>2),h!=64&&b(f<<6&192|h))}},hb=function(){if(!gb){gb={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++){var d=a.concat(b[c].split(""));fb[c]=d;for(var e=0;e<d.length;e++){var g=d[e];gb[g]===void 0&&(gb[g]=e)}}}};var lb=typeof Uint8Array!=="undefined",mb=!(Ta&&$a&&$a.brands.length>0?0:bb("Trident")||bb("MSIE"))&&typeof btoa==="function",nb=/[-_.]/g,ob={"-":"+",_:"/",".":"="};function pb(a){return ob[a]||""}function qb(a){if(!mb)return kb(a);a=nb.test(a)?a.replace(nb,pb):a;a=atob(a);for(var b=new Uint8Array(a.length),c=0;c<a.length;c++)b[c]=a.charCodeAt(c);return b}var rb={};var tb=function(a,b){sb(b);this.g=a;if(a!=null&&a.length===0)throw Error("ByteString should be constructed with non-empty values");},yb=function(){return ub||(ub=new tb(null,rb))},zb=function(a){sb(rb);var b=a.g;b=b==null||lb&&b!=null&&b instanceof Uint8Array?b:typeof b==="string"?qb(b):null;return b==null?b:a.g=b},ub;function sb(a){if(a!==rb)throw Error("illegal external caller");};function Ab(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};var Bb=void 0;function Cb(a){a=Error(a);Ab(a,"warning");return a}function Db(a,b){if(a!=null){var c;var d=(c=Bb)!=null?c:Bb={};c=d[a]||0;c>=b||(d[a]=c+1,a=Error(),Ab(a,"incident"),Sa(a))}};var Eb=typeof q.Symbol==="function"&&typeof(0,q.Symbol)()==="symbol";function Fb(a,b,c){return typeof q.Symbol==="function"&&typeof(0,q.Symbol)()==="symbol"?(c===void 0?0:c)&&q.Symbol.for&&a?q.Symbol.for(a):a!=null?(0,q.Symbol)(a):(0,q.Symbol)():b}var Gb=Fb("jas",void 0,!0),Hb=Fb(void 0,(0,q.Symbol)()),Ib=Fb(void 0,"0ubs"),Jb=Fb(void 0,"0ubsb"),Kb=Fb(void 0,"0actk"),Lb=Fb("m_m","Ia",!0);var Mb={Aa:{value:0,configurable:!0,writable:!0,enumerable:!1}},Nb=Object.defineProperties,E=Eb?Gb:"Aa",Ob,Pb=[];F(Pb,7);Ob=Object.freeze(Pb);function Qb(a,b){Eb||E in a||Nb(a,Mb);a[E]|=b}function F(a,b){Eb||E in a||Nb(a,Mb);a[E]=b};function Rb(){return typeof BigInt==="function"};var Sb={};function G(a,b){return b===void 0?a.g!==Tb&&!!(2&(a.j[E]|0)):!!(2&b)&&a.g!==Tb}var Tb={};function Ub(a,b){if(a!=null)if(typeof a==="string")a=a?new tb(a,rb):yb();else if(a.constructor!==tb)if(lb&&a!=null&&a instanceof Uint8Array)a=a.length?new tb(new Uint8Array(a),rb):yb();else{if(!b)throw Error();a=void 0}return a}var Vb=Object.freeze({});function Wb(a,b,c){b=b&128?0:-1;var d=a.length,e;if(e=!!d)e=a[d-1],e=e!=null&&typeof e==="object"&&e.constructor===Object;for(var g=d+(e?-1:0),f=0;f<g;f++)c(f-b,a[f]);if(e){a=a[d-1];for(var h in a)Object.prototype.hasOwnProperty.call(a,h)&&!isNaN(h)&&c(+h,a[h])}}var Xb={};function Yb(a){a.Ha=!0;return a};var Zb=Yb(function(a){return typeof a==="number"}),$b=Yb(function(a){return typeof a==="string"}),ac=Yb(function(a){return typeof a==="boolean"});var bc=typeof D.BigInt==="function"&&typeof D.BigInt(0)==="bigint";function cc(a){var b=a;if($b(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(Zb(b)&&!w(Number,"isSafeInteger").call(Number,b))throw Error(String(b));return bc?BigInt(a):a=ac(a)?a?"1":"0":$b(a)?a.trim()||"0":String(a)}var jc=Yb(function(a){return bc?a>=dc&&a<=ec:a[0]==="-"?fc(a,hc):fc(a,ic)}),hc=w(Number,"MIN_SAFE_INTEGER").toString(),dc=bc?BigInt(w(Number,"MIN_SAFE_INTEGER")):void 0,ic=w(Number,"MAX_SAFE_INTEGER").toString(),ec=bc?BigInt(w(Number,"MAX_SAFE_INTEGER")):void 0;function fc(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};var kc=typeof Uint8Array.prototype.slice==="function",H=0,I=0;function lc(a){var b=a>>>0;H=b;I=(a-b)/4294967296>>>0}function mc(a){if(a<0){lc(-a);var b=A(nc(H,I));a=b.next().value;b=b.next().value;H=a>>>0;I=b>>>0}else lc(a)}function oc(a,b){var c=b&2147483648;c&&(a=~a+1>>>0,b=~b>>>0,a==0&&(b=b+1>>>0));var d=b*4294967296+(a>>>0);a=w(Number,"isSafeInteger").call(Number,d)?d:pc(a,b);return typeof a==="number"?c?-a:a:c?"-"+a:a}function pc(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else Rb()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+qc(c)+qc(a));return c}function qc(a){a=String(a);return"0000000".slice(a.length)+a}function rc(){var a=H,b=I;b&2147483648?Rb()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=A(nc(a,b)),a=b.next().value,b=b.next().value,a="-"+pc(a,b)):a=pc(a,b);return a}function sc(a){if(a.length<16)mc(Number(a));else if(Rb())a=BigInt(a),H=Number(a&BigInt(4294967295))>>>0,I=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");I=H=0;for(var c=a.length,d=b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),I*=1E6,H=H*1E6+d,H>=4294967296&&(I+=w(Math,"trunc").call(Math,H/4294967296),I>>>=0,H>>>=0);b&&(b=A(nc(H,I)),a=b.next().value,b=b.next().value,H=a,I=b)}}function nc(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};var tc=typeof BigInt==="function"?BigInt.asIntN:void 0,uc=w(Number,"isSafeInteger"),vc=w(Number,"isFinite"),wc=w(Math,"trunc"),xc=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function yc(a){switch(typeof a){case "bigint":return!0;case "number":return vc(a);case "string":return xc.test(a);default:return!1}}function zc(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return vc(a)?a|0:void 0}function Ac(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return vc(a)?a>>>0:void 0}function Bc(a){var b=0;b=b===void 0?0:b;if(!yc(a))throw Cb("int64");var c=typeof a;switch(b){case 512:switch(c){case "string":return Cc(a);case "bigint":return String(tc(64,a));default:return Dc(a)}case 1024:switch(c){case "string":return b=wc(Number(a)),uc(b)?a=cc(b):(b=a.indexOf("."),b!==-1&&(a=a.substring(0,b)),a=Rb()?cc(tc(64,BigInt(a))):cc(Ec(a))),a;case "bigint":return cc(tc(64,a));default:return uc(a)?cc(Fc(a)):cc(Dc(a))}case 0:switch(c){case "string":return Cc(a);case "bigint":return cc(tc(64,a));default:return Fc(a)}default:throw Error("Unknown format requested type for int64");}}function Gc(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}function Ec(a){if(Gc(a))return a;sc(a);return rc()}function Fc(a){a=wc(a);uc(a)||(mc(a),a=oc(H,I));return a}function Dc(a){a=wc(a);if(uc(a))a=String(a);else{var b=String(a);Gc(b)?a=b:(mc(a),a=rc())}return a}function Cc(a){var b=wc(Number(a));if(uc(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return Ec(a)}function Hc(a){if(a==null)return a;var b=typeof a;if(b==="bigint")return String(tc(64,a));if(yc(a)){if(b==="string")return Cc(a);if(b==="number")return Fc(a)}}function Ic(a){return a==null||typeof a==="string"?a:void 0}function Jc(a,b,c){if(a!=null&&a[Lb]===Sb)return a;if(Array.isArray(a)){var d=a[E]|0;c=d|c&32|c&2;c!==d&&F(a,c);return new b(a)}};function Kc(a){return a};function Lc(a){var b=Ja(Hb);return b?a[b]:void 0}var Mc=function(){},Nc=function(a,b){for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&!isNaN(c)&&b(a,+c,a[c])},Oc=function(a){var b=new Mc;Nc(a,function(c,d,e){b[d]=Array.prototype.slice.call(e)});b.g=a.g;return b};function Pc(a,b){b<100||Db(Ib,1)};function Qc(a,b,c,d){var e=d!==void 0;d=!!d;var g=Ja(Hb),f;!e&&Eb&&g&&(f=a[g])&&Nc(f,Pc);g=[];var h=a.length;f=4294967295;var k=!1,l=!!(b&64),n=l?b&128?0:-1:void 0;if(!(b&1)){var p=h&&a[h-1];p!=null&&typeof p==="object"&&p.constructor===Object?(h--,f=h):p=void 0;if(l&&!(b&128)&&!e){k=!0;var r;f=((r=Rc)!=null?r:Kc)(f-n,n,a,p)+n}}b=void 0;for(r=0;r<h;r++){var t=a[r];if(t!=null&&(t=c(t,d))!=null)if(l&&r>=f){var u=r-n,v=void 0;((v=b)!=null?v:b={})[u]=t}else g[r]=t}if(p)for(var x in p)Object.prototype.hasOwnProperty.call(p,x)&&(h=p[x],h!=null&&(h=c(h,d))!=null&&(r=+x,t=void 0,l&&!w(Number,"isNaN").call(Number,r)&&(t=r+n)<f?g[t]=h:(r=void 0,((r=b)!=null?r:b={})[x]=h)));b&&(k?g.push(b):g[f]=b);e&&Ja(Hb)&&(a=Lc(a))&&a instanceof Mc&&(g[Hb]=Oc(a));return g}function Sc(a){switch(typeof a){case "number":return w(Number,"isFinite").call(Number,a)?a:""+a;case "bigint":return jc(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[E]|0;return a.length===0&&b&1?void 0:Qc(a,b,Sc)}if(a!=null&&a[Lb]===Sb)return Tc(a);if(a instanceof tb){b=a.g;if(b==null)a="";else if(typeof b==="string")a=b;else{if(mb){for(var c="",d=0,e=b.length-10240;d<e;)c+=String.fromCharCode.apply(null,b.subarray(d,d+=10240));c+=String.fromCharCode.apply(null,d?b.subarray(d):b);b=btoa(c)}else b=ib(b);a=a.g=b}return a}return}return a}var Rc;function Tc(a){a=a.j;return Qc(a,a[E]|0,Sc)};var Uc,Vc;function Wc(a){switch(typeof a){case "boolean":return Uc||(Uc=[0,void 0,!0]);case "number":return a>0?void 0:a===0?Vc||(Vc=[0,void 0]):[-a,void 0];case "string":return[0,a];case "object":return a}}function Xc(a,b,c){return a=J(a,b[0],b[1],c?1:2)}function J(a,b,c,d){d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("narr");e=a[E]|0;2048&e&&!(2&e)&&Yc();if(e&256)throw Error("farr");if(e&64)return d!==0||e&2048||F(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("mid");a:{c=a;e|=64;var g=c.length;if(g){var f=g-1,h=c[f];if(h!=null&&typeof h==="object"&&h.constructor===Object){b=e&128?0:-1;f-=b;if(f>=1024)throw Error("pvtlmt");for(var k in h)Object.prototype.hasOwnProperty.call(h,k)&&(g=+k,g<f&&(c[g+b]=h[k],delete h[k]));e=e&-8380417|(f&1023)<<13;break a}}if(b){k=Math.max(b,g-(e&128?0:-1));if(k>1024)throw Error("spvt");e=e&-8380417|(k&1023)<<13}}}e|=64;d===0&&(e|=2048);F(a,e);return a}function Yc(){Db(Kb,5)};function Zc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[E]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=$c(a,c,!1,b&&!(c&16)):(Qb(a,34),c&4&&Object.freeze(a)));return a}if(a!=null&&a[Lb]===Sb)return b=a.j,c=b[E]|0,G(a,c)?a:ad(a,b,c)?bd(a,b):$c(b,c);if(a instanceof tb)return a}function bd(a,b,c){a=new a.constructor(b);c&&(a.g=Tb);a.i=Tb;return a}function $c(a,b,c,d){d!=null||(d=!!(34&b));a=Qc(a,b,Zc,d);d=32;c&&(d|=2);b=b&8380609|d;F(a,b);return a}function cd(a){var b=a.j,c=b[E]|0;return G(a,c)?ad(a,b,c)?bd(a,b,!0):new a.constructor($c(b,c,!1)):a}function dd(a){if(a.g!==Tb)return!1;var b=a.j;b=$c(b,b[E]|0);Qb(b,2048);a.j=b;a.g=void 0;a.i=void 0;return!0}function ed(a){if(!dd(a)&&G(a,a.j[E]|0))throw Error();}function fd(a,b){b===void 0&&(b=a[E]|0);b&32&&!(b&4096)&&F(a,b|4096)}function ad(a,b,c){return c&2?!0:c&32&&!(c&4096)?(F(b,c|2),a.g=Tb,!0):!1};var hd=function(a,b,c){a=gd(a.j,b,void 0,c);if(a!==null)return a},gd=function(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),g=a.length-1;if(!(g<1+(c?0:-1))){if(e>=g){var f=a[g];if(f!=null&&typeof f==="object"&&f.constructor===Object){c=f[b];var h=!0}else if(e===g)c=f;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!w(Object,"is").call(Object,d,c))return h?f[b]=d:a[e]=d,d}return c}};function id(a,b,c,d,e){var g=c+(e?0:-1),f=a.length-1;if(f>=1+(e?0:-1)&&g>=f){var h=a[f];if(h!=null&&typeof h==="object"&&h.constructor===Object)return h[c]=d,b}if(g<=f)return a[g]=d,b;if(d!==void 0){var k;f=((k=b)!=null?k:b=a[E]|0)>>13&1023||536870912;c>=f?d!=null&&(g={},a[f+(e?0:-1)]=(g[c]=d,g)):a[g]=d}return b}function jd(a){return!!(2&a)&&!!(4&a)||!!(256&a)}function kd(a){return Ub(a,!0)}var L=function(a,b){a=hd(a,b,kd);return a==null?yb():a};function ld(a,b,c,d){ed(a);var e=a.j;id(e,e[E]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a}var md=function(a,b,c){var d=a[E]|0,e=d&128?Xb:void 0,g=gd(a,c,e);if(g!=null&&g[Lb]===Sb){if(!G(g))return dd(g),g.j;var f=g.j}else Array.isArray(g)&&(f=g);if(f){var h=f[E]|0;h&2&&(f=$c(f,h))}f=Xc(f,b,!0);f!==g&&id(a,d,c,f,e);return f};function nd(a,b,c,d){var e=!1;d=gd(a,d,void 0,function(g){var f=Jc(g,c,b);e=f!==g&&f!=null;return f});if(d!=null)return e&&!G(d)&&fd(a,b),d}var M=function(a,b,c){var d=a.j,e=d[E]|0;b=nd(d,e,b,c);if(b==null)return b;e=d[E]|0;if(!G(a,e)){var g=cd(b);g!==b&&(dd(a)&&(d=a.j,e=d[E]|0),b=g,e=id(d,e,c,b),fd(d,e))}return b},od=function(a,b,c){c==null&&(c=void 0);var d=c;ed(a);var e=a.j;id(e,e[E]|0,b,d);c&&!G(c)&&fd(a.j);return a};function pd(a,b){return a=(2&b?a|2:a&-3)&-273}var N=function(a,b){var c=c===void 0?0:c;a=Ac(hd(a,b));return a!=null?a:c},O=function(a,b){var c=c===void 0?"":c;a=Ic(hd(a,b));return a!=null?a:c},P=function(a,b){var c=c===void 0?0:c;a=hd(a,b);a=a==null?a:vc(a)?a|0:void 0;return a!=null?a:c},qd=function(a,b,c){if(c!=null){if(typeof c!=="number")throw Cb("uint32");if(!vc(c))throw Cb("uint32");c>>>=0}return ld(a,b,c,0)},rd=function(a,b,c){c=c==null?c:Bc(c);return ld(a,b,c,"0")},sd=function(a,b,c){if(c!=null&&typeof c!=="string")throw Error();return ld(a,b,c,"")},Q=function(a,b,c){return ld(a,b,Ub(c,!1),yb())},td=function(a,b,c){if(c!=null){if(!vc(c))throw Cb("enum");c|=0}return ld(a,b,c,0)};function ud(a,b){return Error("Invalid wire type: "+a+" (at position "+b+")")}function vd(){return Error("Failed to read varint, encoding is invalid.")}function wd(a,b){return Error("Tried to read past the end of the data "+b+" > "+a)};var xd=function(a,b,c){this.g=a;if(c&&!b)throw Error();this.h=b};function yd(a,b){if(typeof a==="string")return new xd(qb(a),b);if(Array.isArray(a))return new xd(new Uint8Array(a),b);if(a.constructor===Uint8Array)return new xd(a,!1);if(a.constructor===ArrayBuffer)return a=new Uint8Array(a),new xd(a,!1);if(a.constructor===tb)return b=zb(a)||new Uint8Array(0),new xd(b,!0,a);if(a instanceof Uint8Array)return a=a.constructor===Uint8Array?a:new Uint8Array(a.buffer,a.byteOffset,a.byteLength),new xd(a,!1);throw Error();};var zd=function(a,b,c,d){this.i=null;this.s=!1;this.g=this.h=this.l=0;this.init(a,b,c,d)};zd.prototype.init=function(a,b,c,d){var e=d===void 0?{}:d;d=e.W===void 0?!1:e.W;e=e.aa===void 0?!1:e.aa;this.W=d;this.aa=e;a&&(a=yd(a,this.aa),this.i=a.g,this.s=a.h,this.l=b||0,this.h=c!==void 0?this.l+c:this.i.length,this.g=this.l)};zd.prototype.clear=function(){this.i=null;this.s=!1;this.g=this.h=this.l=0;this.W=!1};var Ad=function(a,b){a.g=b;if(b>a.h)throw wd(a.h,b);},Bd=function(a){var b=a.i,c=a.g,d=b[c++],e=d&127;if(d&128&&(d=b[c++],e|=(d&127)<<7,d&128&&(d=b[c++],e|=(d&127)<<14,d&128&&(d=b[c++],e|=(d&127)<<21,d&128&&(d=b[c++],e|=d<<28,d&128&&b[c++]&128&&b[c++]&128&&b[c++]&128&&b[c++]&128&&b[c++]&128)))))throw vd();Ad(a,c);return e},Cd=function(a,b){if(b<0)throw Error("Tried to read a negative byte length: "+b);var c=a.g,d=c+b;if(d>a.h)throw wd(b,a.h-c);a.g=d;return c},Dd=function(a,b){if(b==0)return yb();var c=Cd(a,b);a.W&&a.s?c=a.i.subarray(c,c+b):(a=a.i,b=c+b,c=c===b?new Uint8Array(0):kc?a.slice(c,b):new Uint8Array(a.subarray(c,b)));return c.length==0?yb():new tb(c,rb)},Ed=[];var Gd=function(a,b,c,d){if(Ed.length){var e=Ed.pop();e.init(a,b,c,d);a=e}else a=new zd(a,b,c,d);this.g=a;this.i=this.g.g;this.h=this.l=-1;Fd(this,d)},Fd=function(a,b){b=b===void 0?{}:b;a.ea=b.ea===void 0?!1:b.ea},Id=function(a,b,c,d){if(Hd.length){var e=Hd.pop();Fd(e,d);e.g.init(a,b,c,d);return e}return new Gd(a,b,c,d)},Jd=function(a){a.g.clear();a.l=-1;a.h=-1;Hd.length<100&&Hd.push(a)},Kd=function(a){var b=a.g;if(b.g==b.h)return!1;a.i=a.g.g;var c=Bd(a.g)>>>0;b=c>>>3;c&=7;if(!(c>=0&&c<=5))throw ud(c,a.i);if(b<1)throw Error("Invalid field number: "+b+" (at position "+a.i+")");a.l=b;a.h=c;return!0},Ld=function(a){switch(a.h){case 0:if(a.h!=0)Ld(a);else a:{a=a.g;for(var b=a.g,c=b+10,d=a.i;b<c;)if((d[b++]&128)===0){Ad(a,b);break a}throw vd();}break;case 1:a=a.g;Ad(a,a.g+8);break;case 2:a.h!=2?Ld(a):(b=Bd(a.g)>>>0,a=a.g,Ad(a,a.g+b));break;case 5:a=a.g;Ad(a,a.g+4);break;case 3:b=a.l;do{if(!Kd(a))throw Error("Unmatched start-group tag: stream EOF");if(a.h==4){if(a.l!=b)throw Error("Unmatched end-group tag");break}Ld(a)}while(1);break;default:throw ud(a.h,a.i);}},Md=function(a,b,c){var d=a.g.h,e=Bd(a.g)>>>0,g=a.g.g+e,f=g-d;f<=0&&(a.g.h=g,c(b,a,void 0,void 0,void 0),f=g-a.g.g);if(f)throw Error("Message parsing ended unexpectedly. Expected to read "+(e+" bytes, instead read "+(e-f)+" bytes, either the data ended unexpectedly or the message misreported its own length"));a.g.g=g;a.g.h=d},Hd=[];var Nd=function(a,b){this.h=a>>>0;this.g=b>>>0},Pd=function(a){if(!a)return Od||(Od=new Nd(0,0));if(!/^-?\d+$/.test(a))return null;sc(a);return new Nd(H,I)},Od;var Qd=function(){this.g=[]};Qd.prototype.length=function(){return this.g.length};Qd.prototype.end=function(){var a=this.g;this.g=[];return a};var Rd=function(a,b,c){for(;c>0||b>127;)a.g.push(b&127|128),b=(b>>>7|c<<25)>>>0,c>>>=7;a.g.push(b)},Sd=function(a,b){for(;b>127;)a.g.push(b&127|128),b>>>=7;a.g.push(b)};var Td=function(){this.i=[];this.h=0;this.g=new Qd},Ud=function(a,b){b.length!==0&&(a.i.push(b),a.h+=b.length)},Vd=function(a,b){Ud(a,a.g.end());Ud(a,b)},Wd=function(a,b){Sd(a.g,b*8+2);b=a.g.end();Ud(a,b);b.push(a.h);return b},Xd=function(a,b){var c=b.pop();for(c=a.h+a.g.length()-c;c>127;)b.push(c&127|128),c>>>=7,a.h++;b.push(c);a.h++},Yd=function(a){Ud(a,a.g.end());for(var b=new Uint8Array(a.h),c=a.i,d=c.length,e=0,g=0;g<d;g++){var f=c[g];b.set(f,e);e+=f.length}a.i=[b];return b},Zd=function(a,b,c,d){c!=null&&(b=Wd(a,b),d(c,a),Xd(a,b))},ae=function(a,b,c){var d=$d;if(c!=null)for(var e=0;e<c.length;e++){var g=Wd(a,b);d(c[e],a);Xd(a,g)}};function be(){var a=function(){throw Error();};w(Object,"setPrototypeOf").call(Object,a,a.prototype);return a}var ce=be(),de=be(),ee=be(),fe=be(),ge=be(),he=be();var R=function(a,b,c){this.j=J(a,b,c)};R.prototype.toJSON=function(){return Tc(this)};R.prototype[Lb]=Sb;R.prototype.toString=function(){return this.j.toString()};var ie=function(a,b,c){this.g=a;this.h=b;a=Ja(ce);this.i=!!a&&c===a||!1};function je(a){var b=ke;var c=c===void 0?ce:c;return new ie(a,b,c)}function ke(a,b,c,d,e){b=b instanceof R?b.j:Array.isArray(b)?Xc(b,d,!1):void 0;Zd(a,c,b,e)}var le=je(function(a,b,c,d,e){if(a.h!==2)return!1;Md(a,md(b,d,c),e);return!0}),me=je(function(a,b,c,d,e){if(a.h!==2)return!1;Md(a,md(b,d,c),e);return!0}),ne=(0,q.Symbol)(),oe=(0,q.Symbol)(),pe=(0,q.Symbol)(),qe=(0,q.Symbol)(),re=(0,q.Symbol)(),se,te;function ue(a,b,c,d){var e=d[a];if(e)return e;e={};e.va=d;e.T=Wc(d[0]);var g=d[1],f=1;g&&g.constructor===Object&&(e.ha=g,g=d[++f],typeof g==="function"&&(e.ka=!0,se!=null||(se=g),te!=null||(te=d[f+1]),g=d[f+=2]));for(var h={};g&&Array.isArray(g)&&g.length&&typeof g[0]==="number"&&g[0]>0;){for(var k=0;k<g.length;k++)h[g[k]]=g;g=d[++f]}for(k=1;g!==void 0;){typeof g==="number"&&(k+=g,g=d[++f]);var l=void 0;if(g instanceof ie)var n=g;else n=le,f--;g=void 0;if((g=n)==null?0:g.i){g=d[++f];l=d;var p=f;typeof g==="function"&&(g=g(),l[p]=g);l=g}g=d[++f];p=k+1;typeof g==="number"&&g<0&&(p-=g,g=d[++f]);for(;k<p;k++){var r=h[k];l?c(e,k,n,l,r):b(e,k,n,r)}}return d[a]=e}function ve(a){return Array.isArray(a)?a[0]instanceof ie?a:[me,a]:[a,void 0]};function we(a,b,c,d){var e=c.g;a[b]=d?function(g,f,h){return e(g,f,h,d)}:e}function xe(a,b,c,d,e){var g=c.g,f,h;a[b]=function(k,l,n){return g(k,l,n,h||(h=ue(oe,we,xe,d).T),f||(f=ye(d)),e)}}function ye(a){var b=a[pe];if(b!=null)return b;var c=ue(oe,we,xe,a);b=c.ka?function(d,e){return se(d,e,c)}:function(d,e){for(;Kd(e)&&e.h!=4;){var g=e.l,f=c[g];if(f==null){var h=c.ha;h&&(h=h[g])&&(h=ze(h),h!=null&&(f=c[g]=h))}if(f==null||!f(e,d,g)){h=e;f=h.i;Ld(h);if(h.ea)var k=void 0;else{var l=h.g.g-f;h.g.g=f;k=Dd(h.g,l)}l=h=f=void 0;var n=d;k&&((f=(h=(l=n[Hb])!=null?l:n[Hb]=new Mc)[g])!=null?f:h[g]=[]).push(k)}}if(d=Lc(d))d.g=c.va[re];return!0};a[pe]=b;a[re]=Ae.bind(a);return b}function Ae(a,b,c,d){var e=this[oe],g=this[pe],f=Xc(void 0,e.T,!1),h=Lc(a);if(h){var k=!1,l=e.ha;if(l){e=function(u,v,x){if(x.length!==0)if(l[v])for(u=A(x),v=u.next();!v.done;v=u.next()){v=Id(v.value);try{k=!0,g(f,v)}finally{Jd(v)}}else d==null||d(a,v,x)};if(b==null)Nc(h,e);else if(h!=null){var n=h[b];n&&e(h,b,n)}if(k){var p=a[E]|0;if(p&2&&p&2048&&(c==null||!c.La))throw Error();var r=p&128?Xb:void 0,t=function(u,v){if(gd(a,u,r)!=null)switch(c==null?void 0:c.Ka){case 1:return;default:throw Error();}v!=null&&(p=id(a,p,u,v,r));delete h[u]};b==null?Wb(f,f[E]|0,function(u,v){t(u,v)}):t(b,gd(f,b,r))}}}}function ze(a){a=ve(a);var b=a[0].g;if(a=a[1]){var c=ye(a),d=ue(oe,we,xe,a).T;return function(e,g,f){return b(e,g,f,d,c)}}return b};function Be(a,b,c){a[b]=c.h}function Ce(a,b,c,d){var e,g,f=c.h;a[b]=function(h,k,l){return f(h,k,l,g||(g=ue(ne,Be,Ce,d).T),e||(e=De(d)))}}function De(a){var b=a[qe];if(!b){var c=ue(ne,Be,Ce,a);b=function(d,e){return Ee(d,e,c)};a[qe]=b}return b}function Ee(a,b,c){Wb(a,a[E]|0,function(d,e){if(e!=null){var g=Fe(c,d);g?g(b,e,d):d<500||Db(Jb,3)}});(a=Lc(a))&&Nc(a,function(d,e,g){Ud(b,b.g.end());for(d=0;d<g.length;d++)Ud(b,zb(g[d])||new Uint8Array(0))})}function Fe(a,b){var c=a[b];if(c)return c;if(c=a.ha)if(c=c[b]){c=ve(c);var d=c[0].h;if(c=c[1]){var e=De(c),g=ue(ne,Be,Ce,c).T;c=a.ka?te(g,e):function(f,h,k){return d(f,h,k,g,e)}}else c=d;return a[b]=c}};var Ge=function(a,b){var c=new Td;Ee(a.j,c,ue(ne,Be,Ce,b));return Yd(c)};function He(a,b,c){return new ie(a,b,c)}function Ie(a,b,c){id(a,a[E]|0,b,c,(a[E]|0)&128?Xb:void 0)}var Je=He(function(a,b,c){if(a.h!==0)return!1;a=a.g;var d=0,e=0,g=0,f=a.i,h=a.g;do{var k=f[h++];d|=(k&127)<<g;g+=7}while(g<32&&k&128);g>32&&(e|=(k&127)>>4);for(g=3;g<32&&k&128;g+=7)k=f[h++],e|=(k&127)<<g;Ad(a,h);if(k<128)a=oc(d>>>0,e>>>0);else throw vd();Ie(b,c,a===0?void 0:a);return!0},function(a,b,c){b=Hc(b);if(b!=null){switch(typeof b){case "string":Pd(b)}if(b!=null)switch(Sd(a.g,c*8),typeof b){case "number":a=a.g;mc(b);Rd(a,H,I);break;case "bigint":c=BigInt.asUintN(64,b);c=new Nd(Number(c&BigInt(4294967295)),Number(c>>BigInt(32)));Rd(a.g,c.h,c.g);break;default:c=Pd(b),Rd(a.g,c.h,c.g)}}},fe),Ke=He(function(a,b,c){if(a.h!==2)return!1;var d,e=Bd(a.g)>>>0;a=a.g;var g=Cd(a,e);a=a.i;if(Oa){var f=a;(d=Na)||(d=Na=new TextDecoder("utf-8",{fatal:!0}));e=g+e;f=g===0&&e===f.length?f:f.subarray(g,e);try{var h=d.decode(f)}catch(p){if(Ma===void 0){try{d.decode(new Uint8Array([128]))}catch(r){}try{d.decode(new Uint8Array([97])),Ma=!0}catch(r){Ma=!1}}!Ma&&(Na=void 0);throw p;}}else{h=g;e=h+e;g=[];for(var k=null,l,n;h<e;)l=a[h++],l<128?g.push(l):l<224?h>=e?Ka():(n=a[h++],l<194||(n&192)!==128?(h--,Ka()):g.push((l&31)<<6|n&63)):l<240?h>=e-1?Ka():(n=a[h++],(n&192)!==128||l===224&&n<160||l===237&&n>=160||((d=a[h++])&192)!==128?(h--,Ka()):g.push((l&15)<<12|(n&63)<<6|d&63)):l<=244?h>=e-2?Ka():(n=a[h++],(n&192)!==128||(l<<28)+(n-144)>>30!==0||((d=a[h++])&192)!==128||((f=a[h++])&192)!==128?(h--,Ka()):(l=(l&7)<<18|(n&63)<<12|(d&63)<<6|f&63,l-=65536,g.push((l>>10&1023)+55296,(l&1023)+56320))):Ka(),g.length>=8192&&(k=La(k,g),g.length=0);h=La(k,g)}d=h;Ie(b,c,d===""?void 0:d);return!0},function(a,b,c){b=Ic(b);if(b!=null){var d=!1;d=d===void 0?!1:d;if(Ra){if(d&&(Qa?!b.isWellFormed():/(?:[^\uD800-\uDBFF]|^)[\uDC00-\uDFFF]|[\uD800-\uDBFF](?![\uDC00-\uDFFF])/.test(b)))throw Error("Found an unpaired surrogate");b=(Pa||(Pa=new TextEncoder)).encode(b)}else{for(var e=0,g=new Uint8Array(3*b.length),f=0;f<b.length;f++){var h=b.charCodeAt(f);if(h<128)g[e++]=h;else{if(h<2048)g[e++]=h>>6|192;else{if(h>=55296&&h<=57343){if(h<=56319&&f<b.length){var k=b.charCodeAt(++f);if(k>=56320&&k<=57343){h=(h-55296)*1024+k-56320+65536;g[e++]=h>>18|240;g[e++]=h>>12&63|128;g[e++]=h>>6&63|128;g[e++]=h&63|128;continue}else f--}if(d)throw Error("Found an unpaired surrogate");h=65533}g[e++]=h>>12|224;g[e++]=h>>6&63|128}g[e++]=h&63|128}}b=e===g.length?g:g.subarray(0,e)}Sd(a.g,c*8+2);Sd(a.g,b.length);Vd(a,b)}},de),Le=He(function(a,b,c){if(a.h!==2)return!1;var d=Bd(a.g)>>>0;a=Dd(a.g,d);Ie(b,c,a===yb()?void 0:a);return!0},function(a,b,c){b=b==null||typeof b=="string"||b instanceof tb?b:void 0;b!=null&&(b=yd(b,!0).g,Sd(a.g,c*8+2),Sd(a.g,b.length),Vd(a,b))},ge),S=He(function(a,b,c){if(a.h!==0)return!1;a=Bd(a.g)>>>0;Ie(b,c,a===0?void 0:a);return!0},function(a,b,c){b=Ac(b);b!=null&&b!=null&&(Sd(a.g,c*8),Sd(a.g,b))},ee),Me=He(function(a,b,c){if(a.h!==0)return!1;a=Bd(a.g);Ie(b,c,a===0?void 0:a);return!0},function(a,b,c){b=zc(b);if(b!=null)if(b=parseInt(b,10),Sd(a.g,c*8),a=a.g,c=b,c>=0)Sd(a,c);else{for(b=0;b<9;b++)a.g.push(c&127|128),c>>=7;a.g.push(1)}},he);function Ne(a,b){return function(c,d){var e={aa:!0};d&&w(Object,"assign").call(Object,e,d);c=Id(c,void 0,void 0,e);try{var g=new a,f=g.j;ye(b)(f,c);var h=g}finally{Jd(c)}return h}}function Oe(a){return function(b){return Ge(b,a)}};var Pe=function(a){this.j=J(a)};z(Pe,R);var Qe=Oe([0,Me,Ke]);var Re=function(a,b){var c=c===void 0?{}:c;this.error=a;this.meta=c;this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"};function Se(a){var b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);for(var d;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(e){b=c;break a}b=void 0}return b};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var Te;var Ue=function(a){this.g=a};Ue.prototype.toString=function(){return this.g+""};function Ve(a){var b;Te===void 0&&(Te=null);a=(b=Te)?b.createScriptURL(a):a;return new Ue(a)};var We=function(){return"border".replace(/\-([a-z])/g,function(a,b){return b.toUpperCase()})},Xe=function(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};function Ye(a){var b=Ia.apply(1,arguments);if(b.length===0)return Ve(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return Ve(c)};var Ze=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},$e=function(a){a=a===void 0?document:a;return a.createElement("img")};var af={};function bf(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=$e(a.document);if(c){var g=function(){if(c){var f=a.google_image_requests,h=Array.prototype.indexOf.call(f,e,void 0);h>=0&&Array.prototype.splice.call(f,h,1)}typeof e.removeEventListener==="function"&&e.removeEventListener("load",g,!1);typeof e.removeEventListener==="function"&&e.removeEventListener("error",g,!1)};typeof e.addEventListener==="function"&&e.addEventListener("load",g,!1);typeof e.addEventListener==="function"&&e.addEventListener("error",g,!1)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}function cf(a){var b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=rcs_internal";Ze(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});df(c,b)}function df(a,b){var c=window;b=b===void 0?!1:b;var d=d===void 0?!1:d;c.fetch?(b={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"},d&&(b.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?b.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:b.headers={"Attribution-Reporting-Eligible":"event-source"}),c.fetch(a,b)):bf(c,a,b===void 0?!1:b,d===void 0?!1:d)};function ef(a,b){var c=new Td;try{var d=a.filter(function(g){return g.la}).map(ff);ae(c,1,d);Zd(c,2,Qe(b),$d);var e=a.filter(function(g){return!g.la}).map(ff);ae(c,3,e)}catch(g){gf(g,b)}return Yd(c)}function gf(a,b){try{cf({m:Se(a instanceof Error?a:Error(String(a))),b:P(b,1)||null,v:O(b,2)||null})}catch(c){}}function ff(a){var b=new Td;Zd(b,a.Ga,a.Da,$d);return Yd(b)}function $d(a,b){Vd(b,a.subarray(0,a.length))}var hf=function(a,b){var c=new Pe;a=td(c,1,a);b=sd(a,2,b);a=b.j;c=a[E]|0;this.g=G(b,c)?b:ad(b,a,c)?bd(b,a):new b.constructor($c(a,c,!0))};var jf=function(a){this.j=J(a)};z(jf,R);var kf=function(a,b){return rd(a,1,b)},lf=function(a,b){return rd(a,2,b)},mf=function(a,b){return td(a,3,b)};var nf=Oe([0,Je,-1,Me]);var of=function(){hf.apply(this,arguments)};z(of,hf);var pf=function(){of.apply(this,arguments)};z(pf,of);pf.prototype.l=function(){this.s.apply(this,qa(Ia.apply(0,arguments).map(function(a){return{la:!0,Ga:16,Da:nf(a)}})))};var qf=function(a,b,c,d){pf.call(this,a,b);this.i=c;this.h=d};z(qf,pf);qf.prototype.s=function(){var a=Ia.apply(0,arguments);try{var b=encodeURIComponent(ib(ef(a,this.g),3));this.h(this.i+"?e=4&d="+b)}catch(c){gf(c,this.g)}};var rf=function(a,b){this.data=a;this.g=b};var sf=function(a){this.g=a},uf=function(a,b,c){c=c===void 0?[]:c;var d=tf();a.g.postMessage(b,[d.port2].concat(c))},wf=function(a,b){vf(a,b);return new sf(a)},tf=function(a){var b=new MessageChannel;vf(b.port1,a);return b},vf=function(a,b){b&&(a.onmessage=function(c){var d=c.data;c=wf(c.ports[0]);b(new rf(d,c))})};var xf=function(a){var b=a.destination;var c=a.origin;var d=a.Fa===void 0?void 0:a.Fa;var e=a.ia===void 0?"ZNWN1d":a.ia;a=a.onMessage===void 0?void 0:a.onMessage;if(c==="*")throw Error("Sending to wildcard origin not allowed.");var g=tf(a),f={};d=d?(f.n=e,f.t=d,f):e;b.postMessage(d,c,[g.port2]);wf(g.port1,a)};var yf=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)"),zf=function(a,b){this.g=a;this.h=b},Af=function(a,b){this.url=a;this.g=!!b;this.depth=null};var Bf=function(){this.i="&";this.h={};this.l=0;this.g=[]},Cf=function(a,b){var c={};c[a]=b;return[c]},Ef=function(a,b,c,d,e){var g=[];Ze(a,function(f,h){(f=Df(f,b,c,d,e))&&g.push(h+"="+f)});return g.join(b)},Df=function(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){for(var g=[],f=0;f<a.length;f++)g.push(Df(a[f],b,c,d+1,e));return g.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(Ef(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))},Kf=function(a,b){var c="https://pagead2.googlesyndication.com"+b,d=Ff(a)-b.length;if(d<0)return"";a.g.sort(function(n,p){return n-p});b=null;for(var e="",g=0;g<a.g.length;g++)for(var f=a.g[g],h=a.h[f],k=0;k<h.length;k++){if(!d){b=b==null?f:b;break}var l=Ef(h[k],a.i,",$");if(l){l=e+l;if(d>=l.length){d-=l.length;c+=l;e=a.i;break}b=b==null?f:b}}a="";b!=null&&(a=e+"trn="+b);return c+a},Ff=function(a){var b=1,c;for(c in a.h)c.length>b&&(b=c.length);return 3997-b-a.i.length-1};var Nf=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$");var Of=function(){this.h=this.g},Pf=function(a,b){try{var c=b()}catch(g){b=!1;try{b=a.h(987,new Re(g,{message:Se(g)}),void 0,void 0)}catch(f){a.g(217,f)}if(b){var d,e;(d=window.console)==null||(e=d.error)==null||e.call(d,g)}else throw g;}return c},Qf=function(a,b){return function(){var c=Ia.apply(0,arguments);return Pf(a,function(){return b.apply(void 0,c)})}};Of.prototype.g=function(a,b,c,d,e){e=e||"topics_frame_error";var g=void 0;try{var f=new Bf;f.g.push(1);f.h[1]=Cf("context",a);b.error&&b.meta&&b.id||(b=new Re(b,{message:Se(b)}));if(b.msg){var h=b.msg.substring(0,512);f.g.push(2);f.h[2]=Cf("msg",h)}var k=b.meta||{};if(d)try{d(k)}catch(vb){}d=[k];f.g.push(3);f.h[3]=d;var l;if(!(l=x)){h=D;d=[];k=null;do{var n=h;try{var p;if(p=!!n&&n.location.href!=null)b:{try{cb(n.foo);p=!0;break b}catch(vb){}p=!1}var r=p}catch(vb){r=!1}if(r){var t=n.location.href;k=n.document&&n.document.referrer||null}else t=k,k=null;d.push(new Af(t||""));try{h=n.parent}catch(vb){h=null}}while(h&&n!==h);t=0;for(var u=d.length-1;t<=u;++t)d[t].depth=u-t;n=D;if(n.location&&n.location.ancestorOrigins&&n.location.ancestorOrigins.length===d.length-1)for(u=1;u<d.length;++u){var v=d[u];v.url||(v.url=n.location.ancestorOrigins[u-1]||"",v.g=!0)}l=d}var x=l;var K=new Af(D.location.href,!1);l=null;var ra=x.length-1;for(n=ra;n>=0;--n){var ha=x[n];!l&&yf.test(ha.url)&&(l=ha);if(ha.url&&!ha.g){K=ha;break}}ha=null;var wb=x.length&&x[ra].url;K.depth!==0&&wb&&(ha=x[ra]);g=new zf(K,ha);if(g.h){var xb=g.h.url||"";f.g.push(4);f.h[4]=Cf("top",xb)}var Ea={url:g.g.url||""};if(g.g.url){var sa=g.g.url.match(Nf),Gf=sa[1],Hf=sa[3],If=sa[4];x="";Gf&&(x+=Gf+":");Hf&&(x+="//",x+=Hf,If&&(x+=":"+If));var Jf=x}else Jf="";Ea=[Ea,{url:Jf}];f.g.push(5);f.h[5]=Ea;Rf(e,f,c)}catch(vb){try{var Lf,Mf;Rf(e,{context:"ecmserr",rctx:a,msg:Se(vb),url:(Mf=(Lf=g)==null?void 0:Lf.g.url)!=null?Mf:""},c)}catch(Dj){}}return!1};var Sf=function(a,b,c){c.catch(function(d){d=d?d:"unknown rejection";a.g(b,d instanceof Error?d:Error(d),void 0,void 0)})};var Rf=function(a,b,c){if(Math.random()<(c||.01))try{if(b instanceof Bf)var d=b;else d=new Bf,Ze(b,function(g,f){var h=d,k=h.l++;g=Cf(f,g);h.g.push(k);h.h[k]=g});var e=Kf(d,"/pagead/gen_204?id="+a+"&");e&&bf(D,e,!1,!1)}catch(g){}};var Tf=ua(["https://securepubads.g.doubleclick.net/pagead/js/car.js"]),Uf=ua(["https://securepubads.g.doubleclick.net/pagead/js/cocar.js"]),Vf=ua(["https://ep3.adtrafficquality.google/ivt/worklet/caw.js"]),Wf=Ye(Tf);Ye(Uf);Ye(Vf);function Xf(a,b){for(var c=[],d=0;d<8;++d){var e=new qf(7,"","https://pagead2.googlesyndication.com/pagead/ping",function(f){c.push({url:f})}),g=mf(lf(kf(new jf,a),d),b);e.l(g)}return c}function Yf(a,b,c){var d=window,e=void 0;e=e===void 0?Wf:e;var g,f;return C(function(h){switch(h.h){case 1:g=d;if(g.sharedStorage){var k=k===void 0?D:k;k=k.performance;k=g.sharedStorage.set("ps_cct",String(k&&k.now&&k.timing?Math.floor(k.now()+k.timing.navigationStart):Date.now()),{ignoreIfPresent:!0});h=B(h,k,2)}else h=h.return();return h;case 2:return B(h,g.sharedStorage.worklet.addModule(e.toString()),3);case 3:return B(h,g.sharedStorage.selectURL("ps_caus",Xf(a,b),c?{resolveToConfig:!0,savedQuery:"ps_cac"}:{resolveToConfig:!0}),4);case 4:f=h.g;k=g.document.body;var l=document.createElement("fencedframe");l.id="ps_caff";l.name="ps_caff";l.mode="opaque-ads";l.config=f;var n=af.border;if(!n){var p=We();n=p;l.style[p]===void 0&&(p=(eb?"Webkit":db?"Moz":null)+Xe(p),l.style[p]!==void 0&&(n=p));af.border=n}n&&(l.style[n]="0");k.appendChild(l);h.h=0}})};var Zf=function(a){this.j=J(a)};z(Zf,R);Zf.prototype.o=function(){return O(this,2)};var $f=function(a){this.j=J(a)};z($f,R);/*

 Copyright 2020 Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var ag=function(){};var T=function(a){a=Error.call(this,a);this.message=a.message;"stack"in a&&(this.stack=a.stack);w(Object,"setPrototypeOf").call(Object,this,T.prototype)};z(T,Error);T.prototype.name="SecurityException";var bg=function(a){this.j=J(a)};z(bg,R);var cg=function(a){this.j=J(a)};z(cg,R);cg.prototype.o=function(){return N(this,1)};var dg=function(a){this.j=J(a)};z(dg,R);var eg=function(a){this.j=J(a)};z(eg,R);eg.prototype.o=function(){return N(this,1)};var fg=function(a){this.j=J(a)};z(fg,R);fg.prototype.o=function(){return N(this,1)};var gg=[0,S];var hg=[0,Me,S];var ig=Ne(fg,[0,S,[0,S,gg,Le],[0,S,hg,Le]]);var jg=function(a){this.j=J(a)};z(jg,R);var kg=function(a){this.j=J(a)};z(kg,R);kg.prototype.o=function(){return N(this,3)};var lg=function(a){this.j=J(a)};z(lg,R);var mg=Ne(lg,[0,[0,gg,S],[0,hg,S,-1]]);var ng=function(a){this.j=J(a)};z(ng,R);ng.prototype.o=function(){return N(this,1)};var og=Ne(ng,[0,S,1,Le]);var pg=function(a){this.j=J(a)};z(pg,R);pg.prototype.o=function(){return N(this,3)};var qg=Ne(pg,[0,1,S,-1]);var rg=function(a){this.j=J(a)};z(rg,R);rg.prototype.getValue=function(){return L(this,2)};var sg=function(a){this.j=J(a)};z(sg,R);var tg=[0,1,[0,Ke,Le,Me]];var ug=function(a){this.j=J(a)};z(ug,R);var vg=[0,Me,-1,8,Le];var wg=function(a){this.j=J(a)};z(wg,R);var xg=[0,vg,tg,Me];var yg=function(a){this.j=J(a)};z(yg,R);var zg=Ne(yg,[0,xg]);var Ag=function(a){this.j=J(a)};z(Ag,R);Ag.prototype.o=function(){return N(this,1)};var Bg=[0,S,xg,Le,-1];var Cg=function(a){this.j=J(a)};z(Cg,R);Cg.prototype.o=function(){return N(this,1)};var Dg=Ne(Cg,[0,S,Bg,Le]);var Eg=Ne(Ag,Bg);var Fg=function(a){this.j=J(a)};z(Fg,R);Fg.prototype.L=function(){return P(this,3)};var Gg=[0,Me,-2];var Hg=function(a){this.j=J(a)};z(Hg,R);var Ig=[0,Gg];Hg.prototype.h=function(a){return function(){return Ge(this,a)}}(Ig);var Jg=Ne(Hg,Ig);var Kg=function(a){this.j=J(a)};z(Kg,R);Kg.prototype.o=function(){return N(this,1)};var Lg=[0,S,Gg,Le];var Mg=function(a){this.j=J(a)};z(Mg,R);Mg.prototype.o=function(){return N(this,1)};var Ng=Ne(Mg,[0,S,Lg,Le]);var Og=Ne(Kg,Lg);var U=function(a){this.j=J(a)};z(U,R);U.prototype.getValue=function(){return L(this,2)};var Pg=function(a){this.j=J(a)};z(Pg,R);var Qg=function(a){this.j=J(a)};z(Qg,R);var Rg=function(a){var b=a.j,c=b,d=b[E]|0;b=void 0===Vb?2:4;var e=G(a,d),g=e?1:b;b=g===3;var f=!e;(g===2||f)&&dd(a)&&(c=a.j,d=c[E]|0);a=gd(c,2);e=Array.isArray(a)?a:Ob;var h=e===Ob?7:e[E]|0;a=h;2&d&&(a|=2);var k=a|1;if(a=!(4&k)){var l=e,n=d,p=!!(2&k);p&&(n|=2);for(var r=!p,t=!0,u=0,v=0;u<l.length;u++){var x=Jc(l[u],Pg,n);if(x instanceof Pg){if(!p){var K=G(x);r&&(r=!K);t&&(t=K)}l[v++]=x}}v<u&&(l.length=v);k|=4;k=t?k&-4097:k|4096;k=r?k|8:k&-9}k!==h&&(F(e,k),2&k&&Object.freeze(e));if(f&&!(8&k||!e.length&&(g===1||(g!==4?0:2&k||!(16&k)&&32&d)))){jd(k)&&(e=Array.prototype.slice.call(e),k=pd(k,d),d=id(c,d,2,e));f=e;h=k;for(l=0;l<f.length;l++)k=f[l],n=cd(k),k!==n&&(f[l]=n);h|=8;k=h=f.length?h|4096:h&-4097;F(e,k)}h=f=k;g===1||(g!==4?0:2&f||!(16&f)&&32&d)?jd(f)||(f|=!e.length||a&&!(4096&f)||32&d&&!(4096&f||16&f)?2:256,f!==h&&F(e,f),Object.freeze(e)):(g===2&&jd(f)&&(e=Array.prototype.slice.call(e),h=0,f=pd(f,d),d=id(c,d,2,e)),jd(f)||(b||(f|=16),f!==h&&F(e,f)));2&f||!(4096&f||16&f)||fd(c,d);return e},Sg=function(a){return function(b){if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");Qb(b,32);b=new a(b)}return b}}(Qg);function V(a){return a==null?void 0:new Uint8Array(zb(a)||0)}function Tg(a){a==null?a=void 0:a=(a=zb(a))?a.length:0;return a};var W=function(a){a=Error.call(this,a);this.message=a.message;"stack"in a&&(this.stack=a.stack);w(Object,"setPrototypeOf").call(Object,this,W.prototype)};z(W,Error);W.prototype.name="InvalidArgumentsException";function X(){for(var a=0,b=0;b<arguments.length;b++)a+=arguments[b].length;a=new Uint8Array(a);for(var c=b=0;c<arguments.length;c++)a.set(arguments[c],b),b+=arguments[c].length;return a}function Ug(a){if(w(Number,"isNaN").call(Number,a)||a%1!==0)throw new W("cannot convert non-integer value");if(a<0)throw new W("cannot convert negative number");if(a>w(Number,"MAX_SAFE_INTEGER"))throw new W("cannot convert number larger than "+w(Number,"MAX_SAFE_INTEGER"));var b=a%4294967296;a/=4294967296;for(var c=new Uint8Array(8),d=7;d>=4;d--)c[d]=b&255,b>>>=8;for(b=3;b>=0;b--)c[b]=a&255,a>>>=8;return c}function Vg(a){for(var b="",c=0;c<a.length;c++){var d=a[c].toString(16);b+=d.length>1?d:"0"+d}return b}function Y(a){return Wg(q.globalThis.atob(a.replace(/-/g,"+").replace(/_/g,"/")))}function Xg(a){for(var b="",c=0;c<a.length;c+=1)b+=String.fromCharCode(a[c]);return q.globalThis.btoa(b).replace(/=/g,"").replace(/\+/g,"-").replace(/\//g,"_")}function Wg(a){for(var b=[],c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);b[c++]=e}return new Uint8Array(b)};function Yg(a){switch(a){case 1:return"P-256";case 2:return"P-384";case 3:return"P-521"}}function Zg(a){switch(a){case "P-256":return 1;case "P-384":return 2;case "P-521":return 3}throw new W("unknown curve: "+a);}function $g(a,b,c){a=ah(Zg(a));switch(b){case 1:b=c.x;var d=c.y;if(b===void 0)throw new W("x must be provided");if(d===void 0)throw new W("y must be provided");c=new Uint8Array(1+2*a);d=Y(d);b=Y(b);c.set(d,1+2*a-d.length);c.set(b,1+a-b.length);c[0]=4;return c;case 3:b=c.x;d=c.y;if(b===void 0)throw new W("x must be provided");if(d===void 0)throw new W("y must be provided");c=Y(b);b=Y(d);c.length>a&&(c=c.slice(c.length-a,c.length));b.length>a&&(b=b.slice(b.length-a,b.length));d=new Uint8Array(2*a);d.set(c,0);d.set(b,a);return d;case 2:b=c.x;d=c.y;if(b===void 0)throw new W("x must be provided");if(d===void 0)throw new W("y must be provided");c=Y(b);b=Y(d);c.length>a&&(c=c.slice(c.length-a,c.length));b.length>a&&(b=b.slice(b.length-a,b.length));d=new Uint8Array(1+a);d.set(c,1+a-c.length);d[0]=bh(BigInt("0x"+Vg(b)),0)?3:2;return d;default:throw new T("invalid format");}}function ch(a){switch(a){case 1:return BigInt("115792089210356248762697446949407573530086143415290314195533631308867097853951");case 2:return BigInt("39402006196394479212279040100143613805079739270465446667948293404245721771496870329047266088258938001861606973112319");case 3:return BigInt("6864797660130609714981900799081393217269435300143305409394463459185543183397656052122559640661454554977296311391480858037121987999716643812574028291115057151");default:throw new W("invalid curve");}}function dh(a){a=a.toString(16);a=a.length%2===0?a:"0"+a;if(a.length%2!=0)throw new W("Hex string length must be multiple of 2");for(var b=new Uint8Array(a.length/2),c=0;c<a.length;c+=2)b[c/2]=parseInt(a.substring(c,c+2),16);return b}function bh(a,b){return(a&BigInt(1)<<BigInt(b))!==BigInt(0)}function eh(a,b,c){var d=ah(Zg(a));switch(b){case 1:if(c.length!==1+2*d||c[0]!==4)throw new T("invalid point");return{kty:"EC",crv:a,x:Xg(new Uint8Array(c.subarray(1,1+d))),y:Xg(new Uint8Array(c.subarray(1+d,c.length))),ext:!0};case 3:if(c.length!==2*d)throw new T("invalid point");return{kty:"EC",crv:a,x:Xg(new Uint8Array(c.subarray(0,d))),y:Xg(new Uint8Array(c.subarray(d,c.length))),ext:!0};case 2:if(c.length!==1+d)throw new T("compressed point has wrong length");if(c[0]!==2&&c[0]!==3)throw new T("invalid format");b=c[0]===3;c=BigInt("0x"+Vg(c.subarray(1,c.length)));d=ch(Zg(a));if(c<BigInt(0)||c>=d)throw new T("x is out of range");d=ch(Zg(a));var e=d-BigInt(3);a:switch(Zg(a)){case 1:var g=BigInt("0x5ac635d8aa3a93e7b3ebbd55769886bc651d06b0cc53b0f63bce3c3e27d2604b");break a;case 2:g=BigInt("0xb3312fa7e23ee7e4988e056be3f82d19181d9c6efe8141120314088f5013875ac656398d8a2ed19d2a85c8edd3ec2aef");break a;case 3:g=BigInt("0x051953eb9618e1c9a1f929a21a0b68540eea2da725b99b315f3b8b489918ef109e156193951ec7e937b1652c0bd3bb1bf073573df883d2c34f1ef451fd46b503f00");break a;default:throw new W("invalid curve");}if(d<=BigInt(0))throw new W("p must be positive");e=((c*c+e)*c+g)%d%d;if(bh(d,0)&&bh(d,1)){var f=d+BigInt(1)>>BigInt(2);if(f===BigInt(0))g=BigInt(1);else{g=e;f=f.toString(2);for(var h=1;h<f.length;++h)g=g*g%d,f[h]==="1"&&(g=g*e%d)}if(g*g%d!==e)throw new T("could not find a modular square root");e=g}else throw new W("unsupported modulus value");b!==bh(e,0)&&(e=(d-e)%d);b=e;return{kty:"EC",crv:a,x:Xg(dh(c)),y:Xg(dh(b)),ext:!0};default:throw new T("invalid format");}}function ah(a){switch(a){case 1:return 32;case 2:return 48;case 3:return 66}}function fh(a,b){var c,d,e,g,f;return C(function(h){if(h.h==1){c=a.algorithm;d=c.namedCurve;if(!d)throw new W("namedCurve must be provided");e=w(Object,"assign").call(Object,{},{"public":b},a.algorithm);g=8*ah(Zg(d));return B(h,q.globalThis.crypto.subtle.deriveBits(e,a,g),2)}f=h.g;return h.return(new Uint8Array(f))})}function gh(a){var b,c;return C(function(d){if(d.h==1)return b={name:"ECDH",namedCurve:a},B(d,q.globalThis.crypto.subtle.generateKey(b,!0,["deriveKey","deriveBits"]),2);c=d.g;return d.return(c)})}function hh(a){var b;return C(function(c){if(c.h==1)return B(c,q.globalThis.crypto.subtle.exportKey("jwk",a),2);b=c.g;if(b.crv===void 0)throw new W("crv must be provided");var d=ah(Zg(b.crv));if(b.x===void 0)throw new W("x must be provided");if(b.y===void 0)throw new W("y must be provided");var e=Y(b.x);if(e.length!==d)throw new W("x-coordinate byte-length is invalid (got: "+e.length+", want: "+d+").");e=Y(b.y);if(e.length!==d)throw new W("y-coordinate byte-length is invalid (got: "+e.length+", want: "+d+").");return c.return(b)})}function ih(a){var b,c,d;return C(function(e){if(e.h==1){b=a;c=b.crv;if(!c)throw new W("crv must be provided");return B(e,q.globalThis.crypto.subtle.importKey("jwk",a,{name:"ECDH",namedCurve:c},!0,[]),2)}d=e.g;return e.return(d)})}function jh(a){var b,c,d;return C(function(e){if(e.h==1){b=a;c=b.crv;if(!c)throw new W("crv must be provided");return B(e,q.globalThis.crypto.subtle.importKey("jwk",a,{name:"ECDH",namedCurve:c},!0,["deriveKey","deriveBits"]),2)}d=e.g;return e.return(d)})};function kh(a,b){var c=a.length;if(c<b)return b=new Uint8Array(b-c),X(b,a);if(c>b){for(var d=0;d<c-b;d++)if(a[d]!=0)throw new T("Number needs more bytes to be represented.");return a.slice(c-b,c)}return a}function lh(a){switch(a){case 2:return 1;case 3:return 2;case 4:return 3;default:throw new T("Unknown curve type.");}}function mh(a){switch(a){case 1:return"SHA-1";case 3:return"SHA-256";case 4:return"SHA-512";default:throw new T("Unknown hash type.");}}function nh(a){switch(a){case 1:return 1;case 2:return 2;case 3:return 3;default:throw new T("Unknown point format.");}};var oh=new q.Map,ph=new q.Map,qh=new q.Map;function rh(a){var b;b===void 0&&(b=!0);if(!a)throw new T("Key manager cannot be null.");var c=a.A();if(oh.has(c)){if(!(oh.get(c)instanceof a.constructor))throw new T("Key manager for key type "+c+" has already been registered and cannot be overwritten.");if(!ph.get(c)&&b)throw new T("Key manager for key type "+c+" has already been registered with forbidden new key operation.");ph.set(c,b)}oh.set(c,a);ph.set(c,b)}function sh(a){var b=oh.get(a);if(!b)throw new T("Key manager for key type "+a+" has not been registered.");return b}function th(a,b,c){var d,e;return C(function(g){if(g.h==1){if(b instanceof U){if(c&&O(b,1)!=c)throw new T("Key type is "+c+", but it is expected to be "+O(b,1)+" or undefined.");c=O(b,1)}if(!c)throw new T("Key type has to be specified.");d=sh(c);return B(g,d.C(a,b),2)}e=g.g;if(!(e instanceof a))throw new TypeError("Unexpected type");return g.return(e)})}function uh(a){if(!a)throw new T("primitive wrapper cannot be null");var b=a.u();if(!b)throw new T("primitive wrapper cannot be undefined");if(qh.has(b)&&!(qh.get(b)instanceof a.constructor))throw new T("primitive wrapper for type "+b+" has already been registered and cannot be overwritten");qh.set(b,a)};function vh(a){if(!w(Number,"isInteger").call(Number,a)||a<0)throw new W("n must be a nonnegative integer");a=new Uint8Array(a);q.globalThis.crypto.getRandomValues(a);return a};var wh=[16,32];function xh(a){if(!w(wh,"includes").call(wh,a))throw new W("unsupported AES key size: "+a);}function Z(a){if(a==null||!(a instanceof Uint8Array))throw new W("input must be a non null Uint8Array");}function yh(a,b){if(a<0||a>b)throw new T("Version is out of bound, must be between 0 and "+b+".");};var zh=function(a,b){this.key=a;this.J=b};zh.prototype.encrypt=function(a){var b=this,c,d,e,g;return C(function(f){if(f.h==1)return Z(a),c=vh(b.J),d=new Uint8Array(16),d.set(c),e={name:"AES-CTR",counter:d,length:128},B(f,q.globalThis.crypto.subtle.encrypt(e,b.key,a),2);g=f.g;return f.return(X(c,new Uint8Array(g)))})};zh.prototype.decrypt=function(a){var b=this,c,d,e;return C(function(g){if(g.h==1){Z(a);if(a.length<b.J)throw new T("ciphertext too short");c=new Uint8Array(16);c.set(a.subarray(0,b.J));d={name:"AES-CTR",counter:c,length:128};e=Uint8Array;return B(g,q.globalThis.crypto.subtle.decrypt(d,b.key,new Uint8Array(a.subarray(b.J))),2)}return g.return(new e(g.g))})};function Ah(a,b){var c;return C(function(d){if(d.h==1){if(!w(Number,"isInteger").call(Number,b))throw new T("invalid IV length, must be an integer");if(b<12||b>16)throw new T("invalid IV length, must be at least 12 and at most 16");Z(a);xh(a.length);return B(d,q.globalThis.crypto.subtle.importKey("raw",a,{name:"AES-CTR",length:a.length},!1,["encrypt","decrypt"]),2)}c=d.g;return d.return(new zh(c,b))})};var Bh=function(){};var Ch=function(a,b,c){this.hash=a;this.key=b;this.K=c};z(Ch,Bh);var Dh=function(a,b){var c;return C(function(d){if(d.h==1)return Z(b),B(d,q.globalThis.crypto.subtle.sign({name:"HMAC",hash:{name:a.hash}},a.key,b),2);c=d.g;return d.return(new Uint8Array(c.slice(0,a.K)))})},Eh=function(a,b,c){var d;return C(function(e){if(e.h==1)return Z(b),Z(c),B(e,Dh(a,c),2);d=e.g;if(b.length!==d.length)var g=!1;else{for(var f=g=0;f<b.length;f++)g|=b[f]^d[f];g=g==0}return e.return(g)})};function Fh(a,b,c){var d;return C(function(e){if(e.h==1){Z(b);if(!w(Number,"isInteger").call(Number,c))throw new W("invalid tag size, must be an integer");if(c<10)throw new W("tag too short, must be at least "+(10).toString()+" bytes");switch(a){case "SHA-1":if(c>20)throw new W("tag too long, must not be larger than 20 bytes");break;case "SHA-256":if(c>32)throw new W("tag too long, must not be larger than 32 bytes");break;case "SHA-384":if(c>48)throw new W("tag too long, must not be larger than 48 bytes");break;case "SHA-512":if(c>64)throw new W("tag too long, must not be larger than 64 bytes");break;default:throw new W(a+" is not supported");}return B(e,q.globalThis.crypto.subtle.importKey("raw",b,{name:"HMAC",hash:{name:a},length:b.length*8},!1,["sign","verify"]),2)}d=e.g;return e.return(new Ch(a,d,c))})};var Gh=function(a,b,c,d){this.g=a;this.J=b;this.h=c;this.K=d};z(Gh,ag);Gh.prototype.encrypt=function(a,b){b=b===void 0?new Uint8Array(0):b;var c=this,d,e,g;return C(function(f){if(f.h==1)return Z(a),B(f,c.g.encrypt(a),2);if(f.h!=3)return d=f.g,Z(b),e=Ug(b.length*8),B(f,Dh(c.h,X(b,d,e)),3);g=f.g;if(c.K!=g.length)throw new T("invalid tag size, expected "+c.K+" but got "+g.length);return f.return(X(d,g))})};Gh.prototype.decrypt=function(a,b){b=b===void 0?new Uint8Array(0):b;var c=this,d,e,g,f,h;return C(function(k){if(k.h==1){Z(a);if(a.length<c.J+c.K)throw new T("ciphertext too short");d=new Uint8Array(a.subarray(0,a.length-c.K));Z(b);e=Ug(b.length*8);g=X(b,d,e);f=new Uint8Array(a.subarray(d.length));return B(k,Eh(c.h,f,g),2)}h=k.g;if(!h)throw new T("invalid MAC");return k.return(c.g.decrypt(d))})};function Hh(a,b,c,d,e){var g,f;return C(function(h){if(h.h==1)return Z(a),Z(d),B(h,Ah(a,b),2);if(h.h!=3)return g=h.g,B(h,Fh(c,d,e),3);f=h.g;return h.return(new Gh(g,b,f,e))})};var Ih=function(){};Ih.prototype.P=function(a){if(a instanceof Uint8Array){try{var b=mg(a)}catch(e){throw new T("Could not parse the given Uint8Array as a serialized proto of type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey");}if(!b||!M(b,jg,1)||!M(b,kg,2))throw new T("Could not parse the given Uint8Array as a serialized proto of type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey");}else if(a instanceof lg)b=a;else throw new T("Expected AesCtrHmacAeadKeyFormat-proto");var c=Jh(M(b,jg,1));a=c.ua;c=c.V;var d=new cg;d=qd(d,1,0);a=od(d,2,a);c=vh(c);a=Q(a,3,c);c=Kh(M(b,kg,2));b=c.za;c=c.ya;d=new eg;d=qd(d,1,0);b=od(d,2,b);c=vh(c);b=Q(b,3,c);c=new fg;a=od(c,2,a);return od(a,3,b)};var Jh=function(a){if(!a)throw new T("Invalid AES CTR HMAC key format: key format undefined");var b=N(a,2);xh(b);a=M(a,bg,1);if(!a)throw new T("Invalid AES CTR HMAC key format: params undefined");var c=N(a,1);if(c<12||c>16)throw new T("Invalid AES CTR HMAC key format: IV size is out of range: "+c);return{ua:a,V:b,J:c}},Kh=function(a){if(!a)throw new T("Invalid AES CTR HMAC key format: key format undefined");var b=N(a,2);if(b<16)throw new T("Invalid AES CTR HMAC key format: HMAC key is too small: "+N(a,2));a=M(a,dg,1);if(!a)throw new T("Invalid AES CTR HMAC key format: params undefined");var c=N(a,2);if(c<10)throw new T("Invalid HMAC params: tag size "+c+" is too small.");if(!Lh.has(P(a,1)))throw new T("Unknown hash type.");if(c>Lh.get(P(a,1)))throw new T("Invalid HMAC params: tag size "+c+" is out of range.");switch(P(a,1)){case 1:var d="SHA-1";break;case 3:d="SHA-256";break;case 4:d="SHA-512";break;default:d="UNKNOWN HASH"}return{za:a,ya:b,xa:d,K:c}},Lh=new q.Map([[1,20],[3,32],[4,64]]),Mh=function(){this.g=new Ih};m=Mh.prototype;m.C=function(a,b){var c,d,e,g,f,h=this,k,l,n,p,r,t;return C(function(u){if(u.h==1){if(a!=h.u())throw new T("Requested primitive type which is not supported by this key manager.");if(b instanceof U){if(!h.N(O(b,1)))throw new T("Key type "+O(b,1)+" is not supported. This key manager supports "+h.A()+".");try{k=ig(b.getValue())}catch(ra){throw new T("Could not parse the key in key data as a serialized proto of type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey");}if(k===null||k===void 0)throw new T("Could not parse the key in key data as a serialized proto of type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey");}else if(b instanceof fg)k=b;else throw new T("Given key type is not supported. This key manager supports "+h.A()+".");var v=M(k,cg,2);if(!v)throw new T("Invalid AES CTR HMAC key format: key undefined");yh(v.o(),h.o());var x=new jg;var K=M(v,bg,2);x=od(x,1,K);K=Tg(L(v,3));x=qd(x,2,K);x=Jh(x).J;g=V(L(v,3));f=x;l=g;n=f;v=M(k,eg,3);if(!v)throw new T("Invalid AES CTR HMAC key format: key undefined");yh(v.o(),h.o());x=new kg;K=M(v,dg,2);x=od(x,1,K);K=Tg(L(v,3));x=qd(x,2,K);K=Kh(x);x=K.xa;K=K.K;c=V(L(v,3));d=x;e=K;p=c;r=d;t=e;return B(u,Hh(l,n,r,p,t),2)}return u.return(u.g)})};m.N=function(a){return a===this.A()};m.A=function(){return"type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey"};m.u=function(){return ag};m.o=function(){return 0};m.O=function(){return this.g};var Nh=function(a){var b=a.key;a=a.F;this.key=b;this.F=a};Nh.prototype.encrypt=function(a,b,c){var d=this,e,g;return C(function(f){if(f.h==1){if(a.length!==12)throw new T("IV must be 12 bytes");e={name:"AES-GCM",iv:a,tagLength:128};c&&(e.additionalData=c);return B(f,q.globalThis.crypto.subtle.encrypt(e,d.key,b),2)}g=f.g;return f.return(d.F?X(a,new Uint8Array(g)):new Uint8Array(g))})};Nh.prototype.decrypt=function(a,b,c){var d=this,e,g,f,h,k;return C(function(l){if(l.h==1){e=d.F?28:16;if(b.length<e)throw new T("ciphertext too short");if(a.length!==12)throw new T("IV must be 12 bytes");g={name:"AES-GCM",iv:a,tagLength:128};c&&(g.additionalData=c);f=d.F?new Uint8Array(b.subarray(12)):b;l.s=2;h=Uint8Array;return B(l,q.globalThis.crypto.subtle.decrypt(g,d.key,f),4)}if(l.h!=2)return l.return(new h(l.g));k=Aa(l);throw new T(k.toString());})};function Oh(a){var b=a.key;var c=a.F;var d;return C(function(e){if(e.h==1){if(!(aa=[16,32],w(aa,"includes")).call(aa,b.length))throw new W("unsupported AES key size: ${n}");return B(e,q.globalThis.crypto.subtle.importKey("raw",b,{name:"AES-GCM",length:b.length},!1,["encrypt","decrypt"]),2)}d=e.g;return e.return(new Nh({key:d,F:c}))})};var Ph=function(a){this.key=a;this.g=new Nh({key:a,F:!0})};z(Ph,ag);Ph.prototype.encrypt=function(a,b){var c=this,d;return C(function(e){d=vh(12);return e.return(c.g.encrypt(d,a,b))})};Ph.prototype.decrypt=function(a,b){var c=this,d;return C(function(e){d=new Uint8Array(12);d.set(a.subarray(0,12));return e.return(c.g.decrypt(d,a,b))})};function Qh(a){var b;return C(function(c){if(c.h==1)return xh(a.length),B(c,q.globalThis.crypto.subtle.importKey("raw",a,{name:"AES-GCM",length:a.length},!1,["encrypt","decrypt"]),2);b=c.g;return c.return(new Ph(b))})};var Rh=function(){};Rh.prototype.P=function(a){if(a instanceof Uint8Array){try{var b=qg(a)}catch(c){throw new T("Could not parse the input as a serialized proto of type.googleapis.com/google.crypto.tink.AesGcmKey key format.");}if(!N(b,2))throw new T("Could not parse the input as a serialized proto of type.googleapis.com/google.crypto.tink.AesGcmKey key format.");a=b}else if(!(a instanceof pg))throw new T("Expected AesGcmKeyFormat-proto");b=a;xh(N(b,2));a=new ng;b=vh(N(b,2));a=Q(a,3,b);return qd(a,1,0)};var Sh=function(){this.g=new Rh};m=Sh.prototype;m.C=function(a,b){var c=this,d;return C(function(e){if(e.h==1){if(a!=c.u())throw new T("Requested primitive type which is not supported by this key manager.");if(b instanceof U){if(O(b,1)!="type.googleapis.com/google.crypto.tink.AesGcmKey")throw new T("Key type "+O(b,1)+" is not supported. This key manager supports type.googleapis.com/google.crypto.tink.AesGcmKey.");try{var g=og(b.getValue())}catch(f){throw new T("Could not parse the input as a serialized proto of type.googleapis.com/google.crypto.tink.AesGcmKey key.");}}else if(b instanceof ng)g=b;else throw new T("Key type is not supported. This key manager supports type.googleapis.com/google.crypto.tink.AesGcmKey.");d=g;xh(Tg(L(d,3)));yh(d.o(),0);return B(e,Qh(V(L(d,3))),2)}return e.return(e.g)})};m.N=function(a){return a===this.A()};m.A=function(){return"type.googleapis.com/google.crypto.tink.AesGcmKey"};m.u=function(){return ag};m.o=function(){return 0};m.O=function(){return this.g};var Th=function(){};var Uh=function(a,b){b=[b];var c=b.concat;if(!w(Number,"isInteger").call(Number,a)||a<0||a>=4294967296)throw new W("Number has to be unsigned 32-bit integer.");for(var d=Array(4),e=0;e<4;e++)d[e]=255&a>>8*(4-e-1);b=c.call(b,d);return new Uint8Array(b)},Vh=new Uint8Array(0);var Wh=function(a){this.g=a};z(Wh,Th);Wh.prototype.decrypt=function(a,b){var c=this,d,e,g,f,h;return C(function(k){switch(k.h){case 1:if(!a)throw new T("Ciphertext has to be non-null.");if(!(a.length>5)){k.h=2;break}d=a.subarray(0,5);return B(k,Xh(c.g,d),3);case 3:return e=k.g,g=a.subarray(5,a.length),k.s=4,B(k,Yh(e,g,b),6);case 6:f=k.g;k.h=5;k.s=0;break;case 4:Aa(k);case 5:if(f)return k.return(f);case 2:return B(k,Xh(c.g,Vh),7);case 7:return h=k.g,k.return(Yh(h,a,b))}})};var Yh=function(a,b,c){var d,e,g,f;return C(function(h){switch(h.h){case 1:d=a.length,e=0;case 2:if(!(e<d)){h.h=4;break}if(a[e].h!=1){h.h=3;break}g=a[e].C();f=void 0;h.s=5;return B(h,g.decrypt(b,c),7);case 7:f=h.g;h.h=6;h.s=0;break;case 5:Aa(h);h.h=3;break;case 6:return h.return(f);case 3:e++;h.h=2;break;case 4:throw new T("Decryption failed for the given ciphertext.");}})},Zh=function(){};Zh.prototype.wrap=function(a){if(!a)throw new T("Primitive set has to be non-null.");return new Wh(a)};Zh.prototype.u=function(){return Th};function $h(a){var b=null;var c=a instanceof Cg?M(a,Ag,2):a;var d=M(c,wg,2);if(!d)throw new T("Params not set");d=M(d,ug,1);if(!d)throw new T("KEM params not set");d=lh(P(d,1));var e=ah(d),g=kh(V(L(c,3)),e);c=kh(V(L(c,4)),e);a instanceof Cg&&(b=kh(V(L(a,3)),e));a=b;b={kty:"EC",crv:Yg(d),x:Xg(g),y:Xg(c),ext:!0};a&&(b.d=Xg(a));return b};function ai(a){var b=M(a,ug,1);if(!b)throw new T("Invalid params - missing KEM params.");var c=P(b,1);if(c!==2&&c!==3&&c!==4)throw new T("Invalid KEM params - unknown curve type.");b=P(b,2);if(b!==1&&b!==3&&b!==2&&b!==4)throw new T("Invalid KEM params - unknown hash type.");b=M(a,sg,2);if(!b)throw new T("Invalid params - missing DEM params.");if(!M(b,rg,2))throw new T("Invalid DEM params - missing AEAD key template.");b=M(b,rg,2);b=O(b,1);if(b!="type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey"&&b!="type.googleapis.com/google.crypto.tink.AesGcmKey")throw new T("Invalid DEM params - "+b+" template is not supported by ECIES AEAD HKDF.");a=P(a,3);if(a!==1&&a!==2&&a!==3)throw new T("Invalid key params - unknown EC point format.");}function bi(a,b){yh(a.o(),b);b=M(a,wg,2);if(!b)throw new T("Invalid public key - missing key params.");ai(b);if(!Tg(L(a,3))||!Tg(L(a,4)))throw new T("Invalid public key - missing value of X or Y.");};var ci=function(){};var di=function(a){var b=O(a,1);switch(b){case "type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey":try{var c=mg(a.getValue())}catch(g){throw new T("Could not parse the given Uint8Array as a serialized proto of type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey.");}if(!M(c,jg,1)||!M(c,kg,2))throw new T("Could not parse the given Uint8Array as a serialized proto of type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey.");a=c;var d=M(a,jg,1);if(!d)throw new T("AES-CTR key format not set");d=N(d,2);c=M(a,kg,2);if(!c)throw new T("HMAC key format not set");c=N(c,2);c=d+c;break;case "type.googleapis.com/google.crypto.tink.AesGcmKey":try{var e=qg(a.getValue())}catch(g){throw new T("Could not parse the given Uint8Array as a serialized proto of type.googleapis.com/google.crypto.tink.AesGcmKey.");}if(!N(e,2))throw new T("Could not parse the given Uint8Array as a serialized proto of type.googleapis.com/google.crypto.tink.AesGcmKey.");a=e;c=N(a,2);break;default:throw new T("Key type URL "+b+" is not supported.");}this.key=sh(b).O().P(a);this.g=b;this.S=c;this.V=d};di.prototype.L=function(a){var b=this,c;return C(function(d){if(a.length!==b.S)throw new T("Key is not of the correct length, expected length: "+b.S+", but got key of length: "+a.length+".");if(b.g==="type.googleapis.com/google.crypto.tink.AesCtrHmacAeadKey"){var e=b.key,g=M(e,cg,2);if(!g)throw new T("AES-CTR key not set");Q(g,3,a.slice(0,b.V));g=M(e,eg,3);if(!g)throw new T("HMAC key not set");Q(g,3,a.slice(b.V,b.S));c=e}else{if(!(b.key instanceof ng))throw new T("Key is not an AES-CTR key");c=Q(b.key,3,a)}return d.return(th(ag,c,b.g))})};function ei(a,b,c,d,e){var g,f,h,k,l,n,p,r,t;return C(function(u){switch(u.h){case 1:if(!w(Number,"isInteger").call(Number,a))throw new W("size must be an integer");if(a<=0)throw new W("size must be positive");switch(b){case "SHA-1":g=20;if(a>5100)throw new W("size too large");break;case "SHA-256":g=32;if(a>8160)throw new W("size too large");break;case "SHA-512":g=64;if(a>16320)throw new W("size too large");break;default:throw new W(b+" is not supported");}Z(c);Z(d);f=e;if(e==null||f===void 0||f.length==0)f=new Uint8Array(g);Z(f);return B(u,Fh(b,f,g),2);case 2:return h=u.g,B(u,Dh(h,c),3);case 3:return k=u.g,B(u,Fh(b,k,g),4);case 4:h=u.g,l=1,n=0,p=new Uint8Array(0),r=new Uint8Array(a);case 5:return t=new Uint8Array(p.length+d.length+1),t.set(p,0),t.set(d,p.length),t[t.length-1]=l,B(u,Dh(h,t),8);case 8:p=u.g;if(n+p.length<a)r.set(p,n),n+=p.length,l++;else{r.set(p.subarray(0,a-n),n);u.h=7;break}u.h=5;break;case 7:return u.return(r)}})};var fi=function(a){if(!a)throw new T("Recipient public key has to be non-null.");if(a.type!=="public"||!a.algorithm)throw new T("Expected Crypto key of type: public.");this.publicKey=a};fi.prototype.fa=function(a,b,c,d,e){var g=this,f,h,k,l,n,p,r,t,u,v;return C(function(x){switch(x.h){case 1:f=g.publicKey.algorithm;h=f.namedCurve;if(!h)throw new T("Curve has to be defined.");return B(x,gh(h),2);case 2:return k=x.g,B(x,fh(k.privateKey,g.publicKey),3);case 3:return l=x.g,B(x,hh(k.publicKey),4);case 4:p=n=x.g;r=p.crv;if(!r)throw new T("Curve has to be defined.");t=$g(r,b,n);u=X(t,l);return B(x,ei(a,c,u,d,e),5);case 5:return v=x.g,x.return({key:v,token:t})}})};function gi(a){var b;return C(function(c){if(c.h==1)return B(c,ih(a),2);b=c.g;return c.return(new fi(b))})};var hi=function(a,b,c,d,e){if(!a)throw new T("KEM sender has to be non-null.");if(!b)throw new T("HMAC algorithm has to be non-null.");if(!c)throw new T("Point format has to be non-null.");if(!d)throw new T("DEM helper has to be non-null.");this.s=a;this.h=b;this.l=c;this.g=d;this.i=e};z(hi,ci);hi.prototype.encrypt=function(a,b){b=b===void 0?new Uint8Array(0):b;var c=this,d,e,g,f,h;return C(function(k){switch(k.h){case 1:return d=c.g.S,B(k,c.s.fa(d,c.l,c.h,b,c.i),2);case 2:return e=k.g,B(k,c.g.L(e.key),3);case 3:return g=k.g,B(k,g.encrypt(a),4);case 4:return f=k.g,h=e.token,k.return(X(h,f))}})};function ii(a,b,c,d,e){var g;return C(function(f){if(f.h==1){if(!a)throw new T("Recipient public key has to be non-null.");if(!b)throw new T("HMAC algorithm has to be non-null.");if(!c)throw new T("Point format has to be non-null.");if(!d)throw new T("DEM helper has to be non-null.");return B(f,gi(a),2)}g=f.g;return f.return(new hi(g,b,c,d,e))})};var ji=function(){};ji.prototype.P=function(){throw new T("This operation is not supported for public keys. Use EciesAeadHkdfPrivateKeyManager to generate new keys.");};var ki=function(){this.g=new ji};m=ki.prototype;m.C=function(a,b){var c=this,d,e,g,f,h,k,l,n,p,r;return C(function(t){if(a!==c.u())throw new T("Requested primitive type which is not supported by this key manager.");if(b instanceof U){if(O(b,1)!=="type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey")throw new T("Key type "+O(b,1)+" is not supported. This key manager supports type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey.");try{var u=Eg(b.getValue())}catch(v){throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey key-proto.");}if(!M(u,wg,2)||!L(u,3)||!L(u,4))throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey key-proto.");}else if(b instanceof Ag)u=b;else throw new T("Key type is not supported. This key manager supports type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey.");d=u;bi(d,c.o());e=$h(d);g=M(d,wg,2);f=M(g,sg,2);if(!f)throw new T("DEM params not set");h=M(f,rg,2);k=new di(h);l=nh(P(g,3));n=M(g,ug,1);if(!n)throw new T("KEM params not set");p=mh(P(n,2));r=V(L(n,11));return t.return(ii(e,p,l,k,r))})};m.N=function(a){return a===this.A()};m.A=function(){return"type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey"};m.u=function(){return ci};m.o=function(){return 0};m.O=function(){return this.g};var li=function(a){if(!a)throw new T("Private key has to be non-null.");if(a.type!=="private"||!a.algorithm)throw new T("Expected crypto key of type: private.");this.g=a};li.prototype.da=function(a,b,c,d,e,g){var f=this,h,k,l,n,p,r,t;return C(function(u){switch(u.h){case 1:h=f.g.algorithm;k=h.namedCurve;if(!k)throw new T("Curve has to be defined.");l=eh(k,c,a);return B(u,ih(l),2);case 2:return n=u.g,B(u,fh(f.g,n),3);case 3:return p=u.g,r=X(a,p),B(u,ei(b,d,r,e,g),4);case 4:return t=u.g,u.return(t)}})};function mi(a){var b;return C(function(c){if(c.h==1)return B(c,jh(a),2);b=c.g;return c.return(new li(b))})};var ni=function(a,b,c,d,e,g){if(!a)throw new T("Recipient private key has to be non-null.");if(!b)throw new T("KEM recipient has to be non-null.");if(!c)throw new T("HKDF hash algorithm has to be non-null.");if(!d)throw new T("Point format has to be non-null.");if(!e)throw new T("DEM helper has to be non-null.");a=a.crv;if(!a)throw new T("Curve has to be defined.");a=Zg(a);a:{switch(d){case 1:a=2*ah(a)+1;break a;case 2:a=ah(a)+1;break a;case 3:a=2*ah(a);break a}a=void 0}this.D=b;this.i=c;this.s=d;this.h=e;this.g=a;this.l=g};z(ni,Th);ni.prototype.decrypt=function(a,b){var c=this,d,e,g;return C(function(f){if(f.h==1){if(a.length<c.g)throw new T("Ciphertext is too short.");d=a.slice(0,c.g);e=a.slice(c.g,a.length);return B(f,c.L(d,b),2)}g=f.g;return f.return(g.decrypt(e))})};ni.prototype.L=function(a,b){var c=this,d;return C(function(e){if(e.h==1)return b||(b=new Uint8Array(0)),B(e,c.D.da(a,c.h.S,c.s,c.i,b,c.l),2);d=e.g;return e.return(c.h.L(d))})};function oi(a,b,c,d,e){var g;return C(function(f){if(f.h==1){if(!a)throw new T("Recipient private key has to be non-null.");if(!b)throw new T("HKDF hash algorithm has to be non-null.");if(!c)throw new T("Point format has to be non-null.");if(!d)throw new T("DEM helper has to be non-null.");if(!a)throw new T("Recipient private key has to be non-null.");return B(f,mi(a),2)}g=f.g;return f.return(new ni(a,g,b,c,d,e))})};var pi=function(){};pi.prototype.P=function(a){var b,c,d,e,g,f,h,k,l;return C(function(n){switch(n.h){case 1:if(!a)throw new T("Key format has to be non-null.");if(a instanceof Uint8Array){try{var p=zg(a)}catch(v){throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey key format proto.");}if(!M(p,wg,1))throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey key format proto.");}else if(a instanceof yg)p=a;else throw new T("Expected type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey key format proto.");b=p;p=M(b,wg,1);if(!p)throw new T("Invalid key format - missing key params.");ai(p);c=M(b,wg,1);if(!c)throw new T("Params not set");d=M(c,ug,1);if(!d)throw new T("KEM params not set");e=P(d,1);g=lh(e);f=Yg(g);return B(n,gh(f),2);case 2:return h=n.g,B(n,hh(h.publicKey),3);case 3:return k=n.g,B(n,hh(h.privateKey),4);case 4:l=n.g;p=n.return;var r=k.x;var t=k.y;if(r===void 0)throw new T("x must be set");if(t===void 0)throw new T("y must be set");var u=new Ag;u=qd(u,1,0);u=od(u,2,c);r=Y(r);r=Q(u,3,r);t=Y(t);r=Q(r,4,t);t=l.d;if(t===void 0)throw new T("d must be set");u=new Cg;u=qd(u,1,0);r=od(u,2,r);t=Y(t);t=Q(r,3,t);return p.call(n,t)}})};var qi=function(){this.g=new pi};m=qi.prototype;m.C=function(a,b){var c=this,d,e,g,f,h,k,l,n,p,r,t;return C(function(u){if(a!==c.u())throw new T("Requested primitive type which is not supported by this key manager.");if(b instanceof U){if(O(b,1)!=="type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey")throw new T("Key type "+O(b,1)+" is not supported. This key manager supports type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey.");var v=b.getValue();try{var x=Dg(v)}catch(K){throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey key-proto.");}if(!M(x,Ag,2)||!L(x,3))throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey key-proto.");v=x}else if(b instanceof Cg)v=b;else throw new T("Key type is not supported. This key manager supports type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey.");d=v;yh(d.o(),0);if(!L(d,3))throw new T("Invalid private key - missing private key value.");v=M(d,Ag,2);if(!v)throw new T("Invalid private key - missing public key information.");bi(v,0);e=$h(d);g=M(d,Ag,2);if(!g)throw new T("Public key not set");f=M(g,wg,2);if(!f)throw new T("Params not set");h=M(f,sg,2);if(!h)throw new T("DEM params not set");k=M(h,rg,2);if(!k)throw new T("Key template not set");l=new di(k);n=nh(P(f,3));p=M(f,ug,1);if(!p)throw new T("KEM params not set");r=mh(P(p,2));t=V(L(p,11));return u.return(oi(e,r,n,l,t))})};m.N=function(a){return a===this.A()};m.A=function(){return"type.googleapis.com/google.crypto.tink.EciesAeadHkdfPrivateKey"};m.u=function(){return Th};m.o=function(){return 0};m.O=function(){return this.g};var ri=function(a){this.g=a};z(ri,ci);ri.prototype.encrypt=function(a,b){var c=this,d,e,g,f;return C(function(h){if(h.h==1){if(!a)throw new T("Plaintext has to be non-null.");d=c.g.h;if(!d)throw new T("Primary not set.");e=d.C();return B(h,e.encrypt(a,b),2)}g=h.g;f=d.g;return h.return(X(f,g))})};var si=function(){};si.prototype.wrap=function(a){if(!a)throw new T("Primitive set has to be non-null.");if(!a.h)throw new T("Primary has to be non-null.");return new ri(a)};si.prototype.u=function(){return ci};var ui=ti(1,0),vi=ti(2,16),wi=ti(2,18),xi=ti(2,1),yi=ti(2,3),zi=ti(2,1),Ai=ti(2,2),Bi=Wg("KEM"),Ci=Wg("HPKE"),Di=Wg("HPKE-v1");function ti(a,b){for(var c=new Uint8Array(a),d=0;d<a;d++)c[d]=b>>8*(a-d-1)&255;return c}function Ei(a){var b=a.I;var c=a.H;return X(Di,a.B,Wg(b),c)}function Fi(a){var b=a.M;var c=a.info;var d=a.B;return X(ti(2,a.length),Di,d,Wg(b),c)}function Gi(a){switch(a){case 2:return 1;case 4:return 3;default:throw new W("Unrecognized NIST HPKE KEM identifier");}}function Hi(a,b){var c;return C(function(d){return d.h==1?(c=eh(a,1,b),B(d,ih(c),2)):d.return(d.g)})}function Ii(a){var b=a.ca;var c=a.publicKey;var d=a.privateKey;var e;return C(function(g){return g.h==1?(e=eh(b,1,c),e.d=Xg(d),B(g,jh(e),2)):g.return(g.g)})}function Ji(a){var b,c;return C(function(d){if(d.h==1)return b=a.algorithm,B(d,hh(a),2);c=d.g;if(!c.crv)throw new T("Curve has to be defined.");return d.return($g(b.namedCurve,1,c))})};var Ki=function(a,b,c,d){this.ga=a;this.key=b;this.i=c;this.h=d;this.g=BigInt(0);this.l=(BigInt(1)<<BigInt(96))-BigInt(1)};Ki.prototype.seal=function(a,b){var c=this,d;return C(function(e){return e.h==1?(d=Li(c),B(e,c.h.seal({key:c.key,nonce:d,Ca:a,ba:b}),2)):e.return(e.g)})};Ki.prototype.open=function(a,b){var c=this,d;return C(function(e){d=Li(c);return e.return(c.h.open({key:c.key,nonce:d,wa:a,ba:b}))})};var Li=function(a){for(var b=a.g,c=new Uint8Array(12),d=0;d<12;d++)c[d]=Number(b>>BigInt(8*(12-d-1)))&255;b=a.i;if(b.length!==c.length)throw new W("Both byte arrays should be of the same length");d=new Uint8Array(b.length);for(var e=0;e<d.length;e++)d[e]=b[e]^c[e];if(a.g>=a.l)throw new T("message limit reached");a.g+=BigInt(1);return d};function Mi(a,b,c,d,e,g){var f,h,k,l,n,p,r,t,u;return C(function(v){switch(v.h){case 1:a:{switch(e.Z){case 16:var x=zi;break a;case 32:x=Ai;break a}x=void 0}a:{switch(d.Y){case "SHA-256":var K=xi;break a;case "SHA-512":K=yi;break a}K=void 0}f=X(Ci,Ni(c),K,x);h=Oi(d,{H:new Uint8Array(0),I:"psk_id_hash",B:f});return B(v,Oi(d,{H:g,I:"info_hash",B:f}),2);case 2:return k=v.g,B(v,h,3);case 3:return l=v.g,n=X(ui,l,k),B(v,Oi(d,{H:new Uint8Array(0),I:"secret",B:f,salt:b}),4);case 4:return p=v.g,r=Pi(d,{ma:p,info:n,M:"key",B:f,length:e.Z}),B(v,Pi(d,{ma:p,info:n,M:"base_nonce",B:f,length:12}),5);case 5:return t=v.g,B(v,r,6);case 6:return u=v.g,v.return(new Ki(a,u,t,e))}})}function Qi(a,b,c,d,e){var g,f,h;return C(function(k){return k.h==1?B(k,b.fa(a),2):k.h!=3?(g=k.g,f=g.ga,h=g.Ea,B(k,Mi(f,h,b,c,d,e),3)):k.return(k.g)})}function Ri(a,b,c,d,e,g){var f;return C(function(h){return h.h==1?B(h,c.da(a,b),2):h.h!=3?(f=h.g,B(h,Mi(a,f,c,d,e,g),3)):h.return(h.g)})};var Si=function(a,b){this.privateKey=a;this.publicKey=b},Ti=function(a){return C(function(b){return b.h==1?B(b,Ji(a.publicKey),2):b.return(b.g)})};function Ui(a){var b=a.privateKey;var c=a.publicKey;var d=a.ca;var e,g;return C(function(f){if(f.h==1){if(!b)throw new W("KEM private key was null or undefined");if(!c)throw new W("KEM public key was null or undefined");return B(f,Hi(Yg(d),c),2)}if(f.h!=3)return e=f.g,B(f,Ii({ca:Yg(d),publicKey:c,privateKey:b}),3);g=f.g;return f.return(new Si(g,e))})}function Vi(a){return C(function(b){Wi(a.privateKey,"private");Wi(a.publicKey,"public");return b.return(new Si(a.privateKey,a.publicKey))})}function Wi(a,b){if(b!==a.type)throw new W("keyPair "+b+" key was of type "+a.type);a=a.algorithm;if("ECDH"!==a.name)throw new W("keyPair "+b+" key should be ECDH but found "+a.name);};var Xi=function(a){this.Z=a};Xi.prototype.seal=function(a){var b=a.key;var c=a.nonce;var d=a.Ca;var e=a.ba;var g=this,f;return C(function(h){if(h.h==1){if(b.length!==g.Z)throw new T("Unexpected key length: "+b.length.toString());return B(h,Oh({key:b,F:!1}),2)}return h.h!=3?(f=h.g,B(h,f.encrypt(c,d,e),3)):h.return(h.g)})};Xi.prototype.open=function(a){var b=a.key;var c=a.nonce;var d=a.wa;var e=a.ba;var g=this,f;return C(function(h){if(h.h==1){if(b.length!==g.Z)throw new T("Unexpected key length: "+b.length.toString());return B(h,Oh({key:b,F:!1}),2)}f=h.g;return h.return(f.decrypt(c,d,e))})};var Yi=function(a){this.Y=a},Oi=function(a,b){var c=b.H;var d=b.I;var e=b.B;var g=b.salt;return C(function(f){return f.h==1?B(f,Zi(a,Ei({I:d,H:c,B:e}),g),2):f.return(f.g)})},Pi=function(a,b){var c=b.ma;var d=b.info;var e=b.M;var g=b.B;var f=b.length;return C(function(h){return h.h==1?B(h,$i(a,c,Fi({M:e,info:d,B:g,length:f}),f),2):h.return(h.g)})},aj=function(a,b){var c=b.H;var d=b.I;var e=b.info;var g=b.M;var f=b.B;var h=b.length;var k=b.salt;var l;return C(function(n){return n.h==1?B(n,Zi(a,Ei({I:d,H:c,B:f}),k),2):n.h!=3?(l=n.g,B(n,$i(a,l,Fi({M:g,info:e,B:f,length:h}),h),3)):n.return(n.g)})},$i=function(a,b,c,d){var e,g,f,h,k,l,n;return C(function(p){switch(p.h){case 1:if(!w(Number,"isInteger").call(Number,d))throw new T("length must be an integer");if(d<=0)throw new T("length must be positive");e=bj(a);if(d>255*e)throw new T("length too large");Z(c);return B(p,Fh(a.Y,b,e),2);case 2:g=p.g,f=1,h=0,k=new Uint8Array(0),l=new Uint8Array(d);case 3:return n=new Uint8Array(k.length+c.length+1),n.set(k,0),n.set(c,k.length),n[n.length-1]=f,B(p,Dh(g,n),6);case 6:k=p.g;if(h+k.length<d)l.set(k,h),h+=k.length,f++;else{l.set(k.subarray(0,d-h),h);p.h=5;break}p.h=3;break;case 5:return p.return(l)}})},Zi=function(a,b,c){var d,e,g,f;return C(function(h){if(h.h==1)return Z(b),d=bj(a),((e=c)==null?0:e.length)||(c=new Uint8Array(d)),Z(c),B(h,Fh(a.Y,c,d),2);if(h.h!=3)return g=h.g,B(h,Dh(g,b),3);f=h.g;return h.return(f)})},bj=function(a){switch(a.Y){case "SHA-256":return 32;case "SHA-512":return 64}};var cj=function(a,b){this.i=a;this.g=b},dj=function(a){switch(a){case 1:return new cj(new Yi("SHA-256"),1);case 3:return new cj(new Yi("SHA-512"),3)}};cj.prototype.h=function(a,b,c){var d=this,e,g;return C(function(f){return f.h==1?(e=X(b,c),g=X(Bi,Ni(d)),B(f,aj(d.i,{H:a,I:"eae_prk",info:e,M:"shared_secret",B:g,length:bj(d.i)}),2)):f.return(f.g)})};cj.prototype.l=function(a,b){var c=this,d,e,g,f,h,k;return C(function(l){switch(l.h){case 1:return B(l,Hi(Yg(c.g),a),2);case 2:return d=l.g,e=fh(b.privateKey,d),B(l,Ti(b),3);case 3:return g=l.g,B(l,e,4);case 4:return f=l.g,B(l,c.h(f,g,a),5);case 5:return h=l.g,k={Ea:h,ga:g},l.return(k)}})};cj.prototype.fa=function(a){var b=this,c,d,e,g;return C(function(f){switch(f.h){case 1:return B(f,gh(Yg(b.g)),2);case 2:return c=f.g,d=b,e=d.l,g=a,B(f,Vi(c),4);case 4:return B(f,e.call(d,g,f.g),3);case 3:return f.return(f.g)}})};cj.prototype.da=function(a,b){var c=this,d,e,g,f,h,k;return C(function(l){switch(l.h){case 1:return d=b.privateKey,B(l,Hi(Yg(c.g),a),2);case 2:return e=l.g,B(l,fh(d,e),3);case 3:return g=l.g,f=c,h=f.h,k=a,B(l,Ti(b),4);case 4:return l.return(h.call(f,g,k,l.g))}})};var Ni=function(a){switch(a.g){case 1:return vi;case 3:return wi}};/*

 Copyright 2023 Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var ej=function(a){switch(P(a,1)){case 2:return dj(1);case 4:return dj(3);default:throw new W("Unrecognized HPKE KEM identifier");}},fj=function(a){switch(P(a,2)){case 1:return new Yi("SHA-256");case 3:return new Yi("SHA-512");default:throw new W("Unrecognized HPKE KDF identifier");}},gj=function(a){switch(a.L()){case 1:return new Xi(16);case 2:return new Xi(32);default:throw new W("Unrecognized HPKE AEAD identifier");}};var hj=function(a,b,c,d,e){if(!a)throw new W("Recipient private key must be non-null.");if(!b)throw new W("KEM algorithm must be non-null.");if(!c)throw new W("KDF algorithm must be non-null.");if(!d)throw new W("AEAD algorithm must be non-null.");this.s=a;this.l=b;this.i=c;this.h=d;this.g=e};z(hj,Th);var ij=function(a){var b,c,d,e,g,f,h;return C(function(k){if(k.h==1){if(V(L(a,3)).length===0)throw new W("Recipient private key is empty.");b=M(a,Kg,2);if(!b)throw new W("Recipient private key is missing public key field.");c=M(b,Fg,2);if(!c)throw new W("Public key is missing params field.");d=ej(c);e=fj(c);g=gj(c);a:switch(P(c,1)){case 2:var l=65;break a;case 4:l=133;break a;default:throw new W("Unable to determine KEM-encoding length");}f=l;a:{l=M(a,Kg,2);if(!l)throw new W("Public key not set");var n=M(l,Fg,2);if(!n)throw new W("Params not set");switch(P(n,1)){case 2:case 4:l=Ui({privateKey:V(L(a,3)),publicKey:V(L(l,3)),ca:Gi(P(n,1))});break a;default:throw new W("Unrecognized HPKE KEM identifier");}}return B(k,l,2)}h=k.g;return k.return(new hj(h,d,e,g,f))})};hj.prototype.decrypt=function(a,b){var c=this,d,e,g;return C(function(f){if(f.h==1){if(a.length<=c.g)throw new T("Ciphertext is too short.");b||(b=new Uint8Array(0));d=a.slice(0,c.g);e=a.slice(c.g,a.length);return B(f,Ri(d,c.s,c.l,c.i,c.h,b),2)}g=f.g;return f.return(g.open(e,jj))})};var jj=new Uint8Array(0);var kj=function(a,b,c,d){if(!a)throw new W("Recipient public key must be non-null.");if(!b)throw new W("KEM algorithm must be non-null.");if(!c)throw new W("KDF algorithm must be non-null.");if(!d)throw new W("AEAD algorithm must be non-null.");this.l=a;this.i=b;this.h=c;this.g=d};z(kj,ci);var lj=function(a){var b,c,d,e;return C(function(g){if(V(L(a,3)).length===0)throw new W("Recipient public key is empty.");b=M(a,Fg,2);if(!b)throw new W("Public key is missing params field.");c=ej(b);d=fj(b);e=gj(b);return g.return(new kj(a,c,d,e))})};kj.prototype.encrypt=function(a,b){var c=this,d,e,g;return C(function(f){if(f.h==1)return b||(b=new Uint8Array(0)),d=V(L(c.l,3)),B(f,Qi(d,c.i,c.h,c.g,b),2);if(f.h!=3)return e=f.g,B(f,e.seal(a,mj),3);g=f.g;return f.return(X(e.ga,g))})};var mj=new Uint8Array(0);function nj(a){var b=P(a,1);if(b!==2&&b!==4)throw new T("Invalid hpke params - unknown KEM identifier.");b=P(a,2);if(b!==1&&b!==3)throw new T("Invalid hpke params - unknown KDF identifier.");a=a.L();if(a!==1&&a!==2)throw new T("Invalid hpke params - unknown AEAD identifier.");}function oj(a,b){yh(a.o(),b);b=M(a,Fg,2);if(!b)throw new T("Invalid public key - missing key params.");nj(b);if(Tg(L(a,3))===0)throw new T("Invalid public key - missing public key value.");};var pj=function(){};pj.prototype.P=function(){throw new T("This operation is not supported for public keys. Use HpkePrivateKeyManager to generate new keys.");};var qj=function(){this.g=new pj};m=qj.prototype;m.C=function(a,b){var c=this,d;return C(function(e){if(a!==c.u())throw new T("Requested primitive type which is not supported by this key manager.");if(b instanceof U){if(O(b,1)!=="type.googleapis.com/google.crypto.tink.HpkePublicKey")throw new T("Key type "+O(b,1)+" is not supported. This key manager supports type.googleapis.com/google.crypto.tink.HpkePublicKey.");try{var g=Og(b.getValue())}catch(f){throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.HpkePublicKey key-proto.");}if(!M(g,Fg,2)||!L(g,3))throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.HpkePublicKey key-proto.");}else if(b instanceof Kg)g=b;else throw new T("Key type is not supported. This key manager supports type.googleapis.com/google.crypto.tink.HpkePublicKey.");d=g;oj(d,c.o());return e.return(lj(d))})};m.N=function(a){return a===this.A()};m.A=function(){return"type.googleapis.com/google.crypto.tink.HpkePublicKey"};m.u=function(){return ci};m.o=function(){return 0};m.O=function(){return this.g};var rj=function(){};rj.prototype.P=function(a){var b,c,d,e,g,f,h,k,l,n;return C(function(p){switch(p.h){case 1:if(!a)throw new T("Key format must be non-null.");if(a instanceof Uint8Array){try{var r=Jg(a)}catch(v){throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.HpkePrivateKey key format proto.");}if(!M(r,Fg,1))throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.HpkePrivateKey key format proto.");}else if(a instanceof Hg)r=a;else throw new T("Expected type.googleapis.com/google.crypto.tink.HpkePrivateKey key format proto.");b=r;r=M(b,Fg,1);if(!r)throw new T("Invalid key format - missing key params.");nj(r);c=M(b,Fg,1);if(!c)throw new T("Params not set");d=Gi(P(c,1));e=Yg(d);return B(p,gh(e),2);case 2:return g=p.g,B(p,hh(g.privateKey),3);case 3:return f=p.g,B(p,Ji(g.publicKey),4);case 4:h=p.g;r=new Kg;r=qd(r,1,0);r=od(r,2,c);k=Q(r,3,h);l=f;n=l.d;if(n===void 0)throw new T("d must be set");r=p.return;var t=new Mg;t=qd(t,1,0);t=od(t,2,k);var u=Y(n);t=Q(t,3,u);return r.call(p,t)}})};var sj=function(){this.g=new rj};m=sj.prototype;m.C=function(a,b){var c=this,d;return C(function(e){if(a!==c.u())throw new T("Requested primitive type which is not supported by this key manager.");if(b instanceof U){if(O(b,1)!=="type.googleapis.com/google.crypto.tink.HpkePrivateKey")throw new T("Key type "+O(b,1)+" is not supported. This key manager supports type.googleapis.com/google.crypto.tink.HpkePrivateKey.");var g=b.getValue();try{var f=Ng(g)}catch(h){throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.HpkePrivateKey key-proto.");}if(!M(f,Kg,2)||!L(f,3))throw new T("Input cannot be parsed as type.googleapis.com/google.crypto.tink.HpkePrivateKey key-proto.");g=f}else if(b instanceof Mg)g=b;else throw new T("Key type is not supported. This key manager supports type.googleapis.com/google.crypto.tink.HpkePrivateKey.");d=g;yh(d.o(),0);if(Tg(L(d,3))===0)throw new T("Invalid private key - missing private key value.");g=M(d,Kg,2);if(!g)throw new T("Invalid private key - missing public key field.");oj(g,0);return e.return(ij(d))})};m.N=function(a){return a===this.A()};m.A=function(){return"type.googleapis.com/google.crypto.tink.HpkePrivateKey"};m.u=function(){return Th};m.o=function(){return 0};m.O=function(){return this.g};function tj(a,b,c,d){var e=new Hg,g=new Fg;a=td(g,1,a);b=td(a,2,b);c=td(b,3,c);e=od(e,1,c);c=new rg;c=sd(c,1,"type.googleapis.com/google.crypto.tink.HpkePrivateKey");e=e.h();e=Q(c,2,e);td(e,3,d)};tj(2,1,1,3);tj(2,1,1,1);tj(2,1,2,3);tj(2,1,2,1);tj(4,3,2,3);tj(4,3,2,1);var uj=function(a,b,c){this.i=a;this.g=b;this.h=c};uj.prototype.C=function(){return this.i};var vj=function(a){this.i=a;this.h=null;this.g=new q.Map};vj.prototype.u=function(){return this.i};var Xh=function(a,b){return(a=wj(a,b))?a:[]},wj=function(a,b){b instanceof Uint8Array&&(b=[].concat(qa(b)).toString());return a.g.get(b)};var xj=function(a){if(!a||!Rg(a)||Rg(a).length<1)throw new T("Keyset should be non null and must contain at least one key.");for(var b=!1,c=Rg(a).length,d=0;d<c;d++){var e=Rg(a)[d];if(!e)throw new T("Key should be non null.");if(!M(e,U,1))throw new T("Key data are missing for key "+N(e,3)+".");if(P(e,4)===0)throw new T("Key "+N(e,3)+" has unknown output prefix type.");if(P(e,2)===0)throw new T("Key "+N(e,3)+" has unknown status.");if(N(a,1)===N(e,3)&&P(e,2)===1){if(b)throw new T("Primary key has to be unique.");b=!0}}if(!b)throw new T("Primary key has to be in the keyset and has to be enabled.");this.g=a};xj.prototype.C=function(a,b){var c=this,d;return C(function(e){if(e.h==1){if(!a)throw new W("primitive type must be non-null");return B(e,yj(c,a,b),2)}d=e.g;var g=e.return;if(!d)throw new T("primitive set cannot be null.");var f=d.u(),h=qh.get(f);if(!h)throw new T("no primitive wrapper found for type "+f);h=h.wrap(d);if(!(h instanceof f))throw new TypeError("Unexpected type");return g.call(e,h)})};var yj=function(a,b,c){var d,e,g,f,h,k,l,n;return C(function(p){switch(p.h){case 1:d=new vj(b),e=Rg(a.g),g=e.length,f=0;case 2:if(!(f<g)){p.h=4;break}h=e[f];if(P(h,2)!==1){p.h=3;break}k=M(h,U,1);if(!k)throw new T("Key data has to be non null.");l=void 0;return c&&c.A()===O(k,1)?B(p,c.C(b,k),9):B(p,th(b,k),8);case 8:l=p.g;p.h=7;break;case 9:l=p.g;case 7:var r=d,t=l;if(!t)throw new T("Primitive has to be non null.");if(!h)throw new T("Key has to be non null.");a:switch(P(h,4)){case 2:case 4:var u=Uh(N(h,3),0);break a;case 1:u=Uh(N(h,3),1);break a;case 3:u=Vh;break a;default:throw new T("Unsupported key prefix type.");}t=new uj(t,u,P(h,2),P(h,4));u=[].concat(qa(t.g)).toString();var v=wj(r,u);v?(v.push(t),r.g.set(u,v)):r.g.set(u,[t]);n=t;if(N(h,3)===N(a.g,1)){if(!n)throw new T("Primary cannot be set to null.");if(n.h!=1)throw new T("Primary has to be enabled.");r=Xh(d,n.g);t=!1;u=r.length;for(v=0;v<u;v++)if(r[v].h===1){t=!0;break}if(!t)throw new T("Primary cannot be set to an entry which is not held by this primitive set.");d.h=n}case 3:f++;p.h=2;break;case 4:return p.return(d)}})};xj.prototype.write=function(){return C(function(){throw new T("KeysetHandle -- write: Not implemented yet.");})};function zj(a){if(a===null)throw new T("Reader has to be non-null.");a=a.read();for(var b=A(Rg(a)),c=b.next();!c.done;c=b.next()){c=M(c.value,U,1);if(c==null)throw new T("Key data has to be non null.");c=P(c,3);if(c===0||c===1||c===2)throw new T("Keyset contains secret key material.");}return new xj(a)};function Aj(a){return zj({read:function(){return Sg(a)},Ja:function(){throw new T("Not implemented yet.");}})};function Bj(a){var b=new Zf;var c=a.topic;if(c!=null){if(typeof c!=="number")throw Cb("int32");if(!vc(c))throw Cb("int32");c|=0}b=ld(b,1,c,0);b=sd(b,2,a.version);b=sd(b,3,a.configVersion);b=sd(b,4,a.modelVersion);return sd(b,5,a.taxonomyVersion)}function Cj(a,b,c){c=c===void 0?document:c;var d,e,g,f,h;return C(function(k){if(k.h==1){d=c;if(typeof d.browsingTopics!=="function")return uf(a.g,2),k.return();e=a.data;g=!!e.skipTopicsObservation;return B(k,d.browsingTopics({skipObservation:g}),2)}if(k.h!=3){f=k.g;if(!f.length)return uf(a.g,3),k.return();var l=b.encrypt,n=new TextEncoder,p=n.encode,r=new $f;var t=f.map(Bj);ed(r);var u=r.j,v=u[E]|0;if(t==null)id(u,v,1);else{for(var x=t===Ob?7:t[E]|0,K=x,ra=jd(x),ha=ra||Object.isFrozen(t),wb=!0,xb=!0,Ea=0;Ea<t.length;Ea++){var sa=t[Ea];ra||(sa=G(sa),wb&&(wb=!sa),xb&&(xb=sa))}ra||(x=wb?13:5,x=xb?x&-4097:x|4096);ha&&x===K||(t=Array.prototype.slice.call(t),K=0,x=pd(x,v));x!==K&&F(t,x);v=id(u,v,1,t);2&x||!(4096&x||16&x)||fd(u,v)}r=JSON.stringify(Tc(r));return B(k,l.call(b,p.call(n,r)),3)}h=k.g;uf(a.g,h,[h.buffer]);k.h=0})};(function(a){var b=new Of;Sf(b,977,function(){var c,d,e;return C(function(g){if(g.h==1){c=window.document.referrer;if(!c)return b.g(988,Error()),g.return();rh(new Mh);rh(new Sh);uh(new Zh);rh(new qi);rh(new ki);uh(new si);rh(new sj);rh(new qj);return B(g,Aj(a).C(ci),2)}d=g.g;e=Qf(b,function(f){var h=h===void 0?document:h;var k=f.data;if(k==="goog:topics:frame:handshake:ack")uf(f.g,"goog:topics:frame:handshake:ack");else switch(k.message){case "goog:spam:client_age":Sf(b,1062,Yf(k.pvsid,k.source,k.useSavedQuery));uf(f.g,"goog:spam:client_age:ack");break;case "goog:topics:frame:get:topics":Sf(b,1002,Cj(f,d,h))}});xf({destination:window.parent,origin:c,ia:"goog:gRpYw:doubleclick",onMessage:e});g.h=0})}())})('[*********,[[["type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey","EkQKBAgCEAMSOhI4CjB0eXBlLmdvb2dsZWFwaXMuY29tL2dvb2dsZS5jcnlwdG8udGluay5BZXNHY21LZXkSAhAQGAEYARog5z+eqzXpk2SD9nfrNTaxxgy+IUFKl+FdJ63gJJGsZMoiIE5SAUd01WBkOowgnddBWBB/yDCFCoqOXYIQ5G9azhRe",3],1,*********,1],[["type.googleapis.com/google.crypto.tink.EciesAeadHkdfPublicKey","EkQKBAgCEAMSOhI4CjB0eXBlLmdvb2dsZWFwaXMuY29tL2dvb2dsZS5jcnlwdG8udGluay5BZXNHY21LZXkSAhAQGAEYARogJVp08rg6Vy/61mR3gbBGw8w3+JLaZnZPs9RrcQ0CDfQiILH3g6blf7mw4V7a9NdkNe0d1PVTGNtNUX7jZmXPnXZs",3],1,2623294582,1]]]');
    </script>
  </head>
  <body>

<fencedframe id="ps_caff" style="border: 0px;"></fencedframe></body></html>