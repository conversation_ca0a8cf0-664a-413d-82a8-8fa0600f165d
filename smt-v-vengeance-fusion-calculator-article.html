<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.smt-interface {
    background: linear-gradient(135deg, #8e24aa 0%, #5e35b1 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.demon-selector {
    background-color: #f3e5f5;
    border: 2px solid #8e24aa;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.demon-card {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 15px;
    margin: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    width: 140px;
}
.demon-card:hover {
    border-color: #8e24aa;
    transform: translateY(-3px);
}
.demon-card.selected {
    border-color: #8e24aa;
    background-color: #f3e5f5;
}
.fusion-result {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.race-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    margin: 3px;
    color: white;
}
.herald { background-color: #ffd700; color: #333; }
.megami { background-color: #ff69b4; }
.avian { background-color: #87ceeb; color: #333; }
.divine { background-color: #daa520; }
.yoma { background-color: #9370db; }
.nymph { background-color: #98fb98; color: #333; }
.vile { background-color: #8b0000; }
.raptor { background-color: #ff4500; }
.wood { background-color: #228b22; }
.deity { background-color: #4169e1; }
.avatar { background-color: #ff1493; }
.holy { background-color: #fffacd; color: #333; }
.genma { background-color: #dc143c; }
.fairy { background-color: #ffb6c1; color: #333; }
.beast { background-color: #8b4513; }
.jirae { background-color: #daa520; }
.fiend { background-color: #2f4f4f; }
.femme { background-color: #ff69b4; }
.brute { background-color: #a0522d; }
.fallen { background-color: #483d8b; }
.tyrant { background-color: #8b0000; }
.night { background-color: #191970; }
.snake { background-color: #006400; }
.dragon { background-color: #b22222; }
.kishin { background-color: #ff4500; }
.lady { background-color: #dda0dd; }
.skill-inheritance {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.level-display {
    background-color: #e8f5e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 15px;
    margin: 15px 0;
    text-align: center;
}
</style>

<h1>SMT V Vengeance Fusion Calculator</h1>
<p>Calculate demon fusion combinations in Shin Megami Tensei V: Vengeance with our comprehensive fusion calculator. Discover optimal fusion paths, plan your demon team compositions, and master the complex fusion mechanics of SMT V.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="smtform" id="smt-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Fusion Calculator</a></li>
                <li><a href="#" data-tab="demons">Demon Database</a></li>
                <li><a href="#" data-tab="skills">Skill Inheritance</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="smt-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">👹 SMT V Vengeance Fusion Calculator</h3>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div class="demon-selector">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">👹 First Demon</h4>
                        <div>
                            <label for="demon1-race"><strong>Race:</strong></label>
                            <select id="demon1-race" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Select Race</option>
                                <option value="herald">Herald</option>
                                <option value="megami">Megami</option>
                                <option value="avian">Avian</option>
                                <option value="divine">Divine</option>
                                <option value="yoma">Yoma</option>
                                <option value="nymph">Nymph</option>
                                <option value="vile">Vile</option>
                                <option value="raptor">Raptor</option>
                                <option value="wood">Wood</option>
                                <option value="deity">Deity</option>
                                <option value="avatar">Avatar</option>
                                <option value="holy">Holy</option>
                                <option value="genma">Genma</option>
                                <option value="fairy">Fairy</option>
                                <option value="beast">Beast</option>
                                <option value="jirae">Jirae</option>
                                <option value="fiend">Fiend</option>
                                <option value="femme">Femme</option>
                                <option value="brute">Brute</option>
                                <option value="fallen">Fallen</option>
                                <option value="tyrant">Tyrant</option>
                                <option value="night">Night</option>
                                <option value="snake">Snake</option>
                                <option value="dragon">Dragon</option>
                                <option value="kishin">Kishin</option>
                                <option value="lady">Lady</option>
                            </select>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="demon1-name"><strong>Demon:</strong></label>
                            <select id="demon1-name" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Select Demon</option>
                            </select>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="demon1-level"><strong>Level:</strong></label>
                            <input type="number" id="demon1-level" value="1" min="1" max="99" class="innormal" style="width: 80px; margin-left: 10px;">
                        </div>
                    </div>
                    
                    <div class="demon-selector">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">👹 Second Demon</h4>
                        <div>
                            <label for="demon2-race"><strong>Race:</strong></label>
                            <select id="demon2-race" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Select Race</option>
                                <option value="herald">Herald</option>
                                <option value="megami">Megami</option>
                                <option value="avian">Avian</option>
                                <option value="divine">Divine</option>
                                <option value="yoma">Yoma</option>
                                <option value="nymph">Nymph</option>
                                <option value="vile">Vile</option>
                                <option value="raptor">Raptor</option>
                                <option value="wood">Wood</option>
                                <option value="deity">Deity</option>
                                <option value="avatar">Avatar</option>
                                <option value="holy">Holy</option>
                                <option value="genma">Genma</option>
                                <option value="fairy">Fairy</option>
                                <option value="beast">Beast</option>
                                <option value="jirae">Jirae</option>
                                <option value="fiend">Fiend</option>
                                <option value="femme">Femme</option>
                                <option value="brute">Brute</option>
                                <option value="fallen">Fallen</option>
                                <option value="tyrant">Tyrant</option>
                                <option value="night">Night</option>
                                <option value="snake">Snake</option>
                                <option value="dragon">Dragon</option>
                                <option value="kishin">Kishin</option>
                                <option value="lady">Lady</option>
                            </select>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="demon2-name"><strong>Demon:</strong></label>
                            <select id="demon2-name" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Select Demon</option>
                            </select>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="demon2-level"><strong>Level:</strong></label>
                            <input type="number" id="demon2-level" value="1" min="1" max="99" class="innormal" style="width: 80px; margin-left: 10px;">
                        </div>
                    </div>
                </div>
                
                <div class="level-display">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🔮 Fusion Options</h4>
                    <div>
                        <label for="fusion-type"><strong>Fusion Type:</strong></label>
                        <select id="fusion-type" class="innormal" style="width: 200px; margin-left: 10px;">
                            <option value="normal">Normal Fusion</option>
                            <option value="special">Special Fusion</option>
                            <option value="reverse">Reverse Fusion</option>
                            <option value="accident">Fusion Accident</option>
                        </select>
                    </div>
                    
                    <div style="margin-top: 15px;">
                        <label for="moon-phase"><strong>Moon Phase:</strong></label>
                        <select id="moon-phase" class="innormal" style="width: 150px; margin-left: 10px;">
                            <option value="new">New Moon</option>
                            <option value="waxing">Waxing Moon</option>
                            <option value="full">Full Moon</option>
                            <option value="waning">Waning Moon</option>
                        </select>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Fusion" id="calculate-smt-btn" style="padding: 15px 30px; font-size: 18px; background-color: #8e24aa; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Clear Selection" id="clear-smt-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Fusion results are based on SMT V: Vengeance mechanics. Some special fusions may require specific conditions or items.
        </div>
        
        <div id="smt-results" style="display: none; margin-top: 30px;">
            <div class="fusion-result">
                <h3 style="margin-bottom: 20px;">✨ Fusion Results</h3>
                <div id="smt-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-smt-btn');
    const clearBtn = document.getElementById('clear-smt-btn');
    const resultsDiv = document.getElementById('smt-results');
    const resultsContent = document.getElementById('smt-results-content');
    
    // Sample demon data (simplified for demonstration)
    const demonData = {
        herald: [
            { name: 'Daitengu', level: 40, skills: ['Wind Cutter', 'Magarula', 'Resist Elec'] },
            { name: 'Sandalphon', level: 75, skills: ['Megidolaon', 'Salvation', 'Enduring Soul'] }
        ],
        megami: [
            { name: 'Pixie', level: 2, skills: ['Zio', 'Dia', 'Patra'] },
            { name: 'Angel', level: 9, skills: ['Hama', 'Media', 'Bless Pleroma'] },
            { name: 'Lakshmi', level: 69, skills: ['Luster Candy', 'Mediarahan', 'Null Dark'] }
        ],
        avian: [
            { name: 'Moh Shuvuu', level: 15, skills: ['Bufu', 'Lunge', 'Ice Pleroma'] },
            { name: 'Garuda', level: 52, skills: ['Garudyne', 'Wind Pleroma', 'Null Force'] }
        ],
        divine: [
            { name: 'Principality', level: 16, skills: ['Mahama', 'Mediarama', 'Bless Pleroma'] },
            { name: 'Throne', level: 51, skills: ['Mahamaon', 'Salvation', 'Enduring Soul'] }
        ],
        fiend: [
            { name: 'Matador', level: 19, skills: ['Andalucia', 'Sukukaja', 'Force Pleroma'] },
            { name: 'Hell Biker', level: 37, skills: ['Hell Thrust', 'Rakukaja', 'Phys Pleroma'] },
            { name: 'Trumpeter', level: 86, skills: ['Apocalypse', 'Debilitate', 'Almighty Pleroma'] }
        ]
    };
    
    // Fusion chart (simplified)
    const fusionChart = {
        'herald+megami': 'divine',
        'herald+avian': 'divine',
        'megami+avian': 'divine',
        'megami+divine': 'herald',
        'avian+divine': 'megami',
        'herald+divine': 'avian',
        'fiend+herald': 'tyrant',
        'fiend+megami': 'fallen',
        'fiend+avian': 'fallen'
    };
    
    // Populate demon dropdowns when race is selected
    document.getElementById('demon1-race').addEventListener('change', function() {
        populateDemonDropdown('demon1-name', this.value);
    });
    
    document.getElementById('demon2-race').addEventListener('change', function() {
        populateDemonDropdown('demon2-name', this.value);
    });
    
    function populateDemonDropdown(dropdownId, race) {
        const dropdown = document.getElementById(dropdownId);
        dropdown.innerHTML = '<option value="">Select Demon</option>';
        
        if (race && demonData[race]) {
            demonData[race].forEach(demon => {
                const option = document.createElement('option');
                option.value = demon.name.toLowerCase().replace(/\s+/g, '_');
                option.textContent = `${demon.name} (Lv.${demon.level})`;
                dropdown.appendChild(option);
            });
        }
    }
    
    calculateBtn.addEventListener('click', function() {
        const race1 = document.getElementById('demon1-race').value;
        const race2 = document.getElementById('demon2-race').value;
        const demon1 = document.getElementById('demon1-name').value;
        const demon2 = document.getElementById('demon2-name').value;
        const level1 = parseInt(document.getElementById('demon1-level').value) || 1;
        const level2 = parseInt(document.getElementById('demon2-level').value) || 1;
        const fusionType = document.getElementById('fusion-type').value;
        const moonPhase = document.getElementById('moon-phase').value;
        
        if (!race1 || !race2) {
            alert('Please select both demon races.');
            return;
        }
        
        if (!demon1 || !demon2) {
            alert('Please select both demons.');
            return;
        }
        
        const fusionResult = calculateFusion(race1, race2, demon1, demon2, level1, level2, fusionType, moonPhase);
        displayFusionResult(fusionResult);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('smt-calculator-form').reset();
        document.getElementById('demon1-name').innerHTML = '<option value="">Select Demon</option>';
        document.getElementById('demon2-name').innerHTML = '<option value="">Select Demon</option>';
        resultsDiv.style.display = 'none';
    });
    
    function calculateFusion(race1, race2, demon1, demon2, level1, level2, fusionType, moonPhase) {
        // Create fusion key
        const fusionKey1 = `${race1}+${race2}`;
        const fusionKey2 = `${race2}+${race1}`;
        
        let resultRace = fusionChart[fusionKey1] || fusionChart[fusionKey2];
        
        if (!resultRace) {
            // Default fusion logic for unknown combinations
            const raceList = ['herald', 'megami', 'avian', 'divine', 'yoma', 'nymph', 'vile', 'raptor'];
            const index1 = raceList.indexOf(race1);
            const index2 = raceList.indexOf(race2);
            
            if (index1 !== -1 && index2 !== -1) {
                const resultIndex = (index1 + index2) % raceList.length;
                resultRace = raceList[resultIndex];
            } else {
                resultRace = 'megami'; // Default
            }
        }
        
        // Calculate result level
        const averageLevel = Math.floor((level1 + level2) / 2);
        let resultLevel = averageLevel;
        
        // Moon phase modifiers
        const moonModifiers = {
            new: -2,
            waxing: 0,
            full: +3,
            waning: -1
        };
        
        resultLevel += moonModifiers[moonPhase] || 0;
        resultLevel = Math.max(1, Math.min(99, resultLevel));
        
        // Get available demons from result race
        const availableDemons = demonData[resultRace] || demonData.megami;
        
        // Find demon closest to result level
        let resultDemon = availableDemons[0];
        let levelDifference = Math.abs(resultDemon.level - resultLevel);
        
        availableDemons.forEach(demon => {
            const diff = Math.abs(demon.level - resultLevel);
            if (diff < levelDifference) {
                levelDifference = diff;
                resultDemon = demon;
            }
        });
        
        // Calculate inherited skills
        const demon1Data = findDemonData(race1, demon1);
        const demon2Data = findDemonData(race2, demon2);
        const inheritedSkills = calculateSkillInheritance(demon1Data, demon2Data, resultDemon);
        
        return {
            race: resultRace,
            demon: resultDemon,
            level: resultLevel,
            fusionType: fusionType,
            moonPhase: moonPhase,
            materials: [{ race: race1, name: demon1, level: level1 }, { race: race2, name: demon2, level: level2 }],
            inheritedSkills: inheritedSkills
        };
    }
    
    function findDemonData(race, demonName) {
        if (!demonData[race]) return null;
        return demonData[race].find(demon => 
            demon.name.toLowerCase().replace(/\s+/g, '_') === demonName
        );
    }
    
    function calculateSkillInheritance(demon1, demon2, resultDemon) {
        const skills = [];
        
        // Add result demon's natural skills
        skills.push(...resultDemon.skills);
        
        // Add some skills from parent demons
        if (demon1 && demon1.skills) {
            skills.push(demon1.skills[0]); // First skill from demon1
        }
        
        if (demon2 && demon2.skills) {
            skills.push(demon2.skills[0]); // First skill from demon2
        }
        
        // Remove duplicates and limit to 8 skills
        return [...new Set(skills)].slice(0, 8);
    }
    
    function displayFusionResult(result) {
        const raceClass = result.race;
        const capitalizedRace = result.race.charAt(0).toUpperCase() + result.race.slice(1);
        
        let html = `
            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 25px;">
                <div style="text-align: center; margin: 0 15px;">
                    <h4>${result.materials[0].name}</h4>
                    <div class="race-badge ${result.materials[0].race}">${result.materials[0].race.toUpperCase()}</div>
                    <div>Lv.${result.materials[0].level}</div>
                </div>
                
                <div style="font-size: 24px; margin: 0 20px;">+</div>
                
                <div style="text-align: center; margin: 0 15px;">
                    <h4>${result.materials[1].name}</h4>
                    <div class="race-badge ${result.materials[1].race}">${result.materials[1].race.toUpperCase()}</div>
                    <div>Lv.${result.materials[1].level}</div>
                </div>
                
                <div style="font-size: 24px; margin: 0 20px;">=</div>
                
                <div style="text-align: center; margin: 0 15px; padding: 20px; background-color: rgba(255,255,255,0.2); border-radius: 10px;">
                    <h3 style="margin: 0;">${result.demon.name}</h3>
                    <div class="race-badge ${raceClass}">${capitalizedRace}</div>
                    <div style="margin-top: 10px;">Level ${result.level}</div>
                </div>
            </div>
            
            <div class="skill-inheritance">
                <h4 style="color: #8e24aa; margin-bottom: 15px;">🎯 Inherited Skills</h4>
                <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 15px;">
        `;
        
        result.inheritedSkills.forEach(skill => {
            html += `<span style="background-color: #8e24aa; color: white; padding: 6px 12px; border-radius: 15px; font-size: 14px;">${skill}</span>`;
        });
        
        html += `
                </div>
                
                <div style="margin-top: 20px; padding: 15px; background-color: #f5f5f5; border-radius: 8px;">
                    <h5 style="color: #2c3e50;">Fusion Details:</h5>
                    <ul style="color: #2c3e50; margin: 10px 0;">
                        <li><strong>Fusion Type:</strong> ${result.fusionType.charAt(0).toUpperCase() + result.fusionType.slice(1)} Fusion</li>
                        <li><strong>Moon Phase:</strong> ${result.moonPhase.charAt(0).toUpperCase() + result.moonPhase.slice(1)} Moon</li>
                        <li><strong>Base Level:</strong> ${Math.floor((result.materials[0].level + result.materials[1].level) / 2)}</li>
                        <li><strong>Final Level:</strong> ${result.level}</li>
                    </ul>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 Fusion Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Moon Phases:</strong> Full Moon gives +3 levels, New Moon gives -2 levels</li>
                    <li><strong>Skill Inheritance:</strong> Higher level demons can inherit more skills</li>
                    <li><strong>Special Fusions:</strong> Some demons require specific fusion recipes</li>
                    <li><strong>Race Combinations:</strong> Different race pairs produce different results</li>
                    <li><strong>Level Planning:</strong> Plan demon levels for optimal fusion outcomes</li>
                    <li><strong>Skill Transfer:</strong> Some skills cannot be inherited by certain races</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
