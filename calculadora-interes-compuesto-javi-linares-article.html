<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.compound-interface {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.investment-input {
    background-color: #e8f5e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.compound-result {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.growth-chart {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.javi-linares-brand {
    background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    color: white;
    text-align: center;
}
.year-breakdown {
    background-color: #f1f8e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
</style>

<h1>Calculadora Interés Compuesto - Javi Linares</h1>
<p>Calcula el crecimiento de tus inversiones con interés compuesto usando la calculadora de Javi Linares. Analiza el poder del interés compuesto, planifica tus inversiones a largo plazo y descubre cómo hacer crecer tu patrimonio financiero.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="compoundform" id="compound-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Calculadora</a></li>
                <li><a href="#" data-tab="growth">Crecimiento</a></li>
                <li><a href="#" data-tab="strategies">Estrategias</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="javi-linares-brand">
                <h3 style="margin: 0;">💰 Javi Linares - Educación Financiera</h3>
                <div style="font-size: 14px; margin-top: 5px;">Calculadora de Interés Compuesto Profesional</div>
            </div>
            
            <div class="compound-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">📈 Calculadora de Interés Compuesto</h3>
                
                <div class="investment-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">💵 Datos de Inversión</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="capital-inicial"><strong>Capital Inicial:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="capital-inicial" value="10000" min="100" max="10000000" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="tasa-interes"><strong>Tasa de Interés Anual:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <input type="number" id="tasa-interes" value="8" min="0.1" max="50" step="0.1" class="innormal" style="width: 100%;">
                                <span style="margin-left: 5px;">%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="periodo-anos"><strong>Período de Inversión:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <input type="number" id="periodo-anos" value="10" min="1" max="50" class="innormal" style="width: 100%;">
                                <span style="margin-left: 5px;">años</span>
                            </div>
                        </div>
                        
                        <div>
                            <label for="frecuencia-capitalizacion"><strong>Frecuencia de Capitalización:</strong></label>
                            <select id="frecuencia-capitalizacion" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="1">Anual</option>
                                <option value="2">Semestral</option>
                                <option value="4">Trimestral</option>
                                <option value="12" selected>Mensual</option>
                                <option value="365">Diaria</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div class="year-breakdown">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">💰 Aportes Adicionales</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="aporte-mensual"><strong>Aporte Mensual:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="aporte-mensual" value="500" min="0" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="incremento-anual"><strong>Incremento Anual de Aportes:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <input type="number" id="incremento-anual" value="3" min="0" max="20" step="0.1" class="innormal" style="width: 100%;">
                                <span style="margin-left: 5px;">%</span>
                            </div>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <label for="inflacion"><strong>Tasa de Inflación Estimada:</strong></label>
                        <div style="display: flex; align-items: center; margin-top: 5px; width: 200px;">
                            <input type="number" id="inflacion" value="3" min="0" max="20" step="0.1" class="innormal" style="width: 100%;">
                            <span style="margin-left: 5px;">%</span>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calcular Interés Compuesto" id="calculate-compound-btn" style="padding: 15px 30px; font-size: 18px; background-color: #4caf50; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Limpiar Calculadora" id="clear-compound-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Nota:</strong> Esta calculadora proporciona estimaciones con fines educativos. Los resultados reales pueden variar según las condiciones del mercado.
        </div>
        
        <div id="compound-results" style="display: none; margin-top: 30px;">
            <div class="compound-result">
                <h3 style="margin-bottom: 20px;">📊 Resultados del Interés Compuesto</h3>
                <div id="compound-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-compound-btn');
    const clearBtn = document.getElementById('clear-compound-btn');
    const resultsDiv = document.getElementById('compound-results');
    const resultsContent = document.getElementById('compound-results-content');
    
    calculateBtn.addEventListener('click', function() {
        const capitalInicial = parseFloat(document.getElementById('capital-inicial').value) || 0;
        const tasaInteres = parseFloat(document.getElementById('tasa-interes').value) || 0;
        const periodoAnos = parseInt(document.getElementById('periodo-anos').value) || 0;
        const frecuenciaCapitalizacion = parseInt(document.getElementById('frecuencia-capitalizacion').value) || 12;
        const aporteMensual = parseFloat(document.getElementById('aporte-mensual').value) || 0;
        const incrementoAnual = parseFloat(document.getElementById('incremento-anual').value) || 0;
        const inflacion = parseFloat(document.getElementById('inflacion').value) || 0;
        
        if (capitalInicial <= 0 || tasaInteres <= 0 || periodoAnos <= 0) {
            alert('Por favor ingrese valores válidos para capital inicial, tasa de interés y período.');
            return;
        }
        
        const compoundData = calculateCompoundInterest(capitalInicial, tasaInteres, periodoAnos, 
            frecuenciaCapitalizacion, aporteMensual, incrementoAnual, inflacion);
        
        displayCompoundResults(compoundData);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('compound-calculator-form').reset();
        resultsDiv.style.display = 'none';
    });
    
    function calculateCompoundInterest(principal, rate, years, frequency, monthlyContribution, 
                                     annualIncrease, inflation) {
        
        const monthlyRate = rate / 100 / 12;
        const totalMonths = years * 12;
        
        let balance = principal;
        let totalContributions = principal;
        let yearlyData = [];
        let currentMonthlyContribution = monthlyContribution;
        
        for (let year = 1; year <= years; year++) {
            let yearStartBalance = balance;
            let yearContributions = 0;
            
            // Calculate monthly for this year
            for (let month = 1; month <= 12; month++) {
                // Add monthly contribution
                balance += currentMonthlyContribution;
                yearContributions += currentMonthlyContribution;
                totalContributions += currentMonthlyContribution;
                
                // Apply compound interest
                if (frequency === 365) {
                    // Daily compounding approximation
                    const dailyRate = rate / 100 / 365;
                    for (let day = 1; day <= 30; day++) {
                        balance *= (1 + dailyRate);
                    }
                } else {
                    // Monthly compounding
                    balance *= (1 + monthlyRate);
                }
            }
            
            // Calculate real value (adjusted for inflation)
            const inflationAdjustedValue = balance / Math.pow(1 + inflation / 100, year);
            
            yearlyData.push({
                year: year,
                balance: balance,
                realValue: inflationAdjustedValue,
                yearContributions: yearContributions,
                interestEarned: balance - yearStartBalance - yearContributions,
                totalContributions: totalContributions
            });
            
            // Increase monthly contribution for next year
            currentMonthlyContribution *= (1 + annualIncrease / 100);
        }
        
        const finalBalance = balance;
        const totalInterestEarned = finalBalance - totalContributions;
        const effectiveRate = Math.pow(finalBalance / principal, 1 / years) - 1;
        
        return {
            capitalInicial: principal,
            tasaInteres: rate,
            periodoAnos: years,
            frecuenciaCapitalizacion: frequency,
            aporteMensual: monthlyContribution,
            incrementoAnual: annualIncrease,
            inflacion: inflation,
            montoFinal: finalBalance,
            totalAportes: totalContributions,
            interesGanado: totalInterestEarned,
            tasaEfectiva: effectiveRate * 100,
            valorReal: finalBalance / Math.pow(1 + inflation / 100, years),
            yearlyData: yearlyData
        };
    }
    
    function displayCompoundResults(data) {
        const frecuenciaNames = {
            1: 'Anual', 2: 'Semestral', 4: 'Trimestral', 12: 'Mensual', 365: 'Diaria'
        };
        
        let html = `
            <div style="font-size: 48px; font-weight: bold; margin-bottom: 20px;">
                $${data.montoFinal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
            </div>
            
            <div style="font-size: 18px; margin-bottom: 25px;">
                Valor Final después de ${data.periodoAnos} años
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Capital Inicial</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #1976d2;">
                        $${data.capitalInicial.toLocaleString()}
                    </div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Total Aportes</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #ff9800;">
                        $${data.totalAportes.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    </div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Interés Ganado</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #4caf50;">
                        $${data.interesGanado.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    </div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Valor Real</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #9c27b0;">
                        $${data.valorReal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    </div>
                    <div style="font-size: 12px; color: #666;">Ajustado por inflación</div>
                </div>
            </div>
            
            <div class="growth-chart">
                <h4 style="color: #4caf50; margin-bottom: 20px;">📈 Análisis Detallado</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h5>Parámetros de Inversión:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Tasa de Interés:</strong> ${data.tasaInteres}% anual</li>
                            <li><strong>Capitalización:</strong> ${frecuenciaNames[data.frecuenciaCapitalizacion]}</li>
                            <li><strong>Aporte Mensual:</strong> $${data.aporteMensual.toLocaleString()}</li>
                            <li><strong>Incremento Anual:</strong> ${data.incrementoAnual}%</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5>Resultados Clave:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Tasa Efectiva:</strong> ${data.tasaEfectiva.toFixed(2)}%</li>
                            <li><strong>Multiplicador:</strong> ${(data.montoFinal / data.capitalInicial).toFixed(2)}x</li>
                            <li><strong>ROI Total:</strong> ${((data.interesGanado / data.totalAportes) * 100).toFixed(1)}%</li>
                            <li><strong>Inflación:</strong> ${data.inflacion}% anual</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div style="background: white; border-radius: 10px; padding: 20px; margin: 20px 0; color: #2c3e50; max-height: 300px; overflow-y: auto;">
                <h4 style="color: #4caf50; margin-bottom: 15px;">📅 Proyección Año por Año</h4>
                <table style="width: 100%; border-collapse: collapse;">
                    <thead>
                        <tr style="background-color: #f5f5f5;">
                            <th style="padding: 10px; border: 1px solid #ddd;">Año</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">Balance</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">Aportes del Año</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">Interés Ganado</th>
                            <th style="padding: 10px; border: 1px solid #ddd;">Valor Real</th>
                        </tr>
                    </thead>
                    <tbody>
        `;
        
        data.yearlyData.forEach(yearData => {
            html += `
                <tr>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: center;">${yearData.year}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$${yearData.balance.toLocaleString(undefined, {minimumFractionDigits: 0, maximumFractionDigits: 0})}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$${yearData.yearContributions.toLocaleString(undefined, {minimumFractionDigits: 0, maximumFractionDigits: 0})}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$${yearData.interestEarned.toLocaleString(undefined, {minimumFractionDigits: 0, maximumFractionDigits: 0})}</td>
                    <td style="padding: 8px; border: 1px solid #ddd; text-align: right;">$${yearData.realValue.toLocaleString(undefined, {minimumFractionDigits: 0, maximumFractionDigits: 0})}</td>
                </tr>
            `;
        });
        
        html += `
                    </tbody>
                </table>
            </div>
            
            <div class="javi-linares-brand" style="margin-top: 20px;">
                <h4>📚 Javi Linares - Consejos de Inversión</h4>
                <p style="margin: 10px 0; font-size: 14px;">
                    El interés compuesto es la octava maravilla del mundo. Quien lo entiende, lo gana; quien no, lo paga.
                    - Albert Einstein
                </p>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 Estrategias de Javi Linares:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Empezar Temprano:</strong> El tiempo es tu mejor aliado en las inversiones</li>
                    <li><strong>Consistencia:</strong> Aportes regulares aprovechan el promedio de costos</li>
                    <li><strong>Paciencia:</strong> El interés compuesto requiere tiempo para mostrar su poder</li>
                    <li><strong>Diversificación:</strong> No pongas todos los huevos en una canasta</li>
                    <li><strong>Educación:</strong> Invierte en tu conocimiento financiero</li>
                    <li><strong>Disciplina:</strong> Mantén tus inversiones a largo plazo</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
