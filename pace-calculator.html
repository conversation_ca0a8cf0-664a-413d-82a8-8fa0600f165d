<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Pace Calculator</title>
    <meta name="description" content="Calculate running pace, time, and distance for your workouts. Free pace calculator for runners with split times and race predictions.">
    <link rel="stylesheet" href="./Carbohydrate Calculator_files/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Mobile responsive styles */
        @media screen and (max-width: 768px) {
            #contentout { width: 100% !important; padding: 0 10px; }
            #content { width: 100% !important; margin: 0; }
            #right { display: none; }
            .panel2 { width: 100% !important; padding: 5px !important; }
            table { width: 100% !important; font-size: 14px; }
            td { padding: 8px 4px !important; word-wrap: break-word; }
            input, select { width: 100% !important; max-width: 100% !important; box-sizing: border-box; font-size: 16px; }
            .inlongest, .innormal, .inlongesthalf, .in2char { width: 100% !important; }
            h1 { font-size: 24px; }
            .cinfoT { font-size: 12px; overflow-x: auto; }
            .cinfoT td { padding: 4px 2px !important; }
            #topmenu ul li { display: inline-block; margin: 2px; font-size: 12px; }
        }
        @media screen and (max-width: 480px) {
            table td:first-child { width: 30% !important; }
            table td:last-child { width: 70% !important; }
            input[type="submit"], input[type="button"] {
                width: 100% !important; margin: 5px 0 !important; padding: 12px !important; font-size: 14px;
            }
            .in2char { width: 30% !important; display: inline-block; margin: 0 2px; }
        }
    </style>
    <link rel="apple-touch-icon" sizes="180x180" href="https://d26tpo4cm8sb6k.cloudfront.net/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-16x16.png">
    <link rel="manifest" href="https://www.calculator.net/manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="https://www.calculator.net/"><img src="./Carbohydrate Calculator_files/calculator-white.svg" width="208" height="22" alt="Calculator.net"></a></div>
        <div id="login"><a href="https://www.calculator.net/my-account/sign-in.php">sign in</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/" itemprop="item"><span itemprop="name">home</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/fitness-and-health-calculator.html" itemprop="item"><span itemprop="name">fitness &amp; health</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="#" itemprop="item"><span itemprop="name">pace calculator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="window.print();return false;">Print</a></div>
        
        <h1>Pace Calculator</h1>
        <p>The <i>Pace Calculator</i> can be used to calculate your pace when running, walking, cycling, or for any activity where you want to determine the relationship between pace, distance, and time.</p>
        
        <div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
        <span id="ucframe"></span>
        
        <div id="topmenu" class="topmenucenter">
            <ul>
                <li id="menuon"><a href="#" onclick="return showCalculator('pace');">Calculate Pace</a></li>
                <li><a href="#" onclick="return showCalculator('time');">Calculate Time</a></li>
                <li><a href="#" onclick="return showCalculator('distance');">Calculate Distance</a></li>
            </ul>
        </div>

        <div class="panel2" style="padding:10px;">
            <form name="paceForm" id="pace-calculator-form">
                <!-- Calculate Pace -->
                <div id="pace-calculator" style="display: block;">
                    <table width="620" bgcolor="#eeeeee">
                        <tbody>
                            <tr>
                                <td width="70">Distance</td>
                                <td width="550">
                                    <input type="number" name="distance_pace" id="distance_pace" value="5" class="innormal" min="0" step="0.1">
                                    <select name="distance_unit_pace" id="distance_unit_pace" class="inlong">
                                        <option value="km" selected="">kilometers</option>
                                        <option value="miles">miles</option>
                                        <option value="meters">meters</option>
                                        <option value="yards">yards</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td>Time</td>
                                <td>
                                    <input type="number" name="hours_pace" id="hours_pace" value="0" class="in2char" min="0" max="23"> hours
                                    <input type="number" name="minutes_pace" id="minutes_pace" value="25" class="in2char" min="0" max="59"> minutes
                                    <input type="number" name="seconds_pace" id="seconds_pace" value="0" class="in2char" min="0" max="59"> seconds
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Calculate Time -->
                <div id="time-calculator" style="display: none;">
                    <table width="620" bgcolor="#eeeeee">
                        <tbody>
                            <tr>
                                <td width="70">Distance</td>
                                <td width="550">
                                    <input type="number" name="distance_time" id="distance_time" value="10" class="innormal" min="0" step="0.1">
                                    <select name="distance_unit_time" id="distance_unit_time" class="inlong">
                                        <option value="km" selected="">kilometers</option>
                                        <option value="miles">miles</option>
                                        <option value="meters">meters</option>
                                        <option value="yards">yards</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td>Pace</td>
                                <td>
                                    <input type="number" name="pace_minutes" id="pace_minutes" value="5" class="in2char" min="0" max="59"> minutes
                                    <input type="number" name="pace_seconds" id="pace_seconds" value="0" class="in2char" min="0" max="59"> seconds
                                    per 
                                    <select name="pace_unit" id="pace_unit" class="innormal">
                                        <option value="km" selected="">km</option>
                                        <option value="mile">mile</option>
                                    </select>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Calculate Distance -->
                <div id="distance-calculator" style="display: none;">
                    <table width="620" bgcolor="#eeeeee">
                        <tbody>
                            <tr>
                                <td width="70">Time</td>
                                <td width="550">
                                    <input type="number" name="hours_distance" id="hours_distance" value="1" class="in2char" min="0" max="23"> hours
                                    <input type="number" name="minutes_distance" id="minutes_distance" value="0" class="in2char" min="0" max="59"> minutes
                                    <input type="number" name="seconds_distance" id="seconds_distance" value="0" class="in2char" min="0" max="59"> seconds
                                </td>
                            </tr>
                            <tr>
                                <td>Pace</td>
                                <td>
                                    <input type="number" name="pace_minutes_dist" id="pace_minutes_dist" value="5" class="in2char" min="0" max="59"> minutes
                                    <input type="number" name="pace_seconds_dist" id="pace_seconds_dist" value="0" class="in2char" min="0" max="59"> seconds
                                    per 
                                    <select name="pace_unit_dist" id="pace_unit_dist" class="innormal">
                                        <option value="km" selected="">km</option>
                                        <option value="mile">mile</option>
                                    </select>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <table width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">&nbsp;</td>
                            <td width="550">
                                <input type="submit" name="x" value="Calculate" id="calculate-btn">
                                <input type="button" value="Clear" id="clear-btn" onclick="clearPaceForm();">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </form>
        </div>

        <ul style="margin-top:0px;margin-left:-20px;">
            <li class="smalltext"><b>Pace:</b> Time per unit distance (e.g., 5:30 per kilometer).</li>
            <li class="smalltext"><b>Speed:</b> Distance per unit time (e.g., 12 km/h).</li>
            <li class="smalltext"><b>Split times:</b> Intermediate times during longer distances.</li>
        </ul>

        <!-- Results Display Area -->
        <div id="pace-results" style="display: none; margin-top: 20px;">
            <div class="h2result" id="pace-results-content"></div>
        </div>

        <br>
        <fieldset>
            <legend>Related</legend>
            <a href="#">Calories Burned Calculator</a> | 
            <a href="#">Target Heart Rate Calculator</a> | 
            <a href="#">BMI Calculator</a>
        </fieldset>

        <br>
        <h3>Understanding Pace</h3>
        <p>Pace is the rate of activity or movement. In running, pace is usually defined as the number of minutes it takes to cover a mile or kilometer. Pace is important in athletics and endurance sports because it helps athletes understand their performance and plan their training and racing strategies.</p>

        <h3>Common Race Distances and Times</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Distance</td>
                    <td class="cinfoHdL">Beginner</td>
                    <td class="cinfoHdL">Average</td>
                    <td class="cinfoHdL">Good</td>
                    <td class="cinfoHdL">Excellent</td>
                </tr>
                <tr><td><strong>5K</strong></td><td>30-35 min</td><td>25-30 min</td><td>20-25 min</td><td>15-20 min</td></tr>
                <tr><td><strong>10K</strong></td><td>60-70 min</td><td>50-60 min</td><td>40-50 min</td><td>30-40 min</td></tr>
                <tr><td><strong>Half Marathon</strong></td><td>2:30-3:00</td><td>2:00-2:30</td><td>1:30-2:00</td><td>1:15-1:30</td></tr>
                <tr><td><strong>Marathon</strong></td><td>5:00-6:00</td><td>4:00-5:00</td><td>3:00-4:00</td><td>2:30-3:00</td></tr>
            </tbody>
        </table>

        <h3>Pace Training Zones</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Zone</td>
                    <td class="cinfoHdL">% of 5K Pace</td>
                    <td class="cinfoHdL">Purpose</td>
                    <td class="cinfoHdL">Duration</td>
                </tr>
                <tr><td><strong>Easy/Recovery</strong></td><td>120-140%</td><td>Base building, recovery</td><td>30-90 minutes</td></tr>
                <tr><td><strong>Marathon Pace</strong></td><td>105-110%</td><td>Marathon training</td><td>20-60 minutes</td></tr>
                <tr><td><strong>Threshold</strong></td><td>100-105%</td><td>Lactate threshold</td><td>20-40 minutes</td></tr>
                <tr><td><strong>Interval</strong></td><td>95-100%</td><td>VO2 max improvement</td><td>3-8 minutes</td></tr>
                <tr><td><strong>Repetition</strong></td><td>90-95%</td><td>Speed and form</td><td>30 seconds-2 minutes</td></tr>
            </tbody>
        </table>

        <h3>Pace Conversion</h3>
        <p>Understanding the relationship between pace and speed can help you set training goals and compare performances across different units:</p>
        <ul>
            <li><strong>Pace to Speed:</strong> Speed (km/h) = 60 ÷ Pace (min/km)</li>
            <li><strong>Speed to Pace:</strong> Pace (min/km) = 60 ÷ Speed (km/h)</li>
            <li><strong>Mile to Kilometer:</strong> Multiply mile pace by 0.621 to get km pace</li>
            <li><strong>Kilometer to Mile:</strong> Multiply km pace by 1.609 to get mile pace</li>
        </ul>

        <h3>Training Tips</h3>
        <ul>
            <li>Use a GPS watch or smartphone app to track your pace during runs</li>
            <li>Practice running at different paces to develop pace awareness</li>
            <li>Start conservatively in races and gradually increase pace if feeling good</li>
            <li>Use negative splits (running the second half faster) for better performance</li>
            <li>Train at various paces to improve different energy systems</li>
            <li>Consider environmental factors (heat, wind, hills) that affect pace</li>
        </ul>
    </div>
    
    <div id="right">
        <div style="padding-top:10px; min-height:280px; text-align:center;">
            <div style="width: 300px; height: 250px; background: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; color: #666;">
                Advertisement Space
            </div>
        </div>
        
        <form name="calcSearchForm" onsubmit="return false;" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td><input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators..."></td>
                        <td><span id="bluebtn" onclick="performSearch();">Search</span></td>
                    </tr>
                    <tr>
                        <td colspan="2"><div id="calcSearchOut"></div></td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="#">Fitness and Health Calculators</a>
            </div>
            <div id="occontent">
                <a href="#">BMI Calculator</a>
                <a href="#">Body Fat Calculator</a>
                <a href="#">Calorie Calculator</a>
                <a href="#">BMR Calculator</a>
                <a href="#">Ideal Weight Calculator</a>
                <a href="#">Macro Calculator</a>
                <a href="#">Pace Calculator</a>
                <a href="#">Pregnancy Calculator</a>
                <a href="#">Heart Rate Calculator</a>
                <a href="#">More Fitness Calculators</a>
            </div>
            <div id="ocother">
                <a href="#">Financial</a> | 
                <a href="#">Fitness and Health</a> | 
                <a href="#">Math</a> | 
                <a href="#">Other</a>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="#">about us</a> | 
            <a href="#">sitemap</a> | 
            <a href="#">terms of use</a> | 
            <a href="#">privacy policy</a> &nbsp;  © 2008 - 2025 <a href="#">calculator.net</a>
        </div>
    </div>
</div>

<div class="topNavAbs">
    <a href="#">Financial</a>
    <a href="#" class="topNavOn">Fitness &amp; Health</a>
    <a href="#">Math</a>
    <a href="#">Other</a>
</div>

<script>
let currentCalculator = 'pace';

function showCalculator(calcType) {
    // Hide all calculators
    document.getElementById('pace-calculator').style.display = 'none';
    document.getElementById('time-calculator').style.display = 'none';
    document.getElementById('distance-calculator').style.display = 'none';
    
    // Show selected calculator
    document.getElementById(calcType + '-calculator').style.display = 'block';
    
    // Update menu
    const menuItems = document.querySelectorAll('#topmenu ul li');
    menuItems.forEach(item => {
        item.id = '';
        const link = item.querySelector('a');
        if (link && link.textContent.toLowerCase().includes(calcType)) {
            item.id = 'menuon';
        }
    });
    
    currentCalculator = calcType;
    return false;
}

document.getElementById('calculate-btn').addEventListener('click', function(e) {
    e.preventDefault();
    
    if (currentCalculator === 'pace') {
        calculatePace();
    } else if (currentCalculator === 'time') {
        calculateTime();
    } else if (currentCalculator === 'distance') {
        calculateDistance();
    }
});

function calculatePace() {
    const distance = parseFloat(document.getElementById('distance_pace').value);
    const distanceUnit = document.getElementById('distance_unit_pace').value;
    const hours = parseInt(document.getElementById('hours_pace').value) || 0;
    const minutes = parseInt(document.getElementById('minutes_pace').value) || 0;
    const seconds = parseInt(document.getElementById('seconds_pace').value) || 0;
    
    if (!distance || distance <= 0) {
        alert('Please enter a valid distance');
        return;
    }
    
    const totalSeconds = (hours * 3600) + (minutes * 60) + seconds;
    if (totalSeconds <= 0) {
        alert('Please enter a valid time');
        return;
    }
    
    // Convert distance to kilometers
    let distanceKm = distance;
    switch(distanceUnit) {
        case 'miles': distanceKm = distance * 1.609344; break;
        case 'meters': distanceKm = distance / 1000; break;
        case 'yards': distanceKm = distance * 0.0009144; break;
    }
    
    // Calculate pace per km and per mile
    const paceSecondsPerKm = totalSeconds / distanceKm;
    const paceSecondsPerMile = totalSeconds / (distanceKm / 1.609344);
    
    const paceKm = formatTime(paceSecondsPerKm);
    const paceMile = formatTime(paceSecondsPerMile);
    
    // Calculate speed
    const speedKmh = (distanceKm / totalSeconds) * 3600;
    const speedMph = speedKmh / 1.609344;
    
    const resultsDiv = document.getElementById('pace-results-content');
    resultsDiv.innerHTML = `
        <strong>Pace Results:</strong><br>
        Pace per kilometer: ${paceKm}<br>
        Pace per mile: ${paceMile}<br>
        Speed: ${speedKmh.toFixed(2)} km/h (${speedMph.toFixed(2)} mph)<br>
        Total time: ${formatTime(totalSeconds)}
    `;
    document.getElementById('pace-results').style.display = 'block';
}

function calculateTime() {
    const distance = parseFloat(document.getElementById('distance_time').value);
    const distanceUnit = document.getElementById('distance_unit_time').value;
    const paceMinutes = parseInt(document.getElementById('pace_minutes').value) || 0;
    const paceSeconds = parseInt(document.getElementById('pace_seconds').value) || 0;
    const paceUnit = document.getElementById('pace_unit').value;
    
    if (!distance || distance <= 0) {
        alert('Please enter a valid distance');
        return;
    }
    
    const paceSecondsPerUnit = (paceMinutes * 60) + paceSeconds;
    if (paceSecondsPerUnit <= 0) {
        alert('Please enter a valid pace');
        return;
    }
    
    // Convert distance to the pace unit
    let adjustedDistance = distance;
    if (distanceUnit === 'km' && paceUnit === 'mile') {
        adjustedDistance = distance / 1.609344;
    } else if (distanceUnit === 'miles' && paceUnit === 'km') {
        adjustedDistance = distance * 1.609344;
    } else if (distanceUnit === 'meters') {
        adjustedDistance = paceUnit === 'km' ? distance / 1000 : distance / 1609.344;
    } else if (distanceUnit === 'yards') {
        adjustedDistance = paceUnit === 'km' ? distance * 0.0009144 : distance / 1760;
    } else if (distanceUnit === 'miles' && paceUnit === 'mile') {
        adjustedDistance = distance;
    } else if (distanceUnit === 'km' && paceUnit === 'km') {
        adjustedDistance = distance;
    }
    
    const totalSeconds = adjustedDistance * paceSecondsPerUnit;
    
    const resultsDiv = document.getElementById('pace-results-content');
    resultsDiv.innerHTML = `
        <strong>Time Results:</strong><br>
        Total time: ${formatTime(totalSeconds)}<br>
        Distance: ${distance} ${distanceUnit}<br>
        Pace: ${paceMinutes}:${paceSeconds.toString().padStart(2, '0')} per ${paceUnit}
    `;
    document.getElementById('pace-results').style.display = 'block';
}

function calculateDistance() {
    const hours = parseInt(document.getElementById('hours_distance').value) || 0;
    const minutes = parseInt(document.getElementById('minutes_distance').value) || 0;
    const seconds = parseInt(document.getElementById('seconds_distance').value) || 0;
    const paceMinutes = parseInt(document.getElementById('pace_minutes_dist').value) || 0;
    const paceSeconds = parseInt(document.getElementById('pace_seconds_dist').value) || 0;
    const paceUnit = document.getElementById('pace_unit_dist').value;
    
    const totalSeconds = (hours * 3600) + (minutes * 60) + seconds;
    const paceSecondsPerUnit = (paceMinutes * 60) + paceSeconds;
    
    if (totalSeconds <= 0) {
        alert('Please enter a valid time');
        return;
    }
    
    if (paceSecondsPerUnit <= 0) {
        alert('Please enter a valid pace');
        return;
    }
    
    const distance = totalSeconds / paceSecondsPerUnit;
    
    // Convert to other units
    const distanceKm = paceUnit === 'km' ? distance : distance * 1.609344;
    const distanceMiles = paceUnit === 'mile' ? distance : distance / 1.609344;
    const distanceMeters = distanceKm * 1000;
    
    const resultsDiv = document.getElementById('pace-results-content');
    resultsDiv.innerHTML = `
        <strong>Distance Results:</strong><br>
        Distance: ${distance.toFixed(2)} ${paceUnit}s<br>
        Equivalent distances:<br>
        • ${distanceKm.toFixed(2)} kilometers<br>
        • ${distanceMiles.toFixed(2)} miles<br>
        • ${distanceMeters.toFixed(0)} meters<br>
        Time: ${formatTime(totalSeconds)}<br>
        Pace: ${paceMinutes}:${paceSeconds.toString().padStart(2, '0')} per ${paceUnit}
    `;
    document.getElementById('pace-results').style.display = 'block';
}

function formatTime(totalSeconds) {
    const hours = Math.floor(totalSeconds / 3600);
    const minutes = Math.floor((totalSeconds % 3600) / 60);
    const seconds = Math.floor(totalSeconds % 60);
    
    if (hours > 0) {
        return `${hours}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else {
        return `${minutes}:${seconds.toString().padStart(2, '0')}`;
    }
}

function clearPaceForm() {
    // Clear pace calculator
    document.getElementById('distance_pace').value = '5';
    document.getElementById('distance_unit_pace').selectedIndex = 0;
    document.getElementById('hours_pace').value = '0';
    document.getElementById('minutes_pace').value = '25';
    document.getElementById('seconds_pace').value = '0';
    
    // Clear time calculator
    document.getElementById('distance_time').value = '10';
    document.getElementById('distance_unit_time').selectedIndex = 0;
    document.getElementById('pace_minutes').value = '5';
    document.getElementById('pace_seconds').value = '0';
    document.getElementById('pace_unit').selectedIndex = 0;
    
    // Clear distance calculator
    document.getElementById('hours_distance').value = '1';
    document.getElementById('minutes_distance').value = '0';
    document.getElementById('seconds_distance').value = '0';
    document.getElementById('pace_minutes_dist').value = '5';
    document.getElementById('pace_seconds_dist').value = '0';
    document.getElementById('pace_unit_dist').selectedIndex = 0;
    
    document.getElementById('pace-results').style.display = 'none';
}

function performSearch() {
    const searchTerm = document.getElementById('calcSearchTerm').value;
    const searchOut = document.getElementById('calcSearchOut');
    
    if (searchTerm.trim() === '') {
        searchOut.innerHTML = '';
        return;
    }
    
    const calculators = [
        'BMI Calculator', 'Body Fat Calculator', 'Calorie Calculator', 
        'BMR Calculator', 'Ideal Weight Calculator', 'Pace Calculator'
    ];
    
    const results = calculators.filter(calc => 
        calc.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    if (results.length > 0) {
        searchOut.innerHTML = '<div>' + results.map(result => 
            `<a href="#" style="display: block; padding: 2px 0;">${result}</a>`
        ).join('') + '</div>';
    } else {
        searchOut.innerHTML = '<div>No calculators found</div>';
    }
}

showCalculator('pace');
</script>

</body>
</html>
