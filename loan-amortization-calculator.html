<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Loan Amortization Calculator</title>
    <meta name="description" content="Calculate loan amortization schedule with monthly payments, interest, and principal breakdown. Free loan calculator with detailed payment schedule and charts.">
    <link rel="stylesheet" href="./Carbohydrate Calculator_files/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Mobile responsive styles */
        @media screen and (max-width: 768px) {
            #contentout { width: 100% !important; padding: 0 10px; }
            #content { width: 100% !important; margin: 0; }
            #right { display: none; }
            .panel2 { width: 100% !important; padding: 5px !important; }
            table { width: 100% !important; font-size: 14px; }
            td { padding: 8px 4px !important; word-wrap: break-word; }
            input, select { width: 100% !important; max-width: 100% !important; box-sizing: border-box; }
            .inlongest, .innormal, .inlong { width: 100% !important; }
            h1 { font-size: 24px; }
            .cinfoT { font-size: 12px; }
            .cinfoT td { padding: 4px 2px !important; }
            #topmenu ul li { display: block; width: 100%; margin: 2px 0; }
        }
        @media screen and (max-width: 480px) {
            table td:first-child { width: 30% !important; }
            table td:last-child { width: 70% !important; }
            input[type="submit"], input[type="button"] {
                width: 100% !important;
                margin: 5px 0 !important;
                padding: 12px !important;
                font-size: 14px;
            }
            .topmenucenter ul li { font-size: 12px; }
        }
    </style>
    <link rel="apple-touch-icon" sizes="180x180" href="https://d26tpo4cm8sb6k.cloudfront.net/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-16x16.png">
    <link rel="manifest" href="https://www.calculator.net/manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="https://www.calculator.net/"><img src="./Carbohydrate Calculator_files/calculator-white.svg" width="208" height="22" alt="Calculator.net"></a></div>
        <div id="login"><a href="https://www.calculator.net/my-account/sign-in.php">sign in</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/" itemprop="item"><span itemprop="name">home</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/financial-calculator.html" itemprop="item"><span itemprop="name">financial</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="#" itemprop="item"><span itemprop="name">loan amortization calculator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="window.print();return false;">Print</a></div>
        
        <h1>Loan Amortization Calculator</h1>
        <p>The <i>Loan Amortization Calculator</i> generates an amortization table showing each monthly payment on an amortizing loan. It also determines out how much of your repayments will go towards the principal and how much will go towards interest.</p>
        
        <div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
        <span id="ucframe"></span>
        
        <div id="topmenu" class="topmenucenter">
            <ul>
                <li id="menuon"><a href="#" onclick="return showCalculator('basic');">Basic Calculator</a></li>
                <li><a href="#" onclick="return showCalculator('advanced');">Advanced Calculator</a></li>
                <li><a href="#" onclick="return showCalculator('schedule');">Payment Schedule</a></li>
            </ul>
        </div>

        <div class="panel2" style="padding:10px;">
            <form name="loanForm" id="loan-calculator-form">
                <!-- Basic Calculator -->
                <div id="basic-calculator" style="display: block;">
                    <table width="620" bgcolor="#eeeeee">
                        <tbody>
                            <tr>
                                <td width="120">Loan Amount</td>
                                <td width="500">$<input type="number" name="loan_amount" id="loan_amount" value="200000" class="inlongest" min="1000" max="10000000" step="1000"></td>
                            </tr>
                            <tr>
                                <td>Loan Term</td>
                                <td>
                                    <input type="number" name="loan_years" id="loan_years" value="30" class="innormal" min="1" max="50"> years
                                    <input type="number" name="loan_months" id="loan_months" value="0" class="innormal" min="0" max="11"> months
                                </td>
                            </tr>
                            <tr>
                                <td>Interest Rate</td>
                                <td><input type="number" name="interest_rate" id="interest_rate" value="6.5" class="innormal" min="0" max="30" step="0.01">% per year</td>
                            </tr>
                            <tr>
                                <td>Start Date</td>
                                <td>
                                    <select name="start_month" id="start_month" class="inlong">
                                        <option value="1" selected="">January</option>
                                        <option value="2">February</option>
                                        <option value="3">March</option>
                                        <option value="4">April</option>
                                        <option value="5">May</option>
                                        <option value="6">June</option>
                                        <option value="7">July</option>
                                        <option value="8">August</option>
                                        <option value="9">September</option>
                                        <option value="10">October</option>
                                        <option value="11">November</option>
                                        <option value="12">December</option>
                                    </select>
                                    <input type="number" name="start_year" id="start_year" value="2025" class="innormal" min="2020" max="2050">
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>

                <!-- Advanced Calculator -->
                <div id="advanced-calculator" style="display: none;">
                    <table width="620" bgcolor="#eeeeee">
                        <tbody>
                            <tr>
                                <td width="120">Loan Amount</td>
                                <td width="500">$<input type="number" name="loan_amount_adv" id="loan_amount_adv" value="200000" class="inlongest" min="1000" max="10000000" step="1000"></td>
                            </tr>
                            <tr>
                                <td>Loan Term</td>
                                <td>
                                    <input type="number" name="loan_years_adv" id="loan_years_adv" value="30" class="innormal" min="1" max="50"> years
                                    <input type="number" name="loan_months_adv" id="loan_months_adv" value="0" class="innormal" min="0" max="11"> months
                                </td>
                            </tr>
                            <tr>
                                <td>Interest Rate</td>
                                <td><input type="number" name="interest_rate_adv" id="interest_rate_adv" value="6.5" class="innormal" min="0" max="30" step="0.01">% per year</td>
                            </tr>
                            <tr>
                                <td>Extra Payment</td>
                                <td>$<input type="number" name="extra_payment" id="extra_payment" value="0" class="innormal" min="0" max="10000" step="50"> per month</td>
                            </tr>
                            <tr>
                                <td>Payment Type</td>
                                <td>
                                    <select name="payment_type" id="payment_type" class="inlongest">
                                        <option value="monthly" selected="">Monthly</option>
                                        <option value="biweekly">Bi-weekly</option>
                                        <option value="weekly">Weekly</option>
                                    </select>
                                </td>
                            </tr>
                            <tr>
                                <td>Compounding</td>
                                <td>
                                    <select name="compounding" id="compounding" class="inlongest">
                                        <option value="monthly" selected="">Monthly</option>
                                        <option value="daily">Daily</option>
                                        <option value="annually">Annually</option>
                                    </select>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                
                <table width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="120">&nbsp;</td>
                            <td width="500">
                                <input type="submit" name="x" value="Calculate Amortization" id="calculate-btn">
                                <input type="button" value="Clear" id="clear-btn" onclick="clearLoanForm();">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </form>
        </div>

        <ul style="margin-top:0px;margin-left:-20px;">
            <li class="smalltext"><b>Amortization:</b> Process of paying off debt with regular payments over time.</li>
            <li class="smalltext"><b>Principal:</b> The original loan amount that decreases with each payment.</li>
            <li class="smalltext"><b>Interest:</b> The cost of borrowing money, calculated on remaining balance.</li>
        </ul>

        <!-- Results Display Area -->
        <div id="loan-results" style="display: none; margin-top: 20px;">
            <div class="h2result" id="loan-results-content"></div>
        </div>

        <!-- Payment Schedule Table -->
        <div id="payment-schedule" style="display: none; margin-top: 20px;">
            <h3>Payment Schedule</h3>
            <div id="schedule-content"></div>
        </div>

        <br>
        <fieldset>
            <legend>Related</legend>
            <a href="#">Mortgage Calculator</a> | 
            <a href="#">Auto Loan Calculator</a> | 
            <a href="#">Personal Loan Calculator</a>
        </fieldset>

        <br>
        <h3>What is Loan Amortization?</h3>
        <p>Loan amortization is the process of paying off a debt over time through regular payments. A portion of each payment is for interest while the remaining amount is applied towards the principal balance. The percentage of interest versus principal in each payment is determined in an amortization schedule.</p>

        <h3>How Amortization Works</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Payment Period</td>
                    <td class="cinfoHdL">Interest Portion</td>
                    <td class="cinfoHdL">Principal Portion</td>
                    <td class="cinfoHdL">Remaining Balance</td>
                </tr>
                <tr><td><strong>Early Payments</strong></td><td>Higher</td><td>Lower</td><td>High</td></tr>
                <tr><td><strong>Middle Payments</strong></td><td>Moderate</td><td>Moderate</td><td>Medium</td></tr>
                <tr><td><strong>Late Payments</strong></td><td>Lower</td><td>Higher</td><td>Low</td></tr>
            </tbody>
        </table>

        <h3>Types of Amortizing Loans</h3>
        <ul>
            <li><strong>Mortgages:</strong> Home loans typically amortized over 15-30 years</li>
            <li><strong>Auto Loans:</strong> Car loans usually amortized over 3-7 years</li>
            <li><strong>Personal Loans:</strong> Unsecured loans often amortized over 2-5 years</li>
            <li><strong>Student Loans:</strong> Education loans may be amortized over 10-25 years</li>
            <li><strong>Business Loans:</strong> Commercial loans with various amortization periods</li>
        </ul>

        <h3>Benefits of Understanding Amortization</h3>
        <ul>
            <li><strong>Payment Planning:</strong> Know exactly how much you'll pay each month</li>
            <li><strong>Interest Savings:</strong> See how extra payments reduce total interest</li>
            <li><strong>Equity Building:</strong> Track how much principal you've paid down</li>
            <li><strong>Refinancing Decisions:</strong> Compare current loan with new options</li>
            <li><strong>Tax Planning:</strong> Understand deductible interest amounts</li>
            <li><strong>Budget Management:</strong> Plan for decreasing interest deductions over time</li>
        </ul>

        <h3>Strategies to Pay Off Loans Faster</h3>
        <ul>
            <li><strong>Extra Principal Payments:</strong> Add extra money to principal each month</li>
            <li><strong>Bi-weekly Payments:</strong> Make 26 payments per year instead of 12</li>
            <li><strong>Annual Lump Sum:</strong> Use tax refunds or bonuses for extra payments</li>
            <li><strong>Round Up Payments:</strong> Round monthly payments to nearest $50 or $100</li>
            <li><strong>Refinancing:</strong> Get a lower interest rate or shorter term</li>
            <li><strong>Recasting:</strong> Make large principal payment and recalculate schedule</li>
        </ul>

        <h3>Amortization vs. Other Loan Types</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Loan Type</td>
                    <td class="cinfoHdL">Payment Structure</td>
                    <td class="cinfoHdL">Principal Paydown</td>
                    <td class="cinfoHdL">Example</td>
                </tr>
                <tr><td><strong>Amortizing</strong></td><td>Fixed payments</td><td>Gradual over time</td><td>30-year mortgage</td></tr>
                <tr><td><strong>Interest-Only</strong></td><td>Interest only initially</td><td>None until later</td><td>Some mortgages</td></tr>
                <tr><td><strong>Balloon</strong></td><td>Small payments, large final</td><td>Mostly at end</td><td>Some commercial loans</td></tr>
                <tr><td><strong>Revolving</strong></td><td>Variable payments</td><td>Based on usage</td><td>Credit cards</td></tr>
            </tbody>
        </table>

        <h3>Important Considerations</h3>
        <ul>
            <li><strong>Total Interest Cost:</strong> Longer terms mean more total interest paid</li>
            <li><strong>Monthly Cash Flow:</strong> Longer terms mean lower monthly payments</li>
            <li><strong>Opportunity Cost:</strong> Consider investment returns vs. loan interest rate</li>
            <li><strong>Tax Implications:</strong> Some loan interest may be tax deductible</li>
            <li><strong>Prepayment Penalties:</strong> Some loans charge fees for early payoff</li>
            <li><strong>Insurance Requirements:</strong> Some loans require insurance coverage</li>
        </ul>
    </div>
    
    <div id="right">
        <div style="padding-top:10px; min-height:280px; text-align:center;">
            <div style="width: 300px; height: 250px; background: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; color: #666;">
                Advertisement Space
            </div>
        </div>
        
        <form name="calcSearchForm" onsubmit="return false;" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td><input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators..."></td>
                        <td><span id="bluebtn" onclick="performSearch();">Search</span></td>
                    </tr>
                    <tr>
                        <td colspan="2"><div id="calcSearchOut"></div></td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="#">Financial Calculators</a>
            </div>
            <div id="occontent">
                <a href="#">Loan Calculator</a>
                <a href="#">Mortgage Calculator</a>
                <a href="#">Auto Loan Calculator</a>
                <a href="#">Personal Loan Calculator</a>
                <a href="#">Refinance Calculator</a>
                <a href="#">Investment Calculator</a>
                <a href="#">Retirement Calculator</a>
                <a href="#">Savings Calculator</a>
                <a href="#">Interest Calculator</a>
                <a href="#">More Financial Calculators</a>
            </div>
            <div id="ocother">
                <a href="#" class="topNavOn">Financial</a> | 
                <a href="#">Fitness and Health</a> | 
                <a href="#">Math</a> | 
                <a href="#">Other</a>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="#">about us</a> | 
            <a href="#">sitemap</a> | 
            <a href="#">terms of use</a> | 
            <a href="#">privacy policy</a> &nbsp;  © 2008 - 2025 <a href="#">calculator.net</a>
        </div>
    </div>
</div>

<div class="topNavAbs">
    <a href="#" class="topNavOn">Financial</a>
    <a href="#">Fitness &amp; Health</a>
    <a href="#">Math</a>
    <a href="#">Other</a>
</div>

<script>
let currentCalculator = 'basic';

function showCalculator(calcType) {
    // Hide all calculators
    document.getElementById('basic-calculator').style.display = 'none';
    document.getElementById('advanced-calculator').style.display = 'none';
    
    // Show selected calculator
    if (calcType === 'basic' || calcType === 'schedule') {
        document.getElementById('basic-calculator').style.display = 'block';
    } else if (calcType === 'advanced') {
        document.getElementById('advanced-calculator').style.display = 'block';
    }
    
    // Update menu
    const menuItems = document.querySelectorAll('#topmenu ul li');
    menuItems.forEach(item => {
        item.id = '';
        const link = item.querySelector('a');
        if (link && link.textContent.toLowerCase().includes(calcType)) {
            item.id = 'menuon';
        }
    });
    
    currentCalculator = calcType;
    return false;
}

document.getElementById('calculate-btn').addEventListener('click', function(e) {
    e.preventDefault();
    calculateLoanAmortization();
});

function calculateLoanAmortization() {
    let loanAmount, loanYears, loanMonths, interestRate, extraPayment = 0;
    
    if (currentCalculator === 'advanced') {
        loanAmount = parseFloat(document.getElementById('loan_amount_adv').value);
        loanYears = parseInt(document.getElementById('loan_years_adv').value) || 0;
        loanMonths = parseInt(document.getElementById('loan_months_adv').value) || 0;
        interestRate = parseFloat(document.getElementById('interest_rate_adv').value);
        extraPayment = parseFloat(document.getElementById('extra_payment').value) || 0;
    } else {
        loanAmount = parseFloat(document.getElementById('loan_amount').value);
        loanYears = parseInt(document.getElementById('loan_years').value) || 0;
        loanMonths = parseInt(document.getElementById('loan_months').value) || 0;
        interestRate = parseFloat(document.getElementById('interest_rate').value);
    }
    
    if (!loanAmount || loanAmount <= 0) {
        alert('Please enter a valid loan amount');
        return;
    }
    
    if (!interestRate || interestRate < 0) {
        alert('Please enter a valid interest rate');
        return;
    }
    
    const totalMonths = (loanYears * 12) + loanMonths;
    if (totalMonths <= 0) {
        alert('Please enter a valid loan term');
        return;
    }
    
    // Calculate monthly payment
    const monthlyRate = interestRate / 100 / 12;
    let monthlyPayment;
    
    if (monthlyRate === 0) {
        monthlyPayment = loanAmount / totalMonths;
    } else {
        monthlyPayment = loanAmount * (monthlyRate * Math.pow(1 + monthlyRate, totalMonths)) / 
                        (Math.pow(1 + monthlyRate, totalMonths) - 1);
    }
    
    // Generate amortization schedule
    const schedule = generateAmortizationSchedule(loanAmount, monthlyPayment, monthlyRate, totalMonths, extraPayment);
    
    // Display results
    displayLoanResults(loanAmount, monthlyPayment, interestRate, totalMonths, schedule, extraPayment);
    
    // Show payment schedule if requested
    if (currentCalculator === 'schedule') {
        displayPaymentSchedule(schedule);
    }
}

function generateAmortizationSchedule(principal, monthlyPayment, monthlyRate, totalMonths, extraPayment) {
    const schedule = [];
    let remainingBalance = principal;
    let totalInterest = 0;
    let totalPrincipal = 0;
    let actualMonths = 0;
    
    for (let month = 1; month <= totalMonths && remainingBalance > 0.01; month++) {
        const interestPayment = remainingBalance * monthlyRate;
        let principalPayment = monthlyPayment - interestPayment + extraPayment;
        
        // Don't pay more principal than remaining balance
        if (principalPayment > remainingBalance) {
            principalPayment = remainingBalance;
        }
        
        remainingBalance -= principalPayment;
        totalInterest += interestPayment;
        totalPrincipal += principalPayment;
        actualMonths = month;
        
        schedule.push({
            month: month,
            payment: interestPayment + principalPayment,
            principal: principalPayment,
            interest: interestPayment,
            balance: remainingBalance
        });
        
        if (remainingBalance <= 0.01) break;
    }
    
    return {
        payments: schedule,
        totalInterest: totalInterest,
        totalPrincipal: totalPrincipal,
        actualMonths: actualMonths,
        totalPayments: totalInterest + totalPrincipal
    };
}

function displayLoanResults(loanAmount, monthlyPayment, interestRate, totalMonths, schedule, extraPayment) {
    const totalPayments = schedule.totalPayments;
    const totalInterest = schedule.totalInterest;
    const actualMonths = schedule.actualMonths;
    const timeSaved = totalMonths - actualMonths;
    const interestSaved = extraPayment > 0 ? calculateInterestSaved(loanAmount, monthlyPayment, interestRate / 100 / 12, totalMonths, totalInterest) : 0;
    
    const resultsDiv = document.getElementById('loan-results-content');
    resultsDiv.innerHTML = `
        <strong>Loan Amortization Summary:</strong><br>
        Loan Amount: $${loanAmount.toLocaleString()}<br>
        Interest Rate: ${interestRate}% per year<br>
        Loan Term: ${Math.floor(totalMonths / 12)} years ${totalMonths % 12} months<br>
        ${extraPayment > 0 ? `Extra Payment: $${extraPayment.toLocaleString()} per month<br>` : ''}
        <br>
        <strong>Payment Information:</strong><br>
        Monthly Payment: $${monthlyPayment.toFixed(2)}<br>
        ${extraPayment > 0 ? `Total Monthly Payment: $${(monthlyPayment + extraPayment).toFixed(2)}<br>` : ''}
        Number of Payments: ${actualMonths}<br>
        Total Interest: $${totalInterest.toFixed(2)}<br>
        Total of Payments: $${totalPayments.toFixed(2)}<br>
        ${timeSaved > 0 ? `<br><strong>Savings with Extra Payments:</strong><br>Time Saved: ${Math.floor(timeSaved / 12)} years ${timeSaved % 12} months<br>Interest Saved: $${interestSaved.toFixed(2)}<br>` : ''}
    `;
    document.getElementById('loan-results').style.display = 'block';
}

function displayPaymentSchedule(schedule) {
    let scheduleHTML = '<table class="cinfoT"><tbody>';
    scheduleHTML += '<tr><td class="cinfoHd">Payment #</td><td class="cinfoHd">Payment Amount</td><td class="cinfoHd">Principal</td><td class="cinfoHd">Interest</td><td class="cinfoHd">Balance</td></tr>';
    
    // Show first 12 payments, then every 12th payment, then last 12 payments
    const payments = schedule.payments;
    const showPayments = [];
    
    // First 12 payments
    for (let i = 0; i < Math.min(12, payments.length); i++) {
        showPayments.push(payments[i]);
    }
    
    // Every 12th payment in the middle
    if (payments.length > 24) {
        for (let i = 12; i < payments.length - 12; i += 12) {
            showPayments.push(payments[i]);
        }
    }
    
    // Last 12 payments
    if (payments.length > 12) {
        const startIndex = Math.max(12, payments.length - 12);
        for (let i = startIndex; i < payments.length; i++) {
            if (!showPayments.find(p => p.month === payments[i].month)) {
                showPayments.push(payments[i]);
            }
        }
    }
    
    showPayments.forEach(payment => {
        scheduleHTML += `<tr>
            <td>${payment.month}</td>
            <td>$${payment.payment.toFixed(2)}</td>
            <td>$${payment.principal.toFixed(2)}</td>
            <td>$${payment.interest.toFixed(2)}</td>
            <td>$${payment.balance.toFixed(2)}</td>
        </tr>`;
    });
    
    scheduleHTML += '</tbody></table>';
    scheduleHTML += `<p class="smalltext">Showing selected payments from ${payments.length} total payments. Full schedule available upon request.</p>`;
    
    document.getElementById('schedule-content').innerHTML = scheduleHTML;
    document.getElementById('payment-schedule').style.display = 'block';
}

function calculateInterestSaved(principal, monthlyPayment, monthlyRate, totalMonths, actualInterest) {
    // Calculate interest without extra payments
    let balance = principal;
    let standardInterest = 0;
    
    for (let month = 1; month <= totalMonths && balance > 0.01; month++) {
        const interest = balance * monthlyRate;
        const principalPayment = monthlyPayment - interest;
        balance -= principalPayment;
        standardInterest += interest;
        
        if (balance <= 0.01) break;
    }
    
    return standardInterest - actualInterest;
}

function clearLoanForm() {
    // Clear basic form
    document.getElementById('loan_amount').value = '200000';
    document.getElementById('loan_years').value = '30';
    document.getElementById('loan_months').value = '0';
    document.getElementById('interest_rate').value = '6.5';
    document.getElementById('start_month').selectedIndex = 0;
    document.getElementById('start_year').value = '2025';
    
    // Clear advanced form
    document.getElementById('loan_amount_adv').value = '200000';
    document.getElementById('loan_years_adv').value = '30';
    document.getElementById('loan_months_adv').value = '0';
    document.getElementById('interest_rate_adv').value = '6.5';
    document.getElementById('extra_payment').value = '0';
    document.getElementById('payment_type').selectedIndex = 0;
    document.getElementById('compounding').selectedIndex = 0;
    
    // Hide results
    document.getElementById('loan-results').style.display = 'none';
    document.getElementById('payment-schedule').style.display = 'none';
}

function performSearch() {
    const searchTerm = document.getElementById('calcSearchTerm').value;
    const searchOut = document.getElementById('calcSearchOut');
    
    if (searchTerm.trim() === '') {
        searchOut.innerHTML = '';
        return;
    }
    
    const calculators = [
        'Loan Calculator', 'Mortgage Calculator', 'Auto Loan Calculator', 
        'Personal Loan Calculator', 'Refinance Calculator', 'Investment Calculator'
    ];
    
    const results = calculators.filter(calc => 
        calc.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    if (results.length > 0) {
        searchOut.innerHTML = '<div>' + results.map(result => 
            `<a href="#" style="display: block; padding: 2px 0;">${result}</a>`
        ).join('') + '</div>';
    } else {
        searchOut.innerHTML = '<div>No calculators found</div>';
    }
}

// Initialize
showCalculator('basic');
</script>

</body>
</html>
