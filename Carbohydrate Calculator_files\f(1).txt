(function(sttc){var window=this;if(window.googletag&&googletag.evalScripts){googletag.evalScripts();}if(window.googletag&&googletag._loaded_)return;var q,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");},da=ca(this),ea=typeof Symbol==="function"&&typeof Symbol("x")==="symbol",u={},fa={},v=function(a,b,c){if(!c||a!=null){c=fa[b];if(c==null)return a[b];c=a[c];return c!==void 0?c:a[b]}},w=function(a,b,c){if(b)a:{var d=a.split(".");a=d.length===1;var e=d[0],f;!a&&e in u?f=u:f=da;for(e=0;e<d.length-1;e++){var g=d[e];if(!(g in f))break a;f=f[g]}d=d[d.length-1];c=ea&&c==="es6"?f[d]:null;b=b(c);b!=null&&(a?ba(u,d,{configurable:!0,writable:!0,value:b}):b!==c&&(fa[d]===void 0&&(a=Math.random()*1E9>>>0,fa[d]=ea?da.Symbol(d):"$jscp$"+a+"$"+d),ba(f,fa[d],{configurable:!0,writable:!0,value:b})))}};w("Symbol",function(a){if(a)return a;var b=function(f,g){this.g=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.g};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e},"es6");w("Symbol.iterator",function(a){if(a)return a;a=(0,u.Symbol)("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=da[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ha(aa(this))}})}return a},"es6");var ha=function(a){a={next:a};a[v(u.Symbol,"iterator")]=function(){return this};return a},ia=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ja;if(ea&&typeof Object.setPrototypeOf=="function")ja=Object.setPrototypeOf;else{var ka;a:{var la={a:!0},ma={};try{ma.__proto__=la;ka=ma.a;break a}catch(a){}ka=!1}ja=ka?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var na=ja,x=function(a,b){a.prototype=ia(b.prototype);a.prototype.constructor=a;if(na)na(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.Ob=b.prototype},y=function(a){var b=typeof u.Symbol!="undefined"&&v(u.Symbol,"iterator")&&a[v(u.Symbol,"iterator")];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},B=function(a){if(!(a instanceof Array)){a=y(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a},qa=function(a){return oa(a,a)},oa=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},C=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},ra=ea&&typeof v(Object,"assign")=="function"?v(Object,"assign"):function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)C(d,e)&&(a[e]=d[e])}return a};w("Object.assign",function(a){return a||ra},"es6");var sa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};w("globalThis",function(a){return a||da},"es_2020");w("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}},"es6");w("WeakMap",function(a){function b(){}function c(g){var h=typeof g;return h==="object"&&g!==null||h==="function"}if(function(){if(!a||!Object.seal)return!1;try{var g=Object.seal({}),h=Object.seal({}),k=new a([[g,2],[h,3]]);if(k.get(g)!=2||k.get(h)!=3)return!1;k.delete(g);k.set(h,4);return!k.has(g)&&k.get(h)==4}catch(m){return!1}}())return a;var d="$jscomp_hidden_"+Math.random(),e=0,f=function(g){this.g=(e+=Math.random()+1).toString();if(g){g=y(g);for(var h;!(h=g.next()).done;)h=h.value,this.set(h[0],h[1])}};f.prototype.set=function(g,h){if(!c(g))throw Error("Invalid WeakMap key");if(!C(g,d)){var k=new b;ba(g,d,{value:k})}if(!C(g,d))throw Error("WeakMap key fail: "+g);g[d][this.g]=h;return this};f.prototype.get=function(g){return c(g)&&C(g,d)?g[d][this.g]:void 0};f.prototype.has=function(g){return c(g)&&C(g,d)&&C(g[d],this.g)};f.prototype.delete=function(g){return c(g)&&C(g,d)&&C(g[d],this.g)?delete g[d][this.g]:!1};return f},"es6");w("Map",function(a){if(function(){if(!a||typeof a!="function"||!v(a.prototype,"entries")||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),k=new a(y([[h,"s"]]));if(k.get(h)!="s"||k.size!=1||k.get({x:4})||k.set({x:4},"t")!=k||k.size!=2)return!1;var m=v(k,"entries").call(k),n=m.next();if(n.done||n.value[0]!=h||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(l){return!1}}())return a;var b=new u.WeakMap,c=function(h){this[0]={};this[1]=f();this.size=0;if(h){h=y(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=h===0?0:h;var m=d(this,h);m.list||(m.list=this[0][m.id]=[]);m.u?m.u.value=k:(m.u={next:this[1],I:this[1].I,head:this[1],key:h,value:k},m.list.push(m.u),this[1].I.next=m.u,this[1].I=m.u,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.u&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.u.I.next=h.u.next,h.u.next.I=h.u.I,h.u.head=null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].I=f();this.size=0};c.prototype.has=function(h){return!!d(this,h).u};c.prototype.get=function(h){return(h=d(this,h).u)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=function(h,k){for(var m=v(this,"entries").call(this),n;!(n=m.next()).done;)n=n.value,h.call(k,n[1],n[0],this)};c.prototype[v(u.Symbol,"iterator")]=v(c.prototype,"entries");var d=function(h,k){var m=k&&typeof k;m=="object"||m=="function"?b.has(k)?m=b.get(k):(m=""+ ++g,b.set(k,m)):m="p_"+k;var n=h[0][m];if(n&&C(h[0],m))for(h=0;h<n.length;h++){var l=n[h];if(k!==k&&l.key!==l.key||k===l.key)return{id:m,list:n,index:h,u:l}}return{id:m,list:n,index:-1,u:void 0}},e=function(h,k){var m=h[1];return ha(function(){if(m){for(;m.head!=h[1];)m=m.I;for(;m.next!=m.head;)return m=m.next,{done:!1,value:k(m)};m=null}return{done:!0,value:void 0}})},f=function(){var h={};return h.I=h.next=h.head=h},g=0;return c},"es6");w("Set",function(a){if(function(){if(!a||typeof a!="function"||!v(a.prototype,"entries")||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(y([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=v(d,"entries").call(d),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;var b=function(c){this.g=new u.Map;if(c){c=y(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.g.size};b.prototype.add=function(c){c=c===0?0:c;this.g.set(c,c);this.size=this.g.size;return this};b.prototype.delete=function(c){c=this.g.delete(c);this.size=this.g.size;return c};b.prototype.clear=function(){this.g.clear();this.size=0};b.prototype.has=function(c){return this.g.has(c)};b.prototype.entries=function(){return v(this.g,"entries").call(this.g)};b.prototype.values=function(){return v(this.g,"values").call(this.g)};b.prototype.keys=v(b.prototype,"values");b.prototype[v(u.Symbol,"iterator")]=v(b.prototype,"values");b.prototype.forEach=function(c,d){var e=this;this.g.forEach(function(f){return c.call(d,f,f,e)})};return b},"es6");w("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)C(b,d)&&c.push(b[d]);return c}},"es8");w("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}},"es6");w("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||v(Object,"is").call(Object,f,b))return!0}return!1}},"es7");var ta=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""};w("String.prototype.includes",function(a){return a?a:function(b,c){return ta(this,b,"includes").indexOf(b,c||0)!==-1}},"es6");w("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(h){return h};var e=[],f=typeof u.Symbol!="undefined"&&v(u.Symbol,"iterator")&&b[v(u.Symbol,"iterator")];if(typeof f=="function"){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}},"es6");w("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)C(b,d)&&c.push([d,b[d]]);return c}},"es8");w("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}},"es6");w("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991},"es6");w("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991},"es6");w("Number.isInteger",function(a){return a?a:function(b){return v(Number,"isFinite").call(Number,b)?b===Math.floor(b):!1}},"es6");w("Number.isSafeInteger",function(a){return a?a:function(b){return v(Number,"isInteger").call(Number,b)&&Math.abs(b)<=v(Number,"MAX_SAFE_INTEGER")}},"es6");w("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=ta(this,b,"startsWith"),e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}},"es6");var ua=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[v(u.Symbol,"iterator")]=function(){return e};return e};w("Array.prototype.entries",function(a){return a?a:function(){return ua(this,function(b,c){return[b,c]})}},"es6");w("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}},"es6");w("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}},"es6");w("Array.prototype.keys",function(a){return a?a:function(){return ua(this,function(b){return b})}},"es6");w("Array.prototype.values",function(a){return a?a:function(){return ua(this,function(b,c){return c})}},"es8");w("String.prototype.repeat",function(a){return a?a:function(b){var c=ta(this,null,"repeat");if(b<0||b>**********)throw new RangeError("Invalid count value");b|=0;for(var d="";b;)if(b&1&&(d+=c),b>>>=1)c+=c;return d}},"es6");w("String.prototype.padStart",function(a){return a?a:function(b,c){var d=ta(this,null,"padStart");b-=d.length;c=c!==void 0?String(c):" ";return(b>0&&c?v(c,"repeat").call(c,Math.ceil(b/c.length)).substring(0,b):"")+d}},"es8");/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var D=this||self,va=function(a){a=a.split(".");for(var b=D,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b},wa=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"},xa=function(a,b,c){a=a.split(".");c=c||D;for(var d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};function ya(a){D.setTimeout(function(){throw a;},0)};var za=function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]};function Aa(a,b){var c=0;a=za(String(a)).split(".");b=za(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",g=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];if(f[0].length==0&&g[0].length==0)break;c=Ba(f[1].length==0?0:parseInt(f[1],10),g[1].length==0?0:parseInt(g[1],10))||Ba(f[2].length==0,g[2].length==0)||Ba(f[2],g[2]);f=f[3];g=g[3]}while(c==0)}return c}function Ba(a,b){return a<b?-1:a>b?1:0};var Ca,Da=va("CLOSURE_FLAGS"),Ea=Da&&Da[610401301];Ca=Ea!=null?Ea:!1;var Fa,Ga=D.navigator;Fa=Ga?Ga.userAgentData||null:null;function Ha(a){if(!Ca||!Fa)return!1;for(var b=0;b<Fa.brands.length;b++){var c=Fa.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function E(a){var b;a:{if(b=D.navigator)if(b=b.userAgent)break a;b=""}return b.indexOf(a)!=-1};function Ia(){return Ca?!!Fa&&Fa.brands.length>0:!1}function Ja(){return Ia()?!1:E("Opera")}function Ka(){return E("Firefox")||E("FxiOS")}function La(){return E("Safari")&&!(Ma()||(Ia()?0:E("Coast"))||Ja()||(Ia()?0:E("Edge"))||(Ia()?Ha("Microsoft Edge"):E("Edg/"))||(Ia()?Ha("Opera"):E("OPR"))||Ka()||E("Silk")||E("Android"))}function Ma(){return Ia()?Ha("Chromium"):(E("Chrome")||E("CriOS"))&&!(Ia()?0:E("Edge"))||E("Silk")};var Na=function(a,b){return Array.prototype.map.call(a,b,void 0)};function Oa(a,b){a:{for(var c=typeof a==="string"?a.split(""):a,d=a.length-1;d>=0;d--)if(d in c&&b.call(void 0,c[d],d,a)){b=d;break a}b=-1}return b<0?null:typeof a==="string"?a.charAt(b):a[b]};var Pa=function(a){Pa[" "](a);return a};Pa[" "]=function(){};var Qa=null,Sa=function(a){var b=[];Ra(a,function(c){b.push(c)});return b},Ra=function(a,b){function c(k){for(;d<a.length;){var m=a.charAt(d++),n=Qa[m];if(n!=null)return n;if(!/^[\s\xa0]*$/.test(m))throw Error("Unknown base64 encoding at char: "+m);}return k}Ta();for(var d=0;;){var e=c(-1),f=c(0),g=c(64),h=c(64);if(h===64&&e===-1)break;b(e<<2|f>>4);g!=64&&(b(f<<4&240|g>>2),h!=64&&b(g<<6&192|h))}},Ta=function(){if(!Qa){Qa={};for(var a="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),b=["+/=","+/","-_=","-_.","-_"],c=0;c<5;c++)for(var d=a.concat(b[c].split("")),e=0;e<d.length;e++){var f=d[e];Qa[f]===void 0&&(Qa[f]=e)}}};function Ua(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};var Va=void 0,Wa;function Xa(a){if(Wa)throw Error("");Wa=function(b){D.setTimeout(function(){a(b)},0)}}function Ya(a){if(Wa)try{Wa(a)}catch(b){throw b.cause=a,b;}}function Za(a){a=Error(a);Ua(a,"warning");Ya(a);return a};var $a=typeof u.Symbol==="function"&&typeof(0,u.Symbol)()==="symbol";function ab(a,b,c){return typeof u.Symbol==="function"&&typeof(0,u.Symbol)()==="symbol"?(c===void 0?0:c)&&u.Symbol.for&&a?u.Symbol.for(a):a!=null?(0,u.Symbol)(a):(0,u.Symbol)():b}var db=ab("jas",void 0,!0),eb=ab(void 0,"0di"),fb=ab(void 0,"1oa"),gb=ab(void 0,"0actk"),hb=ab("m_m","Lb",!0);var ib={fb:{value:0,configurable:!0,writable:!0,enumerable:!1}},jb=Object.defineProperties,F=$a?db:"fb",kb,lb=[];G(lb,7);kb=Object.freeze(lb);function mb(a,b){$a||F in a||jb(a,ib);a[F]|=b}function G(a,b){$a||F in a||jb(a,ib);a[F]=b}function nb(a){if(4&a)return 512&a?512:1024&a?1024:0}function ob(a){mb(a,32);return a};function pb(){return typeof BigInt==="function"};var qb={};function H(a,b){return b===void 0?a.g!==rb&&!!(2&(a.i[F]|0)):!!(2&b)&&a.g!==rb}var rb={};function sb(a,b){if(typeof b!=="number"||b<0||b>=a.length)throw Error();}var tb=Object.freeze({});function ub(a){var b=vb;if(!a)throw Error((typeof b==="function"?b():b)||String(a));}function wb(a){a.Kb=!0;return a}var vb=void 0;var xb=wb(function(a){return typeof a==="number"}),yb=wb(function(a){return typeof a==="string"}),zb=wb(function(a){return typeof a==="boolean"});var Ab=typeof D.BigInt==="function"&&typeof D.BigInt(0)==="bigint";function Bb(a){var b=a;if(yb(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error(String(b));}else if(xb(b)&&!v(Number,"isSafeInteger").call(Number,b))throw Error(String(b));return Ab?BigInt(a):a=zb(a)?a?"1":"0":yb(a)?a.trim()||"0":String(a)}var Hb=wb(function(a){return Ab?a>=Cb&&a<=Db:a[0]==="-"?Eb(a,Fb):Eb(a,Gb)}),Fb=v(Number,"MIN_SAFE_INTEGER").toString(),Cb=Ab?BigInt(v(Number,"MIN_SAFE_INTEGER")):void 0,Gb=v(Number,"MAX_SAFE_INTEGER").toString(),Db=Ab?BigInt(v(Number,"MAX_SAFE_INTEGER")):void 0;function Eb(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}};var I=0,J=0;function Ib(a){var b=a>>>0;I=b;J=(a-b)/4294967296>>>0}function Jb(a){if(a<0){Ib(-a);var b=y(Kb(I,J));a=b.next().value;b=b.next().value;I=a>>>0;J=b>>>0}else Ib(a)}function Lb(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(4294967296*b+a);else pb()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),c=b+Mb(c)+Mb(a));return c}function Mb(a){a=String(a);return"0000000".slice(a.length)+a}function Nb(){var a=I,b=J;b&2147483648?pb()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=y(Kb(a,b)),a=b.next().value,b=b.next().value,a="-"+Lb(a,b)):a=Lb(a,b);return a}function Kb(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};function Ob(a){return Array.prototype.slice.call(a)};function Pb(a,b){throw Error(b===void 0?"unexpected value "+a+"!":b);};var Qb=typeof BigInt==="function"?BigInt.asIntN:void 0,Rb=v(Number,"isSafeInteger"),Sb=v(Number,"isFinite"),Tb=v(Math,"trunc");function Ub(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function Vb(a){if(typeof a!=="boolean")throw Error("Expected boolean but got "+wa(a)+": "+a);return a}var Wb=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/;function Xb(a){switch(typeof a){case "bigint":return!0;case "number":return Sb(a);case "string":return Wb.test(a);default:return!1}}function Yb(a){if(!Sb(a))throw Za("enum");return a|0}function Zb(a){return a==null?a:Sb(a)?a|0:void 0}function $b(a){if(typeof a!=="number")throw Za("int32");if(!Sb(a))throw Za("int32");return a|0}function ac(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Sb(a)?a|0:void 0}function bc(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return Sb(a)?a>>>0:void 0}function cc(a){var b=0;b=b===void 0?0:b;if(!Xb(a))throw Za("int64");var c=typeof a;switch(b){case 512:switch(c){case "string":return dc(a);case "bigint":return String(Qb(64,a));default:return ec(a)}case 1024:switch(c){case "string":return fc(a);case "bigint":return Bb(Qb(64,a));default:return hc(a)}case 0:switch(c){case "string":return dc(a);case "bigint":return Bb(Qb(64,a));default:return ic(a)}default:return Pb(b,"Unknown format requested type for int64")}}function jc(a){return a==null?a:cc(a)}function kc(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}function lc(a){if(kc(a))return a;if(a.length<16)Jb(Number(a));else if(pb())a=BigInt(a),I=Number(a&BigInt(4294967295))>>>0,J=Number(a>>BigInt(32)&BigInt(4294967295));else{var b=+(a[0]==="-");J=I=0;for(var c=a.length,d=b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),J*=1E6,I=I*1E6+d,I>=4294967296&&(J+=v(Math,"trunc").call(Math,I/4294967296),J>>>=0,I>>>=0);b&&(b=y(Kb(I,J)),a=b.next().value,b=b.next().value,I=a,J=b)}return Nb()}function ic(a){a=Tb(a);if(!Rb(a)){Jb(a);var b=I,c=J;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);var d=c*4294967296+(b>>>0);b=v(Number,"isSafeInteger").call(Number,d)?d:Lb(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function ec(a){a=Tb(a);if(Rb(a))a=String(a);else{var b=String(a);kc(b)?a=b:(Jb(a),a=Nb())}return a}function dc(a){var b=Tb(Number(a));if(Rb(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return lc(a)}function fc(a){var b=Tb(Number(a));if(Rb(b))return Bb(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return pb()?Bb(Qb(64,BigInt(a))):Bb(lc(a))}function hc(a){return Rb(a)?Bb(ic(a)):Bb(ec(a))}function mc(a){if(typeof a!=="string")throw Error();return a}function nc(a){if(a!=null&&typeof a!=="string")throw Error();return a}function oc(a){return a==null||typeof a==="string"?a:void 0}function pc(a,b,c,d){if(a!=null&&a[hb]===qb)return a;if(!Array.isArray(a))return c?d&2?((a=b[eb])||(a=new b,mb(a.i,34),a=b[eb]=a),b=a):b=new b:b=void 0,b;c=a[F]|0;d=c|d&32|d&2;d!==c&&G(a,d);return new b(a)};function qc(a){return a};function rc(a,b,c,d){var e=d!==void 0;d=!!d;var f=[],g=a.length,h=4294967295,k=!1,m=!!(b&64),n=m?b&128?0:-1:void 0;if(!(b&1)){var l=g&&a[g-1];l!=null&&typeof l==="object"&&l.constructor===Object?(g--,h=g):l=void 0;if(m&&!(b&128)&&!e){k=!0;var p;h=((p=sc)!=null?p:qc)(h-n,n,a,l)+n}}b=void 0;for(e=0;e<g;e++)if(p=a[e],p!=null&&(p=c(p,d))!=null)if(m&&e>=h){var r=e-n,t=void 0;((t=b)!=null?t:b={})[r]=p}else f[e]=p;if(l)for(var z in l)Object.prototype.hasOwnProperty.call(l,z)&&(a=l[z],a!=null&&(a=c(a,d))!=null&&(g=+z,e=void 0,m&&!v(Number,"isNaN").call(Number,g)&&(e=g+n)<h?f[e]=a:(g=void 0,((g=b)!=null?g:b={})[z]=a)));b&&(k?f.push(b):f[h]=b);return f}function tc(a){switch(typeof a){case "number":return v(Number,"isFinite").call(Number,a)?a:""+a;case "bigint":return Hb(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){var b=a[F]|0;return a.length===0&&b&1?void 0:rc(a,b,tc)}if(a!=null&&a[hb]===qb)return K(a);return}return a}var uc=typeof structuredClone!="undefined"?structuredClone:function(a){return rc(a,0,tc)},sc;function K(a){a=a.i;return rc(a,a[F]|0,tc)};function L(a,b,c){var d=d===void 0?0:d;if(a==null){var e=32;c?(a=[c],e|=128):a=[];b&&(e=e&-8380417|(b&1023)<<13)}else{if(!Array.isArray(a))throw Error("narr");e=a[F]|0;2048&e&&!(2&e)&&vc();if(e&256)throw Error("farr");if(e&64)return d!==0||e&2048||G(a,e|2048),a;if(c&&(e|=128,c!==a[0]))throw Error("mid");a:{c=a;e|=64;var f=c.length;if(f){var g=f-1,h=c[g];if(h!=null&&typeof h==="object"&&h.constructor===Object){b=e&128?0:-1;g-=b;if(g>=1024)throw Error("pvtlmt");for(var k in h)Object.prototype.hasOwnProperty.call(h,k)&&(f=+k,f<g&&(c[f+b]=h[k],delete h[k]));e=e&-8380417|(g&1023)<<13;break a}}if(b){k=Math.max(b,f-(e&128?0:-1));if(k>1024)throw Error("spvt");e=e&-8380417|(k&1023)<<13}}}e|=64;d===0&&(e|=2048);G(a,e);return a}function vc(){if(gb!=null){var a;var b=(a=Va)!=null?a:Va={};a=b[gb]||0;a>=5||(b[gb]=a+1,b=Error(),Ua(b,"incident"),Wa?Ya(b):ya(b))}};function wc(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[F]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=xc(a,c,!1,b&&!(c&16)):(mb(a,34),c&4&&Object.freeze(a)));return a}if(a!=null&&a[hb]===qb)return b=a.i,c=b[F]|0,H(a,c)?a:yc(a,b,c)?zc(a,b):xc(b,c)}function zc(a,b,c){a=new a.constructor(b);c&&(a.g=rb);a.j=rb;return a}function xc(a,b,c,d){d!=null||(d=!!(34&b));a=rc(a,b,wc,d);d=32;c&&(d|=2);b=b&8380609|d;G(a,b);return a}function Ac(a){var b=a.i,c=b[F]|0;return H(a,c)?yc(a,b,c)?zc(a,b,!0):new a.constructor(xc(b,c,!1)):a}function Bc(a){if(a.g!==rb)return!1;var b=a.i;b=xc(b,b[F]|0);mb(b,2048);a.i=b;a.g=void 0;a.j=void 0;return!0}function Cc(a){if(!Bc(a)&&H(a,a.i[F]|0))throw Error();}function Dc(a,b){b===void 0&&(b=a[F]|0);b&32&&!(b&4096)&&G(a,b|4096)}function yc(a,b,c){return c&2?!0:c&32&&!(c&4096)?(G(b,c|2),a.g=rb,!0):!1};var Ec=Bb(0),M=function(a,b,c,d){a=Fc(a.i,b,c,d);if(a!==null)return a},Fc=function(a,b,c,d){if(b===-1)return null;var e=b+(c?0:-1),f=a.length-1;if(!(f<1+(c?0:-1))){if(e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object){c=g[b];var h=!0}else if(e===f)c=g;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!v(Object,"is").call(Object,d,c))return h?g[b]=d:a[e]=d,d}return c}},O=function(a,b,c){Cc(a);var d=a.i;N(d,d[F]|0,b,c);return a};function N(a,b,c,d){var e=c+-1,f=a.length-1;if(f>=0&&e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;if(d!==void 0){var h;f=((h=b)!=null?h:b=a[F]|0)>>13&1023||536870912;c>=f?d!=null&&(e={},a[f+-1]=(e[c]=d,e)):a[e]=d}return b}var Hc=function(a,b,c){a=a.i;return Gc(a,a[F]|0,b,c)!==void 0},P=function(a){return a===tb?2:4};function Ic(a,b,c,d,e){var f=a.i,g=f[F]|0;d=H(a,g)?1:d;e=!!e||d===3;d===2&&Bc(a)&&(f=a.i,g=f[F]|0);a=Fc(f,b);a=Array.isArray(a)?a:kb;var h=a===kb?7:a[F]|0,k=Jc(h,g);var m=4&k?!1:!0;if(m){4&k&&(a=Ob(a),h=0,k=Kc(k,g),g=N(f,g,b,a));for(var n=0,l=0;n<a.length;n++){var p=c(a[n]);p!=null&&(a[l++]=p)}l<n&&(a.length=l);c=(k|4)&-513;k=c&=-1025;k&=-4097}k!==h&&(G(a,k),2&k&&Object.freeze(a));return a=Lc(a,k,f,g,b,d,m,e)}function Lc(a,b,c,d,e,f,g,h){var k=b;f===1||(f!==4?0:2&b||!(16&b)&&32&d)?Mc(b)||(b|=!a.length||g&&!(4096&b)||32&d&&!(4096&b||16&b)?2:256,b!==k&&G(a,b),Object.freeze(a)):(f===2&&Mc(b)&&(a=Ob(a),k=0,b=Kc(b,d),d=N(c,d,e,a)),Mc(b)||(h||(b|=16),b!==k&&G(a,b)));2&b||!(4096&b||16&b)||Dc(c,d);return a}function Jc(a,b){2&b&&(a|=2);return a|1}function Mc(a){return!!(2&a)&&!!(4&a)||!!(256&a)}function Nc(a,b,c,d){Cc(a);var e=a.i,f=e[F]|0;if(c==null)return N(e,f,b),a;var g=c===kb?7:c[F]|0,h=g,k=Mc(g),m=k||Object.isFrozen(c);k||(g=0);m||(c=Ob(c),h=0,g=Kc(g,f),m=!1);g|=5;var n;k=(n=nb(g))!=null?n:0;for(n=0;n<c.length;n++){var l=c[n],p=d(l,k);v(Object,"is").call(Object,l,p)||(m&&(c=Ob(c),h=0,g=Kc(g,f),m=!1),c[n]=p)}g!==h&&(m&&(c=Ob(c),g=Kc(g,f)),G(c,g));N(e,f,b,c);return a}function Oc(a,b,c,d){Cc(a);var e=a.i;N(e,e[F]|0,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a}var Sc=function(a,b,c,d){Cc(a);var e=a.i,f=e[F]|0;if(d==null){var g=Pc(e);if(Qc(g,e,f,c)===b)g.set(c,0);else return a}else f=Rc(e,f,c,b);N(e,f,b,d);return a},Uc=function(a,b,c){return Tc(a,b)===c?c:-1},Tc=function(a,b){a=a.i;return Qc(Pc(a),a,void 0,b)};function Pc(a){if($a){var b;return(b=a[fb])!=null?b:a[fb]=new u.Map}if(fb in a)return a[fb];b=new u.Map;Object.defineProperty(a,fb,{value:b});return b}function Rc(a,b,c,d){var e=Pc(a),f=Qc(e,a,b,c);f!==d&&(f&&(b=N(a,b,f)),e.set(c,d));return b}function Qc(a,b,c,d){var e=a.get(d);if(e!=null)return e;for(var f=e=0;f<d.length;f++){var g=d[f];Fc(b,g)!=null&&(e!==0&&(c=N(b,c,e)),e=g)}a.set(d,e);return e}var Vc=function(a,b,c){Cc(a);a=a.i;var d=a[F]|0,e=Fc(a,c);b=Ac(pc(e,b,!0,d));e!==b&&(d=N(a,d,c,b),Dc(a,d));return b};function Gc(a,b,c,d){var e=!1;d=Fc(a,d,void 0,function(f){var g=pc(f,c,!1,b);e=g!==f&&g!=null;return g});if(d!=null)return e&&!H(d)&&Dc(a,b),d}var Wc=function(a,b,c){a=a.i;(c=Gc(a,a[F]|0,b,c))||(c=b[eb])||(c=new b,mb(c.i,34),c=b[eb]=c);return c},Q=function(a,b,c){var d=a.i,e=d[F]|0;b=Gc(d,e,b,c);if(b==null)return b;e=d[F]|0;if(!H(a,e)){var f=Ac(b);f!==b&&(Bc(a)&&(d=a.i,e=d[F]|0),b=f,e=N(d,e,c,b),Dc(d,e))}return b};function Xc(a,b,c,d,e,f,g,h){var k=H(a,c);f=k?1:f;g=!!g||f===3;k=h&&!k;(f===2||k)&&Bc(a)&&(b=a.i,c=b[F]|0);a=Fc(b,e);a=Array.isArray(a)?a:kb;var m=a===kb?7:a[F]|0,n=Jc(m,c);if(h=!(4&n)){var l=a,p=c,r=!!(2&n);r&&(p|=2);for(var t=!r,z=!0,A=0,pa=0;A<l.length;A++){var bb=pc(l[A],d,!1,p);if(bb instanceof d){if(!r){var cb=H(bb);t&&(t=!cb);z&&(z=cb)}l[pa++]=bb}}pa<A&&(l.length=pa);n|=4;n=z?n&-4097:n|4096;n=t?n|8:n&-9}n!==m&&(G(a,n),2&n&&Object.freeze(a));if(k&&!(8&n||!a.length&&(f===1||(f!==4?0:2&n||!(16&n)&&32&c)))){Mc(n)&&(a=Ob(a),n=Kc(n,c),c=N(b,c,e,a));d=a;k=n;for(m=0;m<d.length;m++)l=d[m],n=Ac(l),l!==n&&(d[m]=n);k|=8;n=k=d.length?k|4096:k&-4097;G(a,n)}return a=Lc(a,n,b,c,e,f,h,g)}var R=function(a,b,c,d){var e=a.i;return Xc(a,e,e[F]|0,b,c,d,!1,!0)};function Yc(a){a==null&&(a=void 0);return a}var Zc=function(a,b,c){c=Yc(c);O(a,b,c);c&&!H(c)&&Dc(a.i);return a},$c=function(a,b,c,d){d=Yc(d);Sc(a,b,c,d);d&&!H(d)&&Dc(a.i);return a},ad=function(a,b,c){Cc(a);var d=a.i,e=d[F]|0;if(c==null)return N(d,e,b),a;for(var f=c===kb?7:c[F]|0,g=f,h=Mc(f),k=h||Object.isFrozen(c),m=!0,n=!0,l=0;l<c.length;l++){var p=c[l];h||(p=H(p),m&&(m=!p),n&&(n=p))}h||(f=m?13:5,f=n?f&-4097:f|4096);k&&f===g||(c=Ob(c),g=0,f=Kc(f,e));f!==g&&G(c,f);e=N(d,e,b,c);2&f||!(4096&f||16&f)||Dc(d,e);return a};function Kc(a,b){return a=(2&b?a|2:a&-3)&-273}function bd(a,b){Cc(a);a=Ic(a,4,oc,2,!0);var c,d=(c=nb(a===kb?7:a[F]|0))!=null?c:0;if(Array.isArray(b)){c=b.length;for(var e=0;e<c;e++)a.push(mc(b[e],d))}else for(b=y(b),c=b.next();!c.done;c=b.next())a.push(mc(c.value,d))}var cd=function(a,b){var c=c===void 0?!1:c;a=M(a,b);a=a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0;return a!=null?a:c},dd=function(a,b){var c=c===void 0?0:c;a=ac(M(a,b));return a!=null?a:c},ed=function(a,b){var c=c===void 0?0:c;a=bc(M(a,b));return a!=null?a:c},fd=function(a,b){var c=c===void 0?Ec:c;a=M(a,b);b=typeof a;a=a==null?a:b==="bigint"?Bb(Qb(64,a)):Xb(a)?b==="string"?fc(a):hc(a):void 0;return a!=null?a:c},gd=function(a,b){var c=c===void 0?0:c;a=M(a,b,void 0,Ub);return a!=null?a:c},S=function(a,b){var c=c===void 0?"":c;var d;return(d=oc(M(a,b)))!=null?d:c},T=function(a,b){var c=c===void 0?0:c;a=Zb(M(a,b));return a!=null?a:c},hd=function(a,b,c){a=Ic(a,b,ac,3,!0);sb(a,c);return a[c]},id=function(a,b,c){return T(a,Uc(a,c,b))},jd=function(a,b,c){return Oc(a,b,c==null?c:$b(c),0)},kd=function(a,b,c){return Oc(a,b,jc(c),"0")},ld=function(a,b,c){return Oc(a,b,nc(c),"")},md=function(a,b,c){return O(a,b,c==null?c:Yb(c))},nd=function(a,b,c){return Oc(a,b,c==null?c:Yb(c),0)},od=function(a,b,c,d){return Sc(a,b,c,d==null?d:Yb(d))};var U=function(a,b,c){this.i=L(a,b,c)};U.prototype.toJSON=function(){return K(this)};var pd=function(a){var b=a.i,c=b[F]|0;return H(a,c)?a:yc(a,b,c)?zc(a,b):new a.constructor(xc(b,c,!0))};U.prototype[hb]=qb;function qd(a,b){if(b==null)return new a;if(!Array.isArray(b))throw Error();if(Object.isFrozen(b)||Object.isSealed(b)||!Object.isExtensible(b))throw Error();return new a(ob(b))};function rd(a){return function(b){if(b==null||b=="")b=new a;else{b=JSON.parse(b);if(!Array.isArray(b))throw Error("dnarr");b=new a(ob(b))}return b}};var sd=function(a){this.i=L(a)};x(sd,U);var td=function(a){return S(a,1)};var ud=function(a){this.i=L(a)};x(ud,U);var vd,wd=64;function xd(){try{return vd!=null||(vd=new Uint32Array(64)),wd>=64&&(crypto.getRandomValues(vd),wd=0),vd[wd++]}catch(a){return Math.floor(Math.random()*4294967296)}};function yd(a,b){if(!xb(a.goog_pvsid))try{var c=xd()+(xd()&2097151)*4294967296;Object.defineProperty(a,"goog_pvsid",{value:c,configurable:!1})}catch(d){b.H({methodName:784,J:d})}a=Number(a.goog_pvsid);(!a||a<=0)&&b.H({methodName:784,J:Error("Invalid correlator, "+a)});return a||-1};var zd=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}};var Ad=function(){return Ca&&Fa?!Fa.mobile&&(E("iPad")||E("Android")||E("Silk")):E("iPad")||E("Android")&&!E("Mobile")||E("Silk")};function Bd(a){var b=[],c=0,d;for(d in a)b[c++]=a[d];return b};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var Cd;function Dd(){Cd===void 0&&(Cd=null);return Cd};var Ed=function(a){this.g=a};Ed.prototype.toString=function(){return this.g+""};function Fd(a){var b=Dd();a=b?b.createScriptURL(a):a;return new Ed(a)}function Gd(a){if(a instanceof Ed)return a.g;throw Error("");};var Hd=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;var Id=function(a){this.g=a};Id.prototype.toString=function(){return this.g+""};function Jd(a){a=a===void 0?document:a;var b,c;a=(c=(b=a).querySelector)==null?void 0:c.call(b,"script[nonce]");return a==null?"":a.nonce||a.getAttribute("nonce")||""};function Kd(a,b){a.src=Gd(b);(b=Jd(a.ownerDocument))&&a.setAttribute("nonce",b)};var Ld="alternate author bookmark canonical cite help icon license modulepreload next prefetch dns-prefetch prerender preconnect preload prev search subresource".split(" ");function Md(a,b){var c=a.write;if(b instanceof Id)b=b.g;else throw Error("");c.call(a,b)};function Nd(a,b){a=Gd(a).toString();a='<script src="'+Od(a)+'"';if(b==null?0:b.async)a+=" async";(b==null?void 0:b.attributionSrc)!==void 0&&(a+=' attributionsrc="'+Od(b.attributionSrc)+'"');if(b==null?0:b.Wa)a+=' custom-element="'+Od(b.Wa)+'"';if(b==null?0:b.defer)a+=" defer";if(b==null?0:b.id)a+=' id="'+Od(b.id)+'"';if(b==null?0:b.nonce)a+=' nonce="'+Od(b.nonce)+'"';if(b==null?0:b.type)a+=' type="'+Od(b.type)+'"';if(b==null?0:b.Ga)a+=' crossorigin="'+Od(b.Ga)+'"';b=a+">\x3c/script>";b=(a=Dd())?a.createHTML(b):b;return new Id(b)}function Od(a){return a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")};function Pd(a){var b=sa.apply(1,arguments);if(b.length===0)return Fd(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return Fd(c)}function Qd(a,b){a=Gd(a).toString();var c=a.split(/[?#]/),d=/[?]/.test(a)?"?"+c[1]:"";return Rd(c[0],d,/[#]/.test(a)?"#"+(d?c[2]:c[1]):"",b)}function Rd(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(function(k){return e(k,h)}):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}var f=b.length?"&":"?";d.constructor===Object&&(d=v(Object,"entries").call(Object,d));Array.isArray(d)?d.forEach(function(g){return e(g[1],g[0])}):d.forEach(e);return Fd(a+b+c)};var Sd=function(a){var b=b===void 0?!1:b;var c=c===void 0?D:c;for(var d=0;c&&d++<40;){var e;if(!(e=b))try{var f;if(f=!!c&&c.location.href!=null)b:{try{Pa(c.foo);f=!0;break b}catch(h){}f=!1}e=f}catch(h){e=!1}if(e&&a(c))break;a:{try{var g=c.parent;if(g&&g!=c){c=g;break a}}catch(h){}c=null}}},Td=function(a){var b=a;Sd(function(c){b=c;return!1});return b},Ud=function(){if(!u.globalThis.crypto)return Math.random();try{var a=new Uint32Array(1);u.globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch(b){return Math.random()}},Vd=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Wd=zd(function(){return(Ca&&Fa?Fa.mobile:!Ad()&&(E("iPod")||E("iPhone")||E("Android")||E("IEMobile")))?2:Ad()?1:0});function Xd(a,b){if(a.length&&b.head){a=y(a);for(var c=a.next();!c.done;c=a.next())if((c=c.value)&&b.head){var d=Yd("META");b.head.appendChild(d);d.httpEquiv="origin-trial";d.content=c}}}var Zd=function(a){return yd(a,{H:function(){}})},Yd=function(a,b){b=b===void 0?document:b;return b.createElement(String(a).toLowerCase())};var $d={Fb:0,Eb:1,Bb:2,wb:3,Cb:4,xb:5,Db:6,zb:7,Ab:8,ub:9,yb:10,Gb:11};var ae={Ib:0,Jb:1,Hb:2};var be=function(a){this.i=L(a)};x(be,U);be.prototype.getVersion=function(){return dd(this,2)};function ce(a){return Sa(a.length%4!==0?a+"A":a).map(function(b){return(q=b.toString(2),v(q,"padStart")).call(q,8,"0")}).join("")}function de(a){if(!/^[0-1]+$/.test(a))throw Error("Invalid input ["+a+"] not a bit string.");return parseInt(a,2)}function ee(a){if(!/^[0-1]+$/.test(a))throw Error("Invalid input ["+a+"] not a bit string.");for(var b=[1,2,3,5],c=0,d=0;d<a.length-1;d++)b.length<=d&&b.push(b[d-1]+b[d-2]),c+=parseInt(a[d],2)*b[d];return c};function fe(a){var b=ce(a),c=de(b.slice(0,6));a=de(b.slice(6,12));var d=new be;c=jd(d,1,c);a=jd(c,2,a);b=b.slice(12);c=de(b.slice(0,12));d=[];for(var e=b.slice(12).replace(/0+$/,""),f=0;f<c;f++){if(e.length===0)throw Error("Found "+f+" of "+c+" sections ["+d+"] but reached end of input ["+b+"]");var g=de(e[0])===0;e=e.slice(1);var h=ge(e,b),k=d.length===0?0:d[d.length-1];k=ee(h)+k;e=e.slice(h.length);if(g)d.push(k);else{g=ge(e,b);h=ee(g);for(var m=0;m<=h;m++)d.push(k+m);e=e.slice(g.length)}}if(e.length>0)throw Error("Found "+c+" sections ["+d+"] but has remaining input ["+e+"], entire input ["+b+"]");return Nc(a,3,d,$b)}function ge(a,b){var c=a.indexOf("11");if(c===-1)throw Error("Expected section bitstring but not found in ["+a+"] part of ["+b+"]");return a.slice(0,c+2)};var he="a".charCodeAt(),ie=Bd($d),je=Bd(ae);var ke=function(a){this.i=L(a)};x(ke,U);var le=function(){var a=new ke;return kd(a,1,0)},me=function(a){var b=Number;var c=c===void 0?"0":c;var d=M(a,1);var e=!0;e=e===void 0?!1:e;var f=typeof d;d=d==null?d:f==="bigint"?String(Qb(64,d)):Xb(d)?f==="string"?dc(d):e?ec(d):ic(d):void 0;b=b(d!=null?d:c);a=dd(a,2);return new Date(b*1E3+a/1E6)};var ne=function(a){if(/[^01]/.test(a))throw Error("Input bitstring "+a+" is malformed!");this.j=a;this.g=0},qe=function(a){var b=V(a,16);return!!V(a,1)===!0?(a=oe(a),a.forEach(function(c){if(c>b)throw Error("ID "+c+" is past MaxVendorId "+b+"!");}),a):pe(a,b)},oe=function(a){for(var b=V(a,12),c=[];b--;){var d=!!V(a,1)===!0,e=V(a,16);if(d)for(d=V(a,16);e<=d;e++)c.push(e);else c.push(e)}c.sort(function(f,g){return f-g});return c},pe=function(a,b,c){for(var d=[],e=0;e<b;e++)if(V(a,1)){var f=e+1;if(c&&c.indexOf(f)===-1)throw Error("ID: "+f+" is outside of allowed values!");d.push(f)}return d},V=function(a,b){if(a.g+b>a.j.length)throw Error("Requested length "+b+" is past end of string.");var c=a.j.substring(a.g,a.g+b);a.g+=b;return parseInt(c,2)};ne.prototype.skip=function(a){this.g+=a};var se=function(a){try{var b=Sa(a.split(".")[0]).map(function(d){return(q=d.toString(2),v(q,"padStart")).call(q,8,"0")}).join(""),c=new ne(b);b={};b.tcString=a;b.gdprApplies=!0;c.skip(78);b.cmpId=V(c,12);b.cmpVersion=V(c,12);c.skip(30);b.tcfPolicyVersion=V(c,6);b.isServiceSpecific=!!V(c,1);b.useNonStandardStacks=!!V(c,1);b.specialFeatureOptins=re(pe(c,12,je),je);b.purpose={consents:re(pe(c,24,ie),ie),legitimateInterests:re(pe(c,24,ie),ie)};b.purposeOneTreatment=!!V(c,1);b.publisherCC=String.fromCharCode(he+V(c,6))+String.fromCharCode(he+V(c,6));b.vendor={consents:re(qe(c),null),legitimateInterests:re(qe(c),null)};return b}catch(d){return null}},re=function(a,b){var c={};if(Array.isArray(b)&&b.length!==0){b=y(b);for(var d=b.next();!d.done;d=b.next())d=d.value,c[d]=a.indexOf(d)!==-1}else for(a=y(a),b=a.next();!b.done;b=a.next())c[b.value]=!0;delete c[0];return c};var te=function(a){this.i=L(a)};x(te,U);var ue=function(a,b){var c=c===void 0?{}:c;this.error=a;this.meta=c;this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"};function ve(a,b,c,d){d=d===void 0?!1:d;a.google_image_requests||(a.google_image_requests=[]);var e=Yd("IMG",a.document);if(c){var f=function(){if(c){var g=a.google_image_requests,h=Array.prototype.indexOf.call(g,e,void 0);h>=0&&Array.prototype.splice.call(g,h,1)}typeof e.removeEventListener==="function"&&e.removeEventListener("load",f,!1);typeof e.removeEventListener==="function"&&e.removeEventListener("error",f,!1)};typeof e.addEventListener==="function"&&e.addEventListener("load",f,!1);typeof e.addEventListener==="function"&&e.addEventListener("error",f,!1)}d&&(e.attributionSrc="");e.src=b;a.google_image_requests.push(e)}function we(a){var b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=rcs_internal";Vd(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});xe(c,b)}function xe(a,b){var c=window;b=b===void 0?!1:b;var d=d===void 0?!1:d;c.fetch?(b={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"},d&&(b.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?b.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:b.headers={"Attribution-Reporting-Eligible":"event-source"}),c.fetch(a,b)):ve(c,a,b===void 0?!1:b,d===void 0?!1:d)};function ye(a,b){try{var c=function(d){var e={};return[(e[d.ba]=d.Y,e)]};return JSON.stringify([a.filter(function(d){return d.O}).map(c),K(b),a.filter(function(d){return!d.O}).map(c)])}catch(d){return ze(d,b),""}}function ze(a,b){try{var c=a instanceof Error?a:Error(String(a)),d=c.toString();c.name&&d.indexOf(c.name)==-1&&(d+=": "+c.name);c.message&&d.indexOf(c.message)==-1&&(d+=": "+c.message);if(c.stack)a:{var e=c.stack;a=d;try{e.indexOf(a)==-1&&(e=a+"\n"+e);for(var f;e!=f;)f=e,e=e.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");d=e.replace(RegExp("\n *","g"),"\n");break a}catch(g){d=a;break a}d=void 0}we({m:d,b:T(b,1)||null,v:S(b,2)||null})}catch(g){}}var Ae=function(a,b){var c=new te;a=nd(c,1,a);b=ld(a,2,b);this.o=pd(b)};var Be=function(a){this.i=L(a)};x(Be,U);var De=function(a,b){return Sc(a,3,Ce,b==null?b:Vb(b))},Ce=[1,2,3];var Ee=function(a){this.i=L(a)};x(Ee,U);var Ge=function(a,b){return Sc(a,2,Fe,jc(b))},Fe=[2,4];var He=function(a){this.i=L(a)};x(He,U);var Ie=function(a){var b=new He;return ld(b,1,a)},Je=function(a,b){return Zc(a,3,b)},Ke=function(a,b){var c=b;Cc(a);b=a.i;var d=Xc(a,b,b[F]|0,Be,4,2,!0);c=c!=null?c:new Be;d.push(c);var e=d===kb?7:d[F]|0,f=e;(c=H(c))?(e&=-9,d.length===1&&(e&=-4097)):e|=4096;e!==f&&G(d,e);c||Dc(b);return a};var Le=function(a){this.i=L(a)};x(Le,U);var Me=function(a){this.i=L(a)};x(Me,U);var Ne=function(a,b){return nd(a,1,b)},Oe=function(a,b){return nd(a,2,b)};var Pe=function(a){this.i=L(a)};x(Pe,U);var Qe=[1,2];var Re=function(a){this.i=L(a)};x(Re,U);var Se=function(a,b){return Zc(a,1,b)},Te=function(a,b){return ad(a,2,b)},Ue=function(a,b){return Nc(a,4,b,$b)},Ve=function(a,b){return ad(a,5,b)},We=function(a,b){return nd(a,6,b)};var Xe=function(a){this.i=L(a)};x(Xe,U);var Ye=[1,2,3,4,6];var Ze=function(a){this.i=L(a)};x(Ze,U);var $e=function(a){this.i=L(a)};x($e,U);var af=[2,3,4];var bf=function(a){this.i=L(a)};x(bf,U);var cf=[3,4,5],df=[6,7];var ef=function(a){this.i=L(a)};x(ef,U);var ff=[4,5];var gf=function(a){this.i=L(a)};x(gf,U);gf.prototype.getTagSessionCorrelator=function(){return fd(this,2)};var jf=function(a){var b=new gf;return $c(b,4,hf,a)},hf=[4,5,7,8,9];var kf=function(a){this.i=L(a)};x(kf,U);var lf=function(a){this.i=L(a)};x(lf,U);var mf=[1,2,4,5,6,9,10];var nf=function(a){this.i=L(a)};x(nf,U);nf.prototype.getTagSessionCorrelator=function(){return fd(this,2)};nf.prototype.da=function(a){return hd(this,4,a)};var of=function(a){this.i=L(a)};x(of,U);of.prototype.bb=function(){return dd(this,2)};of.prototype.ab=function(a){var b=Ic(this,3,oc,3,!0);sb(b,a);return b[a]};var pf=function(a){this.i=L(a)};x(pf,U);var qf=function(a){this.i=L(a)};x(qf,U);qf.prototype.getTagSessionCorrelator=function(){return fd(this,1)};qf.prototype.da=function(a){return hd(this,2,a)};var rf=function(a){this.i=L(a)};x(rf,U);var sf=[1,7],tf=[4,6,8];var vf=function(a){this.g=a;this.Sa=new uf(this.g)},uf=function(a){this.g=a;this.Ia=new wf(this.g)},wf=function(a){this.g=a;this.outstream=new xf;this.request=new yf;this.threadYield=new zf;this.eb=new Af(this.g);this.hb=new Bf(this.g);this.ob=new Cf(this.g)},Af=function(a){this.g=a};Af.prototype.X=function(a){this.g.D(Je(Ke(Ke(Ie("JwITQ"),De(new Be,a.ma)),De(new Be,a.oa)),Ge(new Ee,Math.round(a.aa))))};var Bf=function(a){this.g=a};Bf.prototype.X=function(a){this.g.D(Je(Ke(Ke(Ie("Pn3Upd"),De(new Be,a.ma)),De(new Be,a.oa)),Ge(new Ee,Math.round(a.aa))))};var Cf=function(a){this.g=a};Cf.prototype.X=function(a){var b=this.g,c=b.D,d=Ie("rkgGzc");var e=new Be;e=Sc(e,2,Ce,jc(a.source));d=Ke(d,e);e=new Be;e=Sc(e,2,Ce,jc(a.Va));c.call(b,Je(Ke(d,e),Ge(new Ee,Math.round(a.aa))))};var xf=function(){},yf=function(){},zf=function(){},Df=function(){Ae.apply(this,arguments);this.La=new vf(this)};x(Df,Ae);var Ef=function(){Df.apply(this,arguments)};x(Ef,Df);Ef.prototype.sb=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{O:!0,ba:2,Y:K(a)}})))};Ef.prototype.rb=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{O:!0,ba:29,Y:K(a)}})))};Ef.prototype.fa=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{O:!0,ba:4,Y:K(a)}})))};Ef.prototype.tb=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{O:!0,ba:15,Y:K(a)}})))};Ef.prototype.D=function(){this.l.apply(this,B(sa.apply(0,arguments).map(function(a){return{O:!1,ba:1,Y:K(a)}})))};function Ff(a,b){if(u.globalThis.fetch)u.globalThis.fetch(a,{method:"POST",body:b,keepalive:b.length<65536,credentials:"omit",mode:"no-cors",redirect:"follow"}).catch(function(){});else{var c=new XMLHttpRequest;c.open("POST",a,!0);c.send(b)}};var Gf=function(a,b,c,d,e,f,g,h){Ef.call(this,a,b);this.U=c;this.T=d;this.V=e;this.R=f;this.S=g;this.K=h;this.g=[];this.j=null;this.M=!1};x(Gf,Ef);var Hf=function(a){a.j!==null&&(clearTimeout(a.j),a.j=null);if(a.g.length){var b=ye(a.g,a.o);a.T(a.U+"?e=1",b);a.g=[]}};Gf.prototype.l=function(){var a=sa.apply(0,arguments),b=this;try{this.S&&ye(this.g.concat(a),this.o).length>=65536&&Hf(this),this.K&&!this.M&&(this.M=!0,this.K.g(function(){Hf(b)})),this.g.push.apply(this.g,B(a)),this.g.length>=this.R&&Hf(this),this.g.length&&this.j===null&&(this.j=setTimeout(function(){Hf(b)},this.V))}catch(c){ze(c,this.o)}};var If=function(a,b,c,d,e,f){Gf.call(this,a,b,"https://pagead2.googlesyndication.com/pagead/ping",Ff,c===void 0?1E3:c,d===void 0?100:d,(e===void 0?!1:e)&&!!u.globalThis.fetch,f)};x(If,Gf);var Jf=function(a){this.g=a;this.defaultValue=!1},Kf=function(a,b){this.g=a;this.defaultValue=b===void 0?0:b};var Lf=new Kf(695925491,20),Mf=new Jf(45624259),Nf=new Kf(635239304,100),Of=new Jf(662101539),Pf=new Kf(682056200,100),Qf=new Kf(24),Rf=new function(a,b){b=b===void 0?[]:b;this.g=a;this.defaultValue=b}(1934,["AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==","Amm8/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==","A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9","A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9"]);var Sf=function(a){this.i=L(a)};x(Sf,U);var Tf=function(a){this.i=L(a)};x(Tf,U);var Uf=function(a){this.i=L(a)};x(Uf,U);var Vf=function(a){this.i=L(a)};x(Vf,U);var Wf=rd(Vf);var Xf=function(a){this.g=a||{cookie:""}};Xf.prototype.set=function(a,b,c){var d=!1;if(typeof c==="object"){var e=c.Mb;d=c.Nb||!1;var f=c.domain||void 0;var g=c.path||void 0;var h=c.jb}if(/[;=\s]/.test(a))throw Error('Invalid cookie name "'+a+'"');if(/[;\r\n]/.test(b))throw Error('Invalid cookie value "'+b+'"');h===void 0&&(h=-1);this.g.cookie=a+"="+b+(f?";domain="+f:"")+(g?";path="+g:"")+(h<0?"":h==0?";expires="+(new Date(1970,1,1)).toUTCString():";expires="+(new Date(Date.now()+h*1E3)).toUTCString())+(d?";secure":"")+(e!=null?";samesite="+e:"")};Xf.prototype.get=function(a,b){for(var c=a+"=",d=(this.g.cookie||"").split(";"),e=0,f;e<d.length;e++){f=za(d[e]);if(f.lastIndexOf(c,0)==0)return f.slice(c.length);if(f==a)return""}return b};Xf.prototype.isEmpty=function(){return!this.g.cookie};Xf.prototype.clear=function(){for(var a=(this.g.cookie||"").split(";"),b=[],c=[],d,e,f=0;f<a.length;f++)e=za(a[f]),d=e.indexOf("="),d==-1?(b.push(""),c.push(e)):(b.push(e.substring(0,d)),c.push(e.substring(d+1)));for(a=b.length-1;a>=0;a--)c=b[a],this.get(c),this.set(c,"",{jb:0,path:void 0,domain:void 0})};function Yf(a){a=Zf(a);try{var b=a?Wf(a):null}catch(c){b=null}return b?Q(b,Uf,4)||null:null}function Zf(a){a=(new Xf(a)).get("FCCDCF","");if(a)if(v(a,"startsWith").call(a,"%"))try{var b=decodeURIComponent(a)}catch(c){b=null}else b=a;else b=null;return b};Bd($d).map(function(a){return Number(a)});Bd(ae).map(function(a){return Number(a)});var $f=function(a){this.g=a},bg=function(a){a.__tcfapiPostMessageReady||ag(new $f(a))},ag=function(a){a.j=function(b){var c=typeof b.data==="string";try{var d=c?JSON.parse(b.data):b.data}catch(f){return}var e=d.__tcfapiCall;e&&(e.command==="ping"||e.command==="addEventListener"||e.command==="removeEventListener")&&(0,a.g.__tcfapi)(e.command,e.version,function(f,g){var h={};h.__tcfapiReturn=e.command==="removeEventListener"?{success:f,callId:e.callId}:{returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f,b.origin);return f},e.parameter)};a.g.addEventListener("message",a.j);a.g.__tcfapiPostMessageReady=!0};var cg=function(a){this.g=a;this.j=null},eg=function(a){a.__uspapiPostMessageReady||dg(new cg(a))},dg=function(a){a.j=function(b){var c=typeof b.data==="string";try{var d=c?JSON.parse(b.data):b.data}catch(f){return}var e=d.__uspapiCall;e&&e.command==="getUSPData"&&a.g.__uspapi(e.command,e.version,function(f,g){var h={};h.__uspapiReturn={returnValue:f,success:g,callId:e.callId};f=c?JSON.stringify(h):h;b.source&&typeof b.source.postMessage==="function"&&b.source.postMessage(f,b.origin);return f})};a.g.addEventListener("message",a.j);a.g.__uspapiPostMessageReady=!0};var fg=function(a){this.i=L(a)};x(fg,U);var gg=function(a){this.i=L(a)};x(gg,U);var hg=rd(gg);function ig(a,b){function c(l){if(l.length<10)return null;var p=h(l.slice(0,4));p=k(p);l=h(l.slice(6,10));l=m(l);return"1"+p+l+"N"}function d(l){if(l.length<10)return null;var p=h(l.slice(0,6));p=k(p);l=h(l.slice(6,10));l=m(l);return"1"+p+l+"N"}function e(l){if(l.length<12)return null;var p=h(l.slice(0,6));p=k(p);l=h(l.slice(8,12));l=m(l);return"1"+p+l+"N"}function f(l){if(l.length<18)return null;var p=h(l.slice(0,8));p=k(p);l=h(l.slice(12,18));l=m(l);return"1"+p+l+"N"}function g(l){if(l.length<10)return null;var p=h(l.slice(0,6));p=k(p);l=h(l.slice(6,10));l=m(l);return"1"+p+l+"N"}function h(l){for(var p=[],r=0,t=0;t<l.length/2;t++)p.push(de(l.slice(r,r+2))),r+=2;return p}function k(l){return l.every(function(p){return p===1})?"Y":"N"}function m(l){return l.some(function(p){return p===1})?"Y":"N"}if(a.length===0)return null;a=a.split(".");if(a.length>2)return null;a=ce(a[0]);var n=de(a.slice(0,6));a=a.slice(6);if(n!==1)return null;switch(b){case 8:return c(a);case 10:case 12:case 9:return d(a);case 11:return e(a);case 7:return f(a);case 13:return g(a);default:return null}};function jg(a,b){var c=a.document,d=function(){if(!a.frames[b])if(c.body){var e=Yd("IFRAME",c);e.style.display="none";e.style.width="0px";e.style.height="0px";e.style.border="none";e.style.zIndex="-1000";e.style.left="-1000px";e.style.top="-1000px";e.name=b;c.body.appendChild(e)}else a.setTimeout(d,5)};d()};function kg(a){if(a!=null)return lg(a)}function lg(a){return Hb(a)?Number(a):String(a)};var og=function(a){this.g=a;var b=Zf(this.g.document);try{var c=b?Wf(b):null}catch(e){c=null}(b=c)?(c=Q(b,Tf,5)||null,b=R(b,Sf,7,P()),b=mg(b!=null?b:[]),c={Ea:c,Ha:b}):c={Ea:null,Ha:null};b=c;c=ng(b.Ha);b=b.Ea;if(b!=null&&oc(M(b,2))!=null&&S(b,2).length!==0){var d=Hc(b,ke,1)?Q(b,ke,1):le();b={uspString:S(b,2),ja:me(d)}}else b=null;this.l=b&&c?c.ja>b.ja?c.uspString:b.uspString:b?b.uspString:c?c.uspString:null;this.tcString=(c=Yf(a.document))&&oc(M(c,1))!=null?S(c,1):null;this.j=(a=Yf(a.document))&&oc(M(a,2))!=null?S(a,2):null},rg=function(a){a!==a.top||a.__uspapi||a.frames.__uspapiLocator||(a=new og(a),pg(a),qg(a))},pg=function(a){!a.l||a.g.__uspapi||a.g.frames.__uspapiLocator||(a.g.__uspapiManager="fc",jg(a.g,"__uspapiLocator"),xa("__uspapi",function(b,c,d){typeof d==="function"&&b==="getUSPData"&&d({version:1,uspString:a.l},!0)},a.g),eg(a.g))},mg=function(a){a=v(a,"find").call(a,function(b){return b&&T(b,1)===13});if(a==null?0:oc(M(a,2))!=null)try{return hg(S(a,2))}catch(b){}return null},ng=function(a){if(a==null||oc(M(a,1))==null||S(a,1).length===0||R(a,fg,2,P()).length===0)return null;var b=S(a,1);try{var c=fe(b.split("~")[0]);var d=v(b,"includes").call(b,"~")?b.split("~").slice(1):[]}catch(e){return null}a=R(a,fg,2,P()).reduce(function(e,f){var g=sg(e);g=fd(g,1);g=lg(g);var h=sg(f);h=fd(h,1);return g>lg(h)?e:f});c=Ic(c,3,ac,P()).indexOf(dd(a,1));return c===-1||c>=d.length?null:{uspString:ig(d[c],dd(a,1)),ja:me(sg(a))}},sg=function(a){return Hc(a,ke,2)?Q(a,ke,2):le()},qg=function(a){!a.tcString||a.g.__tcfapi||a.g.frames.__tcfapiLocator||(a.g.__tcfapiManager="fc",jg(a.g,"__tcfapiLocator"),a.g.__tcfapiEventListeners=a.g.__tcfapiEventListeners||[],xa("__tcfapi",function(b,c,d,e){if(typeof d==="function")if(c&&(c>2.2||c<=1))d(null,!1);else switch(c=a.g.__tcfapiEventListeners,b){case "ping":d({gdprApplies:!0,cmpLoaded:!0,cmpStatus:"loaded",displayStatus:"disabled",apiVersion:"2.2",cmpVersion:2,cmpId:300});break;case "addEventListener":b=c.push(d)-1;a.tcString?(e=se(a.tcString),e.addtlConsent=a.j!=null?a.j:void 0,e.cmpStatus="loaded",e.eventStatus="tcloaded",b!=null&&(e.listenerId=b),b=e):b=null;d(b,!0);break;case "removeEventListener":e!==void 0&&c[e]?(c[e]=null,d(!0)):d(!1);break;case "getInAppTCData":case "getVendorList":d(null,!1);break;case "getTCData":d(null,!1)}},a.g),bg(a.g))};var tg=qa(["https://pagead2.googlesyndication.com/pagead/managed/dict/","/gpt"]),ug=qa(["https://securepubads.g.doubleclick.net/pagead/managed/dict/","/gpt"]);function vg(a,b,c){try{var d=a.createElement("link"),e;if((e=d.relList)!=null&&e.supports("compression-dictionary")&&Ma()){if(b instanceof Ed)d.href=Gd(b).toString(),d.rel="compression-dictionary";else{if(Ld.indexOf("compression-dictionary")===-1)throw Error('TrustedResourceUrl href attribute required with rel="compression-dictionary"');var f=Hd.test(b)?b:void 0;f!==void 0&&(d.href=f,d.rel="compression-dictionary")}a.head.appendChild(d)}}catch(g){c.H({methodName:1296,J:g})}}function wg(a,b){return b?Pd(tg,a):Pd(ug,a)};var xg=null;function yg(a,b){var c=R(a,bf,2,P());if(!c.length)return zg(a,b);a=T(a,1);if(a===1){var d=yg(c[0],b);return d.success?{success:!0,value:!d.value}:d}c=Na(c,function(h){return yg(h,b)});switch(a){case 2:var e;return(e=(d=v(c,"find").call(c,function(h){return h.success&&!h.value}))!=null?d:v(c,"find").call(c,function(h){return!h.success}))!=null?e:{success:!0,value:!0};case 3:var f,g;return(g=(f=v(c,"find").call(c,function(h){return h.success&&h.value}))!=null?f:v(c,"find").call(c,function(h){return!h.success}))!=null?g:{success:!0,value:!1};default:return{success:!1,C:3}}}function zg(a,b){var c=Tc(a,cf);a:{switch(c){case 3:var d=id(a,3,cf);break a;case 4:d=id(a,4,cf);break a;case 5:d=id(a,5,cf);break a}d=void 0}if(!d)return{success:!1,C:2};b=(b=b[c])&&b[d];if(!b)return{success:!1,P:d,Z:c,C:1};try{var e=b.apply;var f=Ic(a,8,oc,P());var g=e.call(b,null,B(f))}catch(h){return{success:!1,P:d,Z:c,C:2}}e=T(a,1);if(e===4)return{success:!0,value:!!g};if(e===5)return{success:!0,value:g!=null};if(e===12)a=S(a,Uc(a,df,7));else a:{switch(c){case 4:a=gd(a,Uc(a,df,6));break a;case 5:a=S(a,Uc(a,df,7));break a}a=void 0}if(a==null)return{success:!1,P:d,Z:c,C:3};if(e===6)return{success:!0,value:g===a};if(e===9)return{success:!0,value:g!=null&&Aa(String(g),a)===0};if(g==null)return{success:!1,P:d,Z:c,C:4};switch(e){case 7:c=g<a;break;case 8:c=g>a;break;case 12:c=yb(a)&&yb(g)&&(new RegExp(a)).test(g);break;case 10:c=g!=null&&Aa(String(g),a)===-1;break;case 11:c=g!=null&&Aa(String(g),a)===1;break;default:return{success:!1,C:3}}return{success:!0,value:c}}function Ag(a,b){return a?b?yg(a,b):{success:!1,C:1}:{success:!0,value:!0}};var Bg=function(a){this.i=L(a)};x(Bg,U);var Cg=function(a){return Ic(a,4,oc,P())};var Dg=function(a){this.i=L(a)};x(Dg,U);Dg.prototype.getValue=function(){return Q(this,Bg,2)};var Eg=function(a){this.i=L(a)};x(Eg,U);var Fg=rd(Eg),Gg=[1,2,3,6,7,8];var Hg=function(a,b,c){var d=d===void 0?new If(6,"unknown",b):d;this.D=a;this.o=c;this.j=d;this.g=[];this.l=a>0&&Ud()<1/a},Jg=function(a,b,c,d,e,f){if(a.l){var g=Oe(Ne(new Me,b),c);b=We(Te(Se(Ve(Ue(new Re,d),e),g),a.g.slice()),f);b=jf(b);a.j.fa(Ig(a,b));if(f===1||f===3||f===4&&!a.g.some(function(h){return T(h,1)===T(g,1)&&T(h,2)===c}))a.g.push(g),a.g.length>100&&a.g.shift()}},Kg=function(a,b,c,d){if(a.l){var e=new Le;b=O(e,1,b==null?b:$b(b));c=O(b,2,c==null?c:$b(c));d=md(c,3,d);c=new gf;d=$c(c,8,hf,d);a.j.fa(Ig(a,d))}},Lg=function(a,b,c,d,e){if(a.l){var f=new ef;b=Zc(f,1,b);c=md(b,2,c);d=O(c,3,d==null?d:$b(d));if(e.Z===void 0)od(d,4,ff,e.C);else switch(e.Z){case 3:c=new $e;c=od(c,2,af,e.P);e=md(c,1,e.C);$c(d,5,ff,e);break;case 4:c=new $e;c=od(c,3,af,e.P);e=md(c,1,e.C);$c(d,5,ff,e);break;case 5:c=new $e,c=od(c,4,af,e.P),e=md(c,1,e.C),$c(d,5,ff,e)}e=new gf;e=$c(e,9,hf,d);a.j.fa(Ig(a,e))}},Ig=function(a,b){var c=Date.now();c=v(Number,"isFinite").call(Number,c)?Math.round(c):0;b=kd(b,1,c);c=Zd(window);b=kd(b,2,c);return kd(b,6,a.D)};var W=function(a){var b="la";if(a.la&&a.hasOwnProperty(b))return a.la;b=new a;return a.la=b};var Mg=function(){var a={};this.B=(a[3]={},a[4]={},a[5]={},a)};var Ng=/^true$/.test("false");function Og(a,b){switch(b){case 1:return id(a,1,Gg);case 2:return id(a,2,Gg);case 3:return id(a,3,Gg);case 6:return id(a,6,Gg);case 8:return id(a,8,Gg);default:return null}}function Pg(a,b){if(!a)return null;switch(b){case 1:return cd(a,1);case 7:return S(a,3);case 2:return gd(a,2);case 3:return S(a,3);case 6:return Cg(a);case 8:return Cg(a);default:return null}}var Qg=zd(function(){if(!Ng)return{};try{var a=a===void 0?window:a;try{var b=a.sessionStorage.getItem("GGDFSSK")}catch(c){b=null}if(b)return JSON.parse(b)}catch(c){}return{}});function Rg(a,b,c,d){var e=d=d===void 0?0:d,f,g;W(Sg).l[e]=(g=(f=W(Sg).l[e])==null?void 0:f.add(b))!=null?g:(new u.Set).add(b);e=Qg();if(e[b]!=null)return e[b];b=Tg(d)[b];if(!b)return c;b=Fg(JSON.stringify(b));b=Ug(b);a=Pg(b,a);return a!=null?a:c}function Ug(a){var b=W(Mg).B;if(b&&Tc(a,Gg)!==8){var c=Oa(R(a,Dg,5,P()),function(f){f=Ag(Q(f,bf,1),b);return f.success&&f.value});if(c){var d;return(d=c.getValue())!=null?d:null}}var e;return(e=Q(a,Bg,4))!=null?e:null}var Sg=function(){this.j={};this.o=[];this.l={};this.g=new u.Map};function Vg(a,b,c){return!!Rg(1,a,b===void 0?!1:b,c)}function Wg(a,b,c){b=b===void 0?0:b;a=Number(Rg(2,a,b,c));return isNaN(a)?b:a}function Xg(a,b,c){b=b===void 0?"":b;a=Rg(3,a,b,c);return typeof a==="string"?a:b}function Yg(a,b,c){b=b===void 0?[]:b;a=Rg(6,a,b,c);return Array.isArray(a)?a:b}function Zg(a,b,c){b=b===void 0?[]:b;a=Rg(8,a,b,c);return Array.isArray(a)?a:b}function Tg(a){return W(Sg).j[a]||(W(Sg).j[a]={})}function $g(a,b){var c=Tg(b);Vd(a,function(d,e){if(c[e]){d=Fg(JSON.stringify(d));var f=Uc(d,Gg,8);if(Zb(M(d,f))!=null){var g=Fg(JSON.stringify(c[e]));f=Vc(d,Bg,4);g=Cg(Wc(g,Bg,4));bd(f,g)}c[e]=K(d)}else c[e]=d})}function ah(a,b,c,d,e){e=e===void 0?!1:e;var f=[],g=[];b=y(b);for(var h=b.next();!h.done;h=b.next()){h=h.value;for(var k=Tg(h),m=y(a),n=m.next();!n.done;n=m.next()){n=n.value;var l=Tc(n,Gg),p=Og(n,l);if(p){var r=void 0,t=void 0,z=void 0;var A=(r=(z=W(Sg).g.get(h))==null?void 0:(t=z.get(p))==null?void 0:t.slice(0))!=null?r:[];a:{r=p;t=l;z=new Xe;switch(t){case 1:od(z,1,Ye,r);break;case 2:od(z,2,Ye,r);break;case 3:od(z,3,Ye,r);break;case 6:od(z,4,Ye,r);break;case 8:od(z,6,Ye,r);break;default:A=void 0;break a}Nc(z,5,A,$b);A=z}if(r=A)t=void 0,r=!((t=W(Sg).l[h])==null||!t.has(p));r&&f.push(A);if(l===8&&k[p])A=Fg(JSON.stringify(k[p])),l=Vc(n,Bg,4),A=Cg(Wc(A,Bg,4)),bd(l,A);else{if(l=A)r=void 0,l=!((r=W(Sg).g.get(h))==null||!r.has(p));l&&g.push(A)}e||(l=p,A=h,r=d,t=W(Sg),t.g.has(A)||t.g.set(A,new u.Map),t.g.get(A).has(l)||t.g.get(A).set(l,[]),r&&t.g.get(A).get(l).push(r));k[p]=K(n)}}}if(f.length||g.length)a=d!=null?d:void 0,c.l&&c.o&&(d=new Ze,f=ad(d,2,f),g=ad(f,3,g),a&&jd(g,1,a),f=new gf,g=$c(f,7,hf,g),c.j.fa(Ig(c,g)))}function bh(a,b){b=Tg(b);a=y(a);for(var c=a.next();!c.done;c=a.next()){c=c.value;var d=Fg(JSON.stringify(c)),e=Tc(d,Gg);(d=Og(d,e))&&(b[d]||(b[d]=c))}}function ch(){return v(Object,"keys").call(Object,W(Sg).j).map(function(a){return Number(a)})}function dh(a){(q=W(Sg).o,v(q,"includes")).call(q,a)||$g(Tg(4),a)};function X(a,b,c){c.hasOwnProperty(a)||Object.defineProperty(c,String(a),{value:b})}function Y(a,b,c){return b[a]||c}function eh(a){X(5,Vg,a);X(6,Wg,a);X(7,Xg,a);X(8,Yg,a);X(17,Zg,a);X(13,bh,a);X(15,dh,a)}function fh(a){X(4,function(b){W(Mg).B=b},a);X(9,function(b,c){var d=W(Mg);d.B[3][b]==null&&(d.B[3][b]=c)},a);X(10,function(b,c){var d=W(Mg);d.B[4][b]==null&&(d.B[4][b]=c)},a);X(11,function(b,c){var d=W(Mg);d.B[5][b]==null&&(d.B[5][b]=c)},a);X(14,function(b){for(var c=W(Mg),d=y([3,4,5]),e=d.next();!e.done;e=d.next())e=e.value,v(Object,"assign").call(Object,c.B[e],b[e])},a)}function gh(a){a.hasOwnProperty("init-done")||Object.defineProperty(a,"init-done",{value:!0})};var hh=function(){};hh.prototype.g=function(){};hh.prototype.j=function(){};hh.prototype.l=function(){return[]};var ih=function(a,b,c){a.j=function(d,e){Y(2,b,function(){return[]})(d,c,e)};a.l=function(){return Y(3,b,function(){return[]})(c)};a.g=function(d){Y(16,b,function(){})(d,c)}};function jh(a){W(hh).g(a)}function kh(){return W(hh).l()};function lh(a,b){try{var c=a.split(".");a=D;for(var d=0,e;a!=null&&d<c.length;d++)e=a,a=a[c[d]],typeof a==="function"&&(a=e[c[d]]());var f=a;if(typeof f===b)return f}catch(g){}}var mh={},nh={},oh={},ph={},qh=(ph[3]=(mh[8]=function(a){try{return va(a)!=null}catch(b){}},mh[9]=function(a){try{var b=va(a)}catch(c){return}if(a=typeof b==="function")b=b&&b.toString&&b.toString(),a=typeof b==="string"&&b.indexOf("[native code]")!=-1;return a},mh[10]=function(){return window===window.top},mh[6]=function(a){var b=kh();return Array.prototype.indexOf.call(b,Number(a),void 0)>=0},mh[27]=function(a){a=lh(a,"boolean");return a!==void 0?a:void 0},mh[60]=function(a){try{return!!D.document.querySelector(a)}catch(b){}},mh[80]=function(a){try{return!!D.matchMedia(a).matches}catch(b){}},mh[69]=function(a){var b=D.document;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!(q=c.features(),v(q,"includes")).call(q,a))},mh[70]=function(a){var b=D.document;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!(q=c.allowedFeatures(),v(q,"includes")).call(q,a))},mh[79]=function(a){var b=D.navigator;b=b===void 0?navigator:b;try{var c,d;var e=!!((c=b.protectedAudience)==null?0:(d=c.queryFeatureSupport)==null?0:d.call(c,a))}catch(f){e=!1}return e},mh),ph[4]=(nh[3]=function(){return Wd()},nh[6]=function(a){a=lh(a,"number");return a!==void 0?a:void 0},nh),ph[5]=(oh[2]=function(){return window.location.href},oh[3]=function(){try{return window.top.location.hash}catch(a){return""}},oh[4]=function(a){a=lh(a,"string");return a!==void 0?a:void 0},oh[12]=function(a){try{var b=lh(a,"string");if(b!==void 0)return atob(b)}catch(c){}},oh),ph);function rh(){var a=a===void 0?D:a;return a.ggeac||(a.ggeac={})};var sh=function(a){this.i=L(a)};x(sh,U);sh.prototype.getId=function(){return dd(this,1)};var th=function(a){this.i=L(a)};x(th,U);var uh=function(a){return R(a,sh,2,P())};var vh=function(a){this.i=L(a)};x(vh,U);var wh=function(a){this.i=L(a)};x(wh,U);var xh=function(a){this.i=L(a)};x(xh,U);function yh(a){var b={};return zh((b[0]=new u.Map,b[1]=new u.Map,b[2]=new u.Map,b),a)}function zh(a,b){for(var c=new u.Map,d=y(v(a[1],"entries").call(a[1])),e=d.next();!e.done;e=d.next()){var f=y(e.value);e=f.next().value;f=f.next().value;f=f[f.length-1];c.set(e,f.Ra+f.Ma*f.Na)}b=y(b);for(d=b.next();!d.done;d=b.next())for(d=d.value,e=R(d,th,2,P()),e=y(e),f=e.next();!f.done;f=e.next())if(f=f.value,uh(f).length!==0){var g=ed(f,8);if(T(f,4)&&!T(f,13)&&!T(f,14)){var h=void 0;g=(h=c.get(T(f,4)))!=null?h:0;h=ed(f,1)*uh(f).length;c.set(T(f,4),g+h)}h=[];for(var k=0;k<uh(f).length;k++){var m={Ra:g,Ma:ed(f,1),Na:uh(f).length,kb:k,ca:T(d,1),ga:f,G:uh(f)[k]};h.push(m)}Ah(a[2],T(f,10),h)||Ah(a[1],T(f,4),h)||Ah(a[0],uh(f)[0].getId(),h)}return a}function Ah(a,b,c){if(!b)return!1;a.has(b)||a.set(b,[]);var d;(d=a.get(b)).push.apply(d,B(c));return!0};var Bh=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Ch=function(a){return a?decodeURI(a):a},Dh=/#|$/,Eh=function(a,b){var c=a.search(Dh);a:{var d=0;for(var e=b.length;(d=a.indexOf(b,d))>=0&&d<c;){var f=a.charCodeAt(d-1);if(f==38||f==63)if(f=a.charCodeAt(d+e),!f||f==61||f==38||f==35)break a;d+=e+1}d=-1}if(d<0)return null;e=a.indexOf("&",d);if(e<0||e>c)e=c;d+=b.length+1;return decodeURIComponent(a.slice(d,e!==-1?e:0).replace(/\+/g," "))};function Fh(a){var b=a.length;if(b===0)return 0;for(var c=305419896,d=0;d<b;d++)c^=(c<<5)+(c>>2)+a.charCodeAt(d)&4294967295;return c>0?c:4294967296+c};function Gh(){var a=Zd(window);a=a===void 0?Ud():a;return function(b){return Fh(b+" + "+a)%1E3}};var Hh=[12,13,20],Ih=function(a,b,c,d){d=d===void 0?{}:d;var e=d.ka===void 0?!1:d.ka;d=d.qb===void 0?[]:d.qb;this.N=a;this.A=c;this.o={};this.ka=e;a={};this.g=(a[b]=[],a[4]=[],a);this.j={};this.l={};var f=f===void 0?window:f;if(xg===null){xg="";try{b="";try{b=f.top.location.hash}catch(h){b=f.location.hash}if(b){var g=b.match(/\bdeid=([\d,]+)/);xg=g?g[1]:""}}catch(h){}}if(f=xg)for(f=y(f.split(",")||[]),g=f.next();!g.done;g=f.next())(g=Number(g.value))&&(this.j[g]=!0);d=y(d);for(f=d.next();!f.done;f=d.next())this.j[f.value]=!0},Lh=function(a,b,c,d){var e=[],f;if(f=b!==9)a.o[b]?f=!0:(a.o[b]=!0,f=!1);if(f)return Jg(a.A,b,c,e,[],4),e;f=v(Hh,"includes").call(Hh,b);for(var g=[],h=[],k=y([0,1,2]),m=k.next();!m.done;m=k.next()){m=m.value;for(var n=y(v(a.N[m],"entries").call(a.N[m])),l=n.next();!l.done;l=n.next()){var p=y(l.value);l=p.next().value;p=p.next().value;var r=l,t=p;l=new Pe;p=t.filter(function(cb){return cb.ca===b&&a.j[cb.G.getId()]&&Jh(a,cb)});if(p.length)for(l=y(p),p=l.next();!p.done;p=l.next())h.push(p.value.G);else if(!a.ka){p=void 0;m===2?(p=d[1],od(l,2,Qe,r)):p=d[0];var z=void 0,A=void 0;p=(A=(z=p)==null?void 0:z(String(r)))!=null?A:m===2&&T(t[0].ga,11)===1?void 0:d[0](String(r));if(p!==void 0){r=y(t);for(t=r.next();!t.done;t=r.next())if(t=t.value,t.ca===b){z=p-t.Ra;var pa=t;A=pa.Ma;var bb=pa.Na;pa=pa.kb;z<0||z>=A*bb||z%bb!==pa||!Jh(a,t)||(z=T(t.ga,13),z!==0&&z!==void 0&&(A=a.l[String(z)],A!==void 0&&A!==t.G.getId()?Kg(a.A,a.l[String(z)],t.G.getId(),z):a.l[String(z)]=t.G.getId()),h.push(t.G))}Tc(l,Qe)!==0&&(jd(l,3,p),g.push(l))}}}}d=y(h);for(h=d.next();!h.done;h=d.next())h=h.value,k=h.getId(),e.push(k),Kh(a,k,f?4:c),ah(R(h,Eg,2,P()),f?ch():[c],a.A,k);Jg(a.A,b,c,e,g,1);return e},Kh=function(a,b,c){a.g[c]||(a.g[c]=[]);a=a.g[c];v(a,"includes").call(a,b)||a.push(b)},Jh=function(a,b){var c=W(Mg).B,d=Ag(Q(b.ga,bf,3),c);if(!d.success)return Lg(a.A,Q(b.ga,bf,3),b.ca,b.G.getId(),d),!1;if(!d.value)return!1;c=Ag(Q(b.G,bf,3),c);return c.success?c.value?!0:!1:(Lg(a.A,Q(b.G,bf,3),b.ca,b.G.getId(),c),!1)},Mh=function(a,b){b=b.map(function(c){return new vh(c)}).filter(function(c){return!v(Hh,"includes").call(Hh,T(c,1))});a.N=zh(a.N,b)},Nh=function(a,b){X(1,function(c){a.j[c]=!0},b);X(2,function(c,d,e){return Lh(a,c,d,e)},b);X(3,function(c){return(a.g[c]||[]).concat(a.g[4])},b);X(12,function(c){return void Mh(a,c)},b);X(16,function(c,d){return void Kh(a,c,d)},b)};var Oh=function(){var a={};this.g=function(b,c){return a[b]!=null?a[b]:c};this.j=function(b,c){return a[b]!=null?a[b]:c};this.K=function(b,c){return a[b]!=null?a[b]:c};this.l=function(b,c){return a[b]!=null?a[b]:c};this.D=function(b,c){return a[b]!=null?c.concat(a[b]):c};this.o=function(){}};function Ph(a){return W(Oh).j(a.g,a.defaultValue)};var Qh=function(){this.g=function(){}},Rh=function(a,b){a.g=Y(14,b,function(){})};function Sh(a){W(Qh).g(a)};var Th,Uh,Vh,Wh,Xh,Yh;function Zh(a){var b=a.Za;var c=a.B;var d=a.config;var e=a.Ua===void 0?rh():a.Ua;var f=a.Da===void 0?0:a.Da;var g=a.A===void 0?new Hg((Wh=kg((Th=Q(b,wh,5))==null?void 0:fd(Th,2)))!=null?Wh:0,(Xh=kg((Uh=Q(b,wh,5))==null?void 0:fd(Uh,4)))!=null?Xh:0,(Yh=(Vh=Q(b,wh,5))==null?void 0:cd(Vh,3))!=null?Yh:!1):a.A;a=a.N===void 0?yh(R(b,vh,2,P(tb))):a.N;e.hasOwnProperty("init-done")?(Y(12,e,function(){})(R(b,vh,2,P()).map(function(h){return K(h)})),Y(13,e,function(){})(R(b,Eg,1,P()).map(function(h){return K(h)}),f),c&&Y(14,e,function(){})(c),$h(f,e)):(Nh(new Ih(a,f,g,d),e),eh(e),fh(e),gh(e),$h(f,e),ah(R(b,Eg,1,P(tb)),[f],g,void 0,!0),Ng=Ng||!(!d||!d.na),Sh(qh),c&&Sh(c))}function $h(a,b){var c=b=b===void 0?rh():b;ih(W(hh),c,a);ai(b,a);a=b;Rh(W(Qh),a);W(Oh).o()}function ai(a,b){var c=W(Oh);c.g=function(d,e){return Y(5,a,function(){return!1})(d,e,b)};c.j=function(d,e){return Y(6,a,function(){return 0})(d,e,b)};c.K=function(d,e){return Y(7,a,function(){return""})(d,e,b)};c.l=function(d,e){return Y(8,a,function(){return[]})(d,e,b)};c.D=function(d,e){return Y(17,a,function(){return[]})(d,e,b)};c.o=function(){Y(15,a,function(){})(b)}};var bi=qa(["https://pagead2.googlesyndication.com/pagead/js/err_rep.js"]),ci=function(){var a=a===void 0?"jserror":a;var b=b===void 0?.01:b;var c=c===void 0?Pd(bi):c;this.g=a;this.l=b;this.j=c};var di=function(){var a;this.W=a=a===void 0?{lb:xd()+(xd()&2097151)*4294967296,Xa:v(Number,"MAX_SAFE_INTEGER")}:a};function ei(a,b){return b>0&&a.lb*b<=a.Xa};var fi=function(a){this.i=L(a)};x(fi,U);var gi=function(a){return cd(a,1)},hi=function(a){return cd(a,2)};function ii(a){a=a===void 0?D:a;return(a=a.performance)&&a.now?a.now():null};function ji(a,b){b=b.google_js_reporting_queue=b.google_js_reporting_queue||[];b.length<2048&&b.push(a)}function ki(a,b){var c=ii(b);c&&ji({label:a,type:9,value:c},b)}function li(a,b,c){var d=!1;d=d===void 0?!1:d;var e=window,f=typeof queueMicrotask!=="undefined";return function(){var g=sa.apply(0,arguments);d&&f&&queueMicrotask(function(){e.google_rum_task_id_counter=e.google_rum_task_id_counter||1;e.google_rum_task_id_counter+=1});var h=ii(),k=3;try{var m=b.apply(this,g)}catch(n){k=13;if(!c)throw n;c(a,n)}finally{e.google_measure_js_timing&&h&&ji(v(Object,"assign").call(Object,{},{label:a.toString(),value:h,duration:(ii()||0)-h,type:k},d&&f&&{taskId:e.google_rum_task_id_counter=e.google_rum_task_id_counter||1}),e)}return m}}function mi(a,b){return li(a,b,function(c,d){var e=new ci;var f=f===void 0?e.l:f;var g=g===void 0?e.g:g;Math.random()>f||(d.error&&d.meta&&d.id||(d=new ue(d,{context:c,id:g})),D.google_js_errors=D.google_js_errors||[],D.google_js_errors.push(d),D.error_rep_loaded||(f=D.document,c=Yd("SCRIPT",f),Kd(c,e.j),(e=f.getElementsByTagName("script")[0])&&e.parentNode&&e.parentNode.insertBefore(c,e),D.error_rep_loaded=!0))})};function Z(a,b){return b==null?"&"+a+"=null":"&"+a+"="+Math.floor(b)}function ni(a,b){return"&"+a+"="+b.toFixed(3)}function oi(){var a=new u.Set;var b=window.googletag;b=(b==null?0:b.apiReady)?b:void 0;try{if(!b)return a;for(var c=b.pubads(),d=y(c.getSlots()),e=d.next();!e.done;e=d.next())a.add(e.value.getSlotId().getDomId())}catch(f){}return a}function pi(a){a=a.id;return a!=null&&(oi().has(a)||v(a,"startsWith").call(a,"google_ads_iframe_")||v(a,"startsWith").call(a,"aswift"))}function qi(a,b,c){if(!a.sources)return!1;switch(ri(a)){case 2:var d=si(a);if(d)return c.some(function(f){return ti(d,f)});break;case 1:var e=ui(a);if(e)return b.some(function(f){return ti(e,f)})}return!1}function ri(a){if(!a.sources)return 0;a=a.sources.filter(function(b){return b.previousRect&&b.currentRect});if(a.length>=1){a=a[0];if(a.previousRect.top<a.currentRect.top)return 2;if(a.previousRect.top>a.currentRect.top)return 1}return 0}function ui(a){return vi(a,function(b){return b.currentRect})}function si(a){return vi(a,function(b){return b.previousRect})}function vi(a,b){return a.sources.reduce(function(c,d){d=b(d);return c?d&&d.width*d.height!==0?d.top<c.top?d:c:c:d},null)}function ti(a,b){var c=Math.min(a.right,b.right)-Math.max(a.left,b.left);a=Math.min(a.bottom,b.bottom)-Math.max(a.top,b.top);return c<=0||a<=0?!1:c*a*100/((b.right-b.left)*(b.bottom-b.top))>=50}var wi=function(){var a={Fa:!0};a=a===void 0?{Fa:!1}:a;this.l=this.j=this.V=this.T=this.M=0;this.za=this.wa=Number.NEGATIVE_INFINITY;this.g=[];this.R={};this.ta=0;this.U=Infinity;this.ra=this.ua=this.va=this.xa=this.Ca=this.D=this.Ba=this.ia=this.o=0;this.sa=!1;this.ha=this.S=this.K=0;this.A=null;this.ya=!1;this.qa=function(){};var b=document.querySelector("[data-google-query-id]");this.Aa=b?b.getAttribute("data-google-query-id"):null;this.Ta=a},xi,yi,Bi=function(){var a=new wi;if(W(Oh).g(Of.g,Of.defaultValue)){var b=window;if(!b.google_plmetrics&&window.PerformanceObserver){b.google_plmetrics=!0;b=["layout-shift","largest-contentful-paint","first-input","longtask"];a.Ta.Fa&&b.push("event");b=y(b);for(var c=b.next();!c.done;c=b.next()){c=c.value;var d={type:c,buffered:!0};c==="event"&&(d.durationThreshold=40);zi(a).observe(d)}Ai(a)}}},zi=function(a){a.A||(a.A=new PerformanceObserver(mi(640,function(b){Ci(a,b)})));return a.A},Ai=function(a){var b=mi(641,function(){var d=document;if(d.prerendering)d=3;else{var e;d=(e={visible:1,hidden:2,prerender:3,preview:4,unloaded:5,"":0}[d.visibilityState||d.webkitVisibilityState||d.mozVisibilityState||""])!=null?e:0}d===2&&Di(a)}),c=mi(641,function(){return void Di(a)});document.addEventListener("visibilitychange",b);document.addEventListener("pagehide",c);a.qa=function(){document.removeEventListener("visibilitychange",b);document.removeEventListener("pagehide",c);zi(a).disconnect()}},Di=function(a){if(!a.ya){a.ya=!0;zi(a).takeRecords();var b="https://pagead2.googlesyndication.com/pagead/gen_204?id=plmetrics";window.LayoutShift&&(b+=ni("cls",a.M),b+=ni("mls",a.T),b+=Z("nls",a.V),window.LayoutShiftAttribution&&(b+=ni("cas",a.D),b+=Z("nas",a.xa),b+=ni("was",a.Ca)),b+=ni("wls",a.ia),b+=ni("tls",a.Ba));window.LargestContentfulPaint&&(b+=Z("lcp",a.va),b+=Z("lcps",a.ua));window.PerformanceEventTiming&&a.sa&&(b+=Z("fid",a.ra));window.PerformanceLongTaskTiming&&(b+=Z("cbt",a.K),b+=Z("mbt",a.S),b+=Z("nlt",a.ha));for(var c=0,d=y(document.getElementsByTagName("iframe")),e=d.next();!e.done;e=d.next())pi(e.value)&&c++;b+=Z("nif",c);c=window.google_unique_id;b+=Z("ifi",typeof c==="number"?c:0);c=kh();b+="&eid="+encodeURIComponent(c.join());b+="&top="+(D===D.top?1:0);b+=a.Aa?"&qqid="+encodeURIComponent(a.Aa):Z("pvsid",Zd(D));window.googletag&&(b+="&gpt=1");c=Math.min(a.g.length-1,Math.floor((a.A?a.ta:performance.interactionCount||0)/50));c>=0&&(c=a.g[c].latency,c>=0&&(b+=Z("inp",c)));window.fetch(b,{keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"});a.qa()}},Ei=function(a,b,c,d){if(!b.hadRecentInput){a.M+=Number(b.value);Number(b.value)>a.T&&(a.T=Number(b.value));a.V+=1;if(c=qi(b,c,d))a.D+=b.value,a.xa++;if(b.startTime-a.wa>5E3||b.startTime-a.za>1E3)a.wa=b.startTime,a.j=0,a.l=0;a.za=b.startTime;a.j+=b.value;c&&(a.l+=b.value);a.j>a.ia&&(a.ia=a.j,a.Ca=a.l,a.Ba=b.startTime+b.duration)}},Ci=function(a,b){var c=xi!==window.scrollX||yi!==window.scrollY?[]:Fi,d=Gi();b=y(b.getEntries());for(var e=b.next(),f={};!e.done;f={F:void 0},e=b.next())switch(f.F=e.value,e=f.F.entryType,e){case "layout-shift":Ei(a,f.F,c,d);break;case "largest-contentful-paint":f=f.F;a.va=Math.floor(f.renderTime||f.loadTime);a.ua=f.size;break;case "first-input":e=f.F;a.ra=Number((e.processingStart-e.startTime).toFixed(3));a.sa=!0;a.g.some(function(g){return function(h){return v(h,"entries").some(function(k){return g.F.duration===k.duration&&g.F.startTime===k.startTime})}}(f))||Hi(a,f.F);break;case "longtask":f=Math.max(0,f.F.duration-50);a.K+=f;a.S=Math.max(a.S,f);a.ha+=1;break;case "event":Hi(a,f.F);break;default:Pb(e)}},Hi=function(a,b){Ii(a,b);var c=a.g[a.g.length-1],d=a.R[b.interactionId];if(d||a.g.length<10||b.duration>c.latency)d?(v(d,"entries").push(b),d.latency=Math.max(d.latency,b.duration)):(b={id:b.interactionId,latency:b.duration,entries:[b]},a.R[b.id]=b,a.g.push(b)),a.g.sort(function(e,f){return f.latency-e.latency}),a.g.splice(10).forEach(function(e){delete a.R[e.id]})},Ii=function(a,b){b.interactionId&&(a.U=Math.min(a.U,b.interactionId),a.o=Math.max(a.o,b.interactionId),a.ta=a.o?(a.o-a.U)/7+1:0)},Gi=function(){var a=v(Array,"from").call(Array,document.getElementsByTagName("iframe")).filter(pi),b=[].concat(B(oi())).map(function(c){return document.getElementById(c)}).filter(function(c){return c!==null});xi=window.scrollX;yi=window.scrollY;return Fi=[].concat(B(a),B(b)).map(function(c){return c.getBoundingClientRect()})},Fi=[];var Ji=function(a){this.i=L(a)};x(Ji,U);Ji.prototype.getVersion=function(){return S(this,2)};var Ki=function(a){this.i=L(a)};x(Ki,U);var Li=function(a,b){return O(a,2,nc(b))},Mi=function(a,b){return O(a,3,nc(b))},Ni=function(a,b){return O(a,4,nc(b))},Oi=function(a,b){return O(a,5,nc(b))},Pi=function(a,b){return O(a,9,nc(b))},Qi=function(a,b){return ad(a,10,b)},Ri=function(a,b){return O(a,11,b==null?b:Vb(b))},Si=function(a,b){return O(a,1,nc(b))},Ti=function(a,b){return O(a,7,b==null?b:Vb(b))};var Ui="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Vi(a){var b;return(b=a.google_tag_data)!=null?b:a.google_tag_data={}}function Wi(a){var b,c;return typeof((b=a.navigator)==null?void 0:(c=b.userAgentData)==null?void 0:c.getHighEntropyValues)==="function"}function Xi(a){if(!Wi(a))return null;var b=Vi(a);if(b.uach_promise)return b.uach_promise;a=a.navigator.userAgentData.getHighEntropyValues(Ui).then(function(c){b.uach!=null||(b.uach=c);return c});return b.uach_promise=a}function Yi(a){var b;return Ri(Qi(Oi(Li(Si(Ni(Ti(Pi(Mi(new Ki,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),((b=a.fullVersionList)==null?void 0:b.map(function(c){var d=new Ji;d=O(d,1,nc(c.brand));return O(d,2,nc(c.version))}))||[]),a.wow64||!1)}function Zi(a){var b,c;return(c=(b=Xi(a))==null?void 0:b.then(function(d){return Yi(d)}))!=null?c:null};var $i=function(a){this.i=L(a)};x($i,U);var aj=function(a){this.i=L(a)};x(aj,U);var bj=function(a){var b=new aj;return Zc(b,1,a)};function cj(a,b,c){try{ub(!b._b_);var d={d:K(a.data),p:a.mb};b._b_=d}catch(e){c.H({methodName:1298,J:e})}};var dj=function(a,b,c){this.g=a;this.L=b;this.j=c};dj.prototype.H=function(a){var b=ei(this.L.W,1E3),c=Ph(Lf),d=ei(this.L.W,c);if(b||d){var e=this.j,f=e.Pa,g=e.Oa,h=e.Ja,k=e.da,m=e.bb;e=e.ab;k=k();var n=a.J;try{var l=yb(n==null?void 0:n.name)?n.name:"Unknown error"}catch(z){l="e.name threw"}try{var p=yb(n==null?void 0:n.message)?n.message:"Caught "+n}catch(z){p="e.message threw"}try{var r=yb(n==null?void 0:n.stack)?n.stack:Error().stack;var t=r?r.split(/\n\s*/):[]}catch(z){t=["e.stack threw"]}r=t;t=new rf;t=kd(t,5,1E3);n=new pf;a=nd(n,1,a.methodName);a=ld(a,2,l);a=ld(a,3,p);a=Nc(a,4,r,mc);a=pd(a);a=$c(t,1,sf,a);l=new qf;f=kd(l,1,f);f=Nc(f,2,k,$b);g=ld(f,3,g);g=pd(g);g=Zc(a,2,g);g=pd(g);g=Ac(g);f=new of;h=ld(f,1,h);m=m==null?void 0:m();m=jd(h,2,m);e=e==null?void 0:e();e=Nc(m,3,e,mc);e=pd(e);e=$c(g,4,tf,e);b&&this.g.sb(e);d&&(kd(e,5,c),Cc(e),Rc(e.i,void 0,sf,1),b=Vc(e,pf,1),b!=null&&O(b,4),this.g.rb(e))}};function ej(a){var b={};b=(b[0]=Gh(),b);W(hh).j(a,b)};var fj={},gj=(fj[253]=!1,fj[246]=[],fj[150]="",fj[263]=!1,fj[36]=/^true$/.test("false"),fj[172]=null,fj[260]=void 0,fj[251]=null,fj),hj=function(){this.g=!1};function ij(a){W(hj).g=!0;return gj[a]}function jj(a,b){W(hj).g=!0;gj[a]=b};var kj=/^(?:https?:)?\/\/(?:www\.googletagservices\.com|securepubads\.g\.doubleclick\.net|(pagead2\.googlesyndication\.com))(\/tag\/js\/gpt(?:_[a-z]+)*\.js)/;function lj(a){var b=a.Ka;var c=a.ib;var d=a.Qa;var e=a.gb;var f=a.Ya;var g=a.cb;var h=b?!kj.test(b.src):!0;a={};b={};var k={};return k[3]=(a[3]=function(){return!h},a[59]=function(){var m=sa.apply(0,arguments),n=v(m,"includes"),l=String,p;var r=r===void 0?window:r;var t;r=(t=(p=Ch(r.location.href.match(Bh)[3]||null))==null?void 0:p.split("."))!=null?t:[];r.length<2?p=null:(p=r[r.length-1],p=p==="uk"||p==="br"||p==="nz"?r.length<3?null:Fh(r.splice(r.length-3).join(".")):Fh(r.splice(r.length-2).join(".")));return n.call(m,l(p))},a[74]=function(){return v(sa.apply(0,arguments),"includes").call(sa.apply(0,arguments),String(Fh(window.location.href)))},a[61]=function(){return e},a[63]=function(){return e||g===".google.ch"},a[73]=function(m){return v(d,"includes").call(d,Number(m))},a),k[4]=(b[1]=function(){return f},b[13]=function(){return c||0},b),k[5]={},k};function mj(a,b){if(W(Oh).g(Mf.g,Mf.defaultValue)){var c=function(d){d.data!=null&&d.data!==""||d.origin.indexOf("android-app://")!==0||(b(),a.removeEventListener("message",c))};a.addEventListener("message",c)}};function nj(a){return!(a==null||!a.src)&&Ch(a.src.match(Bh)[3]||null)==="pagead2.googlesyndication.com"};var oj=qa(["https://pagead2.googlesyndication.com/pagead/managed/js/gpt/","/pubads_impl.js"]),pj=qa(["https://securepubads.g.doubleclick.net/pagead/managed/js/gpt/","/pubads_impl.js"]);function qj(a){a=nj(a)?Pd(oj,"m202505270101"):Pd(pj,"m202505270101");var b=Ph(Qf);return b?Qd(a,new u.Map([["cb",b]])):a};function rj(a,b){var c=ij(246);c=uc(c);c=qd(xh,c);if(!R(c,Eg,1,P()).length&&R(a,Eg,1,P()).length){var d=R(a,Eg,1,P());ad(c,1,d)}!R(c,vh,2,P()).length&&R(a,vh,2,P()).length&&(d=R(a,vh,2,P()),ad(c,2,d));!Hc(c,wh,5)&&Hc(a,wh,5)&&(a=Q(a,wh,5),Zc(c,5,a));jj(246,K(c));Zh({Za:c,B:lj(b),Da:2,config:{na:b.na}});b.Qa.forEach(jh)};var sj=function(a,b,c){dj.call(this,a,b,c);this.j=c};x(sj,dj);var tj=qa(["https://pagead2.googlesyndication.com/pagead/ppub_config"]),uj=qa(["https://securepubads.g.doubleclick.net/pagead/ppub_config"]);function vj(a,b,c,d,e){a=a.location.host;var f=Eh(b.src,"domain");b=Eh(b.src,"network-code");if(a||f||b){var g=new u.Map;a&&g.set("ippd",a);f&&g.set("pppd",f);b&&g.set("pppnc",b);a=g}else a=void 0;a?(c=c?Pd(tj):Pd(uj),c=Qd(c,a),wj(c,d,e)):e(new u.globalThis.Error("no provided or inferred data"))}function wj(a,b,c){u.globalThis.fetch(a.toString(),{method:"GET",credentials:"omit"}).then(function(d){d.status<300?(ki("13",window),d.status===204?b(""):d.text().then(function(e){b(e)})):c(new u.globalThis.Error("resp:"+d.status))}).catch(function(d){d instanceof Error?c(new u.globalThis.Error(d.message)):c(new u.globalThis.Error("fetch error: "+d))})};var xj=function(a,b,c){this.L=a;this.pa=b;this.ea=c;this.g=[]},Bj=function(a,b,c,d,e){var f=e==null?void 0:td(Wc(e,sd,1));(f==null?0:f.length)&&v(b.location.hostname,"includes").call(b.location.hostname,f)?(yj(a),zj(a,{nb:e})):(q=["http:","https:"],v(q,"includes")).call(q,b.location.protocol)?c?(yj(a),vj(Td(b),c,d,function(g){return void zj(a,{pb:g})},function(g){zj(a,{error:g})})):Aj(a,5):Aj(a,4)},yj=function(a){ij(260);jj(260,function(b){a.j!==void 0||a.l?b(a.j,a.l):a.g.push(b)})},zj=function(a,b){var c=b.pb;var d=b.nb;b=b.error;a.j=c!=null?c:d==null?void 0:JSON.stringify(K(d));a.l=b;d=y(a.g);for(var e=d.next();!e.done;e=d.next())e=e.value,e(a.j,a.l);a.g.length=0;Aj(a,b?6:c?3:2)},Aj=function(a,b){var c=Ph(Pf);ei(a.L.W,c)&&a.pa.La.Sa.Ia.ob.X({aa:c,source:b,Va:!E("Android")||Ma()||Ka()||Ja()||E("Silk")?Ma()?2:(Ia()?0:E("Edge"))?3:Ka()?4:(Ia()?0:E("Trident")||E("MSIE"))?5:!E("iPad")&&!E("iPhone")||La()||Ma()||(Ia()?0:E("Coast"))||Ka()||!E("AppleWebKit")?Ja()?6:La()?7:E("Silk")?8:0:9:1});a=a.ea;var d=Ph(Pf);if(ei(a.L.W,d)){var e=a.j,f=e.Ja,g=e.da;c=e.Oa;e=e.Pa;var h=new nf;e=kd(h,2,e);f=ld(e,3,f);e=Math;h=e.trunc;a:{if(u.globalThis.performance){var k=performance.timeOrigin+performance.now();if(v(Number,"isFinite").call(Number,k)&&k>0)break a}k=Date.now();k=v(Number,"isFinite").call(Number,k)&&k>0?k:0}f=kd(f,1,h.call(e,k));g=g();g=Nc(f,4,g,$b);d=kd(g,5,d);c=ld(d,6,c);d=new lf;g=new kf;b=md(g,1,b);b=pd(b);b=$c(d,10,mf,b);b=pd(b);b=Zc(c,7,b);b=pd(b);a.g.tb(b)}};var Cj=function(a){return function(b){b=JSON.parse(b);if(!Array.isArray(b))throw Error("Expected jspb data to be an array, got "+wa(b)+": "+b);mb(b,34);return new a(b)}}($i),Dj=function(a){return function(){var b;(b=a[eb])||(b=new a,mb(b.i,34),b=a[eb]=b);return b}}($i);function Ej(a,b){try{var c=vb;if(!yb(a)){var d,e,f=(e=(d=typeof c==="function"?c():c)==null?void 0:d.concat("\n"))!=null?e:"";throw Error(f+String(a));}return Cj(a)}catch(g){return b.H({methodName:838,J:g}),Dj()}};function Fj(){var a;return(a=D.googletag)!=null?a:D.googletag={cmd:[]}}function Gj(a,b){var c=Fj();c.hasOwnProperty(a)||(c[a]=b)};function Hj(){var a=sttc,b=Ij(),c=b.L,d=b.pa,e=b.ea;Xa(function(A){e.H({methodName:1189,J:A})});b=Fj();a=Ej(a,e);ub(!W(hj).g);v(Object,"assign").call(Object,gj,b._vars_);b._vars_=gj;a&&(cd(a,3)&&jj(36,!0),S(a,6)&&jj(150,S(a,6)),hi(Wc(a,fi,13))&&jj(263,!0));var f=Wc(a,xh,1),g={gb:gi(Wc(a,fi,13)),ib:dd(a,2),Qa:Ic(a,10,ac,P()),Ya:dd(a,7),cb:S(a,6),na:cd(a,4)},h=Q(a,ud,9),k=window,m=k.document;Gj("_loaded_",!0);Gj("cmd",[]);var n,l=(n=Jj(m))!=null?n:Kj(m);Lj(f,k,v(Object,"assign").call(Object,{},{Ka:l},g));try{Bi()}catch(A){}ki("1",k);n=qj(l);f=(l==null?void 0:l.crossOrigin)==="anonymous";g=Ph(Nf);if(ei(c.W,g)){var p=d.La.Sa.Ia;p.hb.X({aa:g,ma:(l==null?void 0:l.crossOrigin)==="anonymous",oa:nj(l)});p.eb.X({aa:g,ma:f,oa:Ch(n.toString().match(Bh)[3]||null)==="pagead2.googlesyndication.com"})}var r=!1;cj({data:pd(bj(a)),mb:function(){return r}},b,e);if(!Mj(m)){g="gpt-impl-"+Math.random();try{Md(m,Nd(n,{id:g,nonce:Jd(document),Ga:f?"anonymous":void 0}))}catch(A){}m.getElementById(g)&&(b._loadStarted_=!0)}if(!b._loadStarted_){g=Yd("SCRIPT");Kd(g,n);g.async=!0;f&&(g.crossOrigin="anonymous");n=m.body;f=m.documentElement;var t,z;((z=(t=m.head)!=null?t:n)!=null?z:f).appendChild(g);b._loadStarted_=!0}if(k===k.top)try{rg(k,Wc(a,fi,13))}catch(A){e.H({methodName:1209,J:A})}Bj(new xj(c,d,e),k,l,nj(l),h);mj(k,function(){r=!0});S(a,14)&&vg(k.document,wg(S(a,14),nj(l)),e)}function Ij(){var a=Zd(window),b=new di,c=new If(11,"m202505270101",1E3);return{pa:c,L:b,ea:new sj(c,b,{Pa:a,Oa:window.document.URL,Ja:"m202505270101",da:kh})}}function Jj(a){return(a=a.currentScript)?a:null}function Kj(a){var b;a=y((b=a.scripts)!=null?b:[]);for(b=a.next();!b.done;b=a.next())if(b=b.value,v(b.src,"includes").call(b.src,"/tag/js/gpt"))return b;return null}function Lj(a,b,c){jj(172,c.Ka);rj(a,c);ej(12);ej(5);(a=Zi(b))&&a.then(function(d){return void jj(251,JSON.stringify(K(d)))});Xd(W(Oh).l(Rf.g,Rf.defaultValue),b.document)}function Mj(a){var b=Jj(a);return a.readyState==="complete"||a.readyState==="loaded"||!(b==null||!b.async)};try{Hj()}catch(a){try{Ij().ea.H({methodName:420,J:a})}catch(b){}};}).call(this,"[[[[739271509,null,null,[1]],[740838399,null,null,[1]],[null,7,null,[null,0.1]],[null,null,null,[],[[[4,null,83],[null,null,null,[\"1 bidderRequests.bids bidder userIdAsEids.source\",\"2 bidderRequests.bids.userIdAsEids source provider\",\"3 bidderRequests.bids bidder ortb2Imp.ext.tid?\",\"5 bidderRequests.bids bidder mediaTypes.banner\",\"6 bidderRequests.bids bidder mediaTypes.native?\",\"7 bidderRequests.bids bidder mediaTypes.video\",\"8 bidderRequests.bids bidder ortb2Imp.ext.gpid?\",\"9 bidderRequests.bids bidder ortb2.site.content.data.ext.segment?\",\"10 bidderRequests.bids bidder ortb2.site.page\",\"11 bidderRequests.bids bidder ortb2.user.data.segment?\",\"12 bidderRequests.bids bidder ortb2.user.data.ext.segtax?\",\"13 bidsReceived adId creativeId\",\"14 bidderRequests.bids.userIdAsEids source uids.ext.provider\",\"15 bidderRequests.bids.userIdAsEids source uids.atype\",\"16 bidderRequests.bids.userIdAsEids source uids.length\",\"17 bidsReceived adId ttl\",\"18 bidsReceived adId meta.primaryCatId\",\"19 bidsReceived adId meta.secondaryCatIds\"]]]],*********],[null,*********,null,null,[[[4,null,83],[null,1]]]],[null,*********,null,null,[[[4,null,83],[null,100]]]],[null,*********,null,[null,-1]],[null,*********,null,[null,-1],[[[4,null,83],[null,5000]]]],[null,*********,null,[null,-1],[[[4,null,83],[null,60000]]]],[null,null,null,[null,null,null,[\"1 dbm\/(ad|clkk)\"]],[[[4,null,83],[null,null,null,[\"1 dbm\/(ad|clkk)\",\"2 (adsrvr|adserver)\\\\.org\/bid\/\",\"3 criteo.com\/(delivery|[a-z]+\/auction)\",\"4 yahoo.com\/bw\/[a-z]+\/imp\/\",\"5 (adnxs|adnxs-simple).com\/it\",\"6 amazon-adsystem.com\/[a-z\/]+\/impb\"]]]],*********],[null,*********,null,null,[[[4,null,83],[null,1]]]],[null,741624914,null,[null,100]],[741624915,null,null,[1]],[null,612427113,null,[null,100]],[null,699982590,null,null,[[[4,null,83],[null,100]]]],[null,720326000,null,[null,1]],[null,749055567,null,[null,100]],[null,732179314,null,[null,10]],[45681222,null,null,[]],[null,578655462,null,[null,1000]],[698519058,null,null,[1]],[667794963,null,null,[]],[736254283,null,null,[1]],[null,704895900,null,[null,1000]],[null,680683506,null,[null,1000]],[697841467,null,null,[1]],[null,625028672,null,[null,100]],[null,629733890,null,[null,1000]],[null,695925491,null,[null,20]],[null,null,null,[],null,489560439],[null,null,null,[],null,505762507],[null,1921,null,[null,72]],[null,1920,null,[null,12]],[null,426169222,null,[null,1000]],[null,377289019,null,[null,10000]],[null,750987462,null,[null,10000]],[null,529,null,[null,20]],[null,573282293,null,[null,0.01]],[null,684553008,null,[null,100]],[45624259,null,null,[],[[[4,null,59,null,null,null,null,[\"2452487976\"]],[1]]]],[45627954,null,null,[1]],[45646888,null,null,[]],[45622305,null,null,[1]],[null,447000223,null,[null,0.01]],[360245597,null,null,[1]],[null,716359135,null,[null,10]],[null,716359138,null,[null,50]],[null,716359132,null,[null,100]],[null,716359134,null,[null,1000]],[null,716359137,null,[null,0.25]],[null,716359136,null,[null,10]],[null,716359133,null,[null,5]],[753304527,null,null,[1]],[629201869,null,null,[1]],[null,729624435,null,[null,10000]],[null,729624436,null,[null,500]],[null,550718589,null,[null,250],[[[3,[[4,null,15,null,null,null,null,[\"22814497764\"]],[4,null,15,null,null,null,null,[\"6581\"]],[4,null,15,null,null,null,null,[\"18190176\"]],[4,null,15,null,null,null,null,[\"21881754602\"]],[4,null,15,null,null,null,null,[\"6782\"]],[4,null,15,null,null,null,null,[\"309565630\"]],[4,null,15,null,null,null,null,[\"22306534072\"]],[4,null,15,null,null,null,null,[\"7229\"]],[4,null,15,null,null,null,null,[\"28253241\"]],[4,null,15,null,null,null,null,[\"1254144\"]],[4,null,15,null,null,null,null,[\"21732118914\"]],[4,null,15,null,null,null,null,[\"5441\"]],[4,null,15,null,null,null,null,[\"162717810\"]],[4,null,15,null,null,null,null,[\"51912183\"]],[4,null,15,null,null,null,null,[\"23202586\"]],[4,null,15,null,null,null,null,[\"44520695\"]],[4,null,15,null,null,null,null,[\"1030006\"]],[4,null,15,null,null,null,null,[\"21830601346\"]],[4,null,15,null,null,null,null,[\"23081961\"]],[4,null,15,null,null,null,null,[\"21880406607\"]],[4,null,15,null,null,null,null,[\"93656639\"]],[4,null,15,null,null,null,null,[\"1020351\"]],[4,null,15,null,null,null,null,[\"5931321\"]],[4,null,15,null,null,null,null,[\"3355436\"]],[4,null,15,null,null,null,null,[\"22106840220\"]],[4,null,15,null,null,null,null,[\"22875833199\"]],[4,null,15,null,null,null,null,[\"32866417\"]],[4,null,15,null,null,null,null,[\"8095840\"]],[4,null,15,null,null,null,null,[\"71161633\"]],[4,null,15,null,null,null,null,[\"22668755367\"]],[4,null,15,null,null,null,null,[\"6177\"]],[4,null,15,null,null,null,null,[\"147246189\"]],[4,null,15,null,null,null,null,[\"22152718\"]],[4,null,15,null,null,null,null,[\"21751243814\"]],[4,null,15,null,null,null,null,[\"22013536576\"]],[4,null,15,null,null,null,null,[\"4444\"]],[4,null,15,null,null,null,null,[\"44890869\"]],[4,null,15,null,null,null,null,[\"248415179\"]],[4,null,15,null,null,null,null,[\"5293\"]],[4,null,15,null,null,null,null,[\"21675937462\"]],[4,null,15,null,null,null,null,[\"21726375739\"]],[4,null,15,null,null,null,null,[\"1002212\"]],[4,null,15,null,null,null,null,[\"6718395\"]]]],[null,500]]]],[null,575880738,null,[null,10]],[null,586681283,null,[null,100]],[null,45679761,null,[null,30000]],[null,732179799,null,[null,250]],[null,745376890,null,[null,1]],[null,745376891,null,[null,2]],[null,635239304,null,[null,100]],[null,740510593,null,[null,0.3]],[null,618260805,null,[null,10]],[696192462,null,null,[1]],[null,532520346,null,[null,30]],[null,723123766,null,[null,100]],[null,735866756,null,[null,100]],[null,758860411,null,[null,60]],[null,751161866,null,[null,30000]],[null,630428304,null,[null,100]],[730909248,null,null,[1]],[746075837,null,null,[]],[730909251,null,null,null,[[[1,[[4,null,59,null,null,null,null,[\"473346114\"]]]],[1]]]],[null,624264750,null,[null,-1]],[607106222,null,null,[1]],[null,398776877,null,[null,60000]],[null,374201269,null,[null,60000]],[null,371364213,null,[null,60000]],[null,682056200,null,[null,100]],[null,376149757,null,[null,0.0025]],[570764855,null,null,[1]],[null,null,579921177,[null,null,\"control_1\\\\.\\\\d\"]],[null,570764854,null,[null,50]],[578725095,null,null,[1]],[758234616,null,null,[1]],[684149381,null,null,[1]],[377936516,null,null,[1]],[null,599575765,null,[null,1000]],[null,null,2,[null,null,\"1-0-45\"]],[null,707091695,null,[null,100]],[null,626653285,null,[null,1000]],[null,626653286,null,[null,2]],[null,642407444,null,[null,10]],[715057423,null,null,[1]],[null,741215712,null,[null,100]],[753273857,null,null,[1]],[752744471,null,null,[1]],[754049714,null,null,[1]],[754089348,null,null,[1]],[748666124,null,null,[1]],[754034072,null,null,[1]],[738873455,null,null,[1]],[736498582,null,null,[1]],[738498807,null,null,[1]],[748334750,null,null,[1]],[748358074,null,null,[1]],[738795943,null,null,[1]],[738555947,null,null,[1]],[742771044,null,null,[1]],[738563763,null,null,[1]],[736888757,null,null,[1]],[740355168,null,null,[1]],[742253121,null,null,[1]],[740342407,null,null,[1]],[null,506394061,null,[null,100]],[null,733365673,null,[null,2],[[[4,null,59,null,null,null,null,[\"**********\"]],[null,1]]]],[null,null,null,[null,null,null,[\"95335247\"]],null,631604025],[null,694630217,null,[null,670]],[null,null,null,[],null,489],[392065905,null,null,null,[[[3,[[4,null,68],[4,null,83]]],[1]]]],[null,360245595,null,[null,500]],[null,715129739,null,[null,30]],[null,681088477,null,[null,100]],[676934885,null,null,[1],[[[4,null,59,null,null,null,null,[\"4214592683\",\"3186860772\",\"2930824068\",\"4127372480\",\"3985777170\",\"2998550476\",\"1946945953\",\"2901923877\",\"1931583037\",\"733037847\",\"287365726\",\"396735883\",\"2445204049\"]],[]]]],[751082905,null,null,[]],[747591488,null,null,[]],[754007193,null,null,[1]],[755022768,null,null,[1]],[754228247,null,null,[1]],[703552063,null,null,[1]],[703102329,null,null,[1]],[703102335,null,null,[1]],[703102334,null,null,[1]],[703102333,null,null,[1]],[703102330,null,null,[1]],[703102332,null,null,[1]],[555237688,null,null,[],[[[2,[[4,null,70,null,null,null,null,[\"browsing-topics\"]],[1,[[4,null,27,null,null,null,null,[\"isSecureContext\"]]]]]],[1]]]],[555237686,null,null,[]],[null,638742197,null,[null,500]],[null,*********,null,[null,2]],[null,697023992,null,[null,500]],[null,null,null,[null,null,null,[\"679602798\",\"965728666\",\"3786422334\",\"4071951799\"]],null,603422363],[590730356,null,null,null,[[[12,null,null,null,4,null,\"Chrome\\\\\/((?!1[0-1]\\\\d)(?!12[0-3])\\\\d{3,})\",[\"navigator.userAgent\"]],[1]]]],[564724551,null,null,null,[[[12,null,null,null,4,null,\"Chrome\\\\\/((?!10\\\\d)(?!11[0-6])\\\\d{3,})\",[\"navigator.userAgent\"]],[1]]]],[null,596918936,null,[null,500]],[null,607730666,null,[null,10]],[693431829,null,null,[1]],[616896918,null,null,[1]],[749916001,null,null,[1]],[638632925,null,null,[1]],[647331452,null,null,[1]],[647331451,null,null,[1]],[null,null,null,[null,null,null,[\"AlK2UR5SkAlj8jjdEc9p3F3xuFYlF6LYjAML3EOqw1g26eCwWPjdmecULvBH5MVPoqKYrOfPhYVL71xAXI1IBQoAAAB8eyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"Amm8\/NmvvQfhwCib6I7ZsmUxiSCfOxWxHayJwyU1r3gRIItzr7bNQid6O8ZYaE1GSQTa69WwhPC9flq\/oYkRBwsAAACCeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiV2ViVmlld1hSZXF1ZXN0ZWRXaXRoRGVwcmVjYXRpb24iLCJleHBpcnkiOjE3NTgwNjcxOTksImlzU3ViZG9tYWluIjp0cnVlfQ==\",\"A9wSqI5i0iwGdf6L1CERNdmsTPgVu44ewj8QxTBYgsv1LCPUVF7YmWOvTappqB1139jAymxUW\/RO8zmMqo4zlAAAAACNeyJvcmlnaW4iOiJodHRwczovL2RvdWJsZWNsaWNrLm5ldDo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\",\"A+d7vJfYtay4OUbdtRPZA3y7bKQLsxaMEPmxgfhBGqKXNrdkCQeJlUwqa6EBbSfjwFtJWTrWIioXeMW+y8bWAgQAAACTeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiRmxlZGdlQmlkZGluZ0FuZEF1Y3Rpb25TZXJ2ZXIiLCJleHBpcnkiOjE3MzY4MTI4MDAsImlzU3ViZG9tYWluIjp0cnVlLCJpc1RoaXJkUGFydHkiOnRydWV9\"]],null,1934],[*********,null,null,[]]],[[3,[[null,[[1337,[[77,null,null,[1]],[78,null,null,[1]],[80,null,null,[1]],[76,null,null,[1]],[84,null,null,[1]],[188,null,null,[1]]]]]],[1000,[[31072561]],[2,[[4,null,70,null,null,null,null,[\"run-ad-auction\"]],[12,null,null,null,4,null,\"FLEDGE_GAM_EXTERNAL_TESTER\",[\"navigator.userAgent\"]]]]],[1,[[31075124,[[null,*********,null,[null,4]]]]],[4,null,74,null,null,null,null,[\"1585821863\",\"3976716532\"]],59],[1,[[31080344],[31090560,[[null,*********,null,[null,4]],[null,null,null,[null,null,null,[\"https:\/\/td.doubleclick.net\",\"https:\/\/f.creativecdn.com\"]],null,663711111],[null,607730666,null,[null,1]],[null,641937776,null,[null,38912]]]],[95345212,[[null,*********,null,[null,4]],[null,607730666,null,[null,1]],[null,641937776,null,[null,38912]]]],[95347233],[95348047,[[null,*********,null,[null,4]],[null,641937776,null,[null,38912]]]],[95356372,[[null,*********,null,[null,4]],[null,607730666,null,[null,1]],[null,641937776,null,[null,38912]]]]],[2,[[4,null,9,null,null,null,null,[\"fetch\"]],[4,null,9,null,null,null,null,[\"navigator.getInterestGroupAdAuctionData\"]],[1,[[4,null,63]]],[1,[[4,null,74,null,null,null,null,[\"1585821863\",\"3976716532\"]]]],[1,[[12,null,null,null,4,null,\".* Edg\/.*\",[\"navigator.userAgent\"]]]]]],59],[10,[[31088080],[31088081]]],[3,[[31089135],[31089136,[[null,607730666,null,[null,1]]]],[31089137,[[null,*********,null,[null,4]],[null,607730666,null,[null,1]],[null,641937776,null,[null,38912]]]]],[2,[[4,null,9,null,null,null,null,[\"fetch\"]],[4,null,9,null,null,null,null,[\"navigator.getInterestGroupAdAuctionData\"]],[1,[[4,null,63]]],[1,[[4,null,74,null,null,null,null,[\"1585821863\",\"3976716532\"]]]],[1,[[12,null,null,null,4,null,\".* Edg\/.*\",[\"navigator.userAgent\"]]]]]],59],[1,[[31089990],[31089992,[[null,*********,null,[null,4]],[null,641937776,null,[null,38912]]]]],[2,[[4,null,9,null,null,null,null,[\"fetch\"]],[4,null,9,null,null,null,null,[\"navigator.getInterestGroupAdAuctionData\"]],[1,[[4,null,63]]],[1,[[4,null,74,null,null,null,null,[\"1585821863\",\"3976716532\"]]]],[1,[[12,null,null,null,4,null,\".* Edg\/.*\",[\"navigator.userAgent\"]]]]]],59],[null,[[31092371,[[null,null,null,[null,null,null,[\"v\",\"1-0-45\"]],null,1]]],[31092372,[[null,null,2,[null,null,\"1-0-45\"]]]]]],[null,[[44798283,[[null,*********,null,[null,4]]]]],[2,[[4,null,70,null,null,null,null,[\"run-ad-auction\"]],[1,[[4,null,63]]]]],59],[470,[[83321072],[83321073]],null,136],[10,[[83321253],[83321254]],null,136],[10,[[83321266],[83321267]],null,136],[10,[[83321442],[83321443]],null,136],[null,[[95331143],[95331207]],[2,[[4,null,9,null,null,null,null,[\"fetch\"]],[4,null,9,null,null,null,null,[\"navigator.getInterestGroupAdAuctionData\"]],[1,[[4,null,63]]],[1,[[4,null,74,null,null,null,null,[\"1585821863\",\"3976716532\"]]]],[1,[[12,null,null,null,4,null,\".* Edg\/.*\",[\"navigator.userAgent\"]]]]]],59],[10,[[95332149],[95332150,[[616896918,null,null,[]]]]],null,59],[null,[[95335986],[95346223,[[null,*********,null,[null,4]],[null,607730666,null,[null,1]],[null,641937776,null,[null,38912]]]],[95353416,[[null,*********,null,[null,4]],[null,641937776,null,[null,38912]]]]],[2,[[4,null,9,null,null,null,null,[\"fetch\"]],[4,null,9,null,null,null,null,[\"navigator.getInterestGroupAdAuctionData\"]],[1,[[4,null,63]]],[1,[[4,null,74,null,null,null,null,[\"1585821863\",\"3976716532\"]]]],[1,[[12,null,null,null,4,null,\".* Edg\/.*\",[\"navigator.userAgent\"]]]]]],59],[10,[[95347484],[95347486,[[null,682021787,null,[null,5]]]],[95347487],[95347488,[[null,682021787,null,[null,2]]]],[95347489,[[null,682021787,null,[null,5]]]],[95347490,[[null,682021787,null,[null,10]]]]],null,116],[50,[[95355263],[95355264,[[45627954,null,null,[]]]]]],[null,[[676982960],[676982998]]]]],[12,[[10,[[31061690],[31061691,[[83,null,null,[1]],[84,null,null,[1]]]]],null,59],[40,[[95340252],[95340253,[[662101537,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5],[40,[[95340254],[95340255,[[662101539,null,null,[1]]]]],[4,null,9,null,null,null,null,[\"LayoutShift\"]],71,null,null,null,800,null,null,null,null,null,5]]],[13,[[500,[[31061692],[31061693,[[77,null,null,[1]],[78,null,null,[1]],[80,null,null,[1]],[76,null,null,[1]]]]],[4,null,6,null,null,null,null,[\"31061691\"]]]]],[5,[[50,[[31067420],[31067421,[[360245597,null,null,[]]]],[31077191]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[1000,[[31084129,null,[2,[[2,[[8,null,null,1,null,-1],[7,null,null,1,null,10]]],[4,null,3]]]]],null,80,null,null,null,null,null,null,null,null,4],[1000,[[31084130,null,[2,[[2,[[8,null,null,1,null,9],[7,null,null,1,null,20]]],[4,null,3]]]]],null,80,null,null,null,null,null,null,null,null,4],[50,[[31085776],[31085777,[[45624259,null,null,[1]]]]],null,114],[100,[[31086814],[31086815,[[null,665058368,null,[null,1]]]]]],[1,[[31087707]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[1,[[31087708]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[null,[[31087882],[31087883],[31087884]],[3,[[4,null,8,null,null,null,null,[\"gmaSdk.getQueryInfo\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaQueryInfo.postMessage\"]],[4,null,8,null,null,null,null,[\"webkit.messageHandlers.getGmaSig.postMessage\"]]]],69],[1,[[31089681],[31089682,[[710738456,null,null,[1]]]]]],[100,[[31090591],[31090592,[[726064440,null,null,[1]]]]]],[100,[[31090593],[31090594,[[726064441,null,null,[1]]]]]],[100,[[31091881],[31091882,[[555237687,null,null,[1]]]]]],[1,[[31092190],[31092191,[[751082905,null,null,[1]]]]]],[50,[[31092218],[31092219,[[null,754057781,null,[null,1]]]],[31092220,[[null,754057781,null,[null,2]]]]],[6,null,null,3,null,2]],[100,[[31092253],[31092254,[[null,707155067,null,[null,4]]]],[31092255,[[null,707155067,null,[null,5]]]]]],[10,[[31092363],[31092364,[[null,751161865,null,[null,1]]]],[31092365,[[null,751161865,null,[null,2]]]]]],[10,[[31092504],[31092505,[[738482525,null,null,[1]]]]]],[10,[[31092506],[31092507,[[748362437,null,null,[1]]]]]],[100,[[31092623],[31092624,[[756891373,null,null,[1]]]]]],[100,[[31092625],[31092626,[[756322359,null,null,[1]]]]]],[100,[[31092627],[31092628,[[752877076,null,null,[1]]]]]],[1000,[[31092651,[[null,24,null,[null,31092651]]],[6,null,null,13,null,31092651]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[1000,[[31092652,[[null,24,null,[null,31092652]]],[6,null,null,13,null,31092652]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[1000,[[31092702,[[null,24,null,[null,31092702]]],[6,null,null,13,null,31092702]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[1000,[[31092703,[[null,24,null,[null,31092703]]],[6,null,null,13,null,31092703]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[1000,[[31092746,[[null,24,null,[null,31092746]]],[6,null,null,13,null,31092746]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[1000,[[31092747,[[null,24,null,[null,31092747]]],[6,null,null,13,null,31092747]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[1000,[[31092772,[[null,24,null,[null,31092772]]],[6,null,null,13,null,31092772]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[1000,[[31092773,[[null,24,null,[null,31092773]]],[6,null,null,13,null,31092773]]],[4,null,3],1,null,null,null,null,null,null,null,null,3],[10,[[31092780],[31092781,[[45699877,null,null,[1]]]]]],[500,[[95353384],[95353385,[[667794963,null,null,[1]]]]]],[20,[[95361093],[95361094,[[null,740510593,null,[null,0.5]]]],[95361095],[95361096]]]]],[9,[[1000,[[31083577]],[4,null,13,null,null,null,null,[\"cxbbhbxm\"]]],[1000,[[31091121,null,[4,null,13,null,null,null,null,[\"zxcvbnms\"]]]],null,141,null,null,null,null,null,null,null,null,33],[1000,[[31091122,[[null,720326000,null,[null,1]]],[4,null,13,null,null,null,null,[\"qwrtplkj\"]]]],null,141,null,null,null,null,null,null,null,null,33]]],[2,[[10,[[31084489],[31084490]],null,null,null,null,32,null,null,142,1],[null,[[31084528],[31084529]],null,null,null,null,36,900,null,166,1],[1000,[[31084739,[[null,*********,null,[null,100]]]]],[4,null,6,null,null,null,null,[\"31065645\"]]],[10,[[31084865],[31084866]],null,null,null,null,35,null,null,166,1],[1000,[[31087377,null,[2,[[4,null,86],[4,null,6,null,null,null,null,[\"31065644\"]]]]]],null,131,null,null,null,null,null,null,null,null,28],[1000,[[31087378,null,[2,[[4,null,86],[4,null,6,null,null,null,null,[\"31065645\"]]]]]],null,131,null,null,null,null,null,null,null,null,28],[1000,[[31087490,null,[2,[[1,[[4,null,86]]],[4,null,6,null,null,null,null,[\"31065644\"]]]]]],null,131,null,null,null,null,null,null,null,null,28],[1000,[[31087491,null,[2,[[1,[[4,null,86]]],[4,null,6,null,null,null,null,[\"31065645\"]]]]]],null,131,null,null,null,null,null,null,null,null,28],[10,[[31090843],[31090844,[[725693774,null,null,[1]]]]],null,null,null,null,null,null,null,198,1],[10,[[31090914],[31090915,[[729624434,null,null,[1]]]],[31091637,[[729624434,null,null,[1]],[null,729624435,null,[null,5000]]]],[31091869,[[729624434,null,null,[1]],[745631621,null,null,[1]]]],[31091870,[[729624434,null,null,[1]],[null,729624435,null,[null,5000]],[745631621,null,null,[1]]]]],null,null,null,null,null,100,null,198,1],[10,[[31091119],[31091120,[[732179798,null,null,[1]]]],[31091868,[[732179798,null,null,[1]],[null,745376890,null,[null,2]],[null,745376891,null,[null,4]]]]],null,null,null,null,null,600,null,198,1],[50,[[31092216],[31092217,[[730909248,null,null,[]],[730909251,null,null,[]]]]],null,null,null,null,null,200,null,198,1],[50,[[31092290],[31092292,[[752401957,null,null,[1]]]]],null,null,null,null,null,300,null,198,1],[50,[[31092395]],[2,[[4,null,86],[3,[[4,null,83],[4,null,68]]],[1,[[4,null,6,null,null,null,null,[\"31091121\"]]]],[1,[[4,null,6,null,null,null,null,[\"31091122\"]]]]]],141,null,null,null,null,null,null,null,null,33],[50,[[31092396,[[null,720326000,null,[]]]]],[2,[[4,null,86],[3,[[4,null,83],[4,null,68]]],[1,[[4,null,6,null,null,null,null,[\"31091121\"]]]],[1,[[4,null,6,null,null,null,null,[\"31091122\"]]]]]],141,null,null,null,200,null,null,null,null,33],[1,[[31092720],[31092721,[[null,753762684,null,[null,750]]]],[31092722,[[null,753762684,null,[null,1000]]]]],null,null,null,null,null,990,null,198,1],[10,[[95342027],[95342028]],[4,null,83],129],[50,[[95349880],[95349881,[[null,null,null,null,[[[4,null,83],[null,null,null,[\"1 dbm\/(ad|clkk)\",\"2 (adsrvr|adserver)\\\\.org\/bid\/\",\"3 criteo.com\/(delivery|[a-z]+\/auction)\",\"4 yahoo.com\/bw\/[a-z]+\/imp\/\",\"5 (adnxs|adnxs-simple).com\/it\",\"6 amazon-adsystem.com\/[a-z\/]+\/impb\",\"7 temu.com\/api\/[a-z0-9]+\/ads\",\"8 temu.com\/[a-z0-9]+\/impr\"]]]],*********]]]],[4,null,83],129],[50,[[95351361],[95351362,[[null,null,null,null,[[[4,null,83],[]]],*********]]]],[4,null,83],129],[50,[[95351363],[95351364,[[null,null,null,null,[[[4,null,83],[]]],*********]]]],[4,null,83],129],[1,[[95357519],[95357520,[[null,null,null,null,[[[4,null,83],[null,null,null,[\"1 bidderRequests.bids bidder userIdAsEids.source\",\"2 bidderRequests.bids.userIdAsEids source provider\",\"3 bidderRequests.bids bidder ortb2Imp.ext.tid?\",\"5 bidderRequests.bids bidder mediaTypes.banner\",\"6 bidderRequests.bids bidder mediaTypes.native?\",\"7 bidderRequests.bids bidder mediaTypes.video\",\"8 bidderRequests.bids bidder ortb2Imp.ext.gpid?\",\"9 bidderRequests.bids bidder ortb2.site.content.data.ext.segment?\",\"10 bidderRequests.bids bidder ortb2.site.page\",\"11 bidderRequests.bids bidder ortb2.user.data.segment?\",\"12 bidderRequests.bids bidder ortb2.user.data.ext.segtax?\",\"13 bidsReceived adId creativeId\",\"14 bidderRequests.bids.userIdAsEids source uids.ext.provider\",\"15 bidderRequests.bids.userIdAsEids source uids.atype\",\"16 bidderRequests.bids.userIdAsEids source uids.length\",\"17 bidsReceived adId ttl\",\"18 bidsReceived adId meta.primaryCatId\",\"19 bidsReceived adId meta.secondaryCatIds\",\"22 bidsReceived adId ad\"]]]],*********]]]],[4,null,83],129],[20,[[95357665],[95357666],[95357667],[95357668],[95357669],[95357670]],[4,null,89],null,null,null,37,780,null,166,1]]],[27,[[50,[[31090502,null,[2,[[4,null,59,null,null,null,null,[\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"*********\",\"**********\",\"**********\",\"*********\",\"**********\",\"**********\",\"**********\",\"77481481\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"*********\",\"*********\",\"*********\"]],[8,null,null,17,null,0]]]],[31090503,[[*********,null,null,[1]]],[2,[[4,null,59,null,null,null,null,[\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"*********\",\"**********\",\"**********\",\"*********\",\"**********\",\"**********\",\"**********\",\"77481481\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"**********\",\"*********\",\"*********\",\"*********\"]],[8,null,null,17,null,0]]]]]]]],[4,[[null,[[44714449,[[null,7,null,[null,1]]]],[676982961,[[null,7,null,[null,0.4]],[212,null,null,[1]]]],[676982996,[[null,7,null,[null,1]]]]],null,78]]]],null,null,[null,1000,1,1000]],31092703,null,null,null,\".google.com.sg\",657,null,[[\"calculator.net\",null,\"https:\/\/www.calculator.net\/\",null,null,[\"1057446\"]],[],[],null,null,[[\"1057446\",[[\"google.com\",null,1]]]],null,[[[\"1057446\",1],[\"50533735\",1]]],[[[\"1057446\",1],[\"50533735\",1]]],null,[[24902,1748821200],[38841,1748822400],[35184,1748823600],[45378,1748824800]],[1]],null,null,null,[0,0,0],\"m202505290101\"]")
