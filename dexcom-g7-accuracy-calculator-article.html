<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.accuracy-interface {
    background: linear-gradient(135deg, #3f51b5 0%, #303f9f 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.reading-input {
    background-color: #e8eaf6;
    border: 2px solid #3f51b5;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.accuracy-result {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.comparison-table {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
    max-height: 400px;
    overflow-y: auto;
}
.accuracy-meter {
    background-color: #e0e0e0;
    border-radius: 10px;
    height: 20px;
    margin: 10px 0;
    overflow: hidden;
}
.accuracy-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
}
.excellent { background: linear-gradient(90deg, #4caf50, #8bc34a); }
.good { background: linear-gradient(90deg, #8bc34a, #cddc39); }
.fair { background: linear-gradient(90deg, #ff9800, #ffc107); }
.poor { background: linear-gradient(90deg, #f44336, #ff5722); }
.reading-entry {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 80px;
    gap: 10px;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    background-color: #f5f5f5;
    border-radius: 8px;
}
</style>

<h1>Dexcom G7 Accuracy Calculator</h1>
<p>Calculate and analyze your Dexcom G7 sensor accuracy by comparing CGM readings with fingerstick blood glucose measurements. Track MARD (Mean Absolute Relative Difference), identify patterns, and optimize your continuous glucose monitoring accuracy.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="accuracyform" id="accuracy-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Accuracy Calculator</a></li>
                <li><a href="#" data-tab="analysis">MARD Analysis</a></li>
                <li><a href="#" data-tab="trends">Accuracy Trends</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="accuracy-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">📊 G7 Accuracy Calculator</h3>
                
                <div class="reading-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🩸 Reading Comparisons</h4>
                    
                    <div style="margin-bottom: 15px;">
                        <div class="reading-entry" style="background-color: #3f51b5; color: white; font-weight: bold;">
                            <div>Dexcom G7 Reading</div>
                            <div>Fingerstick Reading</div>
                            <div>Time of Reading</div>
                            <div>Action</div>
                        </div>
                        
                        <div id="readings-container">
                            <div class="reading-entry">
                                <input type="number" class="dexcom-reading innormal" value="120" min="40" max="400" placeholder="mg/dL" style="width: 100%;">
                                <input type="number" class="fingerstick-reading innormal" value="125" min="40" max="400" placeholder="mg/dL" style="width: 100%;">
                                <input type="time" class="reading-time innormal" style="width: 100%;">
                                <button type="button" class="remove-reading" style="background: #f44336; color: white; border: none; border-radius: 4px; padding: 5px;">❌</button>
                            </div>
                        </div>
                        
                        <div style="text-align: center; margin-top: 15px;">
                            <button type="button" id="add-reading-btn" style="padding: 10px 20px; background-color: #4caf50; color: white; border: none; border-radius: 8px; cursor: pointer;">
                                ➕ Add Reading Pair
                            </button>
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #e8eaf6; border: 2px solid #3f51b5; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">⚙️ Analysis Settings</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="sensor-age"><strong>Sensor Age:</strong></label>
                            <select id="sensor-age" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="day1">Day 1 (First day)</option>
                                <option value="day2-3">Day 2-3 (Early)</option>
                                <option value="day4-7" selected>Day 4-7 (Optimal)</option>
                                <option value="day8-10">Day 8-10 (Late)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="glucose-range"><strong>Glucose Range:</strong></label>
                            <select id="glucose-range" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="all" selected>All ranges</option>
                                <option value="low">Low (< 70 mg/dL)</option>
                                <option value="normal">Normal (70-180 mg/dL)</option>
                                <option value="high">High (> 180 mg/dL)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="calibration-status"><strong>Calibration Status:</strong></label>
                            <select id="calibration-status" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="factory" selected>Factory calibrated</option>
                                <option value="user-calibrated">User calibrated</option>
                                <option value="mixed">Mixed calibration</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Accuracy" id="calculate-accuracy-btn" style="padding: 15px 30px; font-size: 18px; background-color: #3f51b5; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Clear All Readings" id="clear-accuracy-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> For best accuracy comparison, take fingerstick readings when glucose levels are stable and hands are clean.
        </div>
        
        <div id="accuracy-results" style="display: none; margin-top: 30px;">
            <div class="accuracy-result">
                <h3 style="margin-bottom: 20px;">📈 Accuracy Analysis Results</h3>
                <div id="accuracy-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-accuracy-btn');
    const clearBtn = document.getElementById('clear-accuracy-btn');
    const addReadingBtn = document.getElementById('add-reading-btn');
    const resultsDiv = document.getElementById('accuracy-results');
    const resultsContent = document.getElementById('accuracy-results-content');
    const readingsContainer = document.getElementById('readings-container');
    
    // Set default time for first reading
    const now = new Date();
    const timeString = now.toTimeString().slice(0, 5);
    document.querySelector('.reading-time').value = timeString;
    
    addReadingBtn.addEventListener('click', function() {
        addReadingEntry();
    });
    
    calculateBtn.addEventListener('click', function() {
        const readings = collectReadings();
        const sensorAge = document.getElementById('sensor-age').value;
        const glucoseRange = document.getElementById('glucose-range').value;
        const calibrationStatus = document.getElementById('calibration-status').value;
        
        if (readings.length < 2) {
            alert('Please enter at least 2 reading pairs for accuracy analysis.');
            return;
        }
        
        const accuracyAnalysis = calculateAccuracy(readings, sensorAge, glucoseRange, calibrationStatus);
        displayAccuracyResults(accuracyAnalysis);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        readingsContainer.innerHTML = '';
        addReadingEntry();
        resultsDiv.style.display = 'none';
    });
    
    function addReadingEntry() {
        const entryDiv = document.createElement('div');
        entryDiv.className = 'reading-entry';
        
        const now = new Date();
        const timeString = now.toTimeString().slice(0, 5);
        
        entryDiv.innerHTML = `
            <input type="number" class="dexcom-reading innormal" value="" min="40" max="400" placeholder="mg/dL" style="width: 100%;">
            <input type="number" class="fingerstick-reading innormal" value="" min="40" max="400" placeholder="mg/dL" style="width: 100%;">
            <input type="time" class="reading-time innormal" value="${timeString}" style="width: 100%;">
            <button type="button" class="remove-reading" style="background: #f44336; color: white; border: none; border-radius: 4px; padding: 5px;">❌</button>
        `;
        
        readingsContainer.appendChild(entryDiv);
        
        // Add remove functionality
        entryDiv.querySelector('.remove-reading').addEventListener('click', function() {
            if (readingsContainer.children.length > 1) {
                entryDiv.remove();
            }
        });
    }
    
    function collectReadings() {
        const readings = [];
        const entries = readingsContainer.querySelectorAll('.reading-entry');
        
        entries.forEach(entry => {
            const dexcomReading = parseFloat(entry.querySelector('.dexcom-reading').value);
            const fingerstickReading = parseFloat(entry.querySelector('.fingerstick-reading').value);
            const readingTime = entry.querySelector('.reading-time').value;
            
            if (dexcomReading && fingerstickReading && readingTime) {
                readings.push({
                    dexcom: dexcomReading,
                    fingerstick: fingerstickReading,
                    time: readingTime,
                    difference: Math.abs(dexcomReading - fingerstickReading),
                    percentDifference: Math.abs((dexcomReading - fingerstickReading) / fingerstickReading) * 100
                });
            }
        });
        
        return readings;
    }
    
    function calculateAccuracy(readings, sensorAge, glucoseRange, calibrationStatus) {
        // Filter readings based on glucose range if specified
        let filteredReadings = readings;
        if (glucoseRange !== 'all') {
            filteredReadings = readings.filter(reading => {
                if (glucoseRange === 'low') return reading.fingerstick < 70;
                if (glucoseRange === 'normal') return reading.fingerstick >= 70 && reading.fingerstick <= 180;
                if (glucoseRange === 'high') return reading.fingerstick > 180;
                return true;
            });
        }
        
        if (filteredReadings.length === 0) {
            filteredReadings = readings; // Fallback to all readings
        }
        
        // Calculate MARD (Mean Absolute Relative Difference)
        const mard = filteredReadings.reduce((sum, reading) => sum + reading.percentDifference, 0) / filteredReadings.length;
        
        // Calculate other accuracy metrics
        const meanAbsoluteDifference = filteredReadings.reduce((sum, reading) => sum + reading.difference, 0) / filteredReadings.length;
        
        // Calculate percentage within different thresholds
        const within15mg = filteredReadings.filter(r => r.difference <= 15).length / filteredReadings.length * 100;
        const within20mg = filteredReadings.filter(r => r.difference <= 20).length / filteredReadings.length * 100;
        const within15percent = filteredReadings.filter(r => r.percentDifference <= 15).length / filteredReadings.length * 100;
        const within20percent = filteredReadings.filter(r => r.percentDifference <= 20).length / filteredReadings.length * 100;
        
        // Determine accuracy grade
        let accuracyGrade = 'Poor';
        let gradeClass = 'poor';
        
        if (mard <= 10) {
            accuracyGrade = 'Excellent';
            gradeClass = 'excellent';
        } else if (mard <= 15) {
            accuracyGrade = 'Good';
            gradeClass = 'good';
        } else if (mard <= 20) {
            accuracyGrade = 'Fair';
            gradeClass = 'fair';
        }
        
        // Calculate bias (systematic error)
        const bias = filteredReadings.reduce((sum, reading) => sum + (reading.dexcom - reading.fingerstick), 0) / filteredReadings.length;
        
        // Identify outliers (readings with >30% difference)
        const outliers = filteredReadings.filter(r => r.percentDifference > 30);
        
        return {
            totalReadings: readings.length,
            analyzedReadings: filteredReadings.length,
            mard: mard,
            meanAbsoluteDifference: meanAbsoluteDifference,
            bias: bias,
            within15mg: within15mg,
            within20mg: within20mg,
            within15percent: within15percent,
            within20percent: within20percent,
            accuracyGrade: accuracyGrade,
            gradeClass: gradeClass,
            outliers: outliers,
            readings: filteredReadings,
            settings: {
                sensorAge: sensorAge,
                glucoseRange: glucoseRange,
                calibrationStatus: calibrationStatus
            }
        };
    }
    
    function displayAccuracyResults(data) {
        const sensorAgeNames = {
            'day1': 'Day 1 (First day)',
            'day2-3': 'Day 2-3 (Early)',
            'day4-7': 'Day 4-7 (Optimal)',
            'day8-10': 'Day 8-10 (Late)'
        };
        
        let html = `
            <div style="margin-bottom: 25px;">
                <div style="font-size: 48px; font-weight: bold; margin-bottom: 10px;">
                    ${data.mard.toFixed(1)}%
                </div>
                <div style="font-size: 18px; margin-bottom: 15px;">
                    MARD (Mean Absolute Relative Difference)
                </div>
                <div class="accuracy-meter">
                    <div class="accuracy-fill ${data.gradeClass}" style="width: ${Math.min(100, (30 - data.mard) / 30 * 100)}%;"></div>
                </div>
                <div style="font-size: 16px; margin-top: 10px;">
                    Accuracy Grade: <strong>${data.accuracyGrade}</strong>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Readings Analyzed</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #3f51b5;">
                        ${data.analyzedReadings}
                    </div>
                    <div style="font-size: 12px; color: #666;">of ${data.totalReadings} total</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Mean Difference</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #ff9800;">
                        ${data.meanAbsoluteDifference.toFixed(1)}
                    </div>
                    <div style="font-size: 12px; color: #666;">mg/dL average</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Within ±15%</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #4caf50;">
                        ${data.within15percent.toFixed(0)}%
                    </div>
                    <div style="font-size: 12px; color: #666;">of readings</div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Bias</h4>
                    <div style="font-size: 24px; font-weight: bold; color: ${data.bias > 0 ? '#f44336' : '#2196f3'};">
                        ${data.bias > 0 ? '+' : ''}${data.bias.toFixed(1)}
                    </div>
                    <div style="font-size: 12px; color: #666;">mg/dL ${data.bias > 0 ? 'high' : 'low'}</div>
                </div>
            </div>
            
            <div class="comparison-table">
                <h4 style="color: #3f51b5; margin-bottom: 20px;">📊 Detailed Accuracy Metrics</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <h5>Accuracy Thresholds:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Within ±15 mg/dL:</strong> ${data.within15mg.toFixed(0)}%</li>
                            <li><strong>Within ±20 mg/dL:</strong> ${data.within20mg.toFixed(0)}%</li>
                            <li><strong>Within ±15%:</strong> ${data.within15percent.toFixed(0)}%</li>
                            <li><strong>Within ±20%:</strong> ${data.within20percent.toFixed(0)}%</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5>Analysis Settings:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Sensor Age:</strong> ${sensorAgeNames[data.settings.sensorAge]}</li>
                            <li><strong>Glucose Range:</strong> ${data.settings.glucoseRange}</li>
                            <li><strong>Calibration:</strong> ${data.settings.calibrationStatus}</li>
                            <li><strong>Outliers:</strong> ${data.outliers.length} readings</li>
                        </ul>
                    </div>
                </div>
                
                <div style="margin-top: 20px;">
                    <h5>Individual Reading Comparisons:</h5>
                    <div style="max-height: 200px; overflow-y: auto;">
                        <table style="width: 100%; border-collapse: collapse;">
                            <thead>
                                <tr style="background-color: #f5f5f5;">
                                    <th style="padding: 8px; border: 1px solid #ddd;">Time</th>
                                    <th style="padding: 8px; border: 1px solid #ddd;">Dexcom</th>
                                    <th style="padding: 8px; border: 1px solid #ddd;">Fingerstick</th>
                                    <th style="padding: 8px; border: 1px solid #ddd;">Difference</th>
                                    <th style="padding: 8px; border: 1px solid #ddd;">% Difference</th>
                                </tr>
                            </thead>
                            <tbody>
        `;
        
        data.readings.forEach(reading => {
            const isOutlier = reading.percentDifference > 30;
            const rowStyle = isOutlier ? 'background-color: #ffebee;' : '';
            
            html += `
                <tr style="${rowStyle}">
                    <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${reading.time}</td>
                    <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${reading.dexcom}</td>
                    <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${reading.fingerstick}</td>
                    <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${reading.difference.toFixed(1)}</td>
                    <td style="padding: 6px; border: 1px solid #ddd; text-align: center;">${reading.percentDifference.toFixed(1)}%</td>
                </tr>
            `;
        });
        
        html += `
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 Accuracy Improvement Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Timing:</strong> Take fingerstick readings when glucose is stable</li>
                    <li><strong>Hygiene:</strong> Wash hands thoroughly before fingerstick tests</li>
                    <li><strong>Calibration:</strong> Consider user calibration if MARD > 15%</li>
                    <li><strong>Sensor Age:</strong> Accuracy typically best on days 4-7</li>
                    <li><strong>Site Rotation:</strong> Use different insertion sites for each sensor</li>
                    <li><strong>Hydration:</strong> Stay well-hydrated for optimal sensor performance</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
    
    // Initialize with one reading entry
    addReadingEntry();
});
</script>
