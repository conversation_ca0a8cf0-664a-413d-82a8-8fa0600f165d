<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.bac-interface {
    background: linear-gradient(135deg, #2e7d32 0%, #1b5e20 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.drink-selector {
    background-color: #f8f9fa;
    border: 2px solid #2e7d32;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.drink-card {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 15px;
    margin: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    width: 120px;
}
.drink-card:hover {
    border-color: #2e7d32;
    transform: translateY(-3px);
}
.drink-card.selected {
    border-color: #2e7d32;
    background-color: #e8f5e9;
}
.bac-result {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.bac-safe { background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%); }
.bac-caution { background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%); }
.bac-danger { background: linear-gradient(135deg, #f44336 0%, #c62828 100%); }
.bac-extreme { background: linear-gradient(135deg, #9c27b0 0%, #6a1b9a 100%); }
.timeline {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.time-marker {
    display: inline-block;
    padding: 8px 16px;
    margin: 5px;
    border-radius: 20px;
    font-weight: bold;
    color: white;
}
.celtic-pattern {
    border: 3px solid #2e7d32;
    border-image: repeating-linear-gradient(45deg, #2e7d32, #2e7d32 10px, #4caf50 10px, #4caf50 20px) 1;
}
</style>

<h1>BAC Calculator Celtic</h1>
<p>Calculate your Blood Alcohol Content (BAC) with our Celtic-themed BAC calculator. Understand how alcohol affects your body, plan safe drinking limits, and know when it's safe to drive. This calculator uses the Widmark formula for accurate BAC estimation.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="bacform" id="bac-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">BAC Calculator</a></li>
                <li><a href="#" data-tab="safety">Safety Guide</a></li>
                <li><a href="#" data-tab="legal">Legal Limits</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="bac-interface celtic-pattern">
                <h3 style="margin-bottom: 20px; text-align: center;">🍺 Celtic BAC Calculator</h3>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <label for="weight"><strong>Body Weight:</strong></label>
                        <div style="display: flex; align-items: center; margin-top: 5px;">
                            <input type="number" id="weight" value="70" min="30" max="200" class="innormal" style="width: 100px;">
                            <select id="weight-unit" class="innormal" style="margin-left: 10px;">
                                <option value="kg">kg</option>
                                <option value="lbs">lbs</option>
                            </select>
                        </div>
                    </div>
                    
                    <div>
                        <label for="gender"><strong>Gender:</strong></label>
                        <select id="gender" class="innormal" style="width: 100%; margin-top: 5px;">
                            <option value="male">Male</option>
                            <option value="female">Female</option>
                        </select>
                    </div>
                </div>
                
                <div style="margin-bottom: 20px;">
                    <label for="time-drinking"><strong>Time Since First Drink (hours):</strong></label>
                    <input type="number" id="time-drinking" value="2" min="0" max="24" step="0.5" class="innormal" style="width: 100px; margin-left: 10px;">
                </div>
            </div>
            
            <div class="drink-selector">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">🍻 Select Your Drinks</h4>
                <div style="text-align: center; margin-bottom: 20px;">
                    <div class="drink-card" data-drink="beer">
                        <div style="font-size: 30px;">🍺</div>
                        <div><strong>Beer</strong></div>
                        <div style="font-size: 12px;">5% ABV</div>
                    </div>
                    <div class="drink-card" data-drink="wine">
                        <div style="font-size: 30px;">🍷</div>
                        <div><strong>Wine</strong></div>
                        <div style="font-size: 12px;">12% ABV</div>
                    </div>
                    <div class="drink-card" data-drink="whiskey">
                        <div style="font-size: 30px;">🥃</div>
                        <div><strong>Whiskey</strong></div>
                        <div style="font-size: 12px;">40% ABV</div>
                    </div>
                    <div class="drink-card" data-drink="vodka">
                        <div style="font-size: 30px;">🍸</div>
                        <div><strong>Vodka</strong></div>
                        <div style="font-size: 12px;">40% ABV</div>
                    </div>
                    <div class="drink-card" data-drink="cocktail">
                        <div style="font-size: 30px;">🍹</div>
                        <div><strong>Cocktail</strong></div>
                        <div style="font-size: 12px;">15% ABV</div>
                    </div>
                    <div class="drink-card" data-drink="irish">
                        <div style="font-size: 30px;">☘️</div>
                        <div><strong>Irish Ale</strong></div>
                        <div style="font-size: 12px;">4.5% ABV</div>
                    </div>
                </div>
                
                <div id="selected-drinks" style="margin-top: 20px;">
                    <h5 style="color: #2c3e50;">Selected Drinks:</h5>
                    <div id="drinks-list" style="margin-top: 10px;"></div>
                </div>
                
                <div style="text-align: center; margin-top: 20px;">
                    <button type="button" id="add-drink-btn" style="padding: 10px 20px; background-color: #2e7d32; color: white; border: none; border-radius: 5px; cursor: pointer;">Add Selected Drink</button>
                    <button type="button" id="clear-drinks-btn" style="padding: 10px 20px; background-color: #f44336; color: white; border: none; border-radius: 5px; cursor: pointer; margin-left: 10px;">Clear All</button>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate BAC" id="calculate-bac-btn" style="padding: 15px 30px; font-size: 18px; background-color: #2e7d32; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Calculator" id="clear-bac-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Disclaimer:</strong> This calculator provides estimates only. BAC can vary based on many factors. Never drink and drive. Always use designated drivers or alternative transportation.
        </div>
        
        <div id="bac-results" style="display: none; margin-top: 30px;">
            <div id="bac-display" class="bac-result">
                <h3 style="margin-bottom: 20px;">🧪 Your BAC Results</h3>
                <div id="bac-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const drinkCards = document.querySelectorAll('.drink-card');
    const addDrinkBtn = document.getElementById('add-drink-btn');
    const clearDrinksBtn = document.getElementById('clear-drinks-btn');
    const calculateBtn = document.getElementById('calculate-bac-btn');
    const clearBtn = document.getElementById('clear-bac-btn');
    const resultsDiv = document.getElementById('bac-results');
    const resultsContent = document.getElementById('bac-results-content');
    const bacDisplay = document.getElementById('bac-display');
    
    let selectedDrinkType = null;
    let consumedDrinks = [];
    
    // Drink data with alcohol content
    const drinkData = {
        beer: { name: 'Beer', abv: 0.05, volume: 355, emoji: '🍺' },
        wine: { name: 'Wine', abv: 0.12, volume: 150, emoji: '🍷' },
        whiskey: { name: 'Whiskey', abv: 0.40, volume: 45, emoji: '🥃' },
        vodka: { name: 'Vodka', abv: 0.40, volume: 45, emoji: '🍸' },
        cocktail: { name: 'Cocktail', abv: 0.15, volume: 200, emoji: '🍹' },
        irish: { name: 'Irish Ale', abv: 0.045, volume: 355, emoji: '☘️' }
    };
    
    // Handle drink selection
    drinkCards.forEach(card => {
        card.addEventListener('click', function() {
            drinkCards.forEach(c => c.classList.remove('selected'));
            this.classList.add('selected');
            selectedDrinkType = this.dataset.drink;
        });
    });
    
    addDrinkBtn.addEventListener('click', function() {
        if (!selectedDrinkType) {
            alert('Please select a drink type first.');
            return;
        }
        
        const drink = drinkData[selectedDrinkType];
        consumedDrinks.push({
            type: selectedDrinkType,
            name: drink.name,
            abv: drink.abv,
            volume: drink.volume,
            emoji: drink.emoji
        });
        
        updateDrinksList();
    });
    
    clearDrinksBtn.addEventListener('click', function() {
        consumedDrinks = [];
        updateDrinksList();
    });
    
    function updateDrinksList() {
        const drinksList = document.getElementById('drinks-list');
        
        if (consumedDrinks.length === 0) {
            drinksList.innerHTML = '<div style="color: #666; font-style: italic;">No drinks added yet</div>';
            return;
        }
        
        let html = '';
        const drinkCounts = {};
        
        consumedDrinks.forEach(drink => {
            drinkCounts[drink.type] = (drinkCounts[drink.type] || 0) + 1;
        });
        
        Object.entries(drinkCounts).forEach(([type, count]) => {
            const drink = drinkData[type];
            html += `
                <div style="display: inline-block; margin: 5px; padding: 8px 12px; background-color: #e8f5e9; border-radius: 15px;">
                    ${drink.emoji} ${drink.name} x${count}
                </div>
            `;
        });
        
        drinksList.innerHTML = html;
    }
    
    calculateBtn.addEventListener('click', function() {
        if (consumedDrinks.length === 0) {
            alert('Please add at least one drink.');
            return;
        }
        
        const weight = parseFloat(document.getElementById('weight').value);
        const weightUnit = document.getElementById('weight-unit').value;
        const gender = document.getElementById('gender').value;
        const timeDrinking = parseFloat(document.getElementById('time-drinking').value);
        
        if (!weight || weight <= 0) {
            alert('Please enter a valid weight.');
            return;
        }
        
        const bac = calculateBAC(weight, weightUnit, gender, timeDrinking, consumedDrinks);
        displayResults(bac, timeDrinking);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('bac-calculator-form').reset();
        consumedDrinks = [];
        selectedDrinkType = null;
        drinkCards.forEach(c => c.classList.remove('selected'));
        updateDrinksList();
        resultsDiv.style.display = 'none';
    });
    
    function calculateBAC(weight, weightUnit, gender, timeDrinking, drinks) {
        // Convert weight to grams if needed
        let weightInKg = weight;
        if (weightUnit === 'lbs') {
            weightInKg = weight * 0.453592;
        }
        
        // Calculate total alcohol consumed in grams
        let totalAlcoholGrams = 0;
        drinks.forEach(drink => {
            const alcoholVolume = drink.volume * drink.abv; // ml of pure alcohol
            const alcoholGrams = alcoholVolume * 0.789; // density of ethanol
            totalAlcoholGrams += alcoholGrams;
        });
        
        // Widmark formula constants
        const r = gender === 'male' ? 0.68 : 0.55; // body water constant
        const beta = 0.015; // alcohol elimination rate per hour
        
        // Calculate BAC
        const bac = (totalAlcoholGrams / (weightInKg * 1000 * r)) - (beta * timeDrinking);
        
        return Math.max(0, bac); // BAC cannot be negative
    }
    
    function displayResults(bac, timeDrinking) {
        const bacPercentage = (bac * 100).toFixed(3);
        
        let riskLevel, riskClass, riskDescription, timeToSober;
        
        if (bac < 0.02) {
            riskLevel = 'Safe';
            riskClass = 'bac-safe';
            riskDescription = 'Minimal impairment. You should be fine to drive.';
        } else if (bac < 0.05) {
            riskLevel = 'Caution';
            riskClass = 'bac-caution';
            riskDescription = 'Mild impairment. Reduced coordination and judgment.';
        } else if (bac < 0.08) {
            riskLevel = 'Impaired';
            riskClass = 'bac-danger';
            riskDescription = 'Legally impaired in many countries. DO NOT DRIVE.';
        } else if (bac < 0.15) {
            riskLevel = 'Dangerous';
            riskClass = 'bac-danger';
            riskDescription = 'Severely impaired. Risk of alcohol poisoning.';
        } else {
            riskLevel = 'Extreme Danger';
            riskClass = 'bac-extreme';
            riskDescription = 'Life-threatening BAC. Seek immediate medical attention.';
        }
        
        // Calculate time to reach 0.00 BAC
        timeToSober = Math.max(0, bac / 0.015);
        
        bacDisplay.className = `bac-result ${riskClass}`;
        
        let html = `
            <div style="font-size: 48px; font-weight: bold; margin-bottom: 15px;">
                ${bacPercentage}%
            </div>
            <div style="font-size: 24px; margin-bottom: 20px;">
                ${riskLevel}
            </div>
            <div style="font-size: 16px; margin-bottom: 25px;">
                ${riskDescription}
            </div>
            
            <div class="timeline">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">⏰ Sobering Timeline</h4>
                <div style="margin-bottom: 15px;">
                    <strong>Time to reach 0.00% BAC:</strong> ${timeToSober.toFixed(1)} hours
                </div>
                <div style="margin-bottom: 15px;">
                    <strong>Safe to drive:</strong> ${timeToSober > 0 ? `In ${timeToSober.toFixed(1)} hours` : 'Now (if you feel capable)'}
                </div>
                
                <div style="margin-top: 20px;">
                    <h5>BAC Reduction Timeline:</h5>
        `;
        
        for (let hour = 1; hour <= Math.ceil(timeToSober); hour++) {
            const futureBAC = Math.max(0, bac - (0.015 * hour));
            const futurePercentage = (futureBAC * 100).toFixed(3);
            let markerClass = 'bac-safe';
            
            if (futureBAC >= 0.08) markerClass = 'bac-danger';
            else if (futureBAC >= 0.05) markerClass = 'bac-caution';
            
            html += `<div class="time-marker" style="background-color: ${markerClass === 'bac-safe' ? '#4caf50' : markerClass === 'bac-caution' ? '#ff9800' : '#f44336'};">Hour ${hour}: ${futurePercentage}%</div>`;
        }
        
        html += `
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>🚨 Important Safety Information:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li>This is an estimate only - actual BAC may vary</li>
                    <li>Factors like food, medications, and health affect BAC</li>
                    <li>Legal driving limits vary by location (0.05-0.08%)</li>
                    <li>Even small amounts of alcohol can impair driving ability</li>
                    <li>Always use designated drivers or ride-sharing services</li>
                    <li>If BAC is above 0.15%, seek medical attention</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
    
    // Initialize drinks list
    updateDrinksList();
});
</script>
