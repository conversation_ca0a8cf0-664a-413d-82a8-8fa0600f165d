<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.paycity-interface {
    background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.payroll-input {
    background-color: #e3f2fd;
    border: 2px solid #1565c0;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.paycity-result {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.deduction-breakdown {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.deduction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 10px;
    margin: 5px 0;
    background-color: #f5f5f5;
    border-radius: 8px;
}
.paycity-brand {
    background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
    border-radius: 10px;
    padding: 15px;
    margin: 10px 0;
    color: white;
    text-align: center;
}
.pay-frequency {
    display: inline-block;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    margin: 5px;
    color: white;
    cursor: pointer;
    transition: all 0.3s ease;
}
.pay-frequency.selected { background-color: #1565c0; }
.pay-frequency:not(.selected) { background-color: #9e9e9e; }
</style>

<h1>PayCity Paycheck Calculator</h1>
<p>Calculate your net paycheck with PayCity's comprehensive payroll calculator. Accurately compute federal and state taxes, FICA deductions, and take-home pay for all pay frequencies. Perfect for employees and HR professionals using PayCity payroll systems.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="paycityform" id="paycity-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">PayCity Calculator</a></li>
                <li><a href="#" data-tab="deductions">Deductions</a></li>
                <li><a href="#" data-tab="benefits">Benefits</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="paycity-brand">
                <h3 style="margin: 0;">💼 PayCity Payroll Calculator</h3>
                <div style="font-size: 14px; margin-top: 5px;">Professional Paycheck & Tax Calculation</div>
            </div>
            
            <div class="paycity-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">💰 Paycheck Calculator</h3>
                
                <div class="payroll-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">💵 Salary Information</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="salary-amount"><strong>Salary/Wage Amount:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="salary-amount" value="60000" min="0" max="1000000" step="0.01" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="salary-type"><strong>Salary Type:</strong></label>
                            <select id="salary-type" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="annual" selected>Annual Salary</option>
                                <option value="hourly">Hourly Wage</option>
                                <option value="monthly">Monthly Salary</option>
                                <option value="weekly">Weekly Salary</option>
                            </select>
                        </div>
                    </div>
                    
                    <div id="hourly-details" style="display: none; margin-bottom: 20px;">
                        <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                            <div>
                                <label for="hours-per-week"><strong>Hours per Week:</strong></label>
                                <input type="number" id="hours-per-week" value="40" min="1" max="80" class="innormal" style="width: 100%; margin-top: 5px;">
                            </div>
                            <div>
                                <label for="weeks-per-year"><strong>Weeks per Year:</strong></label>
                                <input type="number" id="weeks-per-year" value="52" min="1" max="52" class="innormal" style="width: 100%; margin-top: 5px;">
                            </div>
                        </div>
                    </div>
                    
                    <div>
                        <label><strong>Pay Frequency:</strong></label>
                        <div style="margin-top: 10px;">
                            <div class="pay-frequency selected" data-frequency="weekly">Weekly (52)</div>
                            <div class="pay-frequency" data-frequency="biweekly">Bi-weekly (26)</div>
                            <div class="pay-frequency" data-frequency="semimonthly">Semi-monthly (24)</div>
                            <div class="pay-frequency" data-frequency="monthly">Monthly (12)</div>
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #e3f2fd; border: 2px solid #1565c0; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🏛️ Tax Information</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="filing-status"><strong>Filing Status:</strong></label>
                            <select id="filing-status" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="single" selected>Single</option>
                                <option value="married-joint">Married Filing Jointly</option>
                                <option value="married-separate">Married Filing Separately</option>
                                <option value="head">Head of Household</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="federal-allowances"><strong>Federal Allowances:</strong></label>
                            <input type="number" id="federal-allowances" value="1" min="0" max="20" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                        
                        <div>
                            <label for="state-selection"><strong>State:</strong></label>
                            <select id="state-selection" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="CA" selected>California</option>
                                <option value="TX">Texas</option>
                                <option value="NY">New York</option>
                                <option value="FL">Florida</option>
                                <option value="IL">Illinois</option>
                                <option value="PA">Pennsylvania</option>
                                <option value="OH">Ohio</option>
                                <option value="other">Other State</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #f1f8e9; border: 2px solid #4caf50; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🏥 Pre-tax Deductions</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="health-insurance"><strong>Health Insurance:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="health-insurance" value="0" min="0" step="0.01" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="dental-vision"><strong>Dental/Vision:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="dental-vision" value="0" min="0" step="0.01" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="retirement-401k"><strong>401(k) Contribution:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="retirement-401k" value="0" min="0" step="0.01" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px; margin-top: 15px;">
                        <div>
                            <label for="hsa-contribution"><strong>HSA Contribution:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="hsa-contribution" value="0" min="0" step="0.01" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="fsa-contribution"><strong>FSA Contribution:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="fsa-contribution" value="0" min="0" step="0.01" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                        
                        <div>
                            <label for="other-pretax"><strong>Other Pre-tax:</strong></label>
                            <div style="display: flex; align-items: center; margin-top: 5px;">
                                <span style="margin-right: 5px;">$</span>
                                <input type="number" id="other-pretax" value="0" min="0" step="0.01" class="innormal" style="width: 100%;">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate PayCity Paycheck" id="calculate-paycity-btn" style="padding: 15px 30px; font-size: 18px; background-color: #1565c0; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Calculator" id="clear-paycity-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Disclaimer:</strong> This calculator provides estimates based on 2024 tax rates. Actual payroll calculations may vary. Consult PayCity or your HR department for precise figures.
        </div>
        
        <div id="paycity-results" style="display: none; margin-top: 30px;">
            <div class="paycity-result">
                <h3 style="margin-bottom: 20px;">💸 PayCity Paycheck Results</h3>
                <div id="paycity-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-paycity-btn');
    const clearBtn = document.getElementById('clear-paycity-btn');
    const resultsDiv = document.getElementById('paycity-results');
    const resultsContent = document.getElementById('paycity-results-content');
    const salaryTypeSelect = document.getElementById('salary-type');
    const hourlyDetailsDiv = document.getElementById('hourly-details');
    const payFrequencyBtns = document.querySelectorAll('.pay-frequency');
    
    let selectedFrequency = 'weekly';
    
    // Handle salary type change
    salaryTypeSelect.addEventListener('change', function() {
        if (this.value === 'hourly') {
            hourlyDetailsDiv.style.display = 'block';
        } else {
            hourlyDetailsDiv.style.display = 'none';
        }
    });
    
    // Handle pay frequency selection
    payFrequencyBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            payFrequencyBtns.forEach(b => b.classList.remove('selected'));
            this.classList.add('selected');
            selectedFrequency = this.dataset.frequency;
        });
    });
    
    calculateBtn.addEventListener('click', function() {
        const salaryAmount = parseFloat(document.getElementById('salary-amount').value) || 0;
        const salaryType = document.getElementById('salary-type').value;
        const hoursPerWeek = parseFloat(document.getElementById('hours-per-week').value) || 40;
        const weeksPerYear = parseFloat(document.getElementById('weeks-per-year').value) || 52;
        const filingStatus = document.getElementById('filing-status').value;
        const federalAllowances = parseInt(document.getElementById('federal-allowances').value) || 0;
        const state = document.getElementById('state-selection').value;
        
        const preTaxDeductions = {
            healthInsurance: parseFloat(document.getElementById('health-insurance').value) || 0,
            dentalVision: parseFloat(document.getElementById('dental-vision').value) || 0,
            retirement401k: parseFloat(document.getElementById('retirement-401k').value) || 0,
            hsaContribution: parseFloat(document.getElementById('hsa-contribution').value) || 0,
            fsaContribution: parseFloat(document.getElementById('fsa-contribution').value) || 0,
            otherPretax: parseFloat(document.getElementById('other-pretax').value) || 0
        };
        
        if (salaryAmount <= 0) {
            alert('Please enter a valid salary amount.');
            return;
        }
        
        const paycheckCalculation = calculatePayCityPaycheck(salaryAmount, salaryType, hoursPerWeek, 
            weeksPerYear, selectedFrequency, filingStatus, federalAllowances, state, preTaxDeductions);
        
        displayPayCityResults(paycheckCalculation);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('paycity-calculator-form').reset();
        payFrequencyBtns.forEach(b => b.classList.remove('selected'));
        payFrequencyBtns[0].classList.add('selected');
        selectedFrequency = 'weekly';
        hourlyDetailsDiv.style.display = 'none';
        resultsDiv.style.display = 'none';
    });
    
    function calculatePayCityPaycheck(salaryAmount, salaryType, hoursPerWeek, weeksPerYear, 
                                     frequency, filingStatus, allowances, state, preTaxDeductions) {
        
        // Calculate annual salary
        let annualSalary = salaryAmount;
        
        if (salaryType === 'hourly') {
            annualSalary = salaryAmount * hoursPerWeek * weeksPerYear;
        } else if (salaryType === 'monthly') {
            annualSalary = salaryAmount * 12;
        } else if (salaryType === 'weekly') {
            annualSalary = salaryAmount * 52;
        }
        
        // Calculate pay periods per year
        const payPeriods = {
            weekly: 52,
            biweekly: 26,
            semimonthly: 24,
            monthly: 12
        };
        
        const periodsPerYear = payPeriods[frequency];
        const grossPayPerPeriod = annualSalary / periodsPerYear;
        
        // Calculate annual pre-tax deductions
        const annualPreTaxDeductions = Object.values(preTaxDeductions).reduce((sum, val) => sum + val, 0) * periodsPerYear;
        
        // Adjusted gross income for tax calculations
        const adjustedGrossIncome = annualSalary - annualPreTaxDeductions;
        
        // Federal tax calculation (simplified)
        const federalTax = calculateFederalTax(adjustedGrossIncome, filingStatus, allowances);
        
        // State tax calculation (simplified)
        const stateTax = calculateStateTax(adjustedGrossIncome, state, filingStatus);
        
        // FICA taxes
        const socialSecurityTax = Math.min(annualSalary * 0.062, 160200 * 0.062); // 2024 cap
        const medicareTax = annualSalary * 0.0145;
        const additionalMedicareTax = annualSalary > 200000 ? (annualSalary - 200000) * 0.009 : 0;
        
        // Calculate totals
        const totalFederalTax = federalTax;
        const totalStateTax = stateTax;
        const totalFICATax = socialSecurityTax + medicareTax + additionalMedicareTax;
        const totalTaxes = totalFederalTax + totalStateTax + totalFICATax;
        const totalDeductions = totalTaxes + annualPreTaxDeductions;
        const netAnnualPay = annualSalary - totalDeductions;
        const netPayPerPeriod = netAnnualPay / periodsPerYear;
        
        return {
            annualSalary: annualSalary,
            frequency: frequency,
            periodsPerYear: periodsPerYear,
            grossPayPerPeriod: grossPayPerPeriod,
            netPayPerPeriod: netPayPerPeriod,
            netAnnualPay: netAnnualPay,
            taxes: {
                federal: totalFederalTax,
                state: totalStateTax,
                socialSecurity: socialSecurityTax,
                medicare: medicareTax,
                additionalMedicare: additionalMedicareTax,
                totalFICA: totalFICATax,
                total: totalTaxes
            },
            preTaxDeductions: preTaxDeductions,
            annualPreTaxDeductions: annualPreTaxDeductions,
            totalDeductions: totalDeductions,
            effectiveTaxRate: (totalTaxes / annualSalary) * 100,
            settings: {
                salaryType: salaryType,
                state: state,
                filingStatus: filingStatus
            }
        };
    }
    
    function calculateFederalTax(income, filingStatus, allowances) {
        // Simplified federal tax calculation (2024 brackets)
        const standardDeductions = {
            single: 13850,
            'married-joint': 27700,
            'married-separate': 13850,
            head: 20800
        };
        
        const standardDeduction = standardDeductions[filingStatus] || 13850;
        const allowanceAmount = allowances * 4300; // Approximate
        const taxableIncome = Math.max(0, income - standardDeduction - allowanceAmount);
        
        // Federal tax brackets (simplified)
        const brackets = [
            { min: 0, max: 11000, rate: 0.10 },
            { min: 11000, max: 44725, rate: 0.12 },
            { min: 44725, max: 95375, rate: 0.22 },
            { min: 95375, max: 182050, rate: 0.24 },
            { min: 182050, max: 231250, rate: 0.32 },
            { min: 231250, max: 578125, rate: 0.35 },
            { min: 578125, max: Infinity, rate: 0.37 }
        ];
        
        return calculateTaxFromBrackets(taxableIncome, brackets);
    }
    
    function calculateStateTax(income, state, filingStatus) {
        // Simplified state tax rates
        const stateTaxRates = {
            CA: 0.08,  // California average
            NY: 0.065, // New York average
            TX: 0,     // No state income tax
            FL: 0,     // No state income tax
            IL: 0.0495, // Illinois flat rate
            PA: 0.0307, // Pennsylvania flat rate
            OH: 0.035,  // Ohio average
            other: 0.05 // Average for other states
        };
        
        const rate = stateTaxRates[state] || 0.05;
        return income * rate;
    }
    
    function calculateTaxFromBrackets(income, brackets) {
        let tax = 0;
        let remainingIncome = income;
        
        for (const bracket of brackets) {
            if (remainingIncome <= 0) break;
            
            const taxableInBracket = Math.min(remainingIncome, bracket.max - bracket.min);
            tax += taxableInBracket * bracket.rate;
            remainingIncome -= taxableInBracket;
        }
        
        return tax;
    }
    
    function displayPayCityResults(calc) {
        const frequencyNames = {
            weekly: 'Weekly',
            biweekly: 'Bi-weekly',
            semimonthly: 'Semi-monthly',
            monthly: 'Monthly'
        };
        
        const stateNames = {
            CA: 'California', TX: 'Texas', NY: 'New York', FL: 'Florida',
            IL: 'Illinois', PA: 'Pennsylvania', OH: 'Ohio', other: 'Other'
        };
        
        let html = `
            <div style="font-size: 48px; font-weight: bold; margin-bottom: 20px;">
                $${calc.netPayPerPeriod.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
            </div>
            
            <div style="font-size: 18px; margin-bottom: 25px;">
                ${frequencyNames[calc.frequency]} Net Pay
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Gross ${frequencyNames[calc.frequency]}</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #1565c0;">
                        $${calc.grossPayPerPeriod.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    </div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Annual Gross</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #ff9800;">
                        $${calc.annualSalary.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    </div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Annual Net</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #4caf50;">
                        $${calc.netAnnualPay.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}
                    </div>
                </div>
                
                <div style="background: white; border-radius: 10px; padding: 20px; text-align: center; color: #2c3e50;">
                    <h4>Effective Tax Rate</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #f44336;">
                        ${calc.effectiveTaxRate.toFixed(1)}%
                    </div>
                </div>
            </div>
            
            <div class="deduction-breakdown">
                <h4 style="color: #1565c0; margin-bottom: 20px;">📊 Tax & Deduction Breakdown</h4>
                
                <div class="deduction-item">
                    <span><strong>Federal Income Tax:</strong></span>
                    <span>$${calc.taxes.federal.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                
                <div class="deduction-item">
                    <span><strong>State Income Tax (${stateNames[calc.settings.state]}):</strong></span>
                    <span>$${calc.taxes.state.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                
                <div class="deduction-item">
                    <span><strong>Social Security Tax:</strong></span>
                    <span>$${calc.taxes.socialSecurity.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                
                <div class="deduction-item">
                    <span><strong>Medicare Tax:</strong></span>
                    <span>$${calc.taxes.medicare.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                
                ${calc.taxes.additionalMedicare > 0 ? `
                <div class="deduction-item">
                    <span><strong>Additional Medicare Tax:</strong></span>
                    <span>$${calc.taxes.additionalMedicare.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                ` : ''}
                
                <div class="deduction-item" style="background-color: #ffebee; border: 2px solid #f44336;">
                    <span><strong>Total Taxes:</strong></span>
                    <span style="color: #f44336; font-weight: bold;">$${calc.taxes.total.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                
                ${calc.annualPreTaxDeductions > 0 ? `
                <div class="deduction-item" style="background-color: #e8f5e9; border: 2px solid #4caf50;">
                    <span><strong>Pre-tax Deductions:</strong></span>
                    <span style="color: #4caf50; font-weight: bold;">$${calc.annualPreTaxDeductions.toLocaleString(undefined, {minimumFractionDigits: 2, maximumFractionDigits: 2})}</span>
                </div>
                ` : ''}
            </div>
            
            <div class="paycity-brand" style="margin-top: 20px;">
                <h4>💼 PayCity Payroll Services</h4>
                <p style="margin: 10px 0; font-size: 14px;">
                    Professional payroll processing with accurate tax calculations, 
                    direct deposit, and comprehensive reporting for businesses of all sizes.
                </p>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
