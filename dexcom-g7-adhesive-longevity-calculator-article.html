<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.adhesive-interface {
    background: linear-gradient(135deg, #ff5722 0%, #d84315 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.condition-input {
    background-color: #fff3e0;
    border: 2px solid #ff5722;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.adhesive-result {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.longevity-meter {
    background-color: #e0e0e0;
    border-radius: 10px;
    height: 25px;
    margin: 15px 0;
    overflow: hidden;
    position: relative;
}
.longevity-fill {
    height: 100%;
    border-radius: 10px;
    transition: width 0.5s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
}
.excellent-adhesion { background: linear-gradient(90deg, #4caf50, #8bc34a); }
.good-adhesion { background: linear-gradient(90deg, #8bc34a, #cddc39); }
.fair-adhesion { background: linear-gradient(90deg, #ff9800, #ffc107); }
.poor-adhesion { background: linear-gradient(90deg, #f44336, #ff5722); }
.factor-card {
    background: white;
    border-radius: 10px;
    padding: 15px;
    margin: 10px;
    text-align: center;
    color: #2c3e50;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.adhesive-tips {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
</style>

<h1>Dexcom G7 Adhesive Longevity Calculator</h1>
<p>Calculate the expected adhesive longevity of your Dexcom G7 sensor based on environmental factors, skin conditions, and usage patterns. Optimize sensor retention and plan for adhesive reinforcement to maximize wear time.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="adhesiveform" id="adhesive-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Adhesive Calculator</a></li>
                <li><a href="#" data-tab="factors">Longevity Factors</a></li>
                <li><a href="#" data-tab="solutions">Adhesive Solutions</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="adhesive-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">🩹 G7 Adhesive Longevity Calculator</h3>
                
                <div class="condition-input">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🌡️ Environmental Conditions</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                        <div>
                            <label for="climate-type"><strong>Climate Type:</strong></label>
                            <select id="climate-type" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="temperate" selected>Temperate (Mild)</option>
                                <option value="humid">Humid/Tropical</option>
                                <option value="dry">Dry/Arid</option>
                                <option value="cold">Cold/Winter</option>
                                <option value="variable">Variable/Seasonal</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="humidity-level"><strong>Humidity Level:</strong></label>
                            <select id="humidity-level" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="low">Low (< 40%)</option>
                                <option value="moderate" selected>Moderate (40-60%)</option>
                                <option value="high">High (60-80%)</option>
                                <option value="very-high">Very High (> 80%)</option>
                            </select>
                        </div>
                    </div>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="temperature-range"><strong>Temperature Range:</strong></label>
                            <select id="temperature-range" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="cool">Cool (< 70°F)</option>
                                <option value="moderate" selected>Moderate (70-80°F)</option>
                                <option value="warm">Warm (80-90°F)</option>
                                <option value="hot">Hot (> 90°F)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="sun-exposure"><strong>Sun Exposure:</strong></label>
                            <select id="sun-exposure" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="minimal">Minimal (Indoor mostly)</option>
                                <option value="moderate" selected>Moderate (Some outdoor)</option>
                                <option value="high">High (Frequent outdoor)</option>
                                <option value="extreme">Extreme (Outdoor work)</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #fff3e0; border: 2px solid #ff5722; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">👤 Personal Factors</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="skin-type"><strong>Skin Type:</strong></label>
                            <select id="skin-type" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="dry">Dry skin</option>
                                <option value="normal" selected>Normal skin</option>
                                <option value="oily">Oily skin</option>
                                <option value="sensitive">Sensitive skin</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="body-hair"><strong>Body Hair at Site:</strong></label>
                            <select id="body-hair" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="none" selected>None/Shaved</option>
                                <option value="light">Light hair</option>
                                <option value="moderate">Moderate hair</option>
                                <option value="heavy">Heavy hair</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="age-group"><strong>Age Group:</strong></label>
                            <select id="age-group" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="child">Child (< 12)</option>
                                <option value="teen">Teen (12-18)</option>
                                <option value="adult" selected>Adult (18-65)</option>
                                <option value="senior">Senior (> 65)</option>
                            </select>
                        </div>
                    </div>
                </div>
                
                <div style="background-color: #f1f8e9; border: 2px solid #4caf50; border-radius: 10px; padding: 20px; margin: 15px 0;">
                    <h4 style="color: #2c3e50; margin-bottom: 20px;">🏃 Activity & Lifestyle</h4>
                    
                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="activity-level"><strong>Activity Level:</strong></label>
                            <select id="activity-level" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="sedentary">Sedentary (Minimal activity)</option>
                                <option value="light" selected>Light (Some exercise)</option>
                                <option value="moderate">Moderate (Regular exercise)</option>
                                <option value="high">High (Daily intense exercise)</option>
                                <option value="athlete">Athlete (Professional level)</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="water-activities"><strong>Water Activities:</strong></label>
                            <select id="water-activities" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="none" selected>None</option>
                                <option value="occasional">Occasional swimming</option>
                                <option value="frequent">Frequent swimming</option>
                                <option value="daily">Daily water activities</option>
                            </select>
                        </div>
                    </div>
                    
                    <div style="margin-top: 20px;">
                        <label for="adhesive-aids"><strong>Adhesive Aids Used:</strong></label>
                        <div style="margin-top: 10px;">
                            <label style="margin-right: 20px;">
                                <input type="checkbox" id="skin-tac" style="margin-right: 5px;">
                                Skin Tac/Adhesive prep
                            </label>
                            <label style="margin-right: 20px;">
                                <input type="checkbox" id="overlay-patches" style="margin-right: 5px;">
                                Overlay patches
                            </label>
                            <label style="margin-right: 20px;">
                                <input type="checkbox" id="tape-reinforcement" style="margin-right: 5px;">
                                Medical tape reinforcement
                            </label>
                            <label>
                                <input type="checkbox" id="barrier-wipes" style="margin-right: 5px;">
                                Skin barrier wipes
                            </label>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Adhesive Longevity" id="calculate-adhesive-btn" style="padding: 15px 30px; font-size: 18px; background-color: #ff5722; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Calculator" id="clear-adhesive-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Adhesive longevity estimates are based on typical conditions. Individual results may vary based on specific circumstances.
        </div>
        
        <div id="adhesive-results" style="display: none; margin-top: 30px;">
            <div class="adhesive-result">
                <h3 style="margin-bottom: 20px;">🩹 Adhesive Longevity Analysis</h3>
                <div id="adhesive-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-adhesive-btn');
    const clearBtn = document.getElementById('clear-adhesive-btn');
    const resultsDiv = document.getElementById('adhesive-results');
    const resultsContent = document.getElementById('adhesive-results-content');
    
    calculateBtn.addEventListener('click', function() {
        const climateType = document.getElementById('climate-type').value;
        const humidityLevel = document.getElementById('humidity-level').value;
        const temperatureRange = document.getElementById('temperature-range').value;
        const sunExposure = document.getElementById('sun-exposure').value;
        const skinType = document.getElementById('skin-type').value;
        const bodyHair = document.getElementById('body-hair').value;
        const ageGroup = document.getElementById('age-group').value;
        const activityLevel = document.getElementById('activity-level').value;
        const waterActivities = document.getElementById('water-activities').value;
        
        const adhesiveAids = {
            skinTac: document.getElementById('skin-tac').checked,
            overlayPatches: document.getElementById('overlay-patches').checked,
            tapeReinforcement: document.getElementById('tape-reinforcement').checked,
            barrierWipes: document.getElementById('barrier-wipes').checked
        };
        
        const adhesiveAnalysis = calculateAdhesiveLongevity(climateType, humidityLevel, temperatureRange, 
            sunExposure, skinType, bodyHair, ageGroup, activityLevel, waterActivities, adhesiveAids);
        
        displayAdhesiveResults(adhesiveAnalysis);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('adhesive-calculator-form').reset();
        resultsDiv.style.display = 'none';
    });
    
    function calculateAdhesiveLongevity(climate, humidity, temperature, sunExposure, skinType, 
                                      bodyHair, ageGroup, activityLevel, waterActivities, adhesiveAids) {
        
        // Base longevity for G7 (10 days standard)
        let baseLongevity = 10;
        let longevityScore = 100; // Start with 100% (10 days)
        
        // Environmental factors
        const climateMultipliers = {
            temperate: 1.0, humid: 0.7, dry: 1.1, cold: 0.9, variable: 0.85
        };
        
        const humidityMultipliers = {
            low: 1.1, moderate: 1.0, high: 0.8, 'very-high': 0.6
        };
        
        const temperatureMultipliers = {
            cool: 1.05, moderate: 1.0, warm: 0.9, hot: 0.75
        };
        
        const sunExposureMultipliers = {
            minimal: 1.0, moderate: 0.95, high: 0.85, extreme: 0.7
        };
        
        // Personal factors
        const skinTypeMultipliers = {
            dry: 0.9, normal: 1.0, oily: 0.8, sensitive: 0.85
        };
        
        const bodyHairMultipliers = {
            none: 1.0, light: 0.95, moderate: 0.85, heavy: 0.7
        };
        
        const ageGroupMultipliers = {
            child: 0.8, teen: 0.85, adult: 1.0, senior: 0.9
        };
        
        // Activity factors
        const activityMultipliers = {
            sedentary: 1.1, light: 1.0, moderate: 0.9, high: 0.75, athlete: 0.6
        };
        
        const waterActivityMultipliers = {
            none: 1.0, occasional: 0.9, frequent: 0.75, daily: 0.5
        };
        
        // Apply all multipliers
        longevityScore *= climateMultipliers[climate] || 1.0;
        longevityScore *= humidityMultipliers[humidity] || 1.0;
        longevityScore *= temperatureMultipliers[temperature] || 1.0;
        longevityScore *= sunExposureMultipliers[sunExposure] || 1.0;
        longevityScore *= skinTypeMultipliers[skinType] || 1.0;
        longevityScore *= bodyHairMultipliers[bodyHair] || 1.0;
        longevityScore *= ageGroupMultipliers[ageGroup] || 1.0;
        longevityScore *= activityMultipliers[activityLevel] || 1.0;
        longevityScore *= waterActivityMultipliers[waterActivities] || 1.0;
        
        // Adhesive aids bonuses
        if (adhesiveAids.skinTac) longevityScore *= 1.2;
        if (adhesiveAids.overlayPatches) longevityScore *= 1.3;
        if (adhesiveAids.tapeReinforcement) longevityScore *= 1.15;
        if (adhesiveAids.barrierWipes) longevityScore *= 1.1;
        
        // Calculate final longevity
        const expectedDays = Math.max(3, Math.min(14, (longevityScore / 100) * baseLongevity));
        
        // Determine adhesive grade
        let adhesiveGrade = 'Poor';
        let gradeClass = 'poor-adhesion';
        
        if (expectedDays >= 9) {
            adhesiveGrade = 'Excellent';
            gradeClass = 'excellent-adhesion';
        } else if (expectedDays >= 7) {
            adhesiveGrade = 'Good';
            gradeClass = 'good-adhesion';
        } else if (expectedDays >= 5) {
            adhesiveGrade = 'Fair';
            gradeClass = 'fair-adhesion';
        }
        
        // Calculate risk factors
        const riskFactors = [];
        if (humidity === 'very-high' || humidity === 'high') riskFactors.push('High humidity');
        if (temperature === 'hot') riskFactors.push('High temperature');
        if (activityLevel === 'high' || activityLevel === 'athlete') riskFactors.push('High activity level');
        if (waterActivities !== 'none') riskFactors.push('Water exposure');
        if (skinType === 'oily') riskFactors.push('Oily skin');
        if (bodyHair === 'moderate' || bodyHair === 'heavy') riskFactors.push('Body hair');
        
        // Generate recommendations
        const recommendations = generateRecommendations(expectedDays, riskFactors, adhesiveAids);
        
        return {
            expectedDays: expectedDays,
            longevityScore: longevityScore,
            adhesiveGrade: adhesiveGrade,
            gradeClass: gradeClass,
            riskFactors: riskFactors,
            recommendations: recommendations,
            factors: {
                climate, humidity, temperature, sunExposure, skinType, 
                bodyHair, ageGroup, activityLevel, waterActivities
            },
            adhesiveAids: adhesiveAids
        };
    }
    
    function generateRecommendations(expectedDays, riskFactors, adhesiveAids) {
        const recommendations = [];
        
        if (expectedDays < 7) {
            recommendations.push('Consider using adhesive enhancement products');
            recommendations.push('Apply skin prep before sensor insertion');
        }
        
        if (riskFactors.includes('High humidity')) {
            recommendations.push('Use antiperspirant around sensor site (not on it)');
            recommendations.push('Consider overlay patches for humid conditions');
        }
        
        if (riskFactors.includes('High temperature')) {
            recommendations.push('Stay in air-conditioned environments when possible');
            recommendations.push('Use cooling towels to reduce sweating');
        }
        
        if (riskFactors.includes('High activity level')) {
            recommendations.push('Apply extra adhesive tape before workouts');
            recommendations.push('Consider waterproof overlay patches');
        }
        
        if (riskFactors.includes('Water exposure')) {
            recommendations.push('Use waterproof adhesive patches');
            recommendations.push('Dry sensor area thoroughly after water activities');
        }
        
        if (riskFactors.includes('Oily skin')) {
            recommendations.push('Clean insertion site with alcohol prep');
            recommendations.push('Use skin barrier wipes before application');
        }
        
        if (riskFactors.includes('Body hair')) {
            recommendations.push('Trim (don\'t shave) hair at insertion site');
            recommendations.push('Use extra adhesive around hairy areas');
        }
        
        if (!adhesiveAids.skinTac && expectedDays < 8) {
            recommendations.push('Consider using Skin Tac or similar adhesive prep');
        }
        
        if (!adhesiveAids.overlayPatches && riskFactors.length > 2) {
            recommendations.push('Use overlay patches for additional security');
        }
        
        return recommendations;
    }
    
    function displayAdhesiveResults(data) {
        const factorNames = {
            climate: { temperate: 'Temperate', humid: 'Humid/Tropical', dry: 'Dry/Arid', cold: 'Cold/Winter', variable: 'Variable' },
            humidity: { low: 'Low', moderate: 'Moderate', high: 'High', 'very-high': 'Very High' },
            temperature: { cool: 'Cool', moderate: 'Moderate', warm: 'Warm', hot: 'Hot' },
            activityLevel: { sedentary: 'Sedentary', light: 'Light', moderate: 'Moderate', high: 'High', athlete: 'Athlete' }
        };
        
        let html = `
            <div style="margin-bottom: 25px;">
                <div style="font-size: 48px; font-weight: bold; margin-bottom: 10px;">
                    ${data.expectedDays.toFixed(1)} Days
                </div>
                <div style="font-size: 18px; margin-bottom: 15px;">
                    Expected Adhesive Longevity
                </div>
                <div class="longevity-meter">
                    <div class="longevity-fill ${data.gradeClass}" style="width: ${(data.expectedDays / 10) * 100}%;">
                        ${data.adhesiveGrade}
                    </div>
                </div>
                <div style="font-size: 16px; margin-top: 10px;">
                    Adhesive Performance: <strong>${data.adhesiveGrade}</strong>
                </div>
            </div>
            
            <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 20px; margin-bottom: 25px;">
                <div class="factor-card">
                    <h4>Longevity Score</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #ff5722;">
                        ${data.longevityScore.toFixed(0)}%
                    </div>
                    <div style="font-size: 12px; color: #666;">of standard 10 days</div>
                </div>
                
                <div class="factor-card">
                    <h4>Risk Factors</h4>
                    <div style="font-size: 24px; font-weight: bold; color: ${data.riskFactors.length > 3 ? '#f44336' : data.riskFactors.length > 1 ? '#ff9800' : '#4caf50'};">
                        ${data.riskFactors.length}
                    </div>
                    <div style="font-size: 12px; color: #666;">identified</div>
                </div>
                
                <div class="factor-card">
                    <h4>Adhesive Aids</h4>
                    <div style="font-size: 24px; font-weight: bold; color: #2196f3;">
                        ${Object.values(data.adhesiveAids).filter(Boolean).length}
                    </div>
                    <div style="font-size: 12px; color: #666;">in use</div>
                </div>
                
                <div class="factor-card">
                    <h4>Replacement Day</h4>
                    <div style="font-size: 20px; font-weight: bold; color: #9c27b0;">
                        Day ${Math.ceil(data.expectedDays)}
                    </div>
                    <div style="font-size: 12px; color: #666;">estimated</div>
                </div>
            </div>
            
            <div class="adhesive-tips">
                <h4 style="color: #ff5722; margin-bottom: 20px;">📊 Detailed Analysis</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <h5>Environmental Factors:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Climate:</strong> ${factorNames.climate[data.factors.climate]}</li>
                            <li><strong>Humidity:</strong> ${factorNames.humidity[data.factors.humidity]}</li>
                            <li><strong>Temperature:</strong> ${factorNames.temperature[data.factors.temperature]}</li>
                            <li><strong>Sun Exposure:</strong> ${data.factors.sunExposure}</li>
                        </ul>
                    </div>
                    
                    <div>
                        <h5>Personal Factors:</h5>
                        <ul style="margin: 10px 0;">
                            <li><strong>Skin Type:</strong> ${data.factors.skinType}</li>
                            <li><strong>Body Hair:</strong> ${data.factors.bodyHair}</li>
                            <li><strong>Age Group:</strong> ${data.factors.ageGroup}</li>
                            <li><strong>Activity Level:</strong> ${factorNames.activityLevel[data.factors.activityLevel]}</li>
                        </ul>
                    </div>
                </div>
                
                ${data.riskFactors.length > 0 ? `
                <div style="background-color: #ffebee; border-radius: 8px; padding: 15px; margin: 15px 0;">
                    <h5 style="color: #f44336;">⚠️ Risk Factors Identified:</h5>
                    <ul style="margin: 10px 0; color: #2c3e50;">
                        ${data.riskFactors.map(factor => `<li>${factor}</li>`).join('')}
                    </ul>
                </div>
                ` : ''}
                
                <div style="background-color: #e8f5e9; border-radius: 8px; padding: 15px; margin: 15px 0;">
                    <h5 style="color: #4caf50;">💡 Personalized Recommendations:</h5>
                    <ul style="margin: 10px 0; color: #2c3e50;">
                        ${data.recommendations.map(rec => `<li>${rec}</li>`).join('')}
                    </ul>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>🩹 Adhesive Enhancement Products:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Skin Tac:</strong> Liquid adhesive for better initial stick</li>
                    <li><strong>Overlay Patches:</strong> Transparent patches for extra security</li>
                    <li><strong>Medical Tape:</strong> Hypoallergenic tape for edge reinforcement</li>
                    <li><strong>Barrier Wipes:</strong> Protect skin while improving adhesion</li>
                    <li><strong>Adhesive Remover:</strong> Gentle removal to preserve skin health</li>
                    <li><strong>Skin Prep:</strong> Clean and prepare insertion site properly</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
