(function(){var n,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba="function"==typeof Object.defineProperties?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");
},da=ca(this),p=function(a,b){if(b)a:{var c=da;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&null!=b&&ba(c,a,{configurable:!0,writable:!0,value:b})}};
p("Symbol",function(a){if(a)return a;var b=function(f,g){this.Aa=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.Aa};var c="jscomp_symbol_"+(1E9*Math.random()>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e});
p("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=da[b[c]];"function"===typeof d&&"function"!=typeof d.prototype[a]&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ea(aa(this))}})}return a});
var ea=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a},q=function(a){var b="undefined"!=typeof Symbol&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if("number"==typeof a.length)return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},fa="function"==typeof Object.create?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ha;
if("function"==typeof Object.setPrototypeOf)ha=Object.setPrototypeOf;else{var ia;a:{var ja={a:!0},ka={};try{ka.__proto__=ja;ia=ka.a;break a}catch(a){}ia=!1}ha=ia?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var la=ha,ma=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b},t=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)};
p("WeakMap",function(a){function b(){}function c(k){var l=typeof k;return"object"===l&&null!==k||"function"===l}function d(k){if(!t(k,f)){var l=new b;ba(k,f,{value:l})}}function e(k){var l=Object[k];l&&(Object[k]=function(m){if(m instanceof b)return m;Object.isExtensible(m)&&d(m);return l(m)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),l=Object.seal({}),m=new a([[k,2],[l,3]]);if(2!=m.get(k)||3!=m.get(l))return!1;m.delete(k);m.set(l,4);return!m.has(k)&&4==m.get(l)}catch(r){return!1}}())return a;
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var g=0,h=function(k){this.T=(g+=Math.random()+1).toString();if(k){k=q(k);for(var l;!(l=k.next()).done;)l=l.value,this.set(l[0],l[1])}};h.prototype.set=function(k,l){if(!c(k))throw Error("Invalid WeakMap key");d(k);if(!t(k,f))throw Error("WeakMap key fail: "+k);k[f][this.T]=l;return this};h.prototype.get=function(k){return c(k)&&t(k,f)?k[f][this.T]:void 0};h.prototype.has=function(k){return c(k)&&t(k,f)&&t(k[f],this.T)};
h.prototype.delete=function(k){return c(k)&&t(k,f)&&t(k[f],this.T)?delete k[f][this.T]:!1};return h});
p("Map",function(a){if(function(){if(!a||"function"!=typeof a||!a.prototype.entries||"function"!=typeof Object.seal)return!1;try{var h=Object.seal({x:4}),k=new a(q([[h,"s"]]));if("s"!=k.get(h)||1!=k.size||k.get({x:4})||k.set({x:4},"t")!=k||2!=k.size)return!1;var l=k.entries(),m=l.next();if(m.done||m.value[0]!=h||"s"!=m.value[1])return!1;m=l.next();return m.done||4!=m.value[0].x||"t"!=m.value[1]||!l.next().done?!1:!0}catch(r){return!1}}())return a;var b=new WeakMap,c=function(h){this[0]={};this[1]=
f();this.size=0;if(h){h=q(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=0===h?0:h;var l=d(this,h);l.list||(l.list=this[0][l.id]=[]);l.h?l.h.value=k:(l.h={next:this[1],B:this[1].B,head:this[1],key:h,value:k},l.list.push(l.h),this[1].B.next=l.h,this[1].B=l.h,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.h&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.h.B.next=h.h.next,h.h.next.B=h.h.B,h.h.head=
null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].B=f();this.size=0};c.prototype.has=function(h){return!!d(this,h).h};c.prototype.get=function(h){return(h=d(this,h).h)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=function(h,k){for(var l=this.entries(),m;!(m=l.next()).done;)m=
m.value,h.call(k,m[1],m[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(h,k){var l=k&&typeof k;"object"==l||"function"==l?b.has(k)?l=b.get(k):(l=""+ ++g,b.set(k,l)):l="p_"+k;var m=h[0][l];if(m&&t(h[0],l))for(h=0;h<m.length;h++){var r=m[h];if(k!==k&&r.key!==r.key||k===r.key)return{id:l,list:m,index:h,h:r}}return{id:l,list:m,index:-1,h:void 0}},e=function(h,k){var l=h[1];return ea(function(){if(l){for(;l.head!=h[1];)l=l.B;for(;l.next!=l.head;)return l=l.next,{done:!1,value:k(l)};
l=null}return{done:!0,value:void 0}})},f=function(){var h={};return h.B=h.next=h.head=h},g=0;return c});
p("String.prototype.startsWith",function(a){return a?a:function(b,c){if(null==this)throw new TypeError("The 'this' value for String.prototype.startsWith must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype.startsWith must not be a regular expression");var d=this+"";b+="";var e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}});
p("Number.isFinite",function(a){return a?a:function(b){return"number"!==typeof b?!1:!isNaN(b)&&Infinity!==b&&-Infinity!==b}});var na=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};p("Array.prototype.keys",function(a){return a?a:function(){return na(this,function(b){return b})}});
p("Array.prototype.values",function(a){return a?a:function(){return na(this,function(b,c){return c})}});p("Array.from",function(a){return a?a:function(b,c,d){c=null!=c?c:function(h){return h};var e=[],f="undefined"!=typeof Symbol&&Symbol.iterator&&b[Symbol.iterator];if("function"==typeof f){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}});
p("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}});p("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)t(b,d)&&c.push(b[d]);return c}});/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var u=this||self,v=function(a){var b=typeof a;b="object"!=b?b:a?Array.isArray(a)?"array":b:"null";return"array"==b||"object"==b&&"number"==typeof a.length},oa=function(a){var b=typeof a;return"object"==b&&null!=a||"function"==b},pa=function(a,b,c){return a.call.apply(a.bind,arguments)},qa=function(a,b,c){if(!a)throw Error();if(2<arguments.length){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,
e)}}return function(){return a.apply(b,arguments)}},ra=function(a,b,c){ra=Function.prototype.bind&&-1!=Function.prototype.bind.toString().indexOf("native code")?pa:qa;return ra.apply(null,arguments)},sa=function(a,b){function c(){}c.prototype=b.prototype;a.aa=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.Ua=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}},ta=function(a){return a};function w(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,w);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));void 0!==b&&(this.cause=b)}sa(w,Error);w.prototype.name="CustomError";var ua=Array.prototype.indexOf?function(a,b){return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if("string"===typeof a)return"string"!==typeof b||1!=b.length?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},va=Array.prototype.forEach?function(a,b){Array.prototype.forEach.call(a,b,void 0)}:function(a,b){for(var c=a.length,d="string"===typeof a?a.split(""):a,e=0;e<c;e++)e in d&&b.call(void 0,d[e],e,a)};
function wa(a){var b=document;b=xa(b.querySelectorAll&&b.querySelector?b.querySelectorAll(".GoogleActiveViewClass"):ya());a:{for(var c=b.length,d="string"===typeof b?b.split(""):b,e=0;e<c;e++)if(e in d&&a.call(void 0,d[e],e,b)){a=e;break a}a=-1}return 0>a?null:"string"===typeof b?b.charAt(a):b[a]}function za(a,b){b=ua(a,b);var c;(c=0<=b)&&Array.prototype.splice.call(a,b,1);return c}function xa(a){var b=a.length;if(0<b){for(var c=Array(b),d=0;d<b;d++)c[d]=a[d];return c}return[]};var Aa,Ba;a:{for(var Ca=["CLOSURE_FLAGS"],Da=u,Ea=0;Ea<Ca.length;Ea++)if(Da=Da[Ca[Ea]],null==Da){Ba=null;break a}Ba=Da}var Fa=Ba&&Ba[610401301];Aa=null!=Fa?Fa:!1;function Ga(){var a=u.navigator;return a&&(a=a.userAgent)?a:""}var x,Ha=u.navigator;x=Ha?Ha.userAgentData||null:null;function Ia(a){return Aa?x?x.brands.some(function(b){return(b=b.brand)&&-1!=b.indexOf(a)}):!1:!1}function y(a){return-1!=Ga().indexOf(a)};function z(){return Aa?!!x&&0<x.brands.length:!1}function Ja(){return z()?!1:y("Trident")||y("MSIE")}function Ka(){return y("Safari")&&!(La()||(z()?0:y("Coast"))||(z()?0:y("Opera"))||(z()?0:y("Edge"))||(z()?Ia("Microsoft Edge"):y("Edg/"))||(z()?Ia("Opera"):y("OPR"))||y("Firefox")||y("FxiOS")||y("Silk")||y("Android"))}function La(){return z()?Ia("Chromium"):(y("Chrome")||y("CriOS"))&&!(z()?0:y("Edge"))||y("Silk")};var Ma=function(a){Ma[" "](a);return a};Ma[" "]=function(){};var Qa=function(a,b){try{return Ma(a[b]),!0}catch(c){}return!1};var Ra=Ja(),Sa=y("Gecko")&&!(-1!=Ga().toLowerCase().indexOf("webkit")&&!y("Edge"))&&!(y("Trident")||y("MSIE"))&&!y("Edge"),Ta=-1!=Ga().toLowerCase().indexOf("webkit")&&!y("Edge");var Ua=function(){},Va=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}};function Wa(a,b){for(var c in a)if(b.call(void 0,a[c],c,a))return!0;return!1};var Xa;var A=function(a,b){this.Qa=a===Ya&&b||"";this.Ba=Za},Za={},Ya={};var $a=function(a){this.ua=a};$a.prototype.toString=function(){return this.ua+""};
var bb=function(){for(var a=[new A(Ya,"//"),new A(Ya,"tpc.googlesyndication.com"),new A(Ya,"/sodar/62bHydCX.html")],b="",c=0;c<a.length;c++){var d=a[c];b+=d instanceof A&&d.constructor===A&&d.Ba===Za?d.Qa:"type_error:Const"}a=b;if(void 0===Xa){b=null;if((c=u.trustedTypes)&&c.createPolicy)try{b=c.createPolicy("goog#html",{createHTML:ta,createScript:ta,createScriptURL:ta})}catch(e){u.console&&u.console.error(e.message)}Xa=b}a=(b=Xa)?b.createScriptURL(a):a;return new $a(a,ab)},ab={};var cb=function(){return"opacity".replace(/\-([a-z])/g,function(a,b){return b.toUpperCase()})},db=function(a){return a.replace(RegExp("(^|[\\s]+)([a-z])","g"),function(b,c,d){return c+d.toUpperCase()})};var ya=function(){var a,b,c;if(document.querySelectorAll&&document.querySelector)return document.querySelectorAll(".GoogleActiveViewClass");if(document.getElementsByClassName){var d=document.getElementsByClassName("GoogleActiveViewClass");return d}d=document.getElementsByTagName("*");var e={};for(b=c=0;a=d[b];b++){var f=a.className,g;if(g="function"==typeof f.split)g=0<=ua(f.split(/\s+/),"GoogleActiveViewClass");g&&(e[c++]=a)}e.length=c;return e},eb=function(){var a=document;var b="IFRAME";"application/xhtml+xml"===
a.contentType&&(b=b.toLowerCase());return a.createElement(b)},fb=function(a){return 9==a.nodeType?a:a.ownerDocument||a.document},gb=function(a){try{var b;if(!(b=a.contentWindow)){if(a.contentDocument){var c=a.contentDocument;var d=c?c.parentWindow||c.defaultView:window}else d=null;b=d}return b}catch(e){}return null};var hb=function(a){return Ka()?(a=(a=fb(a))&&(a?a.parentWindow||a.defaultView:window),!!(a&&a.location&&a.location.ancestorOrigins&&0<a.location.ancestorOrigins.length&&a.location.origin==a.location.ancestorOrigins[0])):!0};var B=function(){};B.prototype.next=function(){return C};var C={done:!0,value:void 0};B.prototype.oa=function(){return this};
var ib=function(a){if(a instanceof B)return a;if("function"==typeof a.oa)return a.oa(!1);if(v(a)){var b=0,c=new B;c.next=function(){for(;;){if(b>=a.length)return C;if(b in a)return{value:a[b++],done:!1};b++}};return c}throw Error("Not implemented");},jb=function(a,b){if(v(a))va(a,b);else for(a=ib(a);;){var c=a.next();if(c.done)break;b.call(void 0,c.value,void 0,a)}},kb=function(a,b){var c=1;jb(a,function(d){c=b.call(void 0,c,d)});return c},lb=function(a,b){var c=ib(a);a=new B;a.next=function(){var d=
c.next(),e=d.value;return d.done?C:b.call(void 0,e,void 0,c)?{value:e,done:!1}:C};return a},mb=function(a){var b=ib(a);a=new B;var c=100;a.next=function(){return 0<c--?b.next():C};return a};var E=function(a,b){this.Ka=b;this.wa=null==a;this.sa=a};E.prototype=fa(B.prototype);E.prototype.constructor=E;if(la)la(E,B);else for(var G in B)if("prototype"!=G)if(Object.defineProperties){var nb=Object.getOwnPropertyDescriptor(B,G);nb&&Object.defineProperty(E,G,nb)}else E[G]=B[G];E.aa=B.prototype;
E.prototype.next=function(){if(this.wa)return C;var a=this.sa||null;this.wa=null==a;var b;if(b=a){b=this.Ka;if(Qa(a,"parentElement")&&null!=a.parentElement&&a!=a.parentElement)var c=a.parentElement;else if(b){var d=void 0===d?hb:d;if(d(a))try{var e=fb(a),f=e&&(e?e.parentWindow||e.defaultView:window),g=f&&f.frameElement;c=null==g?null:g}catch(h){c=null}else c=null}else c=null;b=c}this.sa=b;return{value:a,done:!1}};var ob={};var pb=function(a){var b=1;a=mb(new E(a,!0));a=lb(a,function(){return 0<b});return kb(a,function(c,d){var e=1;if(Qa(d,"style")&&d.style){var f=parseFloat;a:{var g=fb(d);if(g.defaultView&&g.defaultView.getComputedStyle&&(g=g.defaultView.getComputedStyle(d,null))){g=g.opacity||g.getPropertyValue("opacity")||"";break a}g=""}if(!g){g=d.style[cb()];if("undefined"!==typeof g)d=g;else{g=d.style;var h=ob.opacity;if(!h){var k=cb();h=k;void 0===d.style[k]&&(k=(Ta?"Webkit":Sa?"Moz":Ra?"ms":null)+db(k),void 0!==
d.style[k]&&(h=k));ob.opacity=h}d=g[h]||""}g=d}f=f(g);"number"!==typeof f||isNaN(f)||(e=f)}return b=c*e})};var qb=document,rb=window;var sb=Va(function(){var a=!1;try{var b=Object.defineProperty({},"passive",{get:function(){a=!0}});u.addEventListener("test",null,b)}catch(c){}return a});function tb(a){return a?a.passive&&sb()?a:a.capture||!1:!1}var ub=function(a,b,c,d){a.addEventListener&&a.addEventListener(b,c,tb(d))},vb=function(a,b,c,d){a.removeEventListener&&a.removeEventListener(b,c,tb(d))};var wb=function(a,b){var c=void 0===c?{}:c;this.error=a;this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror";this.meta=c};var xb=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),yb=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(0<=d){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};var zb=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},Ab=function(a){a=void 0===a?document:a;return a.createElement("img")};var Bb=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)"),Cb=function(a,b){this.ca=a;this.za=b},Db=function(a,b){this.url=a;this.qa=!!b;this.depth=null};var Eb=null;function Fb(){var a=void 0===a?u:a;return(a=a.performance)&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function Gb(){var a=void 0===a?u:a;return(a=a.performance)&&a.now?a.now():null};var Hb=function(a,b){var c=Gb()||Fb();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()};var J=u.performance,Ib=!!(J&&J.mark&&J.measure&&J.clearMarks),K=Va(function(){var a;if(a=Ib){var b;if(null===Eb){Eb="";try{a="";try{a=u.top.location.hash}catch(c){a=u.location.hash}a&&(Eb=(b=a.match(/\bdeid=([\d,]+)/))?b[1]:"")}catch(c){}}b=Eb;a=!!b.indexOf&&0<=b.indexOf("1337")}return a}),Jb=function(){var a=window;this.O=[];this.Ia=a||u;var b=null;a&&(a.google_js_reporting_queue=a.google_js_reporting_queue||[],this.O=a.google_js_reporting_queue,b=a.google_measure_js_timing);this.N=K()||(null!=b?
b:1>Math.random())};Jb.prototype.disable=function(){this.N=!1;this.O!=this.Ia.google_js_reporting_queue&&(K()&&va(this.O,Kb),this.O.length=0)};var Kb=function(a){a&&J&&K()&&(J.clearMarks("goog_"+a.label+"_"+a.uniqueId+"_start"),J.clearMarks("goog_"+a.label+"_"+a.uniqueId+"_end"))};Jb.prototype.start=function(a,b){if(!this.N)return null;a=new Hb(a,b);b="goog_"+a.label+"_"+a.uniqueId+"_start";J&&K()&&J.mark(b);return a};
Jb.prototype.end=function(a){if(this.N&&"number"===typeof a.value){a.duration=(Gb()||Fb())-a.value;var b="goog_"+a.label+"_"+a.uniqueId+"_end";J&&K()&&J.mark(b);!this.N||2048<this.O.length||this.O.push(a)}};var Lb=function(){this.Ma=4E3;this.la="&";this.C={};this.Na=0;this.A=[]},Mb=function(a,b){var c={};c[a]=b;return[c]},Sb=function(a,b,c,d,e){var f=[];zb(a,function(g,h){(g=Nb(g,b,c,d,e))&&f.push(h+"="+g)});return f.join(b)},Nb=function(a,b,c,d,e){if(null==a)return"";b=b||"&";c=c||",$";"string"==typeof c&&(c=c.split(""));if(a instanceof Array){if(d=d||0,d<c.length){for(var f=[],g=0;g<a.length;g++)f.push(Nb(a[g],b,c,d+1,e));return f.join(c[d])}}else if("object"==typeof a)return e=e||0,2>e?encodeURIComponent(Sb(a,
b,c,d,e+1)):"...";return encodeURIComponent(String(a))},Ub=function(a,b,c,d){b=b+"//"+c+d;var e=Tb(a)-d.length;if(0>e)return"";a.A.sort(function(m,r){return m-r});d=null;c="";for(var f=0;f<a.A.length;f++)for(var g=a.A[f],h=a.C[g],k=0;k<h.length;k++){if(!e){d=null==d?g:d;break}var l=Sb(h[k],a.la,",$");if(l){l=c+l;if(e>=l.length){e-=l.length;b+=l;c=a.la;break}d=null==d?g:d}}a="";null!=d&&(a=c+"trn="+d);return b+a},Tb=function(a){var b=1,c;for(c in a.C)b=c.length>b?c.length:b;return a.Ma-3-b-a.la.length-
1};var Xb=function(){var a=Vb;this.ta=Wb;this.Ga="jserror";this.xa=!0;this.ba=void 0===a?null:a;this.fa=null;this.na=!1;this.Fa=this.ra},$b=function(a){var b=Yb;try{if(b.ba&&b.ba.N){var c=b.ba.start((374).toString(),3);var d=a();b.ba.end(c)}else d=a()}catch(g){a=b.xa;try{Kb(c),a=b.Fa(374,new wb(g,{message:Zb(g)}),void 0,void 0)}catch(h){b.ra(217,h)}if(a){var e,f;null==(e=window.console)||null==(f=e.error)||f.call(e,g)}else throw g;}return d},ac=function(a){return function(){var b=ma.apply(0,arguments);
return $b(function(){return a.apply(void 0,b)})}};
Xb.prototype.ra=function(a,b,c,d,e){e=e||this.Ga;try{var f=new Lb;f.A.push(1);f.C[1]=Mb("context",a);b.error&&b.meta&&b.id||(b=new wb(b,{message:Zb(b)}));if(b.msg){var g=b.msg.substring(0,512);f.A.push(2);f.C[2]=Mb("msg",g)}var h=b.meta||{};if(this.fa)try{this.fa(h)}catch(W){}if(d)try{d(h)}catch(W){}b=[h];f.A.push(3);f.C[3]=b;d=u;b=[];g=null;do{var k=d;try{var l=!!k&&null!=k.location.href&&Qa(k,"foo")}catch(W){l=!1}if(l){var m=k.location.href;g=k.document&&k.document.referrer||null}else m=g,g=null;
b.push(new Db(m||""));try{d=k.parent}catch(W){d=null}}while(d&&k!=d);m=0;for(var r=b.length-1;m<=r;++m)b[m].depth=r-m;k=u;if(k.location&&k.location.ancestorOrigins&&k.location.ancestorOrigins.length==b.length-1)for(r=1;r<b.length;++r){var H=b[r];H.url||(H.url=k.location.ancestorOrigins[r-1]||"",H.qa=!0)}var D=new Db(u.location.href,!1);k=null;var Na=b.length-1;for(H=Na;0<=H;--H){var F=b[H];!k&&Bb.test(F.url)&&(k=F);if(F.url&&!F.qa){D=F;break}}F=null;var $c=b.length&&b[Na].url;0!=D.depth&&$c&&(F=b[Na]);
var I=new Cb(D,F);if(I.za){var ad=I.za.url||"";f.A.push(4);f.C[4]=Mb("top",ad)}var Oa={url:I.ca.url||""};if(I.ca.url){var Pa=I.ca.url.match(xb),Ob=Pa[1],Pb=Pa[3],Qb=Pa[4];D="";Ob&&(D+=Ob+":");Pb&&(D+="//",D+=Pb,Qb&&(D+=":"+Qb));var Rb=D}else Rb="";Oa=[Oa,{url:Rb}];f.A.push(5);f.C[5]=Oa;bc(this.ta,e,f,this.na,c)}catch(W){try{bc(this.ta,e,{context:"ecmserr",rctx:a,msg:Zb(W),url:I&&I.ca.url},this.na,c)}catch(Jd){}}return this.xa};
var Zb=function(a){var b=a.toString();a.name&&-1==b.indexOf(a.name)&&(b+=": "+a.name);a.message&&-1==b.indexOf(a.message)&&(b+=": "+a.message);if(a.stack){a=a.stack;var c=b;try{-1==a.indexOf(c)&&(a=c+"\n"+a);for(var d;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n")}catch(e){b=c}}return b};var L=function(a,b,c,d){cc(a,b,void 0===c?null:c,void 0===d?!1:d)};function cc(a,b,c,d){var e=!1;e=void 0===e?!1:e;a.google_image_requests||(a.google_image_requests=[]);var f=Ab(a.document);if(c||d){var g=function(h){c&&c(h);d&&za(a.google_image_requests,f);vb(f,"load",g);vb(f,"error",g)};ub(f,"load",g);ub(f,"error",g)}e&&(f.attributionSrc="");f.src=b;a.google_image_requests.push(f)}
function dc(a){var b=void 0===b?!1:b;var c;if(c=u.navigator)c=u.navigator.userAgent,c=/Chrome/.test(c)&&!/Edge/.test(c)?!0:!1;c&&u.navigator.sendBeacon?u.navigator.sendBeacon(a):L(u,a,void 0,b)};var ec=function(a,b){this.La=100;this.Da=a;this.Pa=b;this.Y=0;this.X=null};ec.prototype.get=function(){if(0<this.Y){this.Y--;var a=this.X;this.X=a.next;a.next=null}else a=this.Da();return a};ec.prototype.put=function(a){this.Pa(a);this.Y<this.La&&(this.Y++,a.next=this.X,this.X=a)};var fc,gc=function(){var a=u.MessageChannel;"undefined"===typeof a&&"undefined"!==typeof window&&window.postMessage&&window.addEventListener&&!y("Presto")&&(a=function(){var e=eb();e.style.display="none";document.documentElement.appendChild(e);var f=e.contentWindow;e=f.document;e.open();e.close();var g="callImmediate"+Math.random(),h="file:"==f.location.protocol?"*":f.location.protocol+"//"+f.location.host;e=ra(function(k){if(("*"==h||k.origin==h)&&k.data==g)this.port1.onmessage()},this);f.addEventListener("message",
e,!1);this.port1={};this.port2={postMessage:function(){f.postMessage(g,h)}}});if("undefined"!==typeof a&&!Ja()){var b=new a,c={},d=c;b.port1.onmessage=function(){if(void 0!==c.next){c=c.next;var e=c.pa;c.pa=null;e()}};return function(e){d.next={pa:e};d=d.next;b.port2.postMessage(0)}}return function(e){u.setTimeout(e,0)}};function hc(a){u.setTimeout(function(){throw a;},0)};var ic=function(){this.ea=this.S=null};ic.prototype.add=function(a,b){var c=jc.get();c.set(a,b);this.ea?this.ea.next=c:this.S=c;this.ea=c};ic.prototype.remove=function(){var a=null;this.S&&(a=this.S,this.S=this.S.next,this.S||(this.ea=null),a.next=null);return a};var jc=new ec(function(){return new kc},function(a){return a.reset()}),kc=function(){this.next=this.scope=this.ia=null};kc.prototype.set=function(a,b){this.ia=a;this.scope=b;this.next=null};
kc.prototype.reset=function(){this.next=this.scope=this.ia=null};var lc,mc=!1,nc=new ic,pc=function(a,b){lc||oc();mc||(lc(),mc=!0);nc.add(a,b)},oc=function(){if(u.Promise&&u.Promise.resolve){var a=u.Promise.resolve(void 0);lc=function(){a.then(qc)}}else lc=function(){var b=qc;"function"!==typeof u.setImmediate||u.Window&&u.Window.prototype&&(z()||!y("Edge"))&&u.Window.prototype.setImmediate==u.setImmediate?(fc||(fc=gc()),fc(b)):u.setImmediate(b)}},qc=function(){for(var a;a=nc.remove();){try{a.ia.call(a.scope)}catch(b){hc(b)}jc.put(a)}mc=!1};var N=function(a){this.u=0;this.va=void 0;this.M=this.D=this.K=null;this.W=this.ha=!1;if(a!=Ua)try{var b=this;a.call(void 0,function(c){M(b,2,c)},function(c){M(b,3,c)})}catch(c){M(this,3,c)}},rc=function(){this.next=this.context=this.P=this.U=this.H=null;this.V=!1};rc.prototype.reset=function(){this.context=this.P=this.U=this.H=null;this.V=!1};var sc=new ec(function(){return new rc},function(a){a.reset()}),tc=function(a,b,c){var d=sc.get();d.U=a;d.P=b;d.context=c;return d};
N.prototype.then=function(a,b,c){return uc(this,"function"===typeof a?a:null,"function"===typeof b?b:null,c)};N.prototype.$goog_Thenable=!0;N.prototype.Ra=function(a,b){return uc(this,null,a,b)};N.prototype.catch=N.prototype.Ra;N.prototype.cancel=function(a){if(0==this.u){var b=new O(a);pc(function(){vc(this,b)},this)}};
var vc=function(a,b){if(0==a.u)if(a.K){var c=a.K;if(c.D){for(var d=0,e=null,f=null,g=c.D;g&&(g.V||(d++,g.H==a&&(e=g),!(e&&1<d)));g=g.next)e||(f=g);e&&(0==c.u&&1==d?vc(c,b):(f?(d=f,d.next==c.M&&(c.M=d),d.next=d.next.next):wc(c),xc(c,e,3,b)))}a.K=null}else M(a,3,b)},zc=function(a,b){a.D||2!=a.u&&3!=a.u||yc(a);a.M?a.M.next=b:a.D=b;a.M=b},uc=function(a,b,c,d){var e=tc(null,null,null);e.H=new N(function(f,g){e.U=b?function(h){try{var k=b.call(d,h);f(k)}catch(l){g(l)}}:f;e.P=c?function(h){try{var k=c.call(d,
h);void 0===k&&h instanceof O?g(h):f(k)}catch(l){g(l)}}:g});e.H.K=a;zc(a,e);return e.H};N.prototype.Sa=function(a){this.u=0;M(this,2,a)};N.prototype.Ta=function(a){this.u=0;M(this,3,a)};
var M=function(a,b,c){if(0==a.u){a===c&&(b=3,c=new TypeError("Promise cannot resolve to itself"));a.u=1;a:{var d=c,e=a.Sa,f=a.Ta;if(d instanceof N){zc(d,tc(e||Ua,f||null,a));var g=!0}else{if(d)try{var h=!!d.$goog_Thenable}catch(l){h=!1}else h=!1;if(h)d.then(e,f,a),g=!0;else{if(oa(d))try{var k=d.then;if("function"===typeof k){Ac(d,k,e,f,a);g=!0;break a}}catch(l){f.call(a,l);g=!0;break a}g=!1}}}g||(a.va=c,a.u=b,a.K=null,yc(a),3!=b||c instanceof O||Bc(a,c))}},Ac=function(a,b,c,d,e){var f=!1,g=function(k){f||
(f=!0,c.call(e,k))},h=function(k){f||(f=!0,d.call(e,k))};try{b.call(a,g,h)}catch(k){h(k)}},yc=function(a){a.ha||(a.ha=!0,pc(a.Ha,a))},wc=function(a){var b=null;a.D&&(b=a.D,a.D=b.next,b.next=null);a.D||(a.M=null);return b};N.prototype.Ha=function(){for(var a;a=wc(this);)xc(this,a,this.u,this.va);this.ha=!1};
var xc=function(a,b,c,d){if(3==c&&b.P&&!b.V)for(;a&&a.W;a=a.K)a.W=!1;if(b.H)b.H.K=null,Cc(b,c,d);else try{b.V?b.U.call(b.context):Cc(b,c,d)}catch(e){Dc.call(null,e)}sc.put(b)},Cc=function(a,b,c){2==b?a.U.call(a.context,c):a.P&&a.P.call(a.context,c)},Bc=function(a,b){a.W=!0;pc(function(){a.W&&Dc.call(null,b)})},Dc=hc,O=function(a){w.call(this,a)};sa(O,w);O.prototype.name="cancel";var Ec=function(a,b,c){this.promise=a;this.resolve=b;this.reject=c};var Fc=function(a){if(a.J&&"function"==typeof a.J)return a.J();if("undefined"!==typeof Map&&a instanceof Map||"undefined"!==typeof Set&&a instanceof Set)return Array.from(a.values());if("string"===typeof a)return a.split("");if(v(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b},Gc=function(a){if(a.ja&&"function"==typeof a.ja)return a.ja();if(!a.J||"function"!=typeof a.J){if("undefined"!==typeof Map&&a instanceof Map)return Array.from(a.keys());
if(!("undefined"!==typeof Set&&a instanceof Set)){if(v(a)||"string"===typeof a){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}b=[];c=0;for(var d in a)b[c++]=d;return b}}},Hc=function(a,b,c){if(a.forEach&&"function"==typeof a.forEach)a.forEach(b,c);else if(v(a)||"string"===typeof a)Array.prototype.forEach.call(a,b,c);else for(var d=Gc(a),e=Fc(a),f=e.length,g=0;g<f;g++)b.call(c,e[g],d&&d[g],a)};var P=function(a){this.s=this.L=this.G="";this.R=null;this.I=this.m="";this.l=this.Ja=!1;if(a instanceof P){this.l=a.l;Ic(this,a.G);var b=a.L;Q(this);this.L=b;b=a.s;Q(this);this.s=b;Jc(this,a.R);b=a.m;Q(this);this.m=b;Kc(this,a.o.clone());a=a.I;Q(this);this.I=a}else a&&(b=String(a).match(xb))?(this.l=!1,Ic(this,b[1]||"",!0),a=b[2]||"",Q(this),this.L=R(a),a=b[3]||"",Q(this),this.s=R(a,!0),Jc(this,b[4]),a=b[5]||"",Q(this),this.m=R(a,!0),Kc(this,b[6]||"",!0),a=b[7]||"",Q(this),this.I=R(a)):(this.l=!1,
this.o=new S(null,this.l))};P.prototype.toString=function(){var a=[],b=this.G;b&&a.push(T(b,Lc,!0),":");var c=this.s;if(c||"file"==b)a.push("//"),(b=this.L)&&a.push(T(b,Lc,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.R,null!=c&&a.push(":",String(c));if(c=this.m)this.s&&"/"!=c.charAt(0)&&a.push("/"),a.push(T(c,"/"==c.charAt(0)?Mc:Nc,!0));(c=this.o.toString())&&a.push("?",c);(c=this.I)&&a.push("#",T(c,Oc));return a.join("")};
P.prototype.resolve=function(a){var b=this.clone(),c=!!a.G;c?Ic(b,a.G):c=!!a.L;if(c){var d=a.L;Q(b);b.L=d}else c=!!a.s;c?(d=a.s,Q(b),b.s=d):c=null!=a.R;d=a.m;if(c)Jc(b,a.R);else if(c=!!a.m){if("/"!=d.charAt(0))if(this.s&&!this.m)d="/"+d;else{var e=b.m.lastIndexOf("/");-1!=e&&(d=b.m.slice(0,e+1)+d)}e=d;if(".."==e||"."==e)d="";else if(-1!=e.indexOf("./")||-1!=e.indexOf("/.")){d=0==e.lastIndexOf("/",0);e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];"."==h?d&&g==e.length&&f.push(""):".."==
h?((1<f.length||1==f.length&&""!=f[0])&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?(Q(b),b.m=d):c=""!==a.o.toString();c?Kc(b,a.o.clone()):c=!!a.I;c&&(a=a.I,Q(b),b.I=a);return b};P.prototype.clone=function(){return new P(this)};
var Ic=function(a,b,c){Q(a);a.G=c?R(b,!0):b;a.G&&(a.G=a.G.replace(/:$/,""))},Jc=function(a,b){Q(a);if(b){b=Number(b);if(isNaN(b)||0>b)throw Error("Bad port number "+b);a.R=b}else a.R=null},Kc=function(a,b,c){Q(a);b instanceof S?(a.o=b,a.o.ma(a.l)):(c||(b=T(b,Pc)),a.o=new S(b,a.l))};P.prototype.getQuery=function(){return this.o.toString()};var U=function(a,b,c){Q(a);a.o.set(b,c);return a};P.prototype.removeParameter=function(a){Q(this);this.o.remove(a);return this};
var Q=function(a){if(a.Ja)throw Error("Tried to modify a read-only Uri");};P.prototype.ma=function(a){this.l=a;this.o&&this.o.ma(a)};
var R=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""},T=function(a,b,c){return"string"===typeof a?(a=encodeURI(a).replace(b,Qc),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null},Qc=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)},Lc=/[#\/\?@]/g,Nc=/[#\?:]/g,Mc=/[#\?]/g,Pc=/[#\?@]/g,Oc=/#/g,S=function(a,b){this.i=this.g=null;this.j=a||null;this.l=!!b},V=function(a){a.g||(a.g=new Map,a.i=0,a.j&&yb(a.j,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g,
" ")),c)}))};S.prototype.add=function(a,b){V(this);this.j=null;a=X(this,a);var c=this.g.get(a);c||this.g.set(a,c=[]);c.push(b);this.i+=1;return this};S.prototype.remove=function(a){V(this);a=X(this,a);return this.g.has(a)?(this.j=null,this.i-=this.g.get(a).length,this.g.delete(a)):!1};S.prototype.clear=function(){this.g=this.j=null;this.i=0};var Rc=function(a,b){V(a);b=X(a,b);return a.g.has(b)};n=S.prototype;
n.forEach=function(a,b){V(this);this.g.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};n.ja=function(){V(this);for(var a=Array.from(this.g.values()),b=Array.from(this.g.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};n.J=function(a){V(this);var b=[];if("string"===typeof a)Rc(this,a)&&(b=b.concat(this.g.get(X(this,a))));else{a=Array.from(this.g.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b};
n.set=function(a,b){V(this);this.j=null;a=X(this,a);Rc(this,a)&&(this.i-=this.g.get(a).length);this.g.set(a,[b]);this.i+=1;return this};n.get=function(a,b){if(!a)return b;a=this.J(a);return 0<a.length?String(a[0]):b};
n.toString=function(){if(this.j)return this.j;if(!this.g)return"";for(var a=[],b=Array.from(this.g.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.J(d);for(var f=0;f<d.length;f++){var g=e;""!==d[f]&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.j=a.join("&")};n.clone=function(){var a=new S;a.j=this.j;this.g&&(a.g=new Map(this.g),a.i=this.i);return a};var X=function(a,b){b=String(b);a.l&&(b=b.toLowerCase());return b};
S.prototype.ma=function(a){a&&!this.l&&(V(this),this.j=null,this.g.forEach(function(b,c){var d=c.toLowerCase();c!=d&&(this.remove(c),this.remove(d),0<b.length&&(this.j=null,this.g.set(X(this,d),xa(b)),this.i+=b.length))},this));this.l=a};S.prototype.extend=function(a){for(var b=0;b<arguments.length;b++)Hc(arguments[b],function(c,d){this.add(d,c)},this)};function Sc(a){return U(U(U(new P("//pagead2.googlesyndication.com/pagead/gen_204"),"id","sodar"),"v",44),"t",a)}function Y(a,b,c,d,e){var f=Sc(1);U(f,"e",a);c&&U(f,"li",c);d&&U(f,"cv",d);e&&U(f,"sid",e);b&&U(f,"bgai",b);L(window,f.toString())}
function Tc(a,b,c,d,e,f){c=void 0===c?null:c;d=void 0===d?null:d;e=void 0===e?null:e;f=void 0===f?null:f;"0.01"<Math.random()||(a=U(U(U(U(U(new P("//pagead2.googlesyndication.com/pagead/gen_204"),"id","sodarir"),"v",44),"d",a?1:0),"s",b?1:0),"f","0.01"),d&&U(a,"li",d),e&&U(a,"cv",e),f&&U(a,"sid",f),c&&U(a,"bgai",c),L(window,a.toString()))}
var Wc=function(){void 0===Uc&&(Uc=Vc())},Uc,Xc=function(){new Wc;return Uc||null},Vc=function(){var a=window.GoogleTyFxhY;if(!a)return Y(13),null;if(0==a.length)return Y(1),null;a=a.shift();return("true"===a._isfl_||a._scs_||a._cv_||a._sid_)&&(a._upb_||a._bgu_&&a._bgp_)?a:(Y(2),null)};
function Yc(a,b){return function(){try{return a.apply(this,arguments)}catch(g){if(!(1<=Zc.count)){var c=g,d=Sc(3),e=Xc()._scs_,f=Xc()._sid_;e&&U(d,"bgai",e);f&&U(d,"sid",f);U(d,"c",b);U(d,"ex",Zb(c));c=d.toString();6E4<c.length?Y(11,e):L(window,c);Zc.count+=1}}}}function bd(a,b){var c=Yc(a,b),d=c.apply,e=ma.apply(2,arguments);if(!(e instanceof Array)){e=q(e);for(var f,g=[];!(f=e.next()).done;)g.push(f.value);e=g}d.call(c,null,e)}var Zc={count:0};
function cd(a,b,c,d){c=Yc(c,d);ub(a,b,c,{capture:void 0});return c}function dd(a,b){var c=b;var d=cd(a,"load",function(){if(c){var e=c;c=null;vb(a,"load",d,{capture:void 0});return e.apply(this,arguments)}},"i:lh")}function ed(){return wa(function(a){return"DIV"==a.tagName&&0==a.id.lastIndexOf("DfaVisibilityIdentifier_",0)})}function fd(a){switch(a){case void 0:return 0;case !0:return 1;case !1:return 2;default:return-1}};var gd=function(a,b){this.type=a;this.currentTarget=this.target=b;this.defaultPrevented=!1};gd.prototype.stopPropagation=function(){};gd.prototype.preventDefault=function(){this.defaultPrevented=!0};var hd=function(){if(!u.addEventListener||!Object.defineProperty)return!1;var a=!1,b=Object.defineProperty({},"passive",{get:function(){a=!0}});try{var c=function(){};u.addEventListener("test",c,b);u.removeEventListener("test",c,b)}catch(d){}return a}();var Z=function(a,b){gd.call(this,a?a.type:"");this.relatedTarget=this.currentTarget=this.target=null;this.button=this.screenY=this.screenX=this.clientY=this.clientX=this.offsetY=this.offsetX=0;this.key="";this.charCode=this.keyCode=0;this.metaKey=this.shiftKey=this.altKey=this.ctrlKey=!1;this.state=null;this.pointerId=0;this.pointerType="";this.F=null;if(a){var c=this.type=a.type,d=a.changedTouches&&a.changedTouches.length?a.changedTouches[0]:null;this.target=a.target||a.srcElement;this.currentTarget=
b;(b=a.relatedTarget)?Sa&&(Qa(b,"nodeName")||(b=null)):"mouseover"==c?b=a.fromElement:"mouseout"==c&&(b=a.toElement);this.relatedTarget=b;d?(this.clientX=void 0!==d.clientX?d.clientX:d.pageX,this.clientY=void 0!==d.clientY?d.clientY:d.pageY,this.screenX=d.screenX||0,this.screenY=d.screenY||0):(this.offsetX=Ta||void 0!==a.offsetX?a.offsetX:a.layerX,this.offsetY=Ta||void 0!==a.offsetY?a.offsetY:a.layerY,this.clientX=void 0!==a.clientX?a.clientX:a.pageX,this.clientY=void 0!==a.clientY?a.clientY:a.pageY,
this.screenX=a.screenX||0,this.screenY=a.screenY||0);this.button=a.button;this.keyCode=a.keyCode||0;this.key=a.key||"";this.charCode=a.charCode||("keypress"==c?a.keyCode:0);this.ctrlKey=a.ctrlKey;this.altKey=a.altKey;this.shiftKey=a.shiftKey;this.metaKey=a.metaKey;this.pointerId=a.pointerId||0;this.pointerType="string"===typeof a.pointerType?a.pointerType:id[a.pointerType]||"";this.state=a.state;this.F=a;a.defaultPrevented&&Z.aa.preventDefault.call(this)}};sa(Z,gd);var id={2:"touch",3:"pen",4:"mouse"};
Z.prototype.stopPropagation=function(){Z.aa.stopPropagation.call(this);this.F.stopPropagation?this.F.stopPropagation():this.F.cancelBubble=!0};Z.prototype.preventDefault=function(){Z.aa.preventDefault.call(this);var a=this.F;a.preventDefault?a.preventDefault():a.returnValue=!1};var jd="closure_listenable_"+(1E6*Math.random()|0);var kd=0;var ld=function(a,b,c,d,e){this.listener=a;this.proxy=null;this.src=b;this.type=c;this.capture=!!d;this.ka=e;this.key=++kd;this.Z=this.ga=!1},md=function(a){a.Z=!0;a.listener=null;a.proxy=null;a.src=null;a.ka=null};var nd=function(a){this.src=a;this.v={};this.da=0};nd.prototype.add=function(a,b,c,d,e){var f=a.toString();a=this.v[f];a||(a=this.v[f]=[],this.da++);var g=od(a,b,d,e);-1<g?(b=a[g],c||(b.ga=!1)):(b=new ld(b,this.src,f,!!d,e),b.ga=c,a.push(b));return b};nd.prototype.remove=function(a,b,c,d){a=a.toString();if(!(a in this.v))return!1;var e=this.v[a];b=od(e,b,c,d);return-1<b?(md(e[b]),Array.prototype.splice.call(e,b,1),0==e.length&&(delete this.v[a],this.da--),!0):!1};
nd.prototype.hasListener=function(a,b){var c=void 0!==a,d=c?a.toString():"",e=void 0!==b;return Wa(this.v,function(f){for(var g=0;g<f.length;++g)if(!(c&&f[g].type!=d||e&&f[g].capture!=b))return!0;return!1})};var od=function(a,b,c,d){for(var e=0;e<a.length;++e){var f=a[e];if(!f.Z&&f.listener==b&&f.capture==!!c&&f.ka==d)return e}return-1};var pd="closure_lm_"+(1E6*Math.random()|0),qd={},rd=0,td=function(a,b,c,d,e){if(d&&d.once)sd(a,b,c,d,e);else if(Array.isArray(b))for(var f=0;f<b.length;f++)td(a,b[f],c,d,e);else c=ud(c),a&&a[jd]?a.Va(b,c,oa(d)?!!d.capture:!!d,e):vd(a,b,c,!1,d,e)},vd=function(a,b,c,d,e,f){if(!b)throw Error("Invalid event type");var g=oa(e)?!!e.capture:!!e,h=wd(a);h||(a[pd]=h=new nd(a));c=h.add(b,c,d,g,f);if(!c.proxy){d=xd();c.proxy=d;d.src=a;d.listener=c;if(a.addEventListener)hd||(e=g),void 0===e&&(e=!1),a.addEventListener(b.toString(),
d,e);else if(a.attachEvent)a.attachEvent(yd(b.toString()),d);else if(a.addListener&&a.removeListener)a.addListener(d);else throw Error("addEventListener and attachEvent are unavailable.");rd++}},xd=function(){var a=zd,b=function(c){return a.call(b.src,b.listener,c)};return b},sd=function(a,b,c,d,e){if(Array.isArray(b))for(var f=0;f<b.length;f++)sd(a,b[f],c,d,e);else c=ud(c),a&&a[jd]?a.Wa(b,c,oa(d)?!!d.capture:!!d,e):vd(a,b,c,!0,d,e)},yd=function(a){return a in qd?qd[a]:qd[a]="on"+a},zd=function(a,
b){if(a.Z)a=!0;else{b=new Z(b,this);var c=a.listener,d=a.ka||a.src;if(a.ga&&"number"!==typeof a&&a&&!a.Z){var e=a.src;if(e&&e[jd])e.Xa(a);else{var f=a.type,g=a.proxy;e.removeEventListener?e.removeEventListener(f,g,a.capture):e.detachEvent?e.detachEvent(yd(f),g):e.addListener&&e.removeListener&&e.removeListener(g);rd--;(f=wd(e))?(g=a.type,g in f.v&&za(f.v[g],a)&&(md(a),0==f.v[g].length&&(delete f.v[g],f.da--)),0==f.da&&(f.src=null,e[pd]=null)):md(a)}}a=c.call(d,b)}return a},wd=function(a){a=a[pd];
return a instanceof nd?a:null},Ad="__closure_events_fn_"+(1E9*Math.random()>>>0),ud=function(a){if("function"===typeof a)return a;a[Ad]||(a[Ad]=function(b){return a.handleEvent(b)});return a[Ad]};var Bd=function(a){var b=a._scs_,c=a._li_,d=ed();if(d&&d.getBoundingClientRect){var e=0;td(d,"mouseover",function(){++e});td(d,"mousedown",function(f){var g=d.getBoundingClientRect(),h=0;0==f.F.button?h=1:2==f.F.button?h=4:1==f.F.button&&(h=2);h&&f.shiftKey&&(h|=8);h&&f.altKey&&(h|=16);h&&f.ctrlKey&&(h|=32);var k=Math.floor(100*pb(d)),l=Math.floor(f.clientX-g.left);g=Math.floor(f.clientY-g.top);var m=e;f=f.isTrusted;var r=document.defaultView&&document.defaultView.mozPaintCount;r=void 0===r?-1:"number"===
typeof r&&Number.isInteger(r)?0>r?-3:r:-2;k=U(U(U(U(U(U(U(U(U(U(new P("//pagead2.googlesyndication.com/pagead/gen_204"),"id","sodarde"),"v",44),"nx",l),"ny",g),"bgai",b),"mb",h),"ox",k),"nm",m),"tr",fd(f)),"mz",r);c&&U(k,"li",c);L(window,k.toString())})}};!y("Android")||La();La();Ka();var Cd=function(){this.s="pagead2.googlesyndication.com";this.m="/pagead/gen_204?id=";this.Ea=.01;this.Ca=!1;this.Oa="https:";this.ya=Math.random()},Dd=function(){var a=Wb,b=window.google_srt;0<=b&&1>=b&&(a.ya=b)},bc=function(a,b,c,d,e){if(((void 0===d?0:d)?a.ya:Math.random())<(e||a.Ea))try{if(c instanceof Lb)var f=c;else f=new Lb,zb(c,function(h,k){var l=f,m=l.Na++;h=Mb(k,h);l.A.push(m);l.C[m]=h});var g=Ub(f,a.Oa,a.s,a.m+b+"&");g&&(a.Ca?dc(g):L(u,g))}catch(h){}};var Wb,Yb,Vb=new Jb;(function(a){Wb=null!=a?a:new Cd;"number"!==typeof window.google_srt&&(window.google_srt=Math.random());Dd();Yb=new Xb;Yb.fa=function(){};Yb.na=!0;"complete"==window.document.readyState?window.google_measure_js_timing||Vb.disable():Vb.N&&ub(window,"load",function(){window.google_measure_js_timing||Vb.disable()})})();/*

 SPDX-License-Identifier: Apache-2.0
*/
var Ed=function(){var a,b,c=new N(function(d,e){a=d;b=e});return new Ec(c,a,b)}();function Fd(a){Ed.resolve([(0==a.src.indexOf("https:")?"https":"http")+"://tpc.googlesyndication.com",gb(a)])}
function Gd(a){var b=bb(),c=document.createElement("IFRAME".toString());dd(c,ra(Fd,null,c));c.src=(b instanceof $a&&b.constructor===$a?b.ua:"type_error:TrustedResourceUrl").toString();c.width="0";c.height="0";c.style.display="none";a&&cd(window,"message",function(d){if(d.source===gb(c)){var e=d.data;if(d.data&&e.startsWith("__FledgeSodar__")){d=e.replace("__FledgeSodar__","");var f;e=u;e=void 0===e?window:e;null==e||null==(f=e.fence)||f.reportEvent({eventType:"sodar",eventData:d,destination:["buyer"]})}}},
"i:fl");document.body.appendChild(c)}function Hd(){Ed.promise.then(function(a){var b=q(a);a=b.next().value;b=b.next().value;var c=Xc();var d=c._scs_;var e=c._bgu_,f=c._bgp_,g=c._li_,h=c._cv_,k=c._sid_;c=c._isfl_;var l=document.location.origin,m={};d=(m["0"]="0",m["1"]=d||"",m["2"]=e.split("/").pop().replace(/\.js/g,""),m["3"]=f,m["4"]=g||"",m["5"]=h||"",m["8"]=l&&"null"!=l?l:"*",m["9"]=k||"",m["13"]=c||"",m);b?b.postMessage(d,a):Y(3)})}
var Id=function(){bd(function(){var a=!!window.postMessage,b=!1,c=null,d=null,e=null,f=null,g=!1,h=Xc();h&&(b=!0,c=h._scs_,d=h._li_,e=h._cv_,f=h._sid_,g="true"==h._isfl_,a?(Gd(g),Hd()):Y(8,c,d,e,f),setTimeout(ac(function(){return Bd(h)}),0));Tc(b,a,c,d,e,f)},"i:i")};rb.GoogleTyFxhYEET||(rb.GoogleTyFxhYEET={});rb.GoogleTyFxhYEET[(qb.currentScript||{}).src]=Id;Id();}).call(this);
