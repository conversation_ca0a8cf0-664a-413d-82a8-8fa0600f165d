/* Mobile Responsive CSS for Calculator.net Style Calculators */

/* Tablet and Mobile Styles */
@media screen and (max-width: 768px) {
    /* Main layout adjustments */
    #contentout { 
        width: 100% !important; 
        padding: 0 10px; 
        box-sizing: border-box;
    }
    
    #content { 
        width: 100% !important; 
        margin: 0; 
        padding: 0;
    }
    
    /* Hide sidebar on mobile */
    #right { 
        display: none; 
    }
    
    /* Calculator panel adjustments */
    .panel2 { 
        width: 100% !important; 
        padding: 5px !important; 
        margin: 0;
        box-sizing: border-box;
    }
    
    /* Table responsiveness */
    table { 
        width: 100% !important; 
        font-size: 14px; 
        border-collapse: collapse;
    }
    
    td { 
        padding: 8px 4px !important; 
        word-wrap: break-word; 
        vertical-align: top;
    }
    
    /* Form elements */
    input, select { 
        width: 100% !important; 
        max-width: 100% !important; 
        box-sizing: border-box;
        font-size: 16px; /* Prevents zoom on iOS */
    }
    
    .inlongest, .innormal, .inlong, .in2char { 
        width: 100% !important; 
        margin: 2px 0;
    }
    
    /* Multi-select dropdowns */
    select[multiple] { 
        height: 80px !important; 
    }
    
    /* Typography adjustments */
    h1 { 
        font-size: 24px; 
        margin: 10px 0;
    }
    
    h2, h3 { 
        font-size: 18px; 
        margin: 8px 0;
    }
    
    /* Data tables */
    .cinfoT { 
        font-size: 12px; 
        overflow-x: auto;
        display: block;
        white-space: nowrap;
    }
    
    .cinfoT td { 
        padding: 4px 2px !important; 
    }
    
    /* Top menu adjustments */
    #topmenu ul li { 
        display: inline-block; 
        width: auto; 
        margin: 2px; 
        font-size: 12px;
    }
    
    .topmenucenter { 
        text-align: center; 
    }
    
    /* Checkbox and radio button containers */
    .cbcontainer { 
        font-size: 12px; 
        margin: 2px 0; 
        display: inline-block;
    }
    
    /* Results display */
    .h2result { 
        font-size: 14px; 
        line-height: 1.4;
    }
    
    /* Breadcrumbs */
    #breadcrumbs { 
        font-size: 12px; 
        word-wrap: break-word;
    }
    
    /* Image responsiveness */
    #insmdc img { 
        max-width: 100%; 
        height: auto;
    }
}

/* Small Mobile Styles */
@media screen and (max-width: 480px) {
    /* Tighter spacing for small screens */
    #contentout { 
        padding: 0 5px; 
    }
    
    .panel2 { 
        padding: 3px !important; 
    }
    
    /* Table column adjustments */
    table td:first-child { 
        width: 30% !important; 
        font-size: 12px;
    }
    
    table td:last-child { 
        width: 70% !important; 
    }
    
    /* Smaller multi-select */
    select[multiple] { 
        height: 60px !important; 
        font-size: 12px; 
    }
    
    /* Button styling */
    input[type="submit"], input[type="button"] { 
        width: 100% !important; 
        margin: 5px 0 !important; 
        padding: 12px !important;
        font-size: 14px;
        border-radius: 4px;
        cursor: pointer;
    }
    
    /* Stack checkboxes/radio buttons vertically */
    .cbcontainer { 
        display: block; 
        margin: 3px 0; 
        font-size: 11px; 
    }
    
    /* Smaller top menu */
    .topmenucenter ul li { 
        font-size: 11px; 
        padding: 5px 8px;
    }
    
    /* Compact data tables */
    .cinfoT { 
        font-size: 10px; 
    }
    
    .cinfoT td { 
        padding: 2px 1px !important; 
    }
    
    /* Smaller typography */
    h1 { 
        font-size: 20px; 
    }
    
    h2, h3 { 
        font-size: 16px; 
    }
    
    /* Input field adjustments */
    .in2char { 
        width: 60px !important; 
        display: inline-block; 
        margin: 0 2px;
    }
    
    /* Unit spans */
    .inuifootspan, .inuiinchspan, .inuipoundspan, .inuickspan { 
        font-size: 12px; 
        margin-left: 5px;
    }
}

/* Extra Small Mobile Styles */
@media screen and (max-width: 320px) {
    /* Ultra-compact for very small screens */
    table { 
        font-size: 12px; 
    }
    
    td { 
        padding: 4px 2px !important; 
    }
    
    input, select { 
        font-size: 14px; 
        padding: 8px;
    }
    
    .cinfoT { 
        font-size: 9px; 
    }
    
    h1 { 
        font-size: 18px; 
    }
    
    .topmenucenter ul li { 
        font-size: 10px; 
        padding: 4px 6px;
    }
}

/* Landscape orientation adjustments */
@media screen and (max-width: 768px) and (orientation: landscape) {
    /* Adjust for landscape mode on tablets/phones */
    .panel2 { 
        max-height: 70vh; 
        overflow-y: auto; 
    }
    
    .cinfoT { 
        max-height: 50vh; 
        overflow-y: auto; 
    }
}

/* Print styles for mobile */
@media print {
    #right, #header, #footer, .topNavAbs { 
        display: none !important; 
    }
    
    #content { 
        width: 100% !important; 
        margin: 0 !important; 
    }
    
    table { 
        font-size: 12px !important; 
    }
}

/* Touch-friendly improvements */
@media (hover: none) and (pointer: coarse) {
    /* Touch device specific styles */
    input[type="submit"], input[type="button"], button { 
        min-height: 44px; /* Apple's recommended touch target size */
        min-width: 44px;
    }
    
    select, input[type="text"], input[type="number"] { 
        min-height: 44px; 
        padding: 10px;
    }
    
    .cbcontainer input[type="checkbox"], 
    .cbcontainer input[type="radio"] { 
        transform: scale(1.5); 
        margin-right: 10px;
    }
}

/* High DPI display adjustments */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    /* Sharper text rendering on high DPI displays */
    body { 
        -webkit-font-smoothing: antialiased; 
        -moz-osx-font-smoothing: grayscale; 
    }
}
