<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.measurement-interface {
    background: linear-gradient(135deg, #e91e63 0%, #ad1457 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.measurement-input {
    background-color: #f8f9fa;
    border: 2px solid #e91e63;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.size-result {
    background: linear-gradient(135deg, #9c27b0 0%, #6a1b9a 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.measurement-guide {
    background: white;
    border: 2px solid #e91e63;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.size-chart {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
}
.cup-size {
    display: inline-block;
    padding: 8px 16px;
    margin: 5px;
    border-radius: 20px;
    font-weight: bold;
    color: white;
}
.cup-aa { background-color: #ffeb3b; color: #333; }
.cup-a { background-color: #4caf50; }
.cup-b { background-color: #2196f3; }
.cup-c { background-color: #ff9800; }
.cup-d { background-color: #f44336; }
.cup-dd { background-color: #9c27b0; }
.cup-e { background-color: #795548; }
.cup-f { background-color: #607d8b; }
.cup-g { background-color: #3f51b5; }
.cup-h { background-color: #e91e63; }
.measurement-tip {
    background-color: #e3f2fd;
    border-left: 4px solid #2196f3;
    padding: 15px;
    margin: 15px 0;
    border-radius: 5px;
}
.sister-size {
    background-color: #f3e5f5;
    border: 1px solid #9c27b0;
    border-radius: 8px;
    padding: 10px;
    margin: 5px;
    display: inline-block;
    text-align: center;
    min-width: 80px;
}
</style>

<h1>ABTF Calculator - A Bra That Fits</h1>
<p>Calculate your correct bra size using the ABTF (A Bra That Fits) method. This calculator uses the 6-measurement method recommended by the r/ABraThatFits community for the most accurate bra sizing, helping you find properly fitting bras that provide comfort and support.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="abtfform" id="abtf-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">ABTF Calculator</a></li>
                <li><a href="#" data-tab="guide">Measurement Guide</a></li>
                <li><a href="#" data-tab="fitting">Fitting Tips</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="measurement-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">📏 ABTF 6-Measurement Method</h3>
                
                <div class="measurement-tip">
                    <strong>📋 Before You Start:</strong>
                    <ul style="margin: 10px 0;">
                        <li>Use a soft measuring tape</li>
                        <li>Measure in inches or centimeters</li>
                        <li>Take measurements without a bra or in a non-padded bra</li>
                        <li>Stand up straight and breathe normally</li>
                        <li>Have someone help you for more accurate measurements</li>
                    </ul>
                </div>
            </div>
            
            <div class="measurement-input">
                <h4 style="color: #2c3e50; margin-bottom: 20px;">📐 Enter Your Measurements</h4>
                
                <div style="margin-bottom: 15px;">
                    <label><strong>Measurement Unit:</strong></label>
                    <select id="unit" class="innormal" style="margin-left: 10px;">
                        <option value="inches">Inches</option>
                        <option value="cm">Centimeters</option>
                    </select>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h5 style="color: #e91e63; margin-bottom: 15px;">Band Measurements</h5>
                        
                        <div style="margin-bottom: 15px;">
                            <label for="underbust-loose"><strong>1. Loose Underbust:</strong></label>
                            <input type="number" id="underbust-loose" step="0.25" class="innormal" style="width: 100%; margin-top: 5px;" placeholder="Measure loosely around ribcage">
                        </div>
                        
                        <div style="margin-bottom: 15px;">
                            <label for="underbust-snug"><strong>2. Snug Underbust:</strong></label>
                            <input type="number" id="underbust-snug" step="0.25" class="innormal" style="width: 100%; margin-top: 5px;" placeholder="Measure snugly around ribcage">
                        </div>
                        
                        <div style="margin-bottom: 15px;">
                            <label for="underbust-tight"><strong>3. Tight Underbust:</strong></label>
                            <input type="number" id="underbust-tight" step="0.25" class="innormal" style="width: 100%; margin-top: 5px;" placeholder="Measure as tight as possible">
                        </div>
                    </div>
                    
                    <div>
                        <h5 style="color: #e91e63; margin-bottom: 15px;">Bust Measurements</h5>
                        
                        <div style="margin-bottom: 15px;">
                            <label for="bust-standing"><strong>4. Standing Bust:</strong></label>
                            <input type="number" id="bust-standing" step="0.25" class="innormal" style="width: 100%; margin-top: 5px;" placeholder="Measure around fullest part while standing">
                        </div>
                        
                        <div style="margin-bottom: 15px;">
                            <label for="bust-leaning"><strong>5. Leaning Bust:</strong></label>
                            <input type="number" id="bust-leaning" step="0.25" class="innormal" style="width: 100%; margin-top: 5px;" placeholder="Measure while leaning forward 90°">
                        </div>
                        
                        <div style="margin-bottom: 15px;">
                            <label for="bust-lying"><strong>6. Lying Bust:</strong></label>
                            <input type="number" id="bust-lying" step="0.25" class="innormal" style="width: 100%; margin-top: 5px;" placeholder="Measure while lying flat on back">
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate My Bra Size" id="calculate-abtf-btn" style="padding: 15px 30px; font-size: 18px; background-color: #e91e63; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Clear Measurements" id="clear-abtf-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> This calculator provides a starting point for bra fitting. Individual fit may vary between brands and styles. Always try on bras before purchasing when possible.
        </div>
        
        <div id="abtf-results" style="display: none; margin-top: 30px;">
            <div class="size-result">
                <h3 style="margin-bottom: 20px;">👙 Your ABTF Size Results</h3>
                <div id="abtf-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-abtf-btn');
    const clearBtn = document.getElementById('clear-abtf-btn');
    const resultsDiv = document.getElementById('abtf-results');
    const resultsContent = document.getElementById('abtf-results-content');
    
    calculateBtn.addEventListener('click', function() {
        const measurements = {
            underbustLoose: parseFloat(document.getElementById('underbust-loose').value),
            underbustSnug: parseFloat(document.getElementById('underbust-snug').value),
            underbustTight: parseFloat(document.getElementById('underbust-tight').value),
            bustStanding: parseFloat(document.getElementById('bust-standing').value),
            bustLeaning: parseFloat(document.getElementById('bust-leaning').value),
            bustLying: parseFloat(document.getElementById('bust-lying').value)
        };
        
        const unit = document.getElementById('unit').value;
        
        // Validate measurements
        const requiredMeasurements = Object.values(measurements);
        if (requiredMeasurements.some(val => isNaN(val) || val <= 0)) {
            alert('Please enter all measurements with valid positive numbers.');
            return;
        }
        
        // Validate measurement logic
        if (measurements.underbustLoose < measurements.underbustSnug || 
            measurements.underbustSnug < measurements.underbustTight) {
            alert('Please check your underbust measurements. Loose should be largest, tight should be smallest.');
            return;
        }
        
        const braSize = calculateABTFSize(measurements, unit);
        displayResults(braSize, measurements, unit);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('abtf-calculator-form').reset();
        resultsDiv.style.display = 'none';
    });
    
    function calculateABTFSize(measurements, unit) {
        // Calculate band size
        let bandSize;
        const underbustAvg = (measurements.underbustLoose + measurements.underbustSnug) / 2;
        
        if (unit === 'cm') {
            // Convert to inches for calculation
            const underbustInches = underbustAvg / 2.54;
            bandSize = Math.round(underbustInches / 2) * 2;
        } else {
            bandSize = Math.round(underbustAvg / 2) * 2;
        }
        
        // Adjust band size based on measurements
        const difference = measurements.underbustLoose - measurements.underbustTight;
        if (unit === 'cm') {
            if (difference > 7.5) bandSize += 2; // More than 3 inches difference
        } else {
            if (difference > 3) bandSize += 2;
        }
        
        // Calculate cup size
        let bustAvg;
        const bustDiff = Math.abs(measurements.bustLeaning - measurements.bustStanding);
        
        if (unit === 'cm') {
            const bustDiffInches = bustDiff / 2.54;
            if (bustDiffInches > 2.5) {
                // Use average of leaning and standing for larger busts
                bustAvg = (measurements.bustLeaning + measurements.bustStanding) / 2;
            } else {
                // Use standing measurement for smaller busts
                bustAvg = measurements.bustStanding;
            }
            bustAvg = bustAvg / 2.54; // Convert to inches
        } else {
            if (bustDiff > 2.5) {
                bustAvg = (measurements.bustLeaning + measurements.bustStanding) / 2;
            } else {
                bustAvg = measurements.bustStanding;
            }
        }
        
        const cupDifference = bustAvg - bandSize;
        const cupSize = calculateCupSize(cupDifference);
        
        // Calculate sister sizes
        const sisterSizes = calculateSisterSizes(bandSize, cupSize);
        
        return {
            band: bandSize,
            cup: cupSize,
            fullSize: `${bandSize}${cupSize}`,
            sisterSizes: sisterSizes,
            cupDifference: cupDifference.toFixed(1),
            bustAverage: bustAvg.toFixed(1)
        };
    }
    
    function calculateCupSize(difference) {
        const cupSizes = ['AA', 'A', 'B', 'C', 'D', 'DD/E', 'DDD/F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O'];
        const index = Math.max(0, Math.round(difference));
        return cupSizes[Math.min(index, cupSizes.length - 1)] || 'O+';
    }
    
    function calculateSisterSizes(bandSize, cupSize) {
        const cupSizes = ['AA', 'A', 'B', 'C', 'D', 'DD/E', 'DDD/F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O'];
        const currentCupIndex = cupSizes.indexOf(cupSize);
        
        const sisterSizes = [];
        
        // Smaller band, larger cup
        if (bandSize > 28 && currentCupIndex < cupSizes.length - 1) {
            sisterSizes.push(`${bandSize - 2}${cupSizes[currentCupIndex + 1]}`);
        }
        
        // Larger band, smaller cup
        if (bandSize < 50 && currentCupIndex > 0) {
            sisterSizes.push(`${bandSize + 2}${cupSizes[currentCupIndex - 1]}`);
        }
        
        return sisterSizes;
    }
    
    function displayResults(braSize, measurements, unit) {
        const cupClass = `cup-${braSize.cup.toLowerCase().replace(/[^a-z]/g, '')}`;
        
        let html = `
            <div style="font-size: 48px; font-weight: bold; margin-bottom: 20px;">
                ${braSize.fullSize}
            </div>
            
            <div style="margin-bottom: 25px;">
                <div class="cup-size ${cupClass}">${braSize.cup} Cup</div>
                <div style="margin-top: 10px; font-size: 16px;">
                    Band: ${braSize.band} | Cup Difference: ${braSize.cupDifference}${unit === 'cm' ? 'cm' : '"'}
                </div>
            </div>
            
            <div class="measurement-guide">
                <h4 style="color: #e91e63; margin-bottom: 15px;">📊 Measurement Analysis</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <h5>Underbust Measurements:</h5>
                        <ul style="margin: 10px 0;">
                            <li>Loose: ${measurements.underbustLoose}${unit === 'cm' ? 'cm' : '"'}</li>
                            <li>Snug: ${measurements.underbustSnug}${unit === 'cm' ? 'cm' : '"'}</li>
                            <li>Tight: ${measurements.underbustTight}${unit === 'cm' ? 'cm' : '"'}</li>
                        </ul>
                    </div>
                    <div>
                        <h5>Bust Measurements:</h5>
                        <ul style="margin: 10px 0;">
                            <li>Standing: ${measurements.bustStanding}${unit === 'cm' ? 'cm' : '"'}</li>
                            <li>Leaning: ${measurements.bustLeaning}${unit === 'cm' ? 'cm' : '"'}</li>
                            <li>Lying: ${measurements.bustLying}${unit === 'cm' ? 'cm' : '"'}</li>
                        </ul>
                    </div>
                </div>
            </div>
            
            <div class="size-chart">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">👯 Sister Sizes</h4>
                <p style="margin-bottom: 15px;">These sizes have the same cup volume but different band fits:</p>
                <div style="text-align: center;">
        `;
        
        braSize.sisterSizes.forEach(size => {
            html += `<div class="sister-size">${size}</div>`;
        });
        
        html += `
                </div>
                <div style="margin-top: 15px; font-size: 14px; color: #666;">
                    Sister sizes can be helpful if your calculated size isn't available or if you prefer a different band fit.
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 Fitting Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Band:</strong> Should be snug but comfortable, parallel to the ground</li>
                    <li><strong>Cups:</strong> Should fully contain breast tissue without spillage or gaps</li>
                    <li><strong>Straps:</strong> Should provide minimal support, not dig into shoulders</li>
                    <li><strong>Gore:</strong> Center piece should lie flat against your chest</li>
                    <li><strong>Wires:</strong> Should follow your breast root without poking or sliding</li>
                    <li><strong>Try different brands:</strong> Sizing can vary between manufacturers</li>
                </ul>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.1); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>🛍️ Shopping Recommendations:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li>Start with your calculated size and sister sizes</li>
                    <li>Try multiple brands as fit varies significantly</li>
                    <li>Consider your breast shape (projected, shallow, full on top/bottom)</li>
                    <li>Check return policies when ordering online</li>
                    <li>Visit r/ABraThatFits for brand recommendations and fit checks</li>
                    <li>Consider professional fitting at specialty lingerie stores</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
