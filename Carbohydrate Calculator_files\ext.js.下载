(function(){var aa=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ba=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object");},ca=ba(this),f=function(a,b){if(b)a:{var c=ca;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&aa(c,a,{configurable:!0,writable:!0,value:b})}};f("Symbol",function(a){if(a)return a;var b=function(g,p){this.m=g;aa(this,"description",{configurable:!0,writable:!0,value:p})};b.prototype.toString=function(){return this.m};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(g){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(g||"")+"_"+d++,g)};return e});var da=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},h;if(typeof Object.setPrototypeOf=="function")h=Object.setPrototypeOf;else{var k;a:{var ea={a:!0},fa={};try{fa.__proto__=ea;k=fa.a;break a}catch(a){}k=!1}h=k?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null}var ha=h,l=function(a,b){a.prototype=da(b.prototype);a.prototype.constructor=a;if(ha)ha(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.M=b.prototype},ja=function(a){return ia(a,a)},ia=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},ka=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)Object.prototype.hasOwnProperty.call(d,e)&&(a[e]=d[e])}return a};f("Object.assign",function(a){return a||ka});var la=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};f("globalThis",function(a){return a||ca});f("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});/*

 Copyright The Closure Library Authors.
 SPDX-License-Identifier: Apache-2.0
*/
var m=this||self,n=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"},ma=function(a,b,c){return a.call.apply(a.bind,arguments)},na=function(a,b,c){if(!a)throw Error();if(arguments.length>2){var d=Array.prototype.slice.call(arguments,2);return function(){var e=Array.prototype.slice.call(arguments);Array.prototype.unshift.apply(e,d);return a.apply(b,e)}}return function(){return a.apply(b,arguments)}},q=function(a,b,c){q=Function.prototype.bind&&Function.prototype.bind.toString().indexOf("native code")!=-1?ma:na;return q.apply(null,arguments)},r=function(a,b){a=a.split(".");for(var c=m,d;a.length&&(d=a.shift());)a.length||b===void 0?c[d]&&c[d]!==Object.prototype[d]?c=c[d]:c=c[d]={}:c[d]=b};var oa=function(a,b){var c=c===void 0?{}:c;this.error=a;this.meta=c;this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"};/*

 Copyright Google LLC
 SPDX-License-Identifier: Apache-2.0
*/
var pa=globalThis.trustedTypes,t;function qa(){var a=null;if(!pa)return a;try{var b=function(c){return c};a=pa.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){}return a};var u=function(a){this.m=a};u.prototype.toString=function(){return this.m+""};function ra(a){var b;t===void 0&&(t=qa());a=(b=t)?b.createScriptURL(a):a;return new u(a)};function sa(a){var b=la.apply(1,arguments);if(b.length===0)return ra(a[0]);for(var c=a[0],d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return ra(c)};var ta=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)};function w(a,b){var c=window;typeof c.addEventListener==="function"&&c.addEventListener(a,b,!1)};var ua=function(a){a||(Math.floor(Math.random()***********).toString(36),Math.abs(Math.floor(Math.random()***********)^Date.now()).toString(36))},va=function(){var a=x.goog_safeframe_hlt,b=[];a&&ta(a,function(c){b.push(new ua(c))})};var wa=ja(["https://pagead2.googlesyndication.com/pagead/js/err_rep.js"]),xa=function(){var a=a===void 0?"jserror":a;var b=b===void 0?.01:b;var c=c===void 0?sa(wa):c;this.m=a;this.j=b;this.g=c};var y=function(){this.o=this.o;this.u=this.u};y.prototype.o=!1;y.prototype.dispose=function(){this.o||(this.o=!0,this.D())};y.prototype[Symbol.dispose]=function(){this.dispose()};y.prototype.D=function(){if(this.u)for(;this.u.length;)this.u.shift()()};var z=function(a,b,c,d,e,g){y.call(this);this.C=a;this.status=1;this.B=b;this.A=c;this.K=d;this.G=!!e;this.F=Math.random();this.g={};this.j=null;this.H=q(this.J,this);this.m=g};l(z,y);z.prototype.J=function(a){if(!(this.A!=="*"&&a.origin!==this.A||!this.G&&a.source!=this.B)){var b=null;try{b=JSON.parse(a.data)}catch(c){}if(n(b)&&(a=b.i,b.c===this.C&&a!=this.F)){if(this.status!==2)try{this.status=2,ya(this),this.j&&(this.j(),this.j=null)}catch(c){}a=b.s;b=b.p;if(typeof a==="string"&&(typeof b==="string"||n(b))&&this.g.hasOwnProperty(a))this.g[a](b)}}};var ya=function(a){var b={};b.c=a.C;b.i=a.F;a.m&&(b.e=a.m);a.B.postMessage(JSON.stringify(b),a.A)};z.prototype.I=function(){if(this.status===1){try{this.B.postMessage&&ya(this)}catch(a){}window.setTimeout(q(this.I,this),50)}};z.prototype.connect=function(a){a&&(this.j=a);w("message",this.H);this.K&&this.I()};var za=function(a,b,c){var d={};d.c=a.C;d.i=a.F;d.s=b;d.p=c;try{a.B.postMessage(JSON.stringify(d),a.A)}catch(e){}};z.prototype.D=function(){this.status=3;var a=window;typeof a.removeEventListener==="function"&&a.removeEventListener("message",this.H,!1);y.prototype.D.call(this)};var Aa=function(a){this.uid=a;this.o=null;this.g=this.status=0;this.j=null;this.D="sfchannel"+a},A=function(a){return a.status===1||a.status===2};function B(a,b,c,d){this.o=a;this.j=b;this.m=c;this.g=d}var C=function(a){return new B(a.o,a.j,a.m,a.g)};B.prototype.ceil=function(){this.o=Math.ceil(this.o);this.j=Math.ceil(this.j);this.m=Math.ceil(this.m);this.g=Math.ceil(this.g);return this};B.prototype.floor=function(){this.o=Math.floor(this.o);this.j=Math.floor(this.j);this.m=Math.floor(this.m);this.g=Math.floor(this.g);return this};B.prototype.round=function(){this.o=Math.round(this.o);this.j=Math.round(this.j);this.m=Math.round(this.m);this.g=Math.round(this.g);return this};var D=function(){var a=document.body.offsetHeight;this.width=document.body.offsetWidth;this.height=a};D.prototype.aspectRatio=function(){return this.width/this.height};D.prototype.ceil=function(){this.width=Math.ceil(this.width);this.height=Math.ceil(this.height);return this};D.prototype.floor=function(){this.width=Math.floor(this.width);this.height=Math.floor(this.height);return this};D.prototype.round=function(){this.width=Math.round(this.width);this.height=Math.round(this.height);return this};var Ba=function(a,b,c,d,e,g,p){this.B=c;this.u=e;this.A=g;this.j=C(a);this.g=C(b);this.m=p?C(p):null;this.o=C(d)},E=function(a){var b={windowCoords_t:a.j.o,windowCoords_r:a.j.j,windowCoords_b:a.j.m,windowCoords_l:a.j.g,frameCoords_t:a.g.o,frameCoords_r:a.g.j,frameCoords_b:a.g.m,frameCoords_l:a.g.g,styleZIndex:a.B,allowedExpansion_t:a.o.o,allowedExpansion_r:a.o.j,allowedExpansion_b:a.o.m,allowedExpansion_l:a.o.g,xInView:a.u,yInView:a.A};a.m&&(b.posCoords_t=a.m.o,b.posCoords_b=a.m.m,b.posCoords_l=a.m.g,b.posCoords_r=a.m.j);return JSON.stringify(b)},F=function(a){a=JSON.parse(a);if(!(n(a)&&typeof a.windowCoords_t==="number"&&typeof a.windowCoords_r==="number"&&typeof a.windowCoords_b==="number"&&typeof a.windowCoords_l==="number"&&typeof a.frameCoords_t==="number"&&typeof a.frameCoords_r==="number"&&typeof a.frameCoords_b==="number"&&typeof a.frameCoords_l==="number"&&typeof a.styleZIndex==="string"&&typeof a.allowedExpansion_t==="number"&&typeof a.allowedExpansion_r==="number"&&typeof a.allowedExpansion_b==="number"&&typeof a.allowedExpansion_l==="number"&&typeof a.xInView==="number"&&a.xInView>=0&&a.xInView<=1&&typeof a.yInView==="number"&&a.yInView>=0&&a.yInView<=1)||a.posCoords_t!==void 0&&typeof a.posCoords_t!=="number"||a.posCoords_b!==void 0&&typeof a.posCoords_b!=="number"||a.posCoords_r!==void 0&&typeof a.posCoords_r!=="number"||a.posCoords_l!==void 0&&typeof a.posCoords_l!=="number"||a.posCoords_t!==void 0&&(a.posCoords_b===void 0||a.posCoords_l===void 0||a.posCoords_r===void 0))throw Error("Cannot parse JSON geometry");var b=new B(a.windowCoords_t,a.windowCoords_r,a.windowCoords_b,a.windowCoords_l),c=new B(a.frameCoords_t,a.frameCoords_r,a.frameCoords_b,a.frameCoords_l),d=new B(a.allowedExpansion_t,a.allowedExpansion_r,a.allowedExpansion_b,a.allowedExpansion_l),e;a.posCoords_t!==void 0&&(e=new B(a.posCoords_t,a.posCoords_r,a.posCoords_b,a.posCoords_l));return new Ba(b,c,a.styleZIndex,d,a.xInView,a.yInView,e)};var Da=function(){this.m=G};var Ea=function(){var a=I.expandByPush,b=I.readCookie,c=I.writeCookie;this.m=I.expandByOverlay;this.g=a;this.j=b;this.o=c};var Ia=function(){var a=x.hostPeerName,b=Fa,c=Ga,d=Ha,e=x.reportCreativeGeometry,g=x.isDifferentSourceWindow,p=x.sentinel;this.uid=x.uid;this.g=a;this.j=b;this.permissions=c;this.metadata=d;this.o=e;this.G=g;this.m=p===void 0?"":p},Ja=/^([^;]+);(\d+);([\s\S]*)$/;function J(a){return typeof a==="number"||typeof a==="string"}var K=function(a,b){this.uid=a;this.m=b};K.prototype.g=function(a){this.m&&a&&(a.sentinel=this.m);return JSON.stringify(a)};var L=function(a,b,c){K.call(this,a,c===void 0?"":c);this.version=b};l(L,K);L.prototype.g=function(){return K.prototype.g.call(this,{uid:this.uid,version:this.version})};var M=function(a,b,c,d){K.call(this,a,d===void 0?"":d);this.o=b;this.j=c};l(M,K);M.prototype.g=function(){return K.prototype.g.call(this,{uid:this.uid,initialWidth:this.o,initialHeight:this.j})};var Ka=function(a,b,c){K.call(this,a,c===void 0?"":c);this.description=b};l(Ka,K);Ka.prototype.g=function(){return K.prototype.g.call(this,{uid:this.uid,description:this.description})};var N=function(a,b,c,d){K.call(this,a,d===void 0?"":d);this.j=b;this.push=c};l(N,K);N.prototype.g=function(){return K.prototype.g.call(this,{uid:this.uid,expand_t:this.j.o,expand_r:this.j.j,expand_b:this.j.m,expand_l:this.j.g,push:this.push})};var La=function(a,b){K.call(this,a,b===void 0?"":b)};l(La,K);La.prototype.g=function(){return K.prototype.g.call(this,{uid:this.uid})};var Ma=function(a,b,c){K.call(this,a,c===void 0?"":c);this.j=b};l(Ma,K);Ma.prototype.g=function(){return K.prototype.g.call(this,{uid:this.uid,shrink_t:this.j.o,shrink_r:this.j.j,shrink_b:this.j.m,shrink_l:this.j.g})};var Na=function(a,b,c,d){K.call(this,a,d===void 0?"":d);this.j=b;this.push=c};l(Na,K);Na.prototype.g=function(){return K.prototype.g.call(this,{uid:this.uid,resize_t:this.j.o,resize_r:this.j.j,resize_b:this.j.m,resize_l:this.j.g,push:this.push})};var O=function(a,b,c){K.call(this,a,c===void 0?"":c);this.o=b};l(O,K);O.prototype.g=function(){var a={uid:this.uid,newGeometry:E(this.o)};return K.prototype.g.call(this,a)};var Oa=function(a){a=JSON.parse(a);if(!n(a)||!J(a.uid)||typeof a.newGeometry!=="string")throw Error("Cannot parse JSON message");var b=F(a.newGeometry);return new O(a.uid,b,a.sentinel||"")},Pa=function(a,b,c,d,e,g){O.call(this,a,c,g===void 0?"":g);this.u=b;this.j=d;this.push=e};l(Pa,O);Pa.prototype.g=function(){var a={uid:this.uid,success:this.u,newGeometry:E(this.o),expand_t:this.j.o,expand_r:this.j.j,expand_b:this.j.m,expand_l:this.j.g,push:this.push};this.m&&(a.sentinel=this.m);return JSON.stringify(a)};var Qa=function(a,b,c,d){O.call(this,a,c,d===void 0?"":d);this.j=b};l(Qa,O);Qa.prototype.g=function(){var a={uid:this.uid,success:this.j,newGeometry:E(this.o)};this.m&&(a.sentinel=this.m);return JSON.stringify(a)};var Ra=function(a,b,c,d,e){O.call(this,a,c,e===void 0?"":e);this.j=b;this.u=d};l(Ra,O);Ra.prototype.g=function(){var a={uid:this.uid,success:this.j,newGeometry:E(this.o),expand_t:this.u.o,expand_r:this.u.j,expand_b:this.u.m,expand_l:this.u.g};this.m&&(a.sentinel=this.m);return JSON.stringify(a)};var Sa=function(a,b,c,d){K.call(this,a,d===void 0?"":d);this.width=b;this.height=c};l(Sa,K);Sa.prototype.g=function(){return K.prototype.g.call(this,{uid:this.uid,width:this.width,height:this.height})};var P=function(a){Aa.call(this,a.uid);this.B=null;this.A=[];this.u=0;this.permissions=a.permissions;this.metadata=a.metadata;this.o=a.j;this.m=a.m;var b=this.j=new z(this.D,window.parent,a.g,!0,a.G,this.m),c=q(this.H,this);b.g.expand_response=c;b=this.j;c=q(this.F,this);b.g.collapse_response=c;b=this.j;c=q(this.C,this);b.g.resize_response=c;b=this.j;c=q(this.C,this);b.g.shrink_response=c;b=this.j;c=q(this.I,this);b.g.geometry_update=c;this.j.connect(q(this.J,this));this.status=1;this.sendMessage("init_done",new L(this.uid,"1-0-45",this.m));a.o&&Ta(this)};l(P,Aa);P.prototype.register=function(a,b,c){this.status=2;this.B=c;this.sendMessage("register_done",new M(this.uid,a,b,this.m))};var Q=function(a,b){a.sendMessage("report_error",new Ka(a.uid,b,a.m))};P.prototype.J=function(){for(var a=0;a<this.A.length;a++)za(this.j,this.A[a].type,this.A[a].message.g())};P.prototype.H=function(a){try{if(this.status!==2)throw Error("Container is not registered");if(this.g!==3)throw Error("Container is not expanding");if(typeof a!=="string")throw Error("Could not parse serialized message");var b=JSON.parse(a);if(!n(b)||!J(b.uid)||typeof b.success!=="boolean"||typeof b.newGeometry!=="string"||typeof b.expand_t!=="number"||typeof b.expand_r!=="number"||typeof b.expand_b!=="number"||typeof b.expand_l!=="number"||typeof b.push!=="boolean")throw Error("Cannot parse JSON message");var c=F(b.newGeometry);var d=new Pa(b.uid,b.success,c,new B(b.expand_t,b.expand_r,b.expand_b,b.expand_l),b.push,b.sentinel||"");if(this.uid!==d.uid)throw Error("Wrong source container");this.g=d.u?2:0;this.o=d.o;R(this,d.u?"expanded":"failed",d.push?"exp-push":"exp-ovr","",{t:d.j.o,r:d.j.j,b:d.j.m,l:d.j.g,push:d.push})}catch(e){}};P.prototype.C=function(a){try{if(this.status!==2)throw Error("Container is not registered");if(this.g!==5)throw Error("Container is not resizing");if(typeof a!=="string")throw Error("Could not parse serialized message");try{var b=JSON.parse(a);if(!n(b)||!J(b.uid)||typeof b.success!=="boolean"||typeof b.newGeometry!=="string")throw Error("Cannot parse JSON message");var c=F(b.newGeometry);var d=new Qa(b.uid,b.success,c,b.sentinel||"")}catch(p){var e=JSON.parse(a);if(!n(e)||!J(e.uid)||typeof e.success!=="boolean"||typeof e.newGeometry!=="string"||typeof e.expand_t!=="number"||typeof e.expand_r!=="number"||typeof e.expand_b!=="number"||typeof e.expand_l!=="number")throw Error("Cannot parse JSON message");var g=F(e.newGeometry);d=new Ra(e.uid,e.success,g,new B(e.expand_t,e.expand_r,e.expand_b,e.expand_l),e.sentinel||"")}if(this.uid!==d.uid)throw Error("Wrong source container");this.g=d.j?4:0;this.o=d.o;R(this,d.j?"resized":"failed","resize","",{})}catch(p){}};P.prototype.F=function(a){try{if(this.status!==2)throw Error("Container is not registered");if(this.g!==1)throw Error("Container is not collapsing");if(typeof a!=="string")throw Error("Could not parse serialized message");var b=Oa(a);if(this.uid!==b.uid)throw Error("Wrong source container");this.g=0;this.o=b.o;R(this,"collapsed","collapse","")}catch(c){}};P.prototype.I=function(a){try{if(!A(this))throw Error("Container is not initialized or registered");if(typeof a!=="string")throw Error("Could not parse serialized message");var b=Oa(a);if(this.uid!==b.uid)throw Error("Wrong source container");this.o=b.o;R(this,"geom-update","","")}catch(c){}};var R=function(a,b,c,d,e){if(a.B!==null)try{a.B(b,{cmd:c,reason:d,info:e})}catch(g){Q(a,"Could not manage to call user-supplied callback")}},T=function(a,b,c,d){setTimeout(q(function(){R(this,a,b,c,d)},S),0)};P.prototype.sendMessage=function(a,b){this.j.status===2?za(this.j,a,b.g()):this.A.push({type:a,message:b})};var Ua=function(a){var b=new D;a.sendMessage("creative_geometry_update",new Sa(a.uid,b.width,b.height,a.m))};P.prototype.G=function(){this.u===2&&Ua(this);this.u=0};var Ta=function(a){var b=function(){switch(a.u){case 0:Ua(a);setTimeout(q(a.G,a),200);a.u=1;break;case 1:a.u=2}};w("load",b);w("resize",b)};function Va(){var a=a===void 0?m:a;return(a=a.performance)&&a.now?a.now():null};function Wa(a,b){b=b.google_js_reporting_queue=b.google_js_reporting_queue||[];b.length<2048&&b.push(a)}function Xa(a,b,c){var d=!1;d=d===void 0?!1:d;var e=window,g=typeof queueMicrotask!=="undefined";return function(){var p=la.apply(0,arguments);d&&g&&queueMicrotask(function(){e.google_rum_task_id_counter=e.google_rum_task_id_counter||1;e.google_rum_task_id_counter+=1});var v=Va(),H=3;try{var fb=b.apply(this,p)}catch(Ca){H=13;if(!c)throw Ca;c(a,Ca)}finally{e.google_measure_js_timing&&v&&Wa(Object.assign({},{label:a.toString(),value:v,duration:(Va()||0)-v,type:H},d&&g&&{taskId:e.google_rum_task_id_counter=e.google_rum_task_id_counter||1}),e)}return fb}}function U(a,b){return Xa(a,b,function(c,d){var e=new xa;var g=g===void 0?e.j:g;var p=p===void 0?e.m:p;if(!(Math.random()>g||(d.error&&d.meta&&d.id||(d=new oa(d,{context:c,id:p})),m.google_js_errors=m.google_js_errors||[],m.google_js_errors.push(d),m.error_rep_loaded))){g=c=m.document;g=g===void 0?document:g;g=g.createElement("script");e=e.g;if(e instanceof u)e=e.m;else throw Error("");g.src=e;var v;e=g.ownerDocument;e=e===void 0?document:e;var H;e=(H=(v=e).querySelector)==null?void 0:H.call(v,"script[nonce]");(v=e==null?"":e.nonce||e.getAttribute("nonce")||"")&&g.setAttribute("nonce",v);(v=c.getElementsByTagName("script")[0])&&v.parentNode&&v.parentNode.insertBefore(g,v);m.error_rep_loaded=!0}})};function Ya(a,b,c){if(S.status===2)Q(S,"Called register multiple times");else if(typeof a!=="number"||a<=0)Q(S,"Invalid initial width");else if(typeof b!=="number"||b<=0)Q(S,"Invalid initial height");else{var d=null;if(c!=null){if(typeof c!=="function"){Q(S,"Invalid callback function");return}d=c}S.register(a,b,d)}}function Za(){return A(S)?{"exp-ovr":S.permissions.m,"exp-push":S.permissions.g,"read-cookie":S.permissions.j,"write-cookie":S.permissions.o}:(Q(S,"Called supports on bad container"),null)}function $a(){if(!A(S))return Q(S,"Called geom on bad container"),null;var a=S.o,b={win:{t:a.j.o,r:a.j.j,b:a.j.m,l:a.j.g,w:a.j.j-a.j.g,h:a.j.m-a.j.o},self:{t:a.g.o,r:a.g.j,b:a.g.m,l:a.g.g,w:a.g.j-a.g.g,h:a.g.m-a.g.o,z:Number(a.B),xiv:a.u,yiv:a.A,iv:a.u*a.A},exp:{t:a.o.o,r:a.o.j,b:a.o.m,l:a.o.g,xs:!1,yx:!1},pos:null};a.m&&(b.pos={t:a.m.o,r:a.m.j,b:a.m.m,l:a.m.g,w:a.m.j-a.m.g,h:a.m.m-a.m.o});return b}function ab(){if(!A(S))return Q(S,"Called inViewPercentage on bad container"),null;var a=S.o;return a.u*a.A*100}function bb(){if(!A(S))return Q(S,"Called status on bad container"),null;switch(S.g){case 0:return"collapsed";case 1:return"collapsing";case 2:return"expanded";case 3:return"expanding";case 4:return"resized";case 5:return"resizing";default:return null}}function cb(a,b){if(!A(S))return Q(S,"Called meta on bad container"),null;if(typeof a!=="string"||/^[\s\xa0]*$/.test(a==null?"":String(a)))return Q(S,"Invalid property name"),null;var c="shared";if(b!=null){if(typeof b!=="string"||/^[\s\xa0]*$/.test(b==null?"":String(b)))return Q(S,"Invalid owner key"),null;c=b}b=S.metadata.m[c];b==null?a=null:(a=b[a],a=a===null||a===void 0?null:a);return a}function db(a,b){S.status===2?typeof a!=="string"||/^[\s\xa0]*$/.test(a)?Q(S,"Invalid cookie name"):((a=b==null)||!(a=n(b)&&typeof b.info==="string")||(a=b.expires==null)||(a=b.expires,a=n(a)&&typeof a.getFullYear=="function"),a?(T("unsupported",b!=null?"write-cookie":"read-cookie","$sf.ext.cookie is not supported",b),Q(S,"Used unsupported cookie operations")):(T("failed","write-cookie","Invalid $sf.ext.cookie arguments",b),Q(S,"Invalid cookie data"))):Q(S,"Called cookie on unregistered container")}function eb(a){if(S.status===2)if(S.g===0)if(n(a)&&(a.t!=null||a.r!=null||a.b!=null||a.l!=null)&&(a.t==null||typeof a.t==="number"&&a.t>=0)&&(a.r==null||typeof a.r==="number"&&a.r>=0)&&(a.b==null||typeof a.b==="number"&&a.b>=0)&&(a.l==null||typeof a.l==="number"&&a.l>=0)&&(a.push==null||typeof a.push==="boolean")){var b=new B(a.t||0,a.r||0,a.b||0,a.l||0);a=a.push||!1;var c=S;c.g=3;c.sendMessage("expand_request",new N(c.uid,b,a,c.m))}else T("failed",n(a)&&typeof a.push==="boolean"&&a.push===!0?"exp-push":"exp-ovr","Invalid $sf.ext.expand arguments",a),Q(S,"Invalid expand data");else Q(S,"Called expand on uncollapsed container");else Q(S,"Called expand on unregistered container")}function gb(a){if(S.status===2)if(S.g===1||S.g===3||S.g===5)Q(S,"Called resize on container in incorrect state.");else{var b=!n(a)||a.t==null&&a.r==null&&a.b==null&&a.l==null||a.t!=null&&typeof a.t!=="number"||a.r!=null&&typeof a.r!=="number"||a.b!=null&&typeof a.b!=="number"||a.l!=null&&typeof a.l!=="number"?null:a;if(b){a=new B(b.t||0,b.r||0,b.b||0,b.l||0);b=b.push||!1;var c=S;a.o>0||a.m>0||a.j>0||a.g>0?(c.g=3,c.sendMessage("expand_request",new N(c.uid,a,b,c.m))):(c.g=5,c.sendMessage("shrink_request",new Ma(c.uid,new B(-a.o,-a.j,-a.m,-a.g),c.m)));c.sendMessage("resize_request",new Na(c.uid,a,b,c.m))}else T("failed","resize","Invalid $sf.ext.resize arguments",a),Q(S,"Invalid resize data")}else Q(S,"Called resize on unregistered container")}function hb(){if(S.status===2){var a;(a=S.g===2)||(a=S.g===4);a?(a=S,a.g=1,a.sendMessage("collapse_request",new La(a.uid,a.m))):Q(S,"Called collapse on collapsed container")}else Q(S,"Called collapse on unregistered container")}var ib=null,jb=!1;try{jb=!!m.sf_.cfg._context.ampcontextVersion}catch(a){}var V=window,kb=!(V!==V.parent&&V.parent===V.top);try{var lb,mb;if(m.sf_)lb=m.sf_.cfg,mb=m.sf_.v;else{var nb,ob=window.name,W=Ja.exec(ob);if(W===null)throw Error("Cannot parse serialized data. "+ob.substring(0,50));var X=+W[2],Y=W[3];if(X>Y.length)throw Error("Parsed content size doesn't match. "+X+":"+Y.length);nb={L:W[1],content:Y.substring(0,X),config:Y.substring(X)};lb=JSON.parse(nb.config);mb=nb.L}var pb=mb,qb,x=lb;if(typeof x.uid!=="string"&&typeof x.uid!=="number"||typeof x.hostPeerName!=="string"||typeof x.initialGeometry!=="string"||typeof x.permissions!=="string"||typeof x.metadata!=="string"||typeof x.reportCreativeGeometry!=="boolean"||typeof x.isDifferentSourceWindow!=="boolean"||x.sentinel!=null&&typeof x.sentinel!=="string"||x.goog_safeframe_hlt&&!n(x.goog_safeframe_hlt))throw Error("Cannot parse config");var Fa=F(x.initialGeometry),I=JSON.parse(x.permissions);if(!n(I)||typeof I.expandByOverlay!=="boolean"||typeof I.expandByPush!=="boolean"||typeof I.readCookie!=="boolean"||typeof I.writeCookie!=="boolean")throw Error("Cannot parse JSON permissions");var Ga=new Ea,Z=JSON.parse(x.metadata),rb;(rb=!n(Z))||(rb=!!(!n(Z.shared)||typeof Z.shared.sf_ver!=="string"||typeof Z.shared.ck_on!=="number"||typeof Z.shared.flash_ver!=="string"||Z.shared.canonical_url&&typeof Z.shared.canonical_url!=="string"||Z.shared.amp&&(!n(Z.shared.amp)||Z.shared.amp.canonical_url&&typeof Z.shared.amp.canonical_url!=="string")));if(rb)throw Error("Cannot parse JSON metadata");var G={shared:{sf_ver:Z.shared.sf_ver,ck_on:Z.shared.ck_on,flash_ver:Z.shared.flash_ver}};Z.shared.canonical_url&&(G.shared.canonical_url=Z.shared.canonical_url);Z.shared.amp&&(G.shared.is_amp=!0,G.shared.canonical_url=Z.shared.amp.canonical_url);var Ha=new Da;va();qb=new Ia;if(!qb.m&&"1-0-45"!==pb)throw Error("Host has different version from ext container");ib=new P(qb);if(jb||!kb)r("$sf.ext.register",U(441,Ya)),r("$sf.ext.supports",U(443,Za)),r("$sf.ext.geom",U(438,$a)),r("$sf.ext.inViewPercentage",U(439,ab)),r("$sf.ext.status",U(442,bb)),r("$sf.ext.meta",U(440,cb)),r("$sf.ext.cookie",U(436,db)),r("$sf.ext.expand",U(437,eb)),r("$sf.ext.collapse",U(435,hb)),ib.m&&r("$sf.ext.resize",gb)}catch(a){}m.sf_=void 0;window.name="";var S=ib;}).call(this);
