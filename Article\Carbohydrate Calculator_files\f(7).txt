(function(){'use strict';var aa=Object.defineProperty,fa=globalThis;function ha(a,b){if(b)a:{var c=fa;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&aa(c,a,{configurable:!0,writable:!0,value:b})}}ha("Symbol.dispose",function(a){return a?a:Symbol("Symbol.dispose")});/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var m=this||self;function ia(a,b,c){return a.call.apply(a.bind,arguments)}function t(a,b,c){t=ia;return t.apply(null,arguments)}function ja(a,b){function c(){}c.prototype=b.prototype;a.L=b.prototype;a.prototype=new c;a.prototype.constructor=a;a.M=function(d,e,g){for(var f=Array(arguments.length-2),k=2;k<arguments.length;k++)f[k-2]=arguments[k];return b.prototype[e].apply(d,f)}};function ka(a){m.setTimeout(()=>{throw a;},0)};function u(a,b){Array.prototype.forEach.call(a,b,void 0)};let la=void 0;function ma(a,b=!1){return b&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol()}var na=ma(),v=ma("m_m",!0);const w=ma("jas",!0);var oa;const pa=[];pa[w]=7;oa=Object.freeze(pa);var y={};function z(a,b){return b===void 0?a.h!==A&&!!(2&(a.g[w]|0)):!!(2&b)&&a.h!==A}const A={};var qa=Object.freeze({});const ra=BigInt(Number.MIN_SAFE_INTEGER),sa=BigInt(Number.MAX_SAFE_INTEGER);const ta=Number.isFinite;function wa(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return ta(a)?a|0:void 0}function B(a){return a==null||typeof a==="string"?a:void 0}function xa(a,b,c){if(a!=null&&a[v]===y)return a;if(Array.isArray(a)){var d=a[w]|0;c=d|c&32|c&2;c!==d&&(a[w]=c);return new b(a)}};function ya(a){return a};function za(a,b,c,d){var e=d!==void 0;d=!!d;const g=[];var f=a.length;let k,h=4294967295,l=!1;const n=!!(b&64),p=n?b&128?0:-1:void 0;b&1||(k=f&&a[f-1],k!=null&&typeof k==="object"&&k.constructor===Object?(f--,h=f):k=void 0,!n||b&128||e||(l=!0,h=(Aa??ya)(h-p,p,a,k)+p));b=void 0;for(e=0;e<f;e++){let r=a[e];if(r!=null&&(r=c(r,d))!=null)if(n&&e>=h){const q=e-p;(b??(b={}))[q]=r}else g[e]=r}if(k)for(let r in k){a=k[r];if(a==null||(a=c(a,d))==null)continue;f=+r;let q;n&&!Number.isNaN(f)&&(q=f+p)<h?g[q]= 
a:(b??(b={}))[r]=a}b&&(l?g.push(b):g[h]=b);return g}function Ba(a){switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return a>=ra&&a<=sa?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){const b=a[w]|0;return a.length===0&&b&1?void 0:za(a,b,Ba)}if(a!=null&&a[v]===y)return Ca(a);return}return a}let Aa;function Ca(a){a=a.g;return za(a,a[w]|0,Ba)};function Da(a){if(a==null){var b=32;a=[]}else{if(!Array.isArray(a))throw Error("narr");b=a[w]|0;2048&b&&!(2&b)&&Ea();if(b&256)throw Error("farr");if(b&64)return b&2048||(a[w]=b|2048),a;var c=a;b|=64;var d=c.length;if(d){var e=d-1;d=c[e];if(d!=null&&typeof d==="object"&&d.constructor===Object){const g=b&128?0:-1;e-=g;if(e>=1024)throw Error("pvtlmt");for(const f in d){const k=+f;if(k<e)c[k+g]=d[f],delete d[f];else break}b=b&-8380417|(e&1023)<<13}}}a[w]=b|2112;return a} 
function Ea(){if(na!=null){var a=la??(la={});var b=a[na]||0;b>=5||(a[na]=b+1,a=Error(),a.__closure__error__context__984382||(a.__closure__error__context__984382={}),a.__closure__error__context__984382.severity="incident",ka(a))}};function Fa(a,b){if(typeof a!=="object")return a;if(Array.isArray(a)){var c=a[w]|0;a.length===0&&c&1?a=void 0:c&2||(!b||4096&c||16&c?a=C(a,c,!1,b&&!(c&16)):(a[w]|=34,c&4&&Object.freeze(a)));return a}if(a!=null&&a[v]===y)return b=a.g,c=b[w]|0,z(a,c)?a:Ga(a,b,c)?Ha(a,b):C(b,c)}function Ha(a,b,c){a=new a.constructor(b);c&&(a.h=A);a.i=A;return a}function C(a,b,c,d){d??(d=!!(34&b));a=za(a,b,Fa,d);d=32;c&&(d|=2);b=b&8380609|d;a[w]=b;return a} 
function Ia(a){const b=a.g,c=b[w]|0;if(z(a,c)){var d;Ga(a,b,c)?d=Ha(a,b,!0):d=new a.constructor(C(b,c,!1));a=d}return a}function Ja(a){if(a.h!==A)return!1;var b=a.g;b=C(b,b[w]|0);b[w]|=2048;a.g=b;a.h=void 0;a.i=void 0;return!0}function Ka(a,b){b===void 0&&(b=a[w]|0);b&32&&!(b&4096)&&(a[w]=b|4096)}function Ga(a,b,c){return c&2?!0:c&32&&!(c&4096)?(b[w]=c|2,a.h=A,!0):!1};function F(a,b){a=La(a.g,b);if(a!==null)return a}function La(a,b,c,d){if(b===-1)return null;const e=b+(c?0:-1),g=a.length-1;let f,k;if(!(g<1+(c?0:-1))){if(e>=g)if(f=a[g],f!=null&&typeof f==="object"&&f.constructor===Object)c=f[b],k=!0;else if(e===g)c=f;else return;else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d;if(!Object.is(d,c))return k?f[b]=d:a[e]=d,d}return c}} 
function Ma(a,b,c){var d=a.length-1;if(d>=0&&0>=d){const e=a[d];if(e!=null&&typeof e==="object"&&e.constructor===Object)return e[1]=c,b}if(0<=d)return a[0]=c,b;c!==void 0&&(d=(b??(b=a[w]|0))>>13&1023||536870912,1>=d?c!=null&&(a[d+-1]={[1]:c}):a[0]=c);return b}function G(a){return!!(2&a)&&!!(4&a)||!!(256&a)}function Na(a,b){var c=Oa;let d=!1;const e=La(a,1,void 0,g=>{const f=xa(g,c,b);d=f!==g&&f!=null;return f});if(e!=null)return d&&!z(e)&&Ka(a,b),e} 
function Pa(a){let b=a.g,c=b[w]|0,d=Na(b,c);if(d==null)return d;c=b[w]|0;if(!z(a,c)){const e=Ia(d);e!==d&&(Ja(a)&&(b=a.g,c=b[w]|0),d=e,c=Ma(b,c,d),Ka(b,c))}return d}function Qa(a,b){return a=(2&b?a|2:a&-3)&-273}function H(a,b){a=F(a,b);return(a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0)??!1}function I(a,b){return wa(F(a,b))??0}function J(a,b){a=F(a,b);return(a==null?a:ta(a)?a|0:void 0)??1};var K=class{constructor(a){this.g=Da(a)}toJSON(){return Ca(this)}};K.prototype[v]=y;K.prototype.toString=function(){return this.g.toString()};var Ra=class extends K{};function Sa(a){var b=a.g,c=b,d=b[w]|0;b=void 0===qa?2:4;var e=z(a,d);const g=e?1:b;b=g===3;var f=!e;(g===2||f)&&Ja(a)&&(c=a.g,d=c[w]|0);a=La(c,1);e=Array.isArray(a)?a:oa;var k=e===oa?7:e[w]|0;a=k;2&d&&(a|=2);var h=a|1;if(a=!(4&h)){var l=e,n=d;const p=!!(2&h);p&&(n|=2);let r=!p,q=!0,D=0,x=0;for(;D<l.length;D++){const E=xa(l[D],Ra,n);if(E instanceof Ra){if(!p){const W=z(E);r&&(r=!W);q&&(q=W)}l[x++]=E}}x<D&&(l.length=x);h|=4;h=q?h&-4097:h|4096;h=r?h|8:h&-9}h!==k&&(e[w]=h,2&h&&Object.freeze(e));if(f&& 
!(8&h||!e.length&&(g===1||(g!==4?0:2&h||!(16&h)&&32&d)))){G(h)&&(e=[...e],h=Qa(h,d),d=Ma(c,d,e));f=e;k=h;for(l=0;l<f.length;l++)h=f[l],n=Ia(h),h!==n&&(f[l]=n);k|=8;h=k=f.length?k|4096:k&-4097;e[w]=h}k=f=h;g===1||(g!==4?0:2&f||!(16&f)&&32&d)?G(f)||(f|=!e.length||a&&!(4096&f)||32&d&&!(4096&f||16&f)?2:256,f!==k&&(e[w]=f),Object.freeze(e)):(g===2&&G(f)&&(e=[...e],k=0,f=Qa(f,d),d=Ma(c,d,e)),G(f)||(b||(f|=16),f!==k&&(e[w]=f)));2&f||!(4096&f||16&f)||Ka(c,d);return e}var Oa=class extends K{};var Ta=class extends K{};function Ua(){}function Va(a){let b=!1,c;return function(){b||(c=a(),b=!0);return c}};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var L=class{constructor(a){this.g=a}toString(){return this.g}},Wa=new L("about:invalid#zClosurez");class Xa{constructor(a){this.K=a}}function M(a){return new Xa(b=>b.substr(0,a.length+1).toLowerCase()===a+":")}const Ya=new Xa(a=>/^[^:]*([/?#]|$)/.test(a));var Za=M("http"),$a=M("https"),ab=M("ftp"),bb=M("mailto");const cb=[M("data"),Za,$a,bb,ab,Ya];function db(a=cb){for(let b=0;b<a.length;++b){const c=a[b];if(c instanceof Xa&&c.K("#"))return new L("#")}}function eb(a=cb){return db(a)||Wa}var fb=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i;function gb(a,b){if(a)for(const c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)}function hb(a=document){return a.createElement("img")};let N=[];function ib(){const a=N;N=[];for(const b of a)try{b()}catch{}};var jb={capture:!0},kb={passive:!0},lb=Va(()=>{let a=!1;try{const b=Object.defineProperty({},"passive",{get(){a=!0}});m.addEventListener("test",null,b)}catch(b){}return a});function mb(a){return a?a.passive&&lb()?a:a.capture||!1:!1}function O(a,b,c,d){typeof a.addEventListener==="function"&&a.addEventListener(b,c,mb(d))} 
function nb(a){var b=P;b.readyState==="complete"||b.readyState==="interactive"?(N.push(a),N.length===1&&(window.Promise?Promise.resolve().then(ib):(a=window.setImmediate,typeof a==="function"?a(ib):setTimeout(ib,0)))):b.addEventListener("DOMContentLoaded",a)};function ob(a,b,c=null,d=!1){pb(a,b,c,d)} 
function pb(a,b,c,d){a.google_image_requests||(a.google_image_requests=[]);const e=hb(a.document);if(c||d){const g=f=>{c&&c(f);if(d){f=a.google_image_requests;var k=Array.prototype.indexOf.call(f,e,void 0);k>=0&&Array.prototype.splice.call(f,k,1)}typeof e.removeEventListener==="function"&&e.removeEventListener("load",g,mb());typeof e.removeEventListener==="function"&&e.removeEventListener("error",g,mb())};O(e,"load",g);O(e,"error",g)}e.src=b;a.google_image_requests.push(e)};function qb(a=null){return a&&a.getAttribute("data-jc")==="23"?a:document.querySelector('[data-jc="23"]')} 
function rb(){if(!(Math.random()>.01)){var a=qb(document.currentScript);a=a&&a.getAttribute("data-jc-rcd")==="true"?"pagead2.googlesyndication-cn.com":"pagead2.googlesyndication.com";var b=(b=qb(document.currentScript))&&b.getAttribute("data-jc-version")||"unknown";a=`https://${a}/pagead/gen_204?id=jca&jc=${23}&version=${b}&sample=${.01}`;b=window;var c;if(c=b.navigator)c=b.navigator.userAgent,c=/Chrome/.test(c)&&!/Edge/.test(c)?!0:!1;c&&typeof b.navigator.sendBeacon==="function"?b.navigator.sendBeacon(a): 
ob(b,a,void 0,!1)}};var P=document,Q=window;var sb=(a=[])=>{m.google_logging_queue||(m.google_logging_queue=[]);m.google_logging_queue.push([12,a])};var tb=()=>{var a=P;try{return a.querySelectorAll("*[data-ifc]")}catch(b){return[]}},ub=(a,b)=>{a&&gb(b,(c,d)=>{a.style[d]=c})},vb=a=>{var b=P.body;const c=document.createDocumentFragment(),d=a.length;for(let e=0;e<d;++e)c.appendChild(a[e]);b.appendChild(c)};let R=null;function wb(){const a=m.performance;return a&&a.now&&a.timing?Math.floor(a.now()+a.timing.navigationStart):Date.now()}function xb(){const a=m.performance;return a&&a.now?a.now():null};var yb=class{constructor(a,b){var c=xb()||wb();this.label=a;this.type=b;this.value=c;this.duration=0;this.taskId=this.slotId=void 0;this.uniqueId=Math.random()}};const S=m.performance,zb=!!(S&&S.mark&&S.measure&&S.clearMarks),T=Va(()=>{var a;if(a=zb){var b;a=window;if(R===null){R="";try{let c="";try{c=a.top.location.hash}catch(d){c=a.location.hash}c&&(R=(b=c.match(/\bdeid=([\d,]+)/))?b[1]:"")}catch(c){}}b=R;a=!!b.indexOf&&b.indexOf("1337")>=0}return a});function Ab(a){a&&S&&T()&&(S.clearMarks(`goog_${a.label}_${a.uniqueId}_start`),S.clearMarks(`goog_${a.label}_${a.uniqueId}_end`))};function Bb(a,b,c,d,e){const g=[];gb(a,(f,k)=>{(f=Cb(f,b,c,d,e))&&g.push(`${k}=${f}`)});return g.join(b)}function Cb(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){const g=[];for(let f=0;f<a.length;f++)g.push(Cb(a[f],b,c,d+1,e));return g.join(c[d])}}else if(typeof a==="object")return e||(e=0),e<2?encodeURIComponent(Bb(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))} 
function Db(a){let b=1;for(const c in a.h)c.length>b&&(b=c.length);return 3997-b-a.i.length-1}function Eb(a){let b="https://pagead2.googlesyndication.com/pagead/gen_204?id=fccs&",c=Db(a)-24;if(c<0)return"";a.g.sort((g,f)=>g-f);let d=null,e="";for(let g=0;g<a.g.length;g++){const f=a.g[g],k=a.h[f];for(let h=0;h<k.length;h++){if(!c){d=d==null?f:d;break}let l=Bb(k[h],a.i,",$");if(l){l=e+l;if(c>=l.length){c-=l.length;b+=l;e=a.i;break}d=d==null?f:d}}}a="";d!=null&&(a=`${e}${"trn"}=${d}`);return b+a} 
var Fb=class{constructor(){this.i="&";this.h={};this.l=0;this.g=[]}};function Gb(a,b,c){if(Array.isArray(b))for(let d=0;d<b.length;d++)Gb(a,String(b[d]),c);else b!=null&&c.push(a+(b===""?"":"="+encodeURIComponent(String(b))))};class Hb{};function Ib(){var a=Jb,b=window.google_srt;b>=0&&b<=1&&(a.g=b)}function Kb(a){if(Jb.g<1)try{let b;a instanceof Fb?b=a:(b=new Fb,gb(a,(d,e)=>{var g=b;const f=g.l++,k={};k[e]=d;d=[k];g.g.push(f);g.h[f]=d}));const c=Eb(b);c&&ob(m,c)}catch(b){}}var Lb=class{constructor(){this.g=Math.random()}};const Mb=[Za,$a,bb,ab,Ya,M("market"),M("itms"),M("intent"),M("itms-appss")]; 
function Nb(){var a=`${Q.location.protocol==="http:"?"http:":"https:"}//${"pagead2.googlesyndication.com"}/pagead/gen_204`;return b=>{b={id:"unsafeurl",ctx:625,url:b};var c=[];for(d in b)Gb(d,b[d],c);var d=c.join("&");if(d){b=a.indexOf("#");b<0&&(b=a.length);c=a.indexOf("?");let e;c<0||c>b?(c=b,e=""):e=a.substring(c+1,b);b=[a.slice(0,c),e,a.slice(b)];c=b[1];b[1]=d?c?c+"&"+d:d:c;d=b[0]+(b[1]?"?"+b[1]:"")+b[2]}else d=a;navigator.sendBeacon&&navigator.sendBeacon(d,"")}};let Jb; 
const U=new class{constructor(a,b){this.g=[];this.i=b||m;let c=null;b&&(b.google_js_reporting_queue=b.google_js_reporting_queue||[],this.g=b.google_js_reporting_queue,c=b.google_measure_js_timing);this.h=T()||(c!=null?c:Math.random()<a)}start(a,b){if(!this.h)return null;a=new yb(a,b);b=`goog_${a.label}_${a.uniqueId}_start`;S&&T()&&S.mark(b);return a}end(a){if(this.h&&typeof a.value==="number"){a.duration=(xb()||wb())-a.value;var b=`goog_${a.label}_${a.uniqueId}_end`;S&&T()&&S.mark(b);!this.h||this.g.length> 
2048||this.g.push(a)}}}(1,window);function Ob(){window.google_measure_js_timing||(U.h=!1,U.g!==U.i.google_js_reporting_queue&&(T()&&u(U.g,Ab),U.g.length=0))}(function(a){Jb=a??new Lb;typeof window.google_srt!=="number"&&(window.google_srt=Math.random());Ib();window.document.readyState==="complete"?Ob():U.h&&O(window,"load",()=>{Ob()})})();function Pb(a){O(Q,"message",b=>{let c;try{c=JSON.parse(b.data)}catch(d){return}!c||c.googMsgType!=="ig"||a(c,b)})};function V(){this.h=this.h;this.i=this.i}V.prototype.h=!1;V.prototype.dispose=function(){this.h||(this.h=!0,this.l())};V.prototype[Symbol.dispose]=function(){this.dispose()};V.prototype.l=function(){if(this.i)for(;this.i.length;)this.i.shift()()};function X(a,b,c){V.call(this);this.m=a;this.B=b||0;this.o=c;this.u=t(this.v,this)}ja(X,V);X.prototype.g=0;X.prototype.l=function(){X.L.l.call(this);this.isActive()&&m.clearTimeout(this.g);this.g=0;delete this.m;delete this.o}; 
X.prototype.start=function(a){this.isActive()&&m.clearTimeout(this.g);this.g=0;var b=this.u;a=a!==void 0?a:this.B;if(typeof b!=="function")if(b&&typeof b.handleEvent=="function")b=t(b.handleEvent,b);else throw Error("Invalid listener argument");this.g=Number(a)>2147483647?-1:m.setTimeout(b,a||0)};X.prototype.isActive=function(){return this.g!=0};X.prototype.v=function(){this.g=0;this.m&&this.m.call(this.o)};const Qb={display:"inline-block",position:"absolute"},Rb={display:"none",width:"100%",height:"100%",top:"0",left:"0"};function Y(a,b){a&&(a.style.display=b?"inline-block":"none")}function Sb(a,b){if(a)return Q.getComputedStyle(a).getPropertyValue(b)}function Tb(a=""){const b={top:0,right:0,bottom:0,left:0};a&&(a=a.split(","),a.length===4&&a.reduce((c,d)=>c&&!isNaN(+d),!0)&&([b.top,b.right,b.bottom,b.left]=a.map(c=>+c)));return b} 
function Ub(a,b,c=2147483647){const d=P.createElement("div");ub(d,{...Qb,"z-index":String(c),...b});H(a.data,10)&&O(d,"click",Ua);if(H(a.data,11)){a=P.createElement("a");b=Nb();c=eb(Mb);c===Wa&&b("#");if(c instanceof L)if(c instanceof L)b=c.g;else throw Error("");else b=fb.test(c)?c:void 0;b!==void 0&&(a.href=b);a.appendChild(d);return a}return d} 
function Vb(a,b){switch(J(b.j,5)){case 2:Q.AFMA_Communicator?.addEventListener?.("onshow",()=>{Z(a,b)});break;case 10:O(Q,"i-creative-view",()=>{Z(a,b)});break;case 4:O(P,"DOMContentLoaded",()=>{Z(a,b)});break;case 8:Pb(c=>{c.rr&&Z(a,b)});break;case 9:if("IntersectionObserver"in Q){const c=new IntersectionObserver(d=>{for(const e of d)if(e.intersectionRatio>0){Z(a,b);break}});c.observe(P.body);a.J.push(c)}break;case 11:Q.AFMA_Communicator?.addEventListener?.("onAdVisibilityChanged",()=>{Z(a,b)})}} 
function Wb(a,b){b=Tb(b);const c=I(a.data,9);a.l=[{width:"100%",height:b.top+c+"px",top:-c+"px",left:"0"},{width:b.right+c+"px",height:"100%",top:"0",right:-c+"px"},{width:"100%",height:b.bottom+c+"px",bottom:-c+"px",left:"0"},{width:b.left+c+"px",height:"100%",top:"0",left:-c+"px"}].map(d=>Ub(a,d,9019))} 
function Xb(a){var b=0;for(const d of a.I){const e=d.j,g=a.v[J(e,5)];d.s||g===void 0||(b=Math.max(b,g+I(e,2)))}a.m&&a.m.dispose();b-=Date.now();const c=a.h;b>0?(Y(c,!0),a.m=new X(()=>{Y(c,!1)},b),a.m.start()):Y(c,!1)}function Z(a,b){if(!b.s){var c=J(b.j,5);a.v[c]=Date.now();H(b.j,9)&&(a.I.push(b),Xb(a))}} 
function Yb(a,b,c){if(!a.g||!a.u||b.timeStamp-a.g.timeStamp>=300)return!1;const d=new Map;u(a.u.changedTouches,e=>{d.set(e.identifier,{x:e.clientX,y:e.clientY})});b=I(c.j,11)||10;for(const e of a.g.changedTouches)if(a=d.get(e.identifier),!a||Math.abs(a.x-e.clientX)>b||Math.abs(a.y-e.clientY)>b)return!0;return!1} 
var $b=class{constructor(){var a=Zb;this.l=[];this.m=this.h=null;this.I=[];this.data=null;this.B=[];this.i=[];this.o=[];this.v={};this.J=[];this.u=this.g=null;this.F="";this.G=a["send-fccs"]==="true";this.F=a.qid||""}init(a){sb([a]);this.data=new Ta(a);a=Pa(this.data);u(Sa(a),g=>{this.o.push({C:0,s:!1,D:0,j:g,A:-1})});this.i=tb();let b=!1;a=this.i.length;for(let g=0;g<a;++g){var c=new Oa(JSON.parse(this.i[g].getAttribute("data-ifc")||"[]"));u(Sa(c),f=>{this.o.push({C:0,s:!1,D:0,j:f,A:g});J(f,4)=== 
1&&(b=!0)})}c=a=!1;let d=H(this.data,12);for(var e of this.o){const g=e.j;I(g,2)>0&&J(g,5)>0?(!this.h&&H(g,9)&&(this.h=Ub(this,Rb)),Vb(this,e)):(B(F(g,1))??"")&&H(g,9)&&Wb(this,B(F(g,1))??"");(B(F(g,1))??"")&&(a=!0);I(g,11)>0&&(c=!0);H(g,12)&&(d=!0)}e=[];this.h&&e.push(this.h);!b&&e.push(...this.l);P.body&&vb(e);H(this.data,13)&&nb(()=>{const g=P.body.querySelectorAll(".amp-fcp, .amp-bcp");for(let f=0;f<g.length;++f)Sb(g[f],"position")==="absolute"&&Y(g[f],!1)});O(P,"click",g=>{if(this.G){var f={cx:g.clientX, 
cy:g.clientY,et:Date.now(),qid:this.F};var k=Hb;var h="H";k.H&&k.hasOwnProperty(h)||(h=new k,k.H=h);k=[];!f.eid&&k.length&&(f.eid=k.toString());Kb(f);this.G=!1}if(g.isTrusted===!1&&H(this.data,15))g.preventDefault?g.preventDefault():g.returnValue=!1,g.stopImmediatePropagation(),rb();else{f=-1;k=[];for(var l of this.o){h=l.A;var n=h!==-1;if(!(I(l.j,3)<=f||l.s||n&&k[h]===!1)){var p=!n||k[h]||this.i[h].contains(g.target);n&&p&&(k[h]=!0);if(h=p)if(h=g,n=l.j,I(n,2)>0&&J(n,5)>0)h=this.v[J(n,5)],h=h!==void 0&& 
Date.now()<h+I(n,2);else if(B(F(n,1))??""){{const x=(l.A>=0?this.i[l.A]:P.body).getBoundingClientRect(),E=Number(Sb(P.body,"zoom")||"1"),[W,dc]=[h.clientX,h.clientY],[ba,ca,ua,va]=[W/E-x.left,dc/E-x.top,x.width,x.height];if(!(ua>0&&va>0)||isNaN(ba)||isNaN(ca)||ba<0||ca<0)h=!1;else{n=Tb(B(F(l.j,1))??"");p=!(ba>=n.left&&ua-ba>n.right&&ca>=n.top&&va-ca>n.bottom);var r=H(l.j,12);if(this.g&&(H(this.data,12)||r)&&h.timeStamp-this.g.timeStamp<300){h=this.g.changedTouches[0];const [da,ea]=[h.clientX/E-x.left, 
h.clientY/E-x.top];!isNaN(da)&&!isNaN(ea)&&da>=0&&ea>=0&&(p=(p=H(this.data,16)||r?p:!1)||!(da>=n.left&&ua-da>n.right&&ea>=n.top&&va-ea>n.bottom))}h=p}}}else h=I(n,11)>0?Yb(this,h,l):!0;if(h){var q=l;f=I(l.j,3)}}}if(q)switch(l=q.j,J(l,4)){case 2:case 3:g.preventDefault?g.preventDefault():g.returnValue=!1;f=Date.now();f-q.D>500&&(q.D=f,++q.C);f=q.j;if(I(f,8)&&q.C>=I(f,8))if(q.s=!0,this.h&&I(f,2)>0)Xb(this);else if(this.l.length>0&&(B(F(f,1))??""))for(var D of this.l)Y(D,!1);rb();D=Ca(l);for(const x of this.B)x(g, 
D)}}},jb);c&&O(P,"touchstart",g=>{this.u=g},kb);(a&&d||c)&&O(P,"touchend",g=>{this.g=g},kb)}registerCallback(a){this.B.push(a)}};const ac=qb(document.currentScript);if(ac==null)throw Error("JSC not found 23");var Zb;const bc={},cc=ac.attributes;for(let a=cc.length-1;a>=0;a--){const b=cc[a].name;b.indexOf("data-jcp-")===0&&(bc[b.substring(9)]=cc[a].value)}Zb=bc;const ec=window;ec.googqscp=new $b;Zb["init-data"]&&ec.googqscp.init(JSON.parse(Zb["init-data"]));}).call(this);
