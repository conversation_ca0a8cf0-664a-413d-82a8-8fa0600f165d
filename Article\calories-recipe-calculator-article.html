<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
</style>

<h1>Calories Recipe Calculator</h1>
<p>Use the following calculator to determine the total calories and nutritional content of your recipes. This tool helps you track calories per serving, analyze nutritional breakdown, and make informed decisions about your meal planning and dietary goals.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="recipeform" id="recipe-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="recipe">Recipe Calculator</a></li>
                <li><a href="#" data-tab="ingredient">Ingredient Database</a></li>
                <li><a href="#" data-tab="nutrition">Nutrition Analysis</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 8px 5px;">
            <table id="recipe-input" style="display: block;">
                <tbody>
                    <tr>
                        <td width="120">Recipe Name:</td>
                        <td><input type="text" name="recipename" id="recipename" value="My Recipe" class="inlongest"></td>
                    </tr>
                    <tr>
                        <td width="120">Total Servings:</td>
                        <td><input type="number" name="servings" id="servings" value="4" class="innormal" min="1" max="50"></td>
                    </tr>
                </tbody>
            </table>
            
            <h3>Add Ingredients:</h3>
            <table id="ingredient-input" style="width: 100%; margin: 10px 0;">
                <tbody>
                    <tr style="background-color: #E0F0FE;">
                        <td width="30%"><strong>Ingredient</strong></td>
                        <td width="15%"><strong>Amount</strong></td>
                        <td width="15%"><strong>Unit</strong></td>
                        <td width="15%"><strong>Calories/Unit</strong></td>
                        <td width="15%"><strong>Total Calories</strong></td>
                        <td width="10%"><strong>Action</strong></td>
                    </tr>
                    <tr id="ingredient-row-1">
                        <td><input type="text" name="ingredient1" value="Chicken Breast" class="inlonger"></td>
                        <td><input type="number" name="amount1" value="200" class="innormal" step="0.1"></td>
                        <td>
                            <select name="unit1" class="innormal">
                                <option value="grams" selected>grams</option>
                                <option value="ounces">ounces</option>
                                <option value="cups">cups</option>
                                <option value="tablespoons">tablespoons</option>
                                <option value="teaspoons">teaspoons</option>
                                <option value="pieces">pieces</option>
                            </select>
                        </td>
                        <td><input type="number" name="calories1" value="165" class="innormal" step="0.1"></td>
                        <td><span id="total-calories-1" class="total-calories">330</span></td>
                        <td><input type="button" value="Remove" onclick="removeIngredient(1)" style="font-size: 12px; padding: 3px 6px;"></td>
                    </tr>
                    <tr id="ingredient-row-2">
                        <td><input type="text" name="ingredient2" value="Brown Rice" class="inlonger"></td>
                        <td><input type="number" name="amount2" value="100" class="innormal" step="0.1"></td>
                        <td>
                            <select name="unit2" class="innormal">
                                <option value="grams" selected>grams</option>
                                <option value="ounces">ounces</option>
                                <option value="cups">cups</option>
                                <option value="tablespoons">tablespoons</option>
                                <option value="teaspoons">teaspoons</option>
                                <option value="pieces">pieces</option>
                            </select>
                        </td>
                        <td><input type="number" name="calories2" value="111" class="innormal" step="0.1"></td>
                        <td><span id="total-calories-2" class="total-calories">111</span></td>
                        <td><input type="button" value="Remove" onclick="removeIngredient(2)" style="font-size: 12px; padding: 3px 6px;"></td>
                    </tr>
                    <tr id="ingredient-row-3">
                        <td><input type="text" name="ingredient3" value="Olive Oil" class="inlonger"></td>
                        <td><input type="number" name="amount3" value="1" class="innormal" step="0.1"></td>
                        <td>
                            <select name="unit3" class="innormal">
                                <option value="grams">grams</option>
                                <option value="ounces">ounces</option>
                                <option value="cups">cups</option>
                                <option value="tablespoons" selected>tablespoons</option>
                                <option value="teaspoons">teaspoons</option>
                                <option value="pieces">pieces</option>
                            </select>
                        </td>
                        <td><input type="number" name="calories3" value="119" class="innormal" step="0.1"></td>
                        <td><span id="total-calories-3" class="total-calories">119</span></td>
                        <td><input type="button" value="Remove" onclick="removeIngredient(3)" style="font-size: 12px; padding: 3px 6px;"></td>
                    </tr>
                </tbody>
            </table>
            
            <table width="100%" style="padding-top: 10px;">
                <tbody>
                    <tr>
                        <td align="left">
                            <input type="button" value="Add Ingredient" id="add-ingredient-btn">
                        </td>
                        <td align="center">
                            <input type="submit" name="x" value="Calculate Recipe" id="calculate-recipe-btn">
                            <input type="button" value="Clear All" id="clear-recipe-btn">
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        
        <div class="smalltext">Note: Calorie values are per unit specified. For example, if you select "grams" and enter 165 calories, this means 165 calories per gram. Adjust the calories per unit based on your ingredient's nutritional information.</div>
        
        <div id="recipe-results" style="display: none; margin-top: 20px; padding: 15px; background-color: #f0f8ff; border: 1px solid #ccc; border-radius: 5px;">
            <h3>Recipe Nutrition Summary:</h3>
            <div id="recipe-results-content"></div>
        </div>
    </form>
</div>

<a name="nutrition-database"></a>
<br><h2>Common Ingredients Calorie Database</h2>
<p>Use this reference database to find calorie information for common ingredients. All values are per 100 grams unless otherwise specified.</p>

<h3>Proteins</h3>
<table class="cinfoT">
    <tbody>
        <tr>
            <td class="cinfoHd">Ingredient</td>
            <td class="cinfoHdL">Calories per 100g</td>
            <td class="cinfoHdL">Protein (g)</td>
            <td class="cinfoHdL">Fat (g)</td>
            <td class="cinfoHdL">Carbs (g)</td>
        </tr>
        <tr>
            <td>Chicken Breast (skinless)</td>
            <td class="cinfoBodL">165</td>
            <td class="cinfoBodL">31</td>
            <td class="cinfoBodL">3.6</td>
            <td class="cinfoBodL">0</td>
        </tr>
        <tr>
            <td>Salmon (Atlantic)</td>
            <td class="cinfoBodL">208</td>
            <td class="cinfoBodL">25</td>
            <td class="cinfoBodL">12</td>
            <td class="cinfoBodL">0</td>
        </tr>
        <tr>
            <td>Ground Beef (85% lean)</td>
            <td class="cinfoBodL">250</td>
            <td class="cinfoBodL">26</td>
            <td class="cinfoBodL">15</td>
            <td class="cinfoBodL">0</td>
        </tr>
        <tr>
            <td>Eggs (whole)</td>
            <td class="cinfoBodL">155</td>
            <td class="cinfoBodL">13</td>
            <td class="cinfoBodL">11</td>
            <td class="cinfoBodL">1.1</td>
        </tr>
        <tr>
            <td>Tofu (firm)</td>
            <td class="cinfoBodL">144</td>
            <td class="cinfoBodL">17</td>
            <td class="cinfoBodL">9</td>
            <td class="cinfoBodL">3</td>
        </tr>
        <tr>
            <td>Greek Yogurt (plain, non-fat)</td>
            <td class="cinfoBodL">59</td>
            <td class="cinfoBodL">10</td>
            <td class="cinfoBodL">0.4</td>
            <td class="cinfoBodL">3.6</td>
        </tr>
    </tbody>
</table>

<h3>Carbohydrates</h3>
<table class="cinfoT">
    <tbody>
        <tr>
            <td class="cinfoHd">Ingredient</td>
            <td class="cinfoHdL">Calories per 100g</td>
            <td class="cinfoHdL">Protein (g)</td>
            <td class="cinfoHdL">Fat (g)</td>
            <td class="cinfoHdL">Carbs (g)</td>
        </tr>
        <tr>
            <td>Brown Rice (cooked)</td>
            <td class="cinfoBodL">111</td>
            <td class="cinfoBodL">2.6</td>
            <td class="cinfoBodL">0.9</td>
            <td class="cinfoBodL">23</td>
        </tr>
        <tr>
            <td>White Rice (cooked)</td>
            <td class="cinfoBodL">130</td>
            <td class="cinfoBodL">2.7</td>
            <td class="cinfoBodL">0.3</td>
            <td class="cinfoBodL">28</td>
        </tr>
        <tr>
            <td>Quinoa (cooked)</td>
            <td class="cinfoBodL">120</td>
            <td class="cinfoBodL">4.4</td>
            <td class="cinfoBodL">1.9</td>
            <td class="cinfoBodL">22</td>
        </tr>
        <tr>
            <td>Whole Wheat Pasta (cooked)</td>
            <td class="cinfoBodL">124</td>
            <td class="cinfoBodL">5</td>
            <td class="cinfoBodL">0.5</td>
            <td class="cinfoBodL">25</td>
        </tr>
        <tr>
            <td>Sweet Potato (baked)</td>
            <td class="cinfoBodL">90</td>
            <td class="cinfoBodL">2</td>
            <td class="cinfoBodL">0.2</td>
            <td class="cinfoBodL">21</td>
        </tr>
        <tr>
            <td>Oats (dry)</td>
            <td class="cinfoBodL">389</td>
            <td class="cinfoBodL">17</td>
            <td class="cinfoBodL">7</td>
            <td class="cinfoBodL">66</td>
        </tr>
    </tbody>
</table>

<h3>Vegetables</h3>
<table class="cinfoT">
    <tbody>
        <tr>
            <td class="cinfoHd">Ingredient</td>
            <td class="cinfoHdL">Calories per 100g</td>
            <td class="cinfoHdL">Protein (g)</td>
            <td class="cinfoHdL">Fat (g)</td>
            <td class="cinfoHdL">Carbs (g)</td>
        </tr>
        <tr>
            <td>Broccoli</td>
            <td class="cinfoBodL">34</td>
            <td class="cinfoBodL">2.8</td>
            <td class="cinfoBodL">0.4</td>
            <td class="cinfoBodL">7</td>
        </tr>
        <tr>
            <td>Spinach</td>
            <td class="cinfoBodL">23</td>
            <td class="cinfoBodL">2.9</td>
            <td class="cinfoBodL">0.4</td>
            <td class="cinfoBodL">3.6</td>
        </tr>
        <tr>
            <td>Bell Peppers</td>
            <td class="cinfoBodL">31</td>
            <td class="cinfoBodL">1</td>
            <td class="cinfoBodL">0.3</td>
            <td class="cinfoBodL">7</td>
        </tr>
        <tr>
            <td>Carrots</td>
            <td class="cinfoBodL">41</td>
            <td class="cinfoBodL">0.9</td>
            <td class="cinfoBodL">0.2</td>
            <td class="cinfoBodL">10</td>
        </tr>
        <tr>
            <td>Tomatoes</td>
            <td class="cinfoBodL">18</td>
            <td class="cinfoBodL">0.9</td>
            <td class="cinfoBodL">0.2</td>
            <td class="cinfoBodL">3.9</td>
        </tr>
        <tr>
            <td>Avocado</td>
            <td class="cinfoBodL">160</td>
            <td class="cinfoBodL">2</td>
            <td class="cinfoBodL">15</td>
            <td class="cinfoBodL">9</td>
        </tr>
    </tbody>
</table>

<h3>Fats and Oils</h3>
<table class="cinfoT">
    <tbody>
        <tr>
            <td class="cinfoHd">Ingredient</td>
            <td class="cinfoHdL">Calories per 100g</td>
            <td class="cinfoHdL">Calories per Tablespoon</td>
            <td class="cinfoHdL">Fat Type</td>
        </tr>
        <tr>
            <td>Olive Oil (Extra Virgin)</td>
            <td class="cinfoBodL">884</td>
            <td class="cinfoBodL">119</td>
            <td>Monounsaturated</td>
        </tr>
        <tr>
            <td>Coconut Oil</td>
            <td class="cinfoBodL">862</td>
            <td class="cinfoBodL">117</td>
            <td>Saturated</td>
        </tr>
        <tr>
            <td>Butter</td>
            <td class="cinfoBodL">717</td>
            <td class="cinfoBodL">102</td>
            <td>Saturated</td>
        </tr>
        <tr>
            <td>Almonds</td>
            <td class="cinfoBodL">579</td>
            <td class="cinfoBodL">-</td>
            <td>Monounsaturated</td>
        </tr>
        <tr>
            <td>Walnuts</td>
            <td class="cinfoBodL">654</td>
            <td class="cinfoBodL">-</td>
            <td>Polyunsaturated</td>
        </tr>
    </tbody>
</table>

<a name="nutrition-analysis"></a>
<br><h2>Understanding Recipe Nutrition</h2>
<p>Analyzing the nutritional content of your recipes is essential for maintaining a balanced diet and achieving your health goals. Here's a comprehensive guide to understanding and optimizing your recipe nutrition.</p>

<h3>Macronutrient Breakdown</h3>
<p>Every recipe consists of three main macronutrients, each serving different functions in your body:</p>

<table class="cinfoT" style="margin: 20px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">Macronutrient</td>
            <td class="cinfoHd">Calories per Gram</td>
            <td class="cinfoHd">Primary Function</td>
            <td class="cinfoHd">Recommended % of Total Calories</td>
            <td class="cinfoHd">Good Sources</td>
        </tr>
        <tr>
            <td><strong>Carbohydrates</strong></td>
            <td class="cinfoBodL">4</td>
            <td>Primary energy source</td>
            <td class="cinfoBodL">45-65%</td>
            <td>Grains, fruits, vegetables, legumes</td>
        </tr>
        <tr>
            <td><strong>Proteins</strong></td>
            <td class="cinfoBodL">4</td>
            <td>Muscle building and repair</td>
            <td class="cinfoBodL">10-35%</td>
            <td>Meat, fish, eggs, dairy, legumes</td>
        </tr>
        <tr>
            <td><strong>Fats</strong></td>
            <td class="cinfoBodL">9</td>
            <td>Hormone production, vitamin absorption</td>
            <td class="cinfoBodL">20-35%</td>
            <td>Oils, nuts, seeds, avocado, fatty fish</td>
        </tr>
    </tbody>
</table>

<h3>Calorie Distribution Chart</h3>
<p>Understanding how calories are distributed across different food groups helps in creating balanced recipes:</p>

<div style="text-align:center; margin: 20px 0;">
    <svg width="600" height="400" viewBox="0 0 600 400" style="border: 1px solid #ccc; background: #f9f9f9;">
        <!-- Chart Title -->
        <text x="300" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#003366">Calorie Density by Food Group</text>

        <!-- Y-axis (Calories per 100g) -->
        <line x1="60" y1="50" x2="60" y2="350" stroke="#333" stroke-width="2"/>
        <text x="25" y="200" text-anchor="middle" font-size="12" fill="#333" transform="rotate(-90 25 200)">Calories per 100g</text>

        <!-- X-axis (Food Groups) -->
        <line x1="60" y1="350" x2="550" y2="350" stroke="#333" stroke-width="2"/>
        <text x="305" y="385" text-anchor="middle" font-size="12" fill="#333">Food Groups</text>

        <!-- Y-axis labels -->
        <text x="55" y="55" text-anchor="end" font-size="10" fill="#666">900</text>
        <text x="55" y="105" text-anchor="end" font-size="10" fill="#666">700</text>
        <text x="55" y="155" text-anchor="end" font-size="10" fill="#666">500</text>
        <text x="55" y="205" text-anchor="end" font-size="10" fill="#666">300</text>
        <text x="55" y="255" text-anchor="end" font-size="10" fill="#666">100</text>
        <text x="55" y="305" text-anchor="end" font-size="10" fill="#666">50</text>
        <text x="55" y="355" text-anchor="end" font-size="10" fill="#666">0</text>

        <!-- X-axis labels -->
        <text x="110" y="365" text-anchor="middle" font-size="10" fill="#666">Oils</text>
        <text x="170" y="365" text-anchor="middle" font-size="10" fill="#666">Nuts</text>
        <text x="230" y="365" text-anchor="middle" font-size="10" fill="#666">Meat</text>
        <text x="290" y="365" text-anchor="middle" font-size="10" fill="#666">Grains</text>
        <text x="350" y="365" text-anchor="middle" font-size="10" fill="#666">Dairy</text>
        <text x="410" y="365" text-anchor="middle" font-size="10" fill="#666">Fruits</text>
        <text x="470" y="365" text-anchor="middle" font-size="10" fill="#666">Vegetables</text>

        <!-- Bars -->
        <rect x="95" y="60" width="30" height="290" fill="#ff6b6b" opacity="0.8"/>
        <rect x="155" y="120" width="30" height="230" fill="#4ecdc4" opacity="0.8"/>
        <rect x="215" y="180" width="30" height="170" fill="#45b7d1" opacity="0.8"/>
        <rect x="275" y="220" width="30" height="130" fill="#96ceb4" opacity="0.8"/>
        <rect x="335" y="280" width="30" height="70" fill="#feca57" opacity="0.8"/>
        <rect x="395" y="310" width="30" height="40" fill="#ff9ff3" opacity="0.8"/>
        <rect x="455" y="330" width="30" height="20" fill="#54a0ff" opacity="0.8"/>

        <!-- Values on bars -->
        <text x="110" y="75" text-anchor="middle" font-size="11" fill="#fff" font-weight="bold">884</text>
        <text x="170" y="135" text-anchor="middle" font-size="11" fill="#fff" font-weight="bold">579</text>
        <text x="230" y="195" text-anchor="middle" font-size="11" fill="#fff" font-weight="bold">250</text>
        <text x="290" y="235" text-anchor="middle" font-size="11" fill="#fff" font-weight="bold">130</text>
        <text x="350" y="295" text-anchor="middle" font-size="11" fill="#fff" font-weight="bold">59</text>
        <text x="410" y="325" text-anchor="middle" font-size="11" fill="#fff" font-weight="bold">41</text>
        <text x="470" y="345" text-anchor="middle" font-size="11" fill="#fff" font-weight="bold">25</text>
    </svg>
</div>

<h3>Recipe Optimization Strategies</h3>
<p>Use these strategies to create healthier, more balanced recipes while maintaining great taste:</p>

<div style="background-color: #f0f8ff; padding: 15px; margin: 20px 0; border-left: 4px solid #0066cc;">
    <h4>Reducing Calories Without Sacrificing Flavor</h4>
    <ul>
        <li><strong>Substitute cooking methods:</strong> Bake, grill, or steam instead of frying</li>
        <li><strong>Use herbs and spices:</strong> Add flavor without calories</li>
        <li><strong>Increase vegetables:</strong> Bulk up dishes with low-calorie, nutrient-dense vegetables</li>
        <li><strong>Choose lean proteins:</strong> Opt for chicken breast, fish, or plant-based proteins</li>
        <li><strong>Reduce added fats:</strong> Use cooking sprays or reduce oil quantities</li>
    </ul>

    <h4>Boosting Nutritional Value</h4>
    <ul>
        <li><strong>Add fiber:</strong> Include whole grains, beans, and vegetables</li>
        <li><strong>Include healthy fats:</strong> Use nuts, seeds, and avocado in moderation</li>
        <li><strong>Vary protein sources:</strong> Mix animal and plant-based proteins</li>
        <li><strong>Choose colorful ingredients:</strong> Different colors provide different nutrients</li>
        <li><strong>Minimize processed foods:</strong> Use whole, unprocessed ingredients when possible</li>
    </ul>
</div>

<h3>Portion Control and Serving Sizes</h3>
<p>Understanding proper portion sizes is crucial for accurate calorie counting and maintaining a healthy diet:</p>

<table class="cinfoT" style="margin: 20px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">Food Group</td>
            <td class="cinfoHd">Standard Serving Size</td>
            <td class="cinfoHd">Visual Reference</td>
            <td class="cinfoHd">Typical Calories</td>
        </tr>
        <tr>
            <td><strong>Protein (meat/fish)</strong></td>
            <td class="cinfoBodL">3-4 oz (85-115g)</td>
            <td>Palm of your hand</td>
            <td class="cinfoBodL">150-250</td>
        </tr>
        <tr>
            <td><strong>Grains/Starches</strong></td>
            <td class="cinfoBodL">1/2 cup cooked</td>
            <td>Cupped palm</td>
            <td class="cinfoBodL">80-120</td>
        </tr>
        <tr>
            <td><strong>Vegetables</strong></td>
            <td class="cinfoBodL">1 cup raw, 1/2 cup cooked</td>
            <td>Your fist</td>
            <td class="cinfoBodL">20-50</td>
        </tr>
        <tr>
            <td><strong>Fruits</strong></td>
            <td class="cinfoBodL">1 medium fruit, 1/2 cup</td>
            <td>Tennis ball</td>
            <td class="cinfoBodL">60-100</td>
        </tr>
        <tr>
            <td><strong>Fats/Oils</strong></td>
            <td class="cinfoBodL">1 tablespoon</td>
            <td>Your thumb tip</td>
            <td class="cinfoBodL">100-120</td>
        </tr>
        <tr>
            <td><strong>Nuts/Seeds</strong></td>
            <td class="cinfoBodL">1 oz (28g)</td>
            <td>Small handful</td>
            <td class="cinfoBodL">160-200</td>
        </tr>
    </tbody>
</table>

<h3>Recipe Scaling Calculator</h3>
<p>Use this tool to scale your recipes up or down while maintaining accurate nutritional information:</p>

<div class="panel2" style="padding: 15px; margin: 20px 0;">
    <table>
        <tr>
            <td width="150">Original Servings:</td>
            <td><input type="number" id="original-servings" value="4" class="innormal" min="1"></td>
        </tr>
        <tr>
            <td width="150">Desired Servings:</td>
            <td><input type="number" id="desired-servings" value="6" class="innormal" min="1"></td>
        </tr>
        <tr>
            <td width="150">Scaling Factor:</td>
            <td><span id="scaling-factor" style="font-weight: bold; color: #0066cc;">1.5x</span></td>
        </tr>
        <tr>
            <td colspan="2" style="padding-top: 10px;">
                <input type="button" value="Calculate Scaling" id="calculate-scaling">
            </td>
        </tr>
    </table>

    <div id="scaling-results" style="display: none; margin-top: 15px;">
        <h4>Scaling Instructions:</h4>
        <p>Multiply all ingredient quantities by <span id="multiplier" style="font-weight: bold; color: #0066cc;"></span></p>
        <div class="smalltext">Note: Some ingredients like spices and seasonings may not need to be scaled proportionally. Taste and adjust as needed.</div>
    </div>
</div>

<h3>Common Recipe Modifications</h3>
<table class="cinfoT" style="margin: 20px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">Instead of...</td>
            <td class="cinfoHd">Try...</td>
            <td class="cinfoHd">Calorie Savings</td>
            <td class="cinfoHd">Nutritional Benefit</td>
        </tr>
        <tr>
            <td>1 cup white rice</td>
            <td class="cinfoBodL">1 cup cauliflower rice</td>
            <td class="cinfoBodL">-175 calories</td>
            <td>Lower carbs, more fiber</td>
        </tr>
        <tr>
            <td>1 cup pasta</td>
            <td class="cinfoBodL">1 cup zucchini noodles</td>
            <td class="cinfoBodL">-200 calories</td>
            <td>More vitamins, less carbs</td>
        </tr>
        <tr>
            <td>1 cup heavy cream</td>
            <td class="cinfoBodL">1 cup Greek yogurt</td>
            <td class="cinfoBodL">-650 calories</td>
            <td>More protein, probiotics</td>
        </tr>
        <tr>
            <td>1 cup sugar</td>
            <td class="cinfoBodL">3/4 cup applesauce</td>
            <td class="cinfoBodL">-600 calories</td>
            <td>Added fiber, vitamins</td>
        </tr>
        <tr>
            <td>1 cup butter</td>
            <td class="cinfoBodL">1/2 cup avocado puree</td>
            <td class="cinfoBodL">-1000 calories</td>
            <td>Healthy fats, fiber</td>
        </tr>
    </tbody>
</table>

<h3>Meal Planning with Recipe Calories</h3>
<p>Understanding recipe calories helps you plan balanced meals throughout the day. Here's how to distribute calories across meals:</p>

<div style="text-align:center; margin: 20px 0;">
    <svg width="500" height="300" viewBox="0 0 500 300" style="border: 1px solid #ccc; background: #f9f9f9;">
        <!-- Chart Title -->
        <text x="250" y="25" text-anchor="middle" font-size="16" font-weight="bold" fill="#003366">Daily Calorie Distribution</text>

        <!-- Pie chart center -->
        <g transform="translate(250,150)">
            <!-- Breakfast (25%) -->
            <path d="M 0,-80 A 80,80 0 0,1 56.57,-56.57 L 0,0 Z" fill="#ff6b6b" opacity="0.8"/>
            <text x="30" y="-50" text-anchor="middle" font-size="12" fill="#fff" font-weight="bold">25%</text>
            <text x="30" y="-35" text-anchor="middle" font-size="10" fill="#fff">Breakfast</text>

            <!-- Lunch (30%) -->
            <path d="M 56.57,-56.57 A 80,80 0 0,1 56.57,56.57 L 0,0 Z" fill="#4ecdc4" opacity="0.8"/>
            <text x="50" y="0" text-anchor="middle" font-size="12" fill="#fff" font-weight="bold">30%</text>
            <text x="50" y="15" text-anchor="middle" font-size="10" fill="#fff">Lunch</text>

            <!-- Dinner (35%) -->
            <path d="M 56.57,56.57 A 80,80 0 0,1 -56.57,56.57 L 0,0 Z" fill="#45b7d1" opacity="0.8"/>
            <text x="0" y="50" text-anchor="middle" font-size="12" fill="#fff" font-weight="bold">35%</text>
            <text x="0" y="65" text-anchor="middle" font-size="10" fill="#fff">Dinner</text>

            <!-- Snacks (10%) -->
            <path d="M -56.57,56.57 A 80,80 0 0,1 0,-80 L 0,0 Z" fill="#96ceb4" opacity="0.8"/>
            <text x="-30" y="-20" text-anchor="middle" font-size="12" fill="#fff" font-weight="bold">10%</text>
            <text x="-30" y="-5" text-anchor="middle" font-size="10" fill="#fff">Snacks</text>
        </g>

        <!-- Legend -->
        <text x="50" y="260" font-size="12" fill="#333">Based on 2000-calorie diet</text>
        <text x="50" y="280" font-size="10" fill="#666">Breakfast: 500 cal | Lunch: 600 cal | Dinner: 700 cal | Snacks: 200 cal</text>
    </svg>
</div>

<h3>Healthy Recipe Examples</h3>
<p>Here are some balanced recipe examples with their nutritional breakdowns:</p>

<table class="cinfoT" style="margin: 20px 0;">
    <tbody>
        <tr>
            <td class="cinfoHd">Recipe</td>
            <td class="cinfoHd">Calories per Serving</td>
            <td class="cinfoHd">Protein (g)</td>
            <td class="cinfoHd">Carbs (g)</td>
            <td class="cinfoHd">Fat (g)</td>
            <td class="cinfoHd">Fiber (g)</td>
        </tr>
        <tr>
            <td><strong>Grilled Chicken Salad</strong><br><small>Mixed greens, chicken breast, avocado, olive oil dressing</small></td>
            <td class="cinfoBodL">320</td>
            <td class="cinfoBodL">28</td>
            <td class="cinfoBodL">12</td>
            <td class="cinfoBodL">18</td>
            <td class="cinfoBodL">8</td>
        </tr>
        <tr>
            <td><strong>Quinoa Buddha Bowl</strong><br><small>Quinoa, roasted vegetables, chickpeas, tahini dressing</small></td>
            <td class="cinfoBodL">420</td>
            <td class="cinfoBodL">16</td>
            <td class="cinfoBodL">58</td>
            <td class="cinfoBodL">14</td>
            <td class="cinfoBodL">12</td>
        </tr>
        <tr>
            <td><strong>Baked Salmon with Sweet Potato</strong><br><small>Salmon fillet, roasted sweet potato, steamed broccoli</small></td>
            <td class="cinfoBodL">380</td>
            <td class="cinfoBodL">32</td>
            <td class="cinfoBodL">28</td>
            <td class="cinfoBodL">16</td>
            <td class="cinfoBodL">6</td>
        </tr>
        <tr>
            <td><strong>Vegetable Stir-Fry</strong><br><small>Mixed vegetables, tofu, brown rice, sesame oil</small></td>
            <td class="cinfoBodL">350</td>
            <td class="cinfoBodL">18</td>
            <td class="cinfoBodL">45</td>
            <td class="cinfoBodL">12</td>
            <td class="cinfoBodL">9</td>
        </tr>
        <tr>
            <td><strong>Greek Yogurt Parfait</strong><br><small>Greek yogurt, berries, granola, honey</small></td>
            <td class="cinfoBodL">280</td>
            <td class="cinfoBodL">20</td>
            <td class="cinfoBodL">35</td>
            <td class="cinfoBodL">8</td>
            <td class="cinfoBodL">5</td>
        </tr>
    </tbody>
</table>

<p><strong>Remember:</strong> These are general guidelines. Your individual calorie needs may vary based on age, gender, activity level, and health goals. Consult with a healthcare provider or registered dietitian for personalized nutrition advice.</p>
