jQuery(document).ready(function($) {
    
    // Tab switching functionality
    $('#topmenu a').on('click', function(e) {
        e.preventDefault();
        
        var tab = $(this).data('tab');
        
        // Update active tab
        $('#topmenu li').removeClass('menuon').removeAttr('id');
        $(this).parent().addClass('menuon').attr('id', 'menuon');
        
        // Show/hide appropriate input sections
        $('#calinputtime, #calinputdistance, #calinputpace').hide();
        
        switch(tab) {
            case 'pace':
                $('#calinputtime, #calinputdistance').show();
                break;
            case 'time':
                $('#calinputpace, #calinputdistance').show();
                break;
            case 'distance':
                $('#calinputpace, #calinputtime').show();
                break;
        }
        
        // Update pace unit hint
        updatePaceUnitHint();
    });
    
    // Update pace unit hint based on selected unit
    function updatePaceUnitHint() {
        var selectedUnit = $('#cpaceunit').val();
        var hint = 'hh:mm:ss';
        
        if (['mph', 'kph', 'mpm', 'mps', 'ypm', 'yps'].includes(selectedUnit)) {
            hint = 'number';
        }
        
        $('#cpaceunithint').text(hint);
    }
    
    $('#cpaceunit').on('change', updatePaceUnitHint);
    
    // Event picker functionality
    $('#cpickevent').on('change', function() {
        var event = $(this).val();
        var distances = {
            'M': {value: 42.195, unit: 'Kilometers'},
            'HM': {value: 21.0975, unit: 'Kilometers'},
            '1K': {value: 1, unit: 'Kilometers'},
            '5K': {value: 5, unit: 'Kilometers'},
            '10K': {value: 10, unit: 'Kilometers'},
            '1M': {value: 1, unit: 'Miles'},
            '5M': {value: 5, unit: 'Miles'},
            '10M': {value: 10, unit: 'Miles'},
            '800m': {value: 800, unit: 'Meters'},
            '1500m': {value: 1500, unit: 'Meters'}
        };
        
        if (event && distances[event]) {
            $('#cdistance').val(distances[event].value);
            $('#cdistanceunit').val(distances[event].unit);
        }
    });
    
    // Main calculator form submission
    $('#pace-calculator-form').on('submit', function(e) {
        e.preventDefault();
        
        var activeTab = $('#topmenu #menuon a').data('tab');
        var formData = {
            action: 'pace_calculation',
            calculation_type: activeTab,
            nonce: pace_calculator_ajax.nonce
        };
        
        // Collect form data based on active tab
        switch(activeTab) {
            case 'pace':
                formData.time = $('#ctime').val();
                formData.distance = $('#cdistance').val();
                formData.distance_unit = $('#cdistanceunit').val();
                break;
            case 'time':
                formData.pace = $('#cpace').val();
                formData.pace_unit = $('#cpaceunit').val();
                formData.distance = $('#cdistance').val();
                formData.distance_unit = $('#cdistanceunit').val();
                break;
            case 'distance':
                formData.pace = $('#cpace').val();
                formData.pace_unit = $('#cpaceunit').val();
                formData.time = $('#ctime').val();
                break;
        }
        
        // Show loading
        $('#calculation-results').show();
        $('#results-content').html('<p>Calculating...</p>');
        
        // AJAX request
        $.post(pace_calculator_ajax.ajax_url, formData, function(response) {
            if (response.success) {
                displayResults(response.data, activeTab);
            } else {
                $('#results-content').html('<p style="color: red;">Error: ' + response.data + '</p>');
            }
        }).fail(function() {
            $('#results-content').html('<p style="color: red;">Calculation failed. Please try again.</p>');
        });
    });
    
    // Display calculation results
    function displayResults(data, type) {
        var html = '';
        
        switch(type) {
            case 'pace':
                html = '<p><strong>Pace per kilometer:</strong> ' + data.pace_per_km + '</p>';
                html += '<p><strong>Pace per mile:</strong> ' + data.pace_per_mile + '</p>';
                html += '<p><strong>Speed:</strong> ' + data.speed_kph + ' km/h (' + data.speed_mph + ' mph)</p>';
                break;
            case 'time':
                html = '<p><strong>Total time:</strong> ' + data.total_time + '</p>';
                break;
            case 'distance':
                html = '<p><strong>Distance:</strong></p>';
                html += '<ul>';
                html += '<li>' + data.distance_km + ' kilometers</li>';
                html += '<li>' + data.distance_miles + ' miles</li>';
                html += '<li>' + data.distance_meters + ' meters</li>';
                html += '<li>' + data.distance_yards + ' yards</li>';
                html += '</ul>';
                break;
        }
        
        $('#results-content').html(html);
    }
    
    // Clear button functionality
    $('#clear-btn').on('click', function() {
        $('#pace-calculator-form')[0].reset();
        $('#calculation-results').hide();
        $('#cpickevent').val('');
    });
    
    // Multipoint calculator
    $('#multipoint-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = {
            action: 'pace_calculation',
            calculation_type: 'multipoint',
            nonce: pace_calculator_ajax.nonce
        };
        
        // Collect all point data
        for (var i = 1; i <= 12; i++) {
            formData['distance' + i] = $('input[name="cmpdistance' + i + '"]').val();
            formData['distance_unit' + i] = $('select[name="cmpdistanceunit' + i + '"]').val();
            formData['time' + i] = $('input[name="cmptime' + i + '"]').val();
        }
        formData.pace_unit = $('select[name="cmppaceunit"]').val();
        
        // Show loading
        $('#multipoint-results').show();
        $('#multipoint-results-content').html('<p>Calculating...</p>');
        
        // AJAX request
        $.post(pace_calculator_ajax.ajax_url, formData, function(response) {
            if (response.success) {
                displayMultipointResults(response.data);
            } else {
                $('#multipoint-results-content').html('<p style="color: red;">Error: ' + response.data + '</p>');
            }
        }).fail(function() {
            $('#multipoint-results-content').html('<p style="color: red;">Calculation failed. Please try again.</p>');
        });
    });
    
    // Display multipoint results
    function displayMultipointResults(data) {
        var html = '<table class="panel"><tbody>';
        html += '<tr><td><strong>Segment</strong></td><td><strong>Distance</strong></td><td><strong>Time</strong></td><td><strong>Pace</strong></td></tr>';
        
        data.forEach(function(segment) {
            html += '<tr>';
            html += '<td>' + segment.segment + '</td>';
            html += '<td>' + segment.distance + '</td>';
            html += '<td>' + segment.time + '</td>';
            html += '<td>' + segment.pace + '</td>';
            html += '</tr>';
        });
        
        html += '</tbody></table>';
        $('#multipoint-results-content').html(html);
    }
    
    // Multipoint clear button
    $('#multipoint-clear').on('click', function() {
        $('#multipoint-form')[0].reset();
        $('#multipoint-results').hide();
        
        // Reset default values
        for (var i = 1; i <= 5; i++) {
            $('input[name="cmpdistance' + i + '"]').val(i);
        }
        var defaultTimes = ['', '3:25', '6:55', '10:25', '14:01', '17:25'];
        defaultTimes.forEach(function(time, index) {
            if (index > 0) {
                $('input[name="cmptime' + index + '"]').val(time);
            }
        });
    });
    
    // Pace converter
    $('#pace-converter-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = {
            action: 'pace_calculation',
            calculation_type: 'converter',
            pace: $('#ccfrom').val(),
            from_unit: $('#ccpaceunitfrom').val(),
            to_unit: $('#ccpaceunitto').val(),
            nonce: pace_calculator_ajax.nonce
        };
        
        // Show loading
        $('#converter-results').show();
        $('#converter-results-content').html('<p>Converting...</p>');
        
        // AJAX request
        $.post(pace_calculator_ajax.ajax_url, formData, function(response) {
            if (response.success) {
                $('#converter-results-content').html('<p><strong>Result:</strong> ' + response.data.converted_pace + '</p>');
            } else {
                $('#converter-results-content').html('<p style="color: red;">Error: ' + response.data + '</p>');
            }
        }).fail(function() {
            $('#converter-results-content').html('<p style="color: red;">Conversion failed. Please try again.</p>');
        });
    });
    
    // Converter clear button
    $('#converter-clear').on('click', function() {
        $('#pace-converter-form')[0].reset();
        $('#converter-results').hide();
        $('#ccfrom').val('5:30');
        $('#ccpaceunitfrom').val('tpm');
        $('#ccpaceunitto').val('tpk');
    });
    
    // Finish time calculator
    $('#finish-time-form').on('submit', function(e) {
        e.preventDefault();
        
        var formData = {
            action: 'pace_calculation',
            calculation_type: 'finish_time',
            current_distance: $('#cftdistancenow').val(),
            current_distance_unit: $('select[name="cftdistancenowunit"]').val(),
            elapsed_time: $('#cfttime').val(),
            full_distance: $('#cftdistancefull').val(),
            full_distance_unit: $('select[name="cftdistancefullunit"]').val(),
            nonce: pace_calculator_ajax.nonce
        };
        
        // Show loading
        $('#finish-time-results').show();
        $('#finish-time-results-content').html('<p>Calculating...</p>');
        
        // AJAX request
        $.post(pace_calculator_ajax.ajax_url, formData, function(response) {
            if (response.success) {
                var html = '<p><strong>Estimated finish time:</strong> ' + response.data.finish_time + '</p>';
                html += '<p><strong>Remaining time:</strong> ' + response.data.remaining_time + '</p>';
                html += '<p><strong>Current pace:</strong> ' + response.data.current_pace + ' per km</p>';
                $('#finish-time-results-content').html(html);
            } else {
                $('#finish-time-results-content').html('<p style="color: red;">Error: ' + response.data + '</p>');
            }
        }).fail(function() {
            $('#finish-time-results-content').html('<p style="color: red;">Calculation failed. Please try again.</p>');
        });
    });
    
    // Finish time clear button
    $('#finish-time-clear').on('click', function() {
        $('#finish-time-form')[0].reset();
        $('#finish-time-results').hide();
        $('#cftdistancenow').val('1');
        $('#cfttime').val('6:15');
        $('#cftdistancefull').val('5');
    });
    
    // Search functionality
    $('#bluebtn').on('click', function() {
        var searchTerm = $('#calcSearchTerm').val();
        if (searchTerm.trim()) {
            // Simple search implementation - you can enhance this
            var results = searchCalculators(searchTerm);
            displaySearchResults(results);
        }
    });
    
    // Simple calculator search function
    function searchCalculators(term) {
        var calculators = [
            {name: 'BMI Calculator', url: '/bmi-calculator/'},
            {name: 'Calorie Calculator', url: '/calorie-calculator/'},
            {name: 'Body Fat Calculator', url: '/body-fat-calculator/'},
            {name: 'BMR Calculator', url: '/bmr-calculator/'},
            {name: 'Macro Calculator', url: '/macro-calculator/'},
            {name: 'Ideal Weight Calculator', url: '/ideal-weight-calculator/'},
            {name: 'Pace Calculator', url: '/pace-calculator/'}
        ];
        
        return calculators.filter(function(calc) {
            return calc.name.toLowerCase().includes(term.toLowerCase());
        });
    }
    
    // Display search results
    function displaySearchResults(results) {
        var html = '';
        if (results.length > 0) {
            results.forEach(function(calc) {
                html += '<a href="' + calc.url + '" style="display: block; margin: 2px 0;">' + calc.name + '</a>';
            });
        } else {
            html = '<p>No calculators found.</p>';
        }
        $('#calcSearchOut').html(html);
    }
    
    // Initialize default state
    updatePaceUnitHint();
    
});
