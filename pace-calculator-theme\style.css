/*
Theme Name: Pace Calculator Theme
Description: A WordPress theme that replicates the pace calculator design and functionality
Version: 1.0
Author: Custom Theme
*/

@charset "utf-8";

/* Exact replica of original CSS from calculator.net */
body, p, td, div, span, input, th, li, textarea {
    font-family: arial, helvetica, sans-serif;
    font-size: 16px;
    color: rgb(0, 0, 0);
}

body {
    background: rgb(255, 255, 255);
    margin: 0px;
    padding: 0px;
    border: 0px;
    text-align: center;
}

p {
    margin: 5px 0px 8px;
}

img {
    border: 0px;
}

h1 {
    color: rgb(0, 51, 102);
    font-size: 26px;
    font-weight: bold;
    padding: 0px;
    margin: 12px 0px;
}

h2 {
    font-size: 22px;
    font-weight: bold;
    color: rgb(0, 51, 102);
    padding: 0px;
    margin-bottom: 2px;
}

h3 {
    font-size: 19px;
    font-weight: bold;
}

hr {
    border: 0px;
    color: rgb(170, 170, 170);
    background-color: rgb(170, 170, 170);
    height: 1px;
}

a {
    color: rgb(0, 102, 153);
    text-decoration: underline;
}

a:hover {
    text-decoration: none;
}

input {
    padding: 5px;
    color: rgb(0, 0, 0);
    box-sizing: border-box;
}

select {
    padding: 4px;
    color: rgb(0, 0, 0);
    box-sizing: border-box;
}

option {
    font-size: 16px;
}

input[type="text"], input[type="url"], input[type="tel"], input[type="number"], input[type="color"], input[type="date"], input[type="email"], select {
    border: 1px solid rgb(4, 66, 132);
    border-radius: 2px;
    box-shadow: rgb(102, 102, 102) 1px 1px 2px;
    font-size: 16px;
    background-color: rgb(255, 255, 255);
}

input[type="submit"] {
    border: 0px;
    color: rgb(255, 255, 255);
    padding: 11px 50px 11px 16px;
    font-size: 16px;
    font-weight: bold;
    background-color: rgb(76, 123, 37);
    background-image: url("data:image/svg+xml;utf8,<svg xmlns=\"http://www.w3.org/2000/svg\" width=\"180px\" height=\"40px\"><circle cx=\"112\" cy=\"20\" r=\"11\" fill=\"darkseagreen\" /><path d=\"M110 12 L120 20 L110 28 Z\" fill=\"white\" /></svg>");
    background-repeat: no-repeat;
}

input[type="submit"]:hover {
    background-color: rgb(68, 68, 68);
}

input[type="reset"], input[type="button"] {
    border: 0px;
    color: rgb(255, 255, 255);
    padding: 11px 8px;
    font-size: 16px;
    background: rgb(171, 171, 171);
}

input[type="reset"]:hover, input[type="button"]:hover {
    background: rgb(68, 68, 68);
}

.innormal { width: 90px; }
.in4char { width: 58px; }
.in3char { width: 46px; }
.in2char { width: 35px; }
.inlong { width: 120px; }
.inlonger { width: 170px; }
.inlongest { width: 230px; }
.inlongesthalf { width: 112px; }
.infull { width: 226px; }
.inhalf { width: 110px; }
.infulltxarea { width: 600px; padding: 8px; }
.inshortfull { width: 170px; }
.inshorthalf { width: 82px; }

.scaleimg { max-width: 100%; height: auto; }

#headerout {
    background: rgb(0, 51, 102);
    text-align: center;
}

#header {
    width: 1100px;
    height: 60px;
    background: rgb(0, 51, 102);
    padding: 0px;
    margin-left: auto;
    margin-right: auto;
    text-align: left;
    overflow: hidden;
}

#logo {
    padding: 18px 0px;
    width: 270px;
    float: left;
}

#login {
    padding: 2px;
    float: right;
    color: rgb(204, 204, 204);
}

#login a {
    color: rgb(204, 204, 204);
    text-decoration: none;
}

#login a:hover {
    text-decoration: underline;
}

.topNavAbs {
    position: absolute;
    top: 21px;
    left: 50%;
    width: 520px;
    margin-left: -80px;
    text-align: left;
}

.topNavAbs a {
    color: white;
    padding: 10px 16px;
    border: none;
    cursor: pointer;
    font-size: 16px;
    text-transform: uppercase;
    display: inline-block;
    text-decoration: none;
}

.topNavAbs a:hover {
    background-color: rgb(81, 132, 40);
}

.topNavOn {
    background-color: rgb(81, 132, 40);
}

#contentout {
    width: 1100px;
    padding-top: 5px;
    margin-left: auto;
    margin-right: auto;
    text-align: left;
    overflow: auto;
}

#content {
    padding: 0px 0px 15px;
    width: 728px;
    float: left;
}

#right {
    width: 336px;
    float: right;
    text-align: center;
}

#contentbig {
    padding: 0px 0px 15px;
    width: 843px;
    float: right;
}

#footer {
    background: rgb(225, 225, 225);
    padding: 25px 0px;
    font-size: 13px;
    color: rgb(85, 85, 85);
    text-align: center;
}

#footer a {
    color: rgb(68, 68, 68);
}

#footer a:hover {
    text-decoration: none;
}

#footerin {
    width: 1100px;
    margin-left: auto;
    margin-right: auto;
    text-align: left;
    overflow: auto;
    color: rgb(85, 85, 85);
}

#footernav {
    text-align: center;
}

#breadcrumbs, #breadcrumbs span {
    font-size: 13px;
}

#breadcrumbs a, #breadcrumbs a span {
    text-decoration: none;
    color: rgb(0, 102, 153);
}

#breadcrumbs a:hover, #breadcrumbs a span:hover {
    text-decoration: underline;
}

#othercalc {
    border: 1px solid rgb(51, 102, 153);
    margin: auto;
    text-align: left;
    width: 332px;
}

#octitle {
    background-color: rgb(51, 102, 153);
    padding: 6px;
    color: rgb(255, 255, 255);
    font-size: 18px;
    font-weight: bold;
}

#octitle a {
    color: rgb(255, 255, 255);
    text-decoration: none;
}

#octitle a:hover {
    text-decoration: underline;
}

#occontent {
    padding: 3px 6px;
    font-size: 14px;
}

#occontent a {
    display: inline-block;
    width: 158px;
    padding: 3px 0px;
}

#ocother {
    background-color: rgb(221, 221, 221);
    padding: 6px;
    text-align: center;
    font-size: 15px;
    color: rgb(187, 187, 187);
}

.smalltext {
    font-size: 13px;
}

.bigtext {
    font-size: 18px;
}

.verybigtext {
    font-size: 23px;
}

table.cinfoT {
    border-collapse: collapse;
    border-spacing: 0px;
    margin-top: 0px;
}

table.cinfoT th, table.cinfoT td.cinfoHd, table.cinfoT td.cinfoHdL {
    border-width: 1px;
    border-style: solid;
    border-color: rgb(17, 68, 119) rgb(17, 68, 119) rgb(51, 102, 153);
    background-color: rgb(51, 102, 153);
    font-weight: bold;
    color: rgb(255, 255, 255);
    padding: 5px 3px;
}

table.cinfoT td {
    border: 1px solid rgb(204, 204, 204);
    color: rgb(0, 0, 0);
    padding: 3px;
}

table.cinfoT tr:nth-child(2n+1) {
    background-color: rgb(238, 238, 238);
}

table.cinfoT tr:nth-child(2n) {
    background-color: rgb(255, 255, 255);
}

#topmenu ul {
    color: rgb(0, 0, 0);
    border-bottom: 1px solid rgb(187, 187, 187);
    margin: 12px 0px 0px;
    padding: 0px 0px 8px;
    font-size: 15px;
    font-weight: bold;
}

#topmenu ul li {
    display: inline;
    overflow: hidden;
    list-style-type: none;
    margin-left: 0px;
}

#topmenu ul li a, #topmenu ul li a:visited {
    color: rgb(255, 255, 255);
    background: rgb(51, 102, 153);
    border: 1px solid rgb(51, 102, 153);
    padding: 8px 5px;
    margin: 0px;
    text-decoration: none;
}

#topmenu ul li a:hover {
    background: rgb(238, 238, 238);
    color: rgb(0, 0, 0);
}

#topmenu ul #menuon a {
    color: rgb(0, 0, 0);
    background: rgb(238, 238, 238);
    border-width: 1px 1px 2px;
    border-style: solid;
    border-color: rgb(187, 187, 187) rgb(187, 187, 187) rgb(238, 238, 238);
    border-image: initial;
    padding: 8px 5px;
    margin: 0px;
    text-decoration: none;
}

#topmenu ul #menuon a:hover {
    background: rgb(238, 238, 238);
}

.panel {
    background: rgb(238, 238, 238);
    border: 1px solid rgb(187, 187, 187);
    padding: 5px;
}

.panel2 {
    background-color: rgb(238, 238, 238);
    padding: 5px;
    border-right: 1px solid rgb(187, 187, 187);
    border-bottom: 1px solid rgb(187, 187, 187);
    border-left: 1px solid rgb(187, 187, 187);
}

#searchbox {
    padding-top: 16px;
}

#bluebtn {
    border-radius: 1px;
    background: rgb(51, 102, 153);
    padding: 5px 8px;
    font-size: 18px;
    color: rgb(255, 255, 255);
}

#bluebtn:hover {
    background: rgb(68, 68, 68);
    color: rgb(255, 255, 255);
}

#calcSearchOut {
    padding: 5px;
}

#calcSearchOut div {
    padding: 5px;
    text-align: left;
}

#clear {
    margin-left: auto;
    margin-right: auto;
    clear: both;
    height: 0px;
}

@media (max-width: 1140px) {
    #header {
        width: 990px;
        padding-left: 8px;
    }
    #contentout {
        width: 1000px;
    }
    #content {
        width: 640px;
        float: left;
        padding-left: 10px;
    }
    #footerin {
        width: 990px;
    }
}

@media (max-width: 720px) {
    #header {
        width: auto;
        padding: 0px 8px;
    }
    #contentout {
        width: auto;
        padding: 8px;
    }
    #content {
        float: none;
        width: auto;
        padding: 0px;
    }
    #right {
        width: auto;
        float: none;
    }
    #footerin {
        width: auto;
    }
    .topNavAbs {
        display: none;
    }
}

@media (max-width: 650px) {
    img {
        max-width: 100%;
        height: auto;
    }
}

[style*="display: none"] {
    display: none !important;
}

[style*="display: block"] {
    display: block !important;
}
