<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.tax-interface {
    background: linear-gradient(135deg, #1976d2 0%, #0d47a1 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.income-input {
    background-color: #f8f9fa;
    border: 2px solid #1976d2;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.tax-breakdown {
    background: linear-gradient(135deg, #4caf50 0%, #2e7d32 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.tax-item {
    background: white;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
    color: #2c3e50;
    display: flex;
    justify-content: space-between;
    align-items: center;
}
.country-selector {
    background-color: #e3f2fd;
    border: 2px solid #1976d2;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.tax-rate {
    display: inline-block;
    padding: 6px 12px;
    border-radius: 20px;
    font-size: 12px;
    font-weight: bold;
    margin: 3px;
    color: white;
}
.rate-low { background-color: #4caf50; }
.rate-medium { background-color: #ff9800; }
.rate-high { background-color: #f44336; }
.net-income {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.deduction-section {
    background-color: #f3e5f5;
    border: 2px solid #9c27b0;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.comparison-chart {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
</style>

<h1>Neuvoo Tax Calculator</h1>
<p>Calculate your income tax, social security contributions, and net salary with our comprehensive tax calculator. Compare tax rates across different countries and income levels to understand your tax obligations and take-home pay.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="taxform" id="tax-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Tax Calculator</a></li>
                <li><a href="#" data-tab="comparison">Country Comparison</a></li>
                <li><a href="#" data-tab="deductions">Deductions Guide</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="tax-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">💰 Income Tax Calculator</h3>
                
                <div class="country-selector">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🌍 Select Country/Region</h4>
                    <select id="country" class="innormal" style="width: 100%; padding: 10px;">
                        <option value="us">United States</option>
                        <option value="uk">United Kingdom</option>
                        <option value="canada">Canada</option>
                        <option value="australia">Australia</option>
                        <option value="germany">Germany</option>
                        <option value="france">France</option>
                        <option value="netherlands">Netherlands</option>
                        <option value="sweden">Sweden</option>
                        <option value="norway">Norway</option>
                        <option value="denmark">Denmark</option>
                        <option value="switzerland">Switzerland</option>
                        <option value="singapore">Singapore</option>
                        <option value="japan">Japan</option>
                        <option value="south_korea">South Korea</option>
                        <option value="new_zealand">New Zealand</option>
                    </select>
                </div>
            </div>
            
            <div class="income-input">
                <h4 style="color: #2c3e50; margin-bottom: 20px;">💵 Income Information</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px; margin-bottom: 20px;">
                    <div>
                        <label for="annual-income"><strong>Annual Gross Income:</strong></label>
                        <input type="number" id="annual-income" value="50000" min="0" max="10000000" class="innormal" style="width: 100%; margin-top: 5px;">
                    </div>
                    
                    <div>
                        <label for="currency"><strong>Currency:</strong></label>
                        <select id="currency" class="innormal" style="width: 100%; margin-top: 5px;">
                            <option value="USD">USD ($)</option>
                            <option value="GBP">GBP (£)</option>
                            <option value="EUR">EUR (€)</option>
                            <option value="CAD">CAD ($)</option>
                            <option value="AUD">AUD ($)</option>
                            <option value="CHF">CHF</option>
                            <option value="SEK">SEK</option>
                            <option value="NOK">NOK</option>
                            <option value="DKK">DKK</option>
                            <option value="SGD">SGD</option>
                            <option value="JPY">JPY (¥)</option>
                            <option value="KRW">KRW (₩)</option>
                            <option value="NZD">NZD ($)</option>
                        </select>
                    </div>
                </div>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <label for="filing-status"><strong>Filing Status:</strong></label>
                        <select id="filing-status" class="innormal" style="width: 100%; margin-top: 5px;">
                            <option value="single">Single</option>
                            <option value="married">Married Filing Jointly</option>
                            <option value="married_separate">Married Filing Separately</option>
                            <option value="head">Head of Household</option>
                        </select>
                    </div>
                    
                    <div>
                        <label for="age"><strong>Age:</strong></label>
                        <input type="number" id="age" value="30" min="16" max="100" class="innormal" style="width: 100%; margin-top: 5px;">
                    </div>
                </div>
            </div>
            
            <div class="deduction-section">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">📋 Deductions & Credits</h4>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div>
                        <label for="standard-deduction">
                            <input type="checkbox" id="standard-deduction" checked style="margin-right: 8px;">
                            <strong>Use Standard Deduction</strong>
                        </label>
                        <div style="margin-top: 10px;">
                            <label for="itemized-deductions"><strong>Itemized Deductions:</strong></label>
                            <input type="number" id="itemized-deductions" value="0" min="0" class="innormal" style="width: 100%; margin-top: 5px;" disabled>
                        </div>
                    </div>
                    
                    <div>
                        <label for="dependents"><strong>Number of Dependents:</strong></label>
                        <input type="number" id="dependents" value="0" min="0" max="20" class="innormal" style="width: 100%; margin-top: 5px;">
                        
                        <div style="margin-top: 15px;">
                            <label for="retirement-contrib"><strong>401(k)/Pension Contributions:</strong></label>
                            <input type="number" id="retirement-contrib" value="0" min="0" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Taxes" id="calculate-tax-btn" style="padding: 15px 30px; font-size: 18px; background-color: #1976d2; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Reset Calculator" id="clear-tax-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Disclaimer:</strong> This calculator provides estimates based on general tax rules. Actual taxes may vary. Consult a tax professional for accurate calculations and advice.
        </div>
        
        <div id="tax-results" style="display: none; margin-top: 30px;">
            <div class="net-income">
                <h3 style="margin-bottom: 20px;">💸 Your Tax Calculation Results</h3>
                <div id="tax-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-tax-btn');
    const clearBtn = document.getElementById('clear-tax-btn');
    const resultsDiv = document.getElementById('tax-results');
    const resultsContent = document.getElementById('tax-results-content');
    const standardDeductionCheckbox = document.getElementById('standard-deduction');
    const itemizedDeductionsInput = document.getElementById('itemized-deductions');
    
    // Tax brackets and rates for different countries (simplified)
    const taxData = {
        us: {
            name: 'United States',
            brackets: [
                { min: 0, max: 10275, rate: 0.10 },
                { min: 10275, max: 41775, rate: 0.12 },
                { min: 41775, max: 89450, rate: 0.22 },
                { min: 89450, max: 190750, rate: 0.24 },
                { min: 190750, max: 364200, rate: 0.32 },
                { min: 364200, max: 462500, rate: 0.35 },
                { min: 462500, max: Infinity, rate: 0.37 }
            ],
            standardDeduction: 13850,
            socialSecurity: 0.062,
            medicare: 0.0145,
            currency: 'USD'
        },
        uk: {
            name: 'United Kingdom',
            brackets: [
                { min: 0, max: 12570, rate: 0.00 },
                { min: 12570, max: 50270, rate: 0.20 },
                { min: 50270, max: 125140, rate: 0.40 },
                { min: 125140, max: Infinity, rate: 0.45 }
            ],
            standardDeduction: 12570,
            nationalInsurance: 0.12,
            currency: 'GBP'
        },
        canada: {
            name: 'Canada',
            brackets: [
                { min: 0, max: 53359, rate: 0.15 },
                { min: 53359, max: 106717, rate: 0.205 },
                { min: 106717, max: 165430, rate: 0.26 },
                { min: 165430, max: 235675, rate: 0.29 },
                { min: 235675, max: Infinity, rate: 0.33 }
            ],
            standardDeduction: 15000,
            cpp: 0.0595,
            ei: 0.0163,
            currency: 'CAD'
        },
        germany: {
            name: 'Germany',
            brackets: [
                { min: 0, max: 10908, rate: 0.00 },
                { min: 10908, max: 62810, rate: 0.14 },
                { min: 62810, max: 277826, rate: 0.42 },
                { min: 277826, max: Infinity, rate: 0.45 }
            ],
            standardDeduction: 10908,
            socialContributions: 0.20,
            currency: 'EUR'
        }
    };
    
    // Handle standard deduction checkbox
    standardDeductionCheckbox.addEventListener('change', function() {
        itemizedDeductionsInput.disabled = this.checked;
        if (this.checked) {
            itemizedDeductionsInput.value = 0;
        }
    });
    
    calculateBtn.addEventListener('click', function() {
        const country = document.getElementById('country').value;
        const annualIncome = parseFloat(document.getElementById('annual-income').value) || 0;
        const currency = document.getElementById('currency').value;
        const filingStatus = document.getElementById('filing-status').value;
        const age = parseInt(document.getElementById('age').value) || 30;
        const dependents = parseInt(document.getElementById('dependents').value) || 0;
        const retirementContrib = parseFloat(document.getElementById('retirement-contrib').value) || 0;
        const useStandardDeduction = standardDeductionCheckbox.checked;
        const itemizedDeductions = parseFloat(itemizedDeductionsInput.value) || 0;
        
        if (annualIncome <= 0) {
            alert('Please enter a valid annual income.');
            return;
        }
        
        const taxCalculation = calculateTaxes(country, annualIncome, {
            currency,
            filingStatus,
            age,
            dependents,
            retirementContrib,
            useStandardDeduction,
            itemizedDeductions
        });
        
        displayResults(taxCalculation);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('tax-calculator-form').reset();
        standardDeductionCheckbox.checked = true;
        itemizedDeductionsInput.disabled = true;
        resultsDiv.style.display = 'none';
    });
    
    function calculateTaxes(countryCode, grossIncome, options) {
        const countryData = taxData[countryCode] || taxData.us;
        
        // Calculate deductions
        let totalDeductions = 0;
        if (options.useStandardDeduction) {
            totalDeductions = countryData.standardDeduction || 0;
        } else {
            totalDeductions = options.itemizedDeductions;
        }
        
        // Add retirement contributions
        totalDeductions += options.retirementContrib;
        
        // Add dependent deductions (simplified)
        if (countryCode === 'us') {
            totalDeductions += options.dependents * 4300; // Child tax credit equivalent
        }
        
        // Calculate taxable income
        const taxableIncome = Math.max(0, grossIncome - totalDeductions);
        
        // Calculate income tax using brackets
        let incomeTax = 0;
        let currentIncome = taxableIncome;
        
        for (const bracket of countryData.brackets) {
            if (currentIncome <= 0) break;
            
            const taxableInBracket = Math.min(currentIncome, bracket.max - bracket.min);
            incomeTax += taxableInBracket * bracket.rate;
            currentIncome -= taxableInBracket;
        }
        
        // Calculate social security/payroll taxes
        let payrollTaxes = 0;
        if (countryData.socialSecurity) {
            payrollTaxes += grossIncome * countryData.socialSecurity;
        }
        if (countryData.medicare) {
            payrollTaxes += grossIncome * countryData.medicare;
        }
        if (countryData.nationalInsurance) {
            payrollTaxes += Math.max(0, grossIncome - 12570) * countryData.nationalInsurance;
        }
        if (countryData.cpp) {
            payrollTaxes += grossIncome * countryData.cpp;
        }
        if (countryData.ei) {
            payrollTaxes += grossIncome * countryData.ei;
        }
        if (countryData.socialContributions) {
            payrollTaxes += grossIncome * countryData.socialContributions;
        }
        
        const totalTax = incomeTax + payrollTaxes;
        const netIncome = grossIncome - totalTax;
        const effectiveRate = (totalTax / grossIncome) * 100;
        const marginalRate = getMarginalRate(taxableIncome, countryData.brackets) * 100;
        
        return {
            country: countryData.name,
            currency: options.currency,
            grossIncome,
            taxableIncome,
            totalDeductions,
            incomeTax,
            payrollTaxes,
            totalTax,
            netIncome,
            effectiveRate,
            marginalRate,
            monthlyNet: netIncome / 12,
            weeklyNet: netIncome / 52
        };
    }
    
    function getMarginalRate(income, brackets) {
        for (const bracket of brackets) {
            if (income >= bracket.min && income < bracket.max) {
                return bracket.rate;
            }
        }
        return brackets[brackets.length - 1].rate;
    }
    
    function displayResults(calc) {
        const currencySymbol = getCurrencySymbol(calc.currency);
        
        let html = `
            <div style="font-size: 36px; font-weight: bold; margin-bottom: 20px;">
                ${currencySymbol}${calc.netIncome.toLocaleString()} Net
            </div>
            
            <div style="font-size: 18px; margin-bottom: 25px;">
                Annual Net Income in ${calc.country}
            </div>
            
            <div class="tax-breakdown">
                <h4 style="margin-bottom: 20px;">📊 Tax Breakdown</h4>
                
                <div class="tax-item">
                    <span><strong>Gross Income:</strong></span>
                    <span>${currencySymbol}${calc.grossIncome.toLocaleString()}</span>
                </div>
                
                <div class="tax-item">
                    <span><strong>Total Deductions:</strong></span>
                    <span>-${currencySymbol}${calc.totalDeductions.toLocaleString()}</span>
                </div>
                
                <div class="tax-item">
                    <span><strong>Taxable Income:</strong></span>
                    <span>${currencySymbol}${calc.taxableIncome.toLocaleString()}</span>
                </div>
                
                <div class="tax-item">
                    <span><strong>Income Tax:</strong></span>
                    <span>-${currencySymbol}${calc.incomeTax.toLocaleString()}</span>
                </div>
                
                <div class="tax-item">
                    <span><strong>Payroll/Social Taxes:</strong></span>
                    <span>-${currencySymbol}${calc.payrollTaxes.toLocaleString()}</span>
                </div>
                
                <div class="tax-item" style="background-color: #ffebee; border: 2px solid #f44336;">
                    <span><strong>Total Tax:</strong></span>
                    <span style="color: #f44336; font-weight: bold;">-${currencySymbol}${calc.totalTax.toLocaleString()}</span>
                </div>
                
                <div class="tax-item" style="background-color: #e8f5e9; border: 2px solid #4caf50;">
                    <span><strong>Net Income:</strong></span>
                    <span style="color: #4caf50; font-weight: bold;">${currencySymbol}${calc.netIncome.toLocaleString()}</span>
                </div>
            </div>
            
            <div class="comparison-chart">
                <h4 style="color: #2c3e50; margin-bottom: 15px;">📈 Tax Rate Analysis</h4>
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div style="text-align: center;">
                        <h5>Effective Tax Rate</h5>
                        <div style="font-size: 24px; font-weight: bold; color: #1976d2;">
                            ${calc.effectiveRate.toFixed(1)}%
                        </div>
                        <div class="tax-rate ${calc.effectiveRate < 15 ? 'rate-low' : calc.effectiveRate < 25 ? 'rate-medium' : 'rate-high'}">
                            ${calc.effectiveRate < 15 ? 'Low' : calc.effectiveRate < 25 ? 'Medium' : 'High'} Rate
                        </div>
                    </div>
                    <div style="text-align: center;">
                        <h5>Marginal Tax Rate</h5>
                        <div style="font-size: 24px; font-weight: bold; color: #f44336;">
                            ${calc.marginalRate.toFixed(1)}%
                        </div>
                        <div style="font-size: 14px; color: #666;">
                            Rate on next dollar earned
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💰 Take-Home Pay Breakdown:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Monthly Net:</strong> ${currencySymbol}${calc.monthlyNet.toLocaleString()}</li>
                    <li><strong>Weekly Net:</strong> ${currencySymbol}${calc.weeklyNet.toLocaleString()}</li>
                    <li><strong>Daily Net:</strong> ${currencySymbol}${(calc.netIncome / 365).toLocaleString()}</li>
                    <li><strong>Hourly Net:</strong> ${currencySymbol}${(calc.netIncome / 2080).toLocaleString()} (40hr/week)</li>
                </ul>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.1); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 Tax Optimization Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li>Maximize retirement contributions to reduce taxable income</li>
                    <li>Consider itemizing deductions if they exceed standard deduction</li>
                    <li>Take advantage of tax credits for dependents and education</li>
                    <li>Plan major purchases and charitable donations strategically</li>
                    <li>Consult a tax professional for complex situations</li>
                    <li>Keep detailed records of all deductible expenses</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
    
    function getCurrencySymbol(currency) {
        const symbols = {
            USD: '$', GBP: '£', EUR: '€', CAD: '$', AUD: '$',
            CHF: 'CHF ', SEK: 'SEK ', NOK: 'NOK ', DKK: 'DKK ',
            SGD: 'S$', JPY: '¥', KRW: '₩', NZD: 'NZ$'
        };
        return symbols[currency] || '$';
    }
});
</script>
