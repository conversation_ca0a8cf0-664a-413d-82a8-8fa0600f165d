(function(stc){var l,aa=function(a){var b=0;return function(){return b<a.length?{done:!1,value:a[b++]}:{done:!0}}},ba=typeof Object.defineProperties=="function"?Object.defineProperty:function(a,b,c){if(a==Array.prototype||a==Object.prototype)return a;a[b]=c.value;return a},ca=function(a){a=["object"==typeof globalThis&&globalThis,a,"object"==typeof window&&window,"object"==typeof self&&self,"object"==typeof global&&global];for(var b=0;b<a.length;++b){var c=a[b];if(c&&c.Math==Math)return c}throw Error("Cannot find global object"); 
},da=ca(this),p=function(a,b){if(b)a:{var c=da;a=a.split(".");for(var d=0;d<a.length-1;d++){var e=a[d];if(!(e in c))break a;c=c[e]}a=a[a.length-1];d=c[a];b=b(d);b!=d&&b!=null&&ba(c,a,{configurable:!0,writable:!0,value:b})}}; 
p("Symbol",function(a){if(a)return a;var b=function(f,g){this.tc=f;ba(this,"description",{configurable:!0,writable:!0,value:g})};b.prototype.toString=function(){return this.tc};var c="jscomp_symbol_"+(Math.random()*1E9>>>0)+"_",d=0,e=function(f){if(this instanceof e)throw new TypeError("Symbol is not a constructor");return new b(c+(f||"")+"_"+d++,f)};return e}); 
p("Symbol.iterator",function(a){if(a)return a;a=Symbol("Symbol.iterator");for(var b="Array Int8Array Uint8Array Uint8ClampedArray Int16Array Uint16Array Int32Array Uint32Array Float32Array Float64Array".split(" "),c=0;c<b.length;c++){var d=da[b[c]];typeof d==="function"&&typeof d.prototype[a]!="function"&&ba(d.prototype,a,{configurable:!0,writable:!0,value:function(){return ea(aa(this))}})}return a}); 
var ea=function(a){a={next:a};a[Symbol.iterator]=function(){return this};return a},fa=typeof Object.create=="function"?Object.create:function(a){var b=function(){};b.prototype=a;return new b},ha=function(){function a(){function c(){}new c;Reflect.construct(c,[],function(){});return new c instanceof c}if(typeof Reflect!="undefined"&&Reflect.construct){if(a())return Reflect.construct;var b=Reflect.construct;return function(c,d,e){c=b(c,d);e&&Reflect.setPrototypeOf(c,e.prototype);return c}}return function(c, 
d,e){e===void 0&&(e=c);e=fa(e.prototype||Object.prototype);return Function.prototype.apply.call(c,e,d)||e}}(),ia;if(typeof Object.setPrototypeOf=="function")ia=Object.setPrototypeOf;else{var ja;a:{var ka={a:!0},la={};try{la.__proto__=ka;ja=la.a;break a}catch(a){}ja=!1}ia=ja?function(a,b){a.__proto__=b;if(a.__proto__!==b)throw new TypeError(a+" is not extensible");return a}:null} 
var ma=ia,t=function(a,b){a.prototype=fa(b.prototype);a.prototype.constructor=a;if(ma)ma(a,b);else for(var c in b)if(c!="prototype")if(Object.defineProperties){var d=Object.getOwnPropertyDescriptor(b,c);d&&Object.defineProperty(a,c,d)}else a[c]=b[c];a.ed=b.prototype},x=function(a){var b=typeof Symbol!="undefined"&&Symbol.iterator&&a[Symbol.iterator];if(b)return b.call(a);if(typeof a.length=="number")return{next:aa(a)};throw Error(String(a)+" is not an iterable or ArrayLike");},pa=function(a){if(!(a instanceof 
Array)){a=x(a);for(var b,c=[];!(b=a.next()).done;)c.push(b.value);a=c}return a},ra=function(a){return qa(a,a)},qa=function(a,b){a.raw=b;Object.freeze&&(Object.freeze(a),Object.freeze(b));return a},sa=function(){for(var a=Number(this),b=[],c=a;c<arguments.length;c++)b[c-a]=arguments[c];return b};p("globalThis",function(a){return a||da});p("Reflect",function(a){return a?a:{}});p("Reflect.construct",function(){return ha}); 
p("Reflect.setPrototypeOf",function(a){return a?a:ma?function(b,c){try{return ma(b,c),!0}catch(d){return!1}}:null});p("Object.setPrototypeOf",function(a){return a||ma});var ta=function(a,b){return Object.prototype.hasOwnProperty.call(a,b)},ua=typeof Object.assign=="function"?Object.assign:function(a,b){for(var c=1;c<arguments.length;c++){var d=arguments[c];if(d)for(var e in d)ta(d,e)&&(a[e]=d[e])}return a};p("Object.assign",function(a){return a||ua}); 
p("Array.prototype.find",function(a){return a?a:function(b,c){a:{var d=this;d instanceof String&&(d=String(d));for(var e=d.length,f=0;f<e;f++){var g=d[f];if(b.call(c,g,f,d)){b=g;break a}}b=void 0}return b}}); 
p("WeakMap",function(a){function b(){}function c(k){var m=typeof k;return m==="object"&&k!==null||m==="function"}function d(k){if(!ta(k,f)){var m=new b;ba(k,f,{value:m})}}function e(k){var m=Object[k];m&&(Object[k]=function(n){if(n instanceof b)return n;Object.isExtensible(n)&&d(n);return m(n)})}if(function(){if(!a||!Object.seal)return!1;try{var k=Object.seal({}),m=Object.seal({}),n=new a([[k,2],[m,3]]);if(n.get(k)!=2||n.get(m)!=3)return!1;n.delete(k);n.set(m,4);return!n.has(k)&&n.get(m)==4}catch(q){return!1}}())return a; 
var f="$jscomp_hidden_"+Math.random();e("freeze");e("preventExtensions");e("seal");var g=0,h=function(k){this.ta=(g+=Math.random()+1).toString();if(k){k=x(k);for(var m;!(m=k.next()).done;)m=m.value,this.set(m[0],m[1])}};h.prototype.set=function(k,m){if(!c(k))throw Error("Invalid WeakMap key");d(k);if(!ta(k,f))throw Error("WeakMap key fail: "+k);k[f][this.ta]=m;return this};h.prototype.get=function(k){return c(k)&&ta(k,f)?k[f][this.ta]:void 0};h.prototype.has=function(k){return c(k)&&ta(k,f)&&ta(k[f], 
this.ta)};h.prototype.delete=function(k){return c(k)&&ta(k,f)&&ta(k[f],this.ta)?delete k[f][this.ta]:!1};return h}); 
p("Map",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var h=Object.seal({x:4}),k=new a(x([[h,"s"]]));if(k.get(h)!="s"||k.size!=1||k.get({x:4})||k.set({x:4},"t")!=k||k.size!=2)return!1;var m=k.entries(),n=m.next();if(n.done||n.value[0]!=h||n.value[1]!="s")return!1;n=m.next();return n.done||n.value[0].x!=4||n.value[1]!="t"||!m.next().done?!1:!0}catch(q){return!1}}())return a;var b=new WeakMap,c=function(h){this[0]={};this[1]= 
f();this.size=0;if(h){h=x(h);for(var k;!(k=h.next()).done;)k=k.value,this.set(k[0],k[1])}};c.prototype.set=function(h,k){h=h===0?0:h;var m=d(this,h);m.list||(m.list=this[0][m.id]=[]);m.B?m.B.value=k:(m.B={next:this[1],N:this[1].N,head:this[1],key:h,value:k},m.list.push(m.B),this[1].N.next=m.B,this[1].N=m.B,this.size++);return this};c.prototype.delete=function(h){h=d(this,h);return h.B&&h.list?(h.list.splice(h.index,1),h.list.length||delete this[0][h.id],h.B.N.next=h.B.next,h.B.next.N=h.B.N,h.B.head= 
null,this.size--,!0):!1};c.prototype.clear=function(){this[0]={};this[1]=this[1].N=f();this.size=0};c.prototype.has=function(h){return!!d(this,h).B};c.prototype.get=function(h){return(h=d(this,h).B)&&h.value};c.prototype.entries=function(){return e(this,function(h){return[h.key,h.value]})};c.prototype.keys=function(){return e(this,function(h){return h.key})};c.prototype.values=function(){return e(this,function(h){return h.value})};c.prototype.forEach=function(h,k){for(var m=this.entries(),n;!(n=m.next()).done;)n= 
n.value,h.call(k,n[1],n[0],this)};c.prototype[Symbol.iterator]=c.prototype.entries;var d=function(h,k){var m=k&&typeof k;m=="object"||m=="function"?b.has(k)?m=b.get(k):(m=""+ ++g,b.set(k,m)):m="p_"+k;var n=h[0][m];if(n&&ta(h[0],m))for(h=0;h<n.length;h++){var q=n[h];if(k!==k&&q.key!==q.key||k===q.key)return{id:m,list:n,index:h,B:q}}return{id:m,list:n,index:-1,B:void 0}},e=function(h,k){var m=h[1];return ea(function(){if(m){for(;m.head!=h[1];)m=m.N;for(;m.next!=m.head;)return m=m.next,{done:!1,value:k(m)}; 
m=null}return{done:!0,value:void 0}})},f=function(){var h={};return h.N=h.next=h.head=h},g=0;return c}); 
p("Set",function(a){if(function(){if(!a||typeof a!="function"||!a.prototype.entries||typeof Object.seal!="function")return!1;try{var c=Object.seal({x:4}),d=new a(x([c]));if(!d.has(c)||d.size!=1||d.add(c)!=d||d.size!=1||d.add({x:4})!=d||d.size!=2)return!1;var e=d.entries(),f=e.next();if(f.done||f.value[0]!=c||f.value[1]!=c)return!1;f=e.next();return f.done||f.value[0]==c||f.value[0].x!=4||f.value[1]!=f.value[0]?!1:e.next().done}catch(g){return!1}}())return a;var b=function(c){this.L=new Map;if(c){c= 
x(c);for(var d;!(d=c.next()).done;)this.add(d.value)}this.size=this.L.size};b.prototype.add=function(c){c=c===0?0:c;this.L.set(c,c);this.size=this.L.size;return this};b.prototype.delete=function(c){c=this.L.delete(c);this.size=this.L.size;return c};b.prototype.clear=function(){this.L.clear();this.size=0};b.prototype.has=function(c){return this.L.has(c)};b.prototype.entries=function(){return this.L.entries()};b.prototype.values=function(){return this.L.values()};b.prototype.keys=b.prototype.values; 
b.prototype[Symbol.iterator]=b.prototype.values;b.prototype.forEach=function(c,d){var e=this;this.L.forEach(function(f){return c.call(d,f,f,e)})};return b});p("Math.log2",function(a){return a?a:function(b){return Math.log(b)/Math.LN2}});p("Object.values",function(a){return a?a:function(b){var c=[],d;for(d in b)ta(b,d)&&c.push(b[d]);return c}});p("Object.is",function(a){return a?a:function(b,c){return b===c?b!==0||1/b===1/c:b!==b&&c!==c}}); 
p("Array.prototype.includes",function(a){return a?a:function(b,c){var d=this;d instanceof String&&(d=String(d));var e=d.length;c=c||0;for(c<0&&(c=Math.max(c+e,0));c<e;c++){var f=d[c];if(f===b||Object.is(f,b))return!0}return!1}});var va=function(a,b,c){if(a==null)throw new TypeError("The 'this' value for String.prototype."+c+" must not be null or undefined");if(b instanceof RegExp)throw new TypeError("First argument to String.prototype."+c+" must not be a regular expression");return a+""}; 
p("String.prototype.includes",function(a){return a?a:function(b,c){return va(this,b,"includes").indexOf(b,c||0)!==-1}});p("Array.from",function(a){return a?a:function(b,c,d){c=c!=null?c:function(h){return h};var e=[],f=typeof Symbol!="undefined"&&Symbol.iterator&&b[Symbol.iterator];if(typeof f=="function"){b=f.call(b);for(var g=0;!(f=b.next()).done;)e.push(c.call(d,f.value,g++))}else for(f=b.length,g=0;g<f;g++)e.push(c.call(d,b[g],g));return e}}); 
p("Object.entries",function(a){return a?a:function(b){var c=[],d;for(d in b)ta(b,d)&&c.push([d,b[d]]);return c}});p("Number.isFinite",function(a){return a?a:function(b){return typeof b!=="number"?!1:!isNaN(b)&&b!==Infinity&&b!==-Infinity}});p("Number.MAX_SAFE_INTEGER",function(){return 9007199254740991});p("Number.MIN_SAFE_INTEGER",function(){return-9007199254740991});p("Number.isInteger",function(a){return a?a:function(b){return Number.isFinite(b)?b===Math.floor(b):!1}}); 
p("Number.isSafeInteger",function(a){return a?a:function(b){return Number.isInteger(b)&&Math.abs(b)<=Number.MAX_SAFE_INTEGER}});p("String.prototype.startsWith",function(a){return a?a:function(b,c){var d=va(this,b,"startsWith");b+="";var e=d.length,f=b.length;c=Math.max(0,Math.min(c|0,d.length));for(var g=0;g<f&&c<e;)if(d[c++]!=b[g++])return!1;return g>=f}}); 
var wa=function(a,b){a instanceof String&&(a+="");var c=0,d=!1,e={next:function(){if(!d&&c<a.length){var f=c++;return{value:b(f,a[f]),done:!1}}d=!0;return{done:!0,value:void 0}}};e[Symbol.iterator]=function(){return e};return e};p("Array.prototype.entries",function(a){return a?a:function(){return wa(this,function(b,c){return[b,c]})}});p("Math.imul",function(a){return a?a:function(b,c){b=Number(b);c=Number(c);var d=b&65535,e=c&65535;return d*e+((b>>>16&65535)*e+d*(c>>>16&65535)<<16>>>0)|0}}); 
p("Math.trunc",function(a){return a?a:function(b){b=Number(b);if(isNaN(b)||b===Infinity||b===-Infinity||b===0)return b;var c=Math.floor(Math.abs(b));return b<0?-c:c}});p("Number.isNaN",function(a){return a?a:function(b){return typeof b==="number"&&isNaN(b)}});p("Array.prototype.keys",function(a){return a?a:function(){return wa(this,function(b){return b})}});p("Array.prototype.values",function(a){return a?a:function(){return wa(this,function(b,c){return c})}});/* 
 
 Copyright The Closure Library Authors. 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var y=this||self,xa=function(a){a=a.split(".");for(var b=y,c=0;c<a.length;c++)if(b=b[a[c]],b==null)return null;return b},z=function(a){var b=typeof a;return b!="object"?b:a?Array.isArray(a)?"array":b:"null"},ya=function(a){var b=z(a);return b=="array"||b=="object"&&typeof a.length=="number"},za=function(a){var b=typeof a;return b=="object"&&a!=null||b=="function"},Aa=function(a){return a},Ba=function(a,b){function c(){}c.prototype=b.prototype;a.ed=b.prototype;a.prototype=new c;a.prototype.constructor= 
a;a.yd=function(d,e,f){for(var g=Array(arguments.length-2),h=2;h<arguments.length;h++)g[h-2]=arguments[h];return b.prototype[e].apply(d,g)}};function Ca(a,b){if(Error.captureStackTrace)Error.captureStackTrace(this,Ca);else{var c=Error().stack;c&&(this.stack=c)}a&&(this.message=String(a));b!==void 0&&(this.cause=b)}Ba(Ca,Error);Ca.prototype.name="CustomError";function Da(a,b){a=a.split("%s");for(var c="",d=a.length-1,e=0;e<d;e++)c+=a[e]+(e<b.length?b[e]:"%s");Ca.call(this,c+a[d])}Ba(Da,Ca);Da.prototype.name="AssertionError";function Ea(a,b,c,d){var e="Assertion failed";if(c){e+=": "+c;var f=d}else a&&(e+=": "+a,f=b);throw new Da(""+e,f||[]);} 
var A=function(a,b,c){a||Ea("",null,b,Array.prototype.slice.call(arguments,2))},C=function(a,b,c){a==null&&Ea("Expected to exist: %s.",[a],b,Array.prototype.slice.call(arguments,2));return a},Fa=function(a,b,c){typeof a!=="number"&&Ea("Expected number but got %s: %s.",[z(a),a],b,Array.prototype.slice.call(arguments,2));return a},Ga=function(a,b,c){typeof a!=="string"&&Ea("Expected string but got %s: %s.",[z(a),a],b,Array.prototype.slice.call(arguments,2));return a},Ha=function(a,b,c){typeof a!=="function"&& 
Ea("Expected function but got %s: %s.",[z(a),a],b,Array.prototype.slice.call(arguments,2))},D=function(a,b,c){Array.isArray(a)||Ea("Expected array but got %s: %s.",[z(a),a],b,Array.prototype.slice.call(arguments,2));return a},Ia=function(a,b,c){typeof a!=="boolean"&&Ea("Expected boolean but got %s: %s.",[z(a),a],b,Array.prototype.slice.call(arguments,2));return a},Ka=function(a,b,c,d){a instanceof b||Ea("Expected instanceof %s but got %s.",[Ja(b),Ja(a)],c,Array.prototype.slice.call(arguments,3)); 
return a};function Ja(a){return a instanceof Function?a.displayName||a.name||"unknown type name":a instanceof Object?a.constructor.displayName||a.constructor.name||Object.prototype.toString.call(a):a===null?"null":typeof a};function La(a){y.setTimeout(function(){throw a;},0)};var Ma=String.prototype.trim?function(a){return a.trim()}:function(a){return/^[\s\xa0]*([\s\S]*?)[\s\xa0]*$/.exec(a)[1]}; 
function Na(a,b){var c=0;a=Ma(String(a)).split(".");b=Ma(String(b)).split(".");for(var d=Math.max(a.length,b.length),e=0;c==0&&e<d;e++){var f=a[e]||"",g=b[e]||"";do{f=/(\d*)(\D*)(.*)/.exec(f)||["","","",""];g=/(\d*)(\D*)(.*)/.exec(g)||["","","",""];if(f[0].length==0&&g[0].length==0)break;c=Oa(f[1].length==0?0:parseInt(f[1],10),g[1].length==0?0:parseInt(g[1],10))||Oa(f[2].length==0,g[2].length==0)||Oa(f[2],g[2]);f=f[3];g=g[3]}while(c==0)}return c}function Oa(a,b){return a<b?-1:a>b?1:0};var Pa,Qa=xa("CLOSURE_FLAGS"),Ra=Qa&&Qa[610401301];Pa=Ra!=null?Ra:!1;function Sa(){var a=y.navigator;return a&&(a=a.userAgent)?a:""}var Ta,Ua=y.navigator;Ta=Ua?Ua.userAgentData||null:null;function Va(a){if(!Pa||!Ta)return!1;for(var b=0;b<Ta.brands.length;b++){var c=Ta.brands[b].brand;if(c&&c.indexOf(a)!=-1)return!0}return!1}function E(a){return Sa().indexOf(a)!=-1};function Wa(){return Pa?!!Ta&&Ta.brands.length>0:!1}function Xa(){return Wa()?!1:E("Opera")}function Ya(){return E("Firefox")||E("FxiOS")}function Za(){return Wa()?Va("Chromium"):(E("Chrome")||E("CriOS"))&&!(Wa()?0:E("Edge"))||E("Silk")};function $a(){return Pa?!!Ta&&!!Ta.platform:!1}function ab(){return E("iPhone")&&!E("iPod")&&!E("iPad")}function bb(){ab()||E("iPad")||E("iPod")};var cb=Array.prototype.indexOf?function(a,b){A(a.length!=null);return Array.prototype.indexOf.call(a,b,void 0)}:function(a,b){if(typeof a==="string")return typeof b!=="string"||b.length!=1?-1:a.indexOf(b,0);for(var c=0;c<a.length;c++)if(c in a&&a[c]===b)return c;return-1},db=Array.prototype.map?function(a,b){A(a.length!=null);return Array.prototype.map.call(a,b,void 0)}:function(a,b){for(var c=a.length,d=Array(c),e=typeof a==="string"?a.split(""):a,f=0;f<c;f++)f in e&&(d[f]=b.call(void 0,e[f],f,a)); 
return d};function eb(a,b){a:{for(var c=typeof a==="string"?a.split(""):a,d=a.length-1;d>=0;d--)if(d in c&&b.call(void 0,c[d],d,a)){b=d;break a}b=-1}return b<0?null:typeof a==="string"?a.charAt(b):a[b]};var fb=function(a){fb[" "](a);return a};fb[" "]=function(){};Xa();Wa()||E("Trident")||E("MSIE");E("Edge");var gb=E("Gecko")&&!(Sa().toLowerCase().indexOf("webkit")!=-1&&!E("Edge"))&&!(E("Trident")||E("MSIE"))&&!E("Edge"),hb=Sa().toLowerCase().indexOf("webkit")!=-1&&!E("Edge");hb&&E("Mobile");$a()||E("Macintosh");$a()||E("Windows");($a()?Ta.platform==="Linux":E("Linux"))||$a()||E("CrOS");$a()||E("Android");ab();E("iPad");E("iPod");bb();Sa().toLowerCase().indexOf("kaios");Ya();ab()||E("iPod");E("iPad");!E("Android")||Za()||Ya()||Xa()||E("Silk");Za();!E("Safari")||Za()||(Wa()?0:E("Coast"))||Xa()||(Wa()?0:E("Edge"))||(Wa()?Va("Microsoft Edge"):E("Edg/"))||(Wa()?Va("Opera"):E("OPR"))||Ya()||E("Silk")||E("Android")||bb();var ib={},jb=null,kb=gb||hb||typeof y.btoa=="function",lb=function(a){if(kb)var b=y.btoa(a);else{b=[];for(var c=0,d=0;d<a.length;d++){var e=a.charCodeAt(d);if(e>255)throw Error("go/unicode-to-byte-error");b[c++]=e}a=void 0;A(ya(b),"encodeByteArray takes an array as a parameter");a===void 0&&(a=0);if(!jb)for(jb={},c="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789".split(""),d=["+/=","+/","-_=","-_.","-_"],e=0;e<5;e++){var f=c.concat(d[e].split(""));ib[e]=f;for(var g=0;g<f.length;g++){var h= 
f[g],k=jb[h];k===void 0?jb[h]=g:A(k===g)}}a=ib[a];c=Array(Math.floor(b.length/3));d=a[64]||"";for(e=f=0;f<b.length-2;f+=3){k=b[f];var m=b[f+1];h=b[f+2];g=a[k>>2];k=a[(k&3)<<4|m>>4];m=a[(m&15)<<2|h>>6];h=a[h&63];c[e++]=""+g+k+m+h}g=0;h=d;switch(b.length-f){case 2:g=b[f+1],h=a[(g&15)<<2]||d;case 1:b=b[f],c[e]=""+a[b>>2]+a[(b&3)<<4|g>>4]+h+d}b=c.join("")}return b};function mb(a,b){a.__closure__error__context__984382||(a.__closure__error__context__984382={});a.__closure__error__context__984382.severity=b};var nb={};function ob(a){a=Error(a);mb(a,"warning");return a}function pb(a,b,c){if(a!=null){var d;var e=(d=nb)!=null?d:nb={};d=e[a]||0;d>=b||(e[a]=d+1,a=Error(c),mb(a,"incident"),La(a))}};var qb=typeof Symbol==="function"&&typeof Symbol()==="symbol";function rb(a,b,c){return typeof Symbol==="function"&&typeof Symbol()==="symbol"?(c===void 0?0:c)&&Symbol.for&&a?Symbol.for(a):a!=null?Symbol(a):Symbol():b}var sb=rb("jas",void 0,!0),tb=rb("defaultInstance","0di"),ub=rb("oneofCases","1oa"),vb=rb("unknownBinaryFields",Symbol()),wb=rb("unknownBinaryThrottleKey","0ub"),xb=rb("unknownBinaryThrottleKey","0ubs"),yb=rb("m_m","Ed",!0),zb=rb("validPivotSelector","vps"),Ab=rb("lazilyParseLateLoadedExtensions");A(Math.round(Math.log2(Math.max.apply(Math,pa(Object.values({od:1,nd:2,md:4,rd:8,td:16,pd:32,gd:64,kd:128,hd:256,sd:512,jd:1024,ld:2048,qd:4096})))))===12);var Bb={Kc:{value:0,configurable:!0,writable:!0,enumerable:!1}},Cb=Object.defineProperties,F=qb?C(sb):"Kc",Db,Eb=[];G(Eb,7);Db=Object.freeze(Eb);function Fb(a){D(a,"state is only maintained on arrays.");return a[F]|0}function Gb(a,b){A((b&8388607)===b);D(a,"state is only maintained on arrays.");qb||F in a||Cb(a,Bb);a[F]|=b} 
function G(a,b){A((b&8388607)===b);D(a,"state is only maintained on arrays.");qb||F in a||Cb(a,Bb);a[F]=b} 
function H(a,b,c){(c===void 0||!c||b&2048)&&A(b&64,"state for messages must be constructed");A((b&5)===0,"state for messages should not contain repeated field state");if(b&64){A(b&64);c=b>>13&1023||536870912;A(b&64);var d=a.length;A(c+(b&128?0:-1)>=d-1,"pivot %s is pointing at an index earlier than the last index of the array, length: %s",c,d);b&128&&A(typeof a[0]==="string","arrays with a message_id bit must have a string in the first position, got: %s",a[0])}} 
function Hb(a){D(a,"state is only maintained on arrays.");return!!((a[F]|0)&2)}function Ib(a,b){Fa(b);A(b>0&&b<=1023||536870912===b,"pivot must be in the range [1, 1024) or NO_PIVOT got %s",b);return a&-8380417|(b&1023)<<13}var Jb=Object.getOwnPropertyDescriptor(Array.prototype,"Mc");Object.defineProperties(Array.prototype,{Mc:{get:function(){var a=Kb(this);return Jb?Jb.get.call(this)+"|"+a:a},configurable:!0,enumerable:!1}}); 
function Kb(a){function b(e,f){e&c&&d.push(f)}var c=Fb(a),d=[];b(1,"IS_REPEATED_FIELD");b(2,"IS_IMMUTABLE_ARRAY");b(4,"IS_API_FORMATTED");b(512,"STRING_FORMATTED");b(1024,"GBIGINT_FORMATTED");b(1024,"BINARY");b(8,"ONLY_MUTABLE_VALUES");b(16,"UNFROZEN_SHARED");b(32,"MUTABLE_REFERENCES_ARE_OWNED");b(64,"CONSTRUCTED");b(128,"HAS_MESSAGE_ID");b(256,"FROZEN_ARRAY");b(2048,"HAS_WRAPPER");b(4096,"MUTABLE_SUBSTRUCTURES");c&64&&(A(c&64),a=c>>13&1023||536870912,a!==536870912&&d.push("pivot: "+a));return d.join(",")} 
;function Lb(){return typeof BigInt==="function"};var I=qb&&Math.random()<.5,J=I?Symbol():void 0,Mb,Nb=typeof yb==="symbol",Ob={};function K(a){var b=a[yb],c=b===Ob;A(!Mb||c===a instanceof Mb);if(Nb&&b&&!c)throw Error("multiple jspb runtimes detected");return c}function Pb(a,b){Fa(a);A(a>0);A(b===0||b===-1);return a+b}function Rb(a,b){A(b===Sb||b===void 0);return a+(b?0:-1)}function Tb(a,b){Fa(a);A(a>=0);A(b===0||b===-1);return a-b} 
function Ub(a,b){if(b===void 0){if(b=!Vb(a))A(K(a)),a=I?a[C(J)]:a.g,D(a,"state is only maintained on arrays."),b=a[F]|0,H(a,b),b=!!(2&b);return b}A(K(a));var c=I?a[C(J)]:a.g;D(c,"state is only maintained on arrays.");var d=c[F]|0;H(c,d);A(b===d);return!!(2&b)&&!Vb(a)}var Wb={};function Vb(a){var b=a.Bc,c;(c=!b)||(A(K(a)),a=I?a[C(J)]:a.g,D(a,"state is only maintained on arrays."),c=a[F]|0,H(a,c),c=!!(2&c));A(c);A(b===void 0||b===Wb);return b===Wb} 
function Xb(a,b){A(K(a));var c=I?a[C(J)]:a.g;D(c,"state is only maintained on arrays.");var d=c[F]|0;H(c,d);A(b===!!(2&d));a.Bc=b?Wb:void 0}var Yb=Symbol("exempted jspb subclass"),Zb=typeof Symbol!="undefined"&&typeof Symbol.hasInstance!="undefined";function $b(){}function ac(a,b){var c=D(a);D(c,"state is only maintained on arrays.");c=c[F]|0;b||A(!(c&2&&c&4||c&256)||Object.isFrozen(a));bc(a)} 
function bc(a){D(a,"state is only maintained on arrays.");a=a[F]|0;var b=a&4,c=(512&a?1:0)+(1024&a?1:0);A(b&&c<=1||!b&&c===0,"Expected at most 1 type-specific formatting bit, but got "+c+" with state: "+a)}var cc=Object.freeze({}),Sb={};function dc(a,b){D(a,"state is only maintained on arrays.");a=a[F]|0;A(a&64);a&128?A(b===Sb):A(b===void 0)};function ec(a,b){b=b===void 0?new Set:b;if(b.has(a))return"(Recursive reference)";switch(typeof a){case "object":if(a){var c=Object.getPrototypeOf(a);switch(c){case Map.prototype:case Set.prototype:case Array.prototype:b.add(a);var d="["+Array.from(a,function(e){return ec(e,b)}).join(", ")+"]";b.delete(a);c!==Array.prototype&&(d=fc(c.constructor)+"("+d+")");return d;case Object.prototype:return b.add(a),c="{"+Object.entries(a).map(function(e){var f=x(e);e=f.next().value;f=f.next().value;return e+ 
": "+ec(f,b)}).join(", ")+"}",b.delete(a),c;default:return d="Object",c&&c.constructor&&(d=fc(c.constructor)),typeof a.toString==="function"&&a.toString!==Object.prototype.toString?d+"("+String(a)+")":"(object "+d+")"}}break;case "function":return"function "+fc(a);case "number":if(!Number.isFinite(a))return String(a);break;case "bigint":return a.toString(10)+"n";case "symbol":return a.toString()}return JSON.stringify(a)} 
function fc(a){var b=a.displayName;return b&&typeof b==="string"||(b=a.name)&&typeof b==="string"?b:(a=/function\s+([^\(]+)/m.exec(String(a)))?a[1]:"(Anonymous)"};function hc(a,b){var c=ic,d=[];jc(b,a,d)||kc.apply(null,[void 0,c,"Guard "+b.fb().trim()+" failed:"].concat(pa(d.reverse())))}function lc(a,b){hc(a,b);return a}function mc(a,b){a.Dd=!0;a.fb=typeof b==="function"?b:function(){return b};return a}function jc(a,b,c){var d=a(b,c);d||nc(c,function(){var e="";e.length>0&&(e+=": ");return e+"Expected "+a.fb().trim()+", got "+ec(b)});return d}function nc(a,b){a==null||a.push((typeof b==="function"?b():b).trim())}var ic=void 0; 
function oc(a){return typeof a==="function"?a():a}function kc(){throw Error(sa.apply(0,arguments).map(oc).filter(Boolean).join("\n").trim().replace(/:$/,""));}var pc=mc(function(a){return a!==null&&a!==void 0},"exists");var qc=mc(function(a){return typeof a==="number"},"number"),rc=mc(function(a){return typeof a==="string"},"string"),sc=mc(function(a){return typeof a==="boolean"},"boolean"),tc=mc(function(a){return typeof a==="bigint"},"bigint");function uc(a){return mc(function(b){return b instanceof a},function(){return fc(a)})}function vc(){var a=sa.apply(0,arguments);return mc(function(b){return a.some(function(c){return c(b)})},function(){return""+a.map(function(b){return b.fb().trim()}).join(" | ")})};var wc=typeof y.BigInt==="function"&&typeof y.BigInt(0)==="bigint";var xc=function(a){this.wb=a};xc.prototype.toString=function(a){return this.wb.toString(a)};xc.prototype.valueOf=function(){throw Error("Convert JSBI instances to native numbers using `toNumber`.");};xc.prototype[Symbol.toPrimitive]=function(){return this.wb};/* 
 
 Copyright 2018 Google Inc 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var M=function(a,b){var c=ha(Array,[a],this.constructor);c.sign=b;Object.setPrototypeOf(c,M.prototype);if(a>yc)throw new RangeError("Maximum BigInt size exceeded");return c};t(M,Array); 
M.prototype.toString=function(a){a=a===void 0?10:a;if(a<2||a>36)throw new RangeError("toString() radix argument must be between 2 and 36");if(this.length===0)var b="0";else if((a&a-1)===0){b=this.length;var c=a-1;c=(c>>>1&85)+(c&85);c=(c>>>2&51)+(c&51);c=(c>>>4&15)+(c&15);--a;var d=this.i(b-1),e=(b*30-zc(d)+c-1)/c|0;this.sign&&e++;if(e>268435456)throw Error("string too long");var f=Array(e);--e;for(var g=0,h=0,k=0;k<b-1;k++){var m=this.i(k);g=(g|m<<h)&a;f[e--]=Bc[g];h=c-h;g=m>>>h;for(h=30-h;h>=c;)f[e--]= 
Bc[g&a],g>>>=c,h-=c}f[e--]=Bc[(g|d<<h)&a];for(g=d>>>c-h;g!==0;)f[e--]=Bc[g&a],g>>>=c;this.sign&&(f[e--]="-");if(e!==-1)throw Error("implementation bug");b=f.join("")}else b=Cc(this,a,!1);return b};M.prototype.valueOf=function(){throw Error("Convert JSBI instances to native numbers using `toNumber`.");}; 
var Gc=function(a,b){if(b.sign)throw new RangeError("Exponent must be positive");if(b.length===0)return Dc(1);if(a.length===0)return a;if(a.length===1&&a.i(0)===1)return a.sign&&(b.i(0)&1)===0&&a.length!==0&&(b=a.uc(),b.sign=!a.sign,a=b),a;if(b.length>1)throw new RangeError("BigInt too big");b=b.Aa(0);if(b===1)return a;if(b>=Ec)throw new RangeError("BigInt too big");if(a.length===1&&a.i(0)===2){var c=1+(b/30|0);a=new M(c,a.sign&&(b&1)!==0);a.ia();a.o(c-1,1<<b%30);return a}c=null;var d=a;(b&1)!==0&& 
(c=a);for(b>>=1;b!==0;b>>=1)d=Fc(d,d),(b&1)!==0&&(c=c===null?d:Fc(c,d));return c},Fc=function(a,b){if(a.length===0)return a;if(b.length===0)return b;var c=a.length+b.length;a.xb()+b.xb()>=30&&c--;c=new M(c,a.sign!==b.sign);c.ia();for(var d=0;d<a.length;d++){var e=b,f=a.i(d),g=c,h=d;if(f!==0){for(var k=f&32767,m=f>>>15,n=f=0,q=0;q<e.length;q++,h++){var r=g.i(h),u=e.i(q),w=u&32767,v=u>>>15;u=Hc(w,m);var B=Hc(v,k);v=Hc(v,m);r+=n+Hc(w,k)+f;f=r>>>30;r&=1073741823;r+=((u&32767)<<15)+((B&32767)<<15);f+= 
r>>>30;n=v+(u>>>15)+(B>>>15);g.o(h,r&1073741823)}for(;f!==0||n!==0;h++)e=g.i(h),e+=f+n,n=0,f=e>>>30,g.o(h,e&1073741823)}}return c.Ta()},Dc=function(a){var b=new M(1,!1);b.o(0,a);return b};M.prototype.uc=function(){for(var a=new M(this.length,this.sign),b=0;b<this.length;b++)a[b]=this[b];return a};M.prototype.Ta=function(){for(var a=this.length,b=this[a-1];b===0;)a--,b=this[a-1],this.pop();a===0&&(this.sign=!1);return this};M.prototype.ia=function(){for(var a=0;a<this.length;a++)this[a]=0}; 
var Cc=function(a,b,c){var d=a.length;if(d===0)return"";if(d===1)return b=a.Aa(0).toString(b),c===!1&&a.sign&&(b="-"+b),b;var e=Ic[b]-1;d=(((d*30-zc(a.i(d-1)))*Jc+(e-1))/e|0)+1>>1;var f=Gc(Dc(b),Dc(d));e=f.Aa(0);if(f.length===1&&e<=32767){f=new M(a.length,!1);f.ia();for(var g=0,h=a.length*2-1;h>=0;h--)g=g<<15|a.K(h),f.Sa(h,g/e|0),g=g%e|0;e=g.toString(b)}else{h=f.yb();g=f.length;var k=a.yb()-h;var m=new M(k+2>>>1,!1);m.ia();var n=new M(h+2>>>1,!1);n.ia();var q=zc(f.K(h-1))-15;q>0&&(f=Kc(f,q,0));e= 
Kc(a,q,1);for(var r=f.K(h-1),u=0;k>=0;k--){var w=32767,v=e.K(k+h);if(v!==r){v=(v<<15|e.K(k+h-1))>>>0;w=v/r|0;v=v%r|0;for(var B=f.K(h-2),L=e.K(k+h-2);Hc(w,B)>>>0>(v<<16|L)>>>0&&!(w--,v+=r,v>32767););}v=f;B=w;L=g;for(var na=0,oa=0,Ac=0;Ac<L;Ac++){var Qb=v.i(Ac),xf=Hc(Qb>>>15,B);Qb=Hc(Qb&32767,B)+((xf&32767)<<15)+oa+na;na=Qb>>>30;oa=xf>>>15;n.o(Ac,Qb&1073741823)}if(n.length>L)for(n.o(L++,na+oa);L<n.length;)n.o(L++,0);else if(na+oa!==0)throw Error("implementation bug");v=e.xc(n,k,h+1);v!==0&&(v=e.vc(f, 
k,h),e.Sa(k+h,e.K(k+h)+v&32767),w--);k&1?u=w<<15:m.o(k>>>1,u|w)}e.wc(q);f=m;e=e.Ta();e=Cc(e,b,!0)}f.Ta();for(b=Cc(f,b,!0);e.length<d;)e="0"+e;c===!1&&a.sign&&(b="-"+b);return b+e};l=M.prototype;l.xb=function(){return zc(this.i(this.length-1))}; 
l.ud=function(a,b,c){c>this.length&&(c=this.length);var d=a&32767;a>>>=15;for(var e=0,f=0;f<c;f++){var g=this.i(f),h=g&32767,k=g>>>15;g=Hc(h,a);var m=Hc(k,d);k=Hc(k,a);h=b+Hc(h,d)+e;e=h>>>30;h&=1073741823;h+=((g&32767)<<15)+((m&32767)<<15);e+=h>>>30;b=k+(g>>>15)+(m>>>15);this.o(f,h&1073741823)}if(e!==0||b!==0)throw Error("implementation bug");};l.vc=function(a,b,c){for(var d=0,e=0;e<c;e++){var f=this.K(b+e)+a.K(e)+d;d=f>>>15;this.Sa(b+e,f&32767)}return d}; 
l.xc=function(a,b,c){var d=c-1>>>1,e=0;if(b&1){b>>=1;for(var f=this.i(b),g=f&32767,h=0;h<d;h++){var k=a.i(h);f=(f>>>15)-(k&32767)-e;e=f>>>15&1;this.o(b+h,(f&32767)<<15|g&32767);f=this.i(b+h+1);g=(f&32767)-(k>>>15)-e;e=g>>>15&1}d=a.i(h);f=(f>>>15)-(d&32767)-e;e=f>>>15&1;this.o(b+h,(f&32767)<<15|g&32767);if(b+h+1>=this.length)throw new RangeError("out of bounds");(c&1)===0&&(f=this.i(b+h+1),g=(f&32767)-(d>>>15)-e,e=g>>>15&1,this.o(b+a.length,f&1073709056|g&32767))}else{b>>=1;for(g=0;g<a.length-1;g++)d= 
this.i(b+g),f=a.i(g),h=(d&32767)-(f&32767)-e,e=h>>>15&1,d=(d>>>15)-(f>>>15)-e,e=d>>>15&1,this.o(b+g,(d&32767)<<15|h&32767);h=this.i(b+g);a=a.i(g);d=(h&32767)-(a&32767)-e;e=d>>>15&1;f=0;(c&1)===0&&(f=(h>>>15)-(a>>>15)-e,e=f>>>15&1);this.o(b+g,(f&32767)<<15|d&32767)}return e};l.wc=function(a){if(a!==0){for(var b=this.i(0)>>>a,c=this.length-1,d=0;d<c;d++){var e=this.i(d+1);this.o(d,e<<30-a&1073741823|b);b=e>>>a}this.o(c,b)}}; 
var Kc=function(a,b,c){var d=a.length,e=new M(d+c,!1);if(b===0){for(b=0;b<d;b++)e.o(b,a.i(b));c>0&&e.o(d,0);return e}for(var f=0,g=0;g<d;g++){var h=a.i(g);e.o(g,h<<b&1073741823|f);f=h>>>30-b}c>0&&e.o(d,f);return e};l=M.prototype;l.i=function(a){return this[a]};l.Aa=function(a){return this[a]>>>0};l.o=function(a,b){this[a]=b|0};l.vd=function(a,b){this[a]=b|0};l.yb=function(){var a=this.length;return this.Aa(a-1)<=32767?a*2-1:a*2};l.K=function(a){return this[a>>>1]>>>(a&1)*15&32767}; 
l.Sa=function(a,b){var c=a>>>1,d=this.i(c);this.o(c,a&1?d&32767|b<<15:d&1073709056|b&32767)};var yc=33554432,Ec=yc<<5,Ic=[0,0,32,51,64,75,83,90,96,102,107,111,115,119,122,126,128,131,134,136,139,141,143,145,147,149,151,153,154,156,158,159,160,162,163,165,166],Jc=32,Bc="0123456789abcdefghijklmnopqrstuvwxyz".split(""),zc=Math.clz32?function(a){return Math.clz32(a)-2}:function(a){return a===0?30:29-(Math.log(a>>>0)/Math.LN2|0)|0},Hc=Math.imul||function(a,b){return a*b|0};var Lc=uc(M),Mc=uc(xc);var Nc=wc?Mc:Lc;function Oc(a){var b=a;if(rc(b)){if(!/^\s*(?:-?[1-9]\d*|0)?\s*$/.test(b))throw Error("Invalid string for toGbigint: "+b);}else if(qc(b)&&!Number.isSafeInteger(b))throw Error("Invalid number for toGbigint: "+b);if(wc){if(Nc(a)){if(!wc)throw Error("This platform does not use BigInt.");a=lc(a,Mc).wb}else tc(a)||(hc(a,vc(rc,sc,qc)),a=BigInt(a));return a%BigInt(2)===BigInt(Pc())?a.toString():a}return a=sc(a)?a?"1":"0":rc(a)?a.trim()||"0":String(a)} 
var Rc=mc(function(a){return wc?Qc(a):rc(a)&&/^(?:-?[1-9]\d*|0)$/.test(a)},"gbigint"),Xc=mc(function(a){if(wc)return hc(Sc,tc),hc(Tc,tc),a=BigInt(a),a>=Sc&&a<=Tc;a=lc(a,rc);return a[0]==="-"?Uc(a,Vc):Uc(a,Wc)},"isSafeInt52"),Vc=Number.MIN_SAFE_INTEGER.toString(),Sc=wc?BigInt(Number.MIN_SAFE_INTEGER):void 0,Wc=Number.MAX_SAFE_INTEGER.toString(),Tc=wc?BigInt(Number.MAX_SAFE_INTEGER):void 0; 
function Uc(a,b){if(a.length>b.length)return!1;if(a.length<b.length||a===b)return!0;for(var c=0;c<a.length;c++){var d=a[c],e=b[c];if(d>e)return!1;if(d<e)return!0}c=ic;kc("Assertion fail:","isInRange weird case. Value was: "+a+". Boundary was: "+b+"."||c)} 
function Qc(a){if(typeof a==="bigint")return a%BigInt(2)===BigInt(Pc())?(console.error("isGbigint: got a `bigint` when we were expecting a `string`. Make sure to call `toGbigint()` when creating `gbigint` instances!"),!1):!0;if(rc(a)){if(!/^(?:-?[1-9]\d*|0)$/.test(a))return!1;if(Number(a[a.length-1])%2===Pc())return!0;console.error("isGbigint: got a `string` when we were expecting a `bigint`. Make sure to call `toGbigint()` when creating `gbigint` instances!")}return!1} 
function Pc(){var a=typeof Window==="function"&&globalThis.top instanceof Window?globalThis.top:globalThis;a.gbigintUseStrInDebugToggleVal==null&&Object.defineProperties(a,{gbigintUseStrInDebugToggleVal:{value:Math.round(Math.random())}});return a.gbigintUseStrInDebugToggleVal};var N=0,O=0;function Yc(a){var b=a>>>0;N=b;O=(a-b)/**********>>>0}function Zc(a){if(a<0){Yc(0-a);var b=x($c(N,O));a=b.next().value;b=b.next().value;N=a>>>0;O=b>>>0}else Yc(a)}function ad(a,b){var c=b***********+(a>>>0);return Number.isSafeInteger(c)?c:bd(a,b)} 
function bd(a,b){b>>>=0;a>>>=0;if(b<=2097151)var c=""+(***********b+a);else Lb()?c=""+(BigInt(b)<<BigInt(32)|BigInt(a)):(c=(a>>>24|b<<8)&16777215,b=b>>16&65535,a=(a&16777215)+c*6777216+b*6710656,c+=b*8147497,b*=2,a>=1E7&&(c+=a/1E7>>>0,a%=1E7),c>=1E7&&(b+=c/1E7>>>0,c%=1E7),A(b),c=b+cd(c)+cd(a));return c}function cd(a){a=String(a);return"0000000".slice(a.length)+a} 
function dd(){var a=N,b=O;b&2147483648?Lb()?a=""+(BigInt(b|0)<<BigInt(32)|BigInt(a>>>0)):(b=x($c(a,b)),a=b.next().value,b=b.next().value,a="-"+bd(a,b)):a=bd(a,b);return a} 
function ed(a){A(a.length>0);if(a.length<16)Zc(Number(a));else if(Lb())a=BigInt(a),N=Number(a&BigInt(4294967295))>>>0,O=Number(a>>BigInt(32)&BigInt(4294967295));else{A(a.length>0);var b=+(a[0]==="-");O=N=0;for(var c=a.length,d=0+b,e=(c-b)%6+b;e<=c;d=e,e+=6)d=Number(a.slice(d,e)),O*=1E6,N=N*1E6+d,N>=**********&&(O+=Math.trunc(N/**********),O>>>=0,N>>>=0);b&&(b=x($c(N,O)),a=b.next().value,b=b.next().value,N=a,O=b)}}function $c(a,b){b=~b;a?a=~a+1:b+=1;return[a,b]};function fd(a){return Array.prototype.slice.call(a)};var gd=typeof BigInt==="function"?BigInt.asIntN:void 0,hd=typeof BigInt==="function"?BigInt.asUintN:void 0,id=Number.isSafeInteger,jd=Number.isFinite,kd=Math.trunc,ld=Number.MAX_SAFE_INTEGER;function md(a){if(a==null||typeof a==="number")return a;if(a==="NaN"||a==="Infinity"||a==="-Infinity")return Number(a)}function nd(a){return a.displayName||a.name||"unknown type name"}function od(a){if(typeof a!=="boolean")throw Error("Expected boolean but got "+z(a)+": "+a);return a}var pd=/^-?([1-9][0-9]*|0)(\.[0-9]+)?$/; 
function qd(a){switch(typeof a){case "bigint":return!0;case "number":return jd(a);case "string":return pd.test(a);default:return!1}}function rd(a){if(!jd(a))throw a="Expected enum as finite number but got "+z(a)+": "+a,ob(a);return a|0}function sd(a){return a==null?a:jd(a)?a|0:void 0}function td(a){return"Expected int32 as finite number but got "+z(a)+": "+a}function ud(a){if(typeof a!=="number")throw ob(td(a));if(!jd(a))throw ob(td(a));return a|0}function vd(a){return a==null?a:ud(a)} 
function wd(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return jd(a)?a|0:void 0}function xd(a){if(a==null)return a;if(typeof a==="string"&&a)a=+a;else if(typeof a!=="number")return;return jd(a)?a>>>0:void 0} 
function yd(a){var b=0;b=b===void 0?0:b;if(!qd(a))throw a="Expected an int64 value encoded as a number or a string but got "+z(a)+": "+a,ob(a);var c=typeof a;switch(b){case 512:switch(c){case "string":return zd(a,!0);case "bigint":return String(gd(64,a));default:return Ad(lc(a,qc))}case 1024:switch(c){case "string":return Bd(a);case "bigint":return Cd(a);default:return Dd(lc(a,qc))}case 0:switch(c){case "string":return zd(a,!1);case "bigint":return Cd(a);default:return Ed(lc(a,qc),!1)}default:throw Error("Unknown format requested type for int64"); 
}}function Fd(a){if(a[0]==="-")return!1;var b=a.length;return b<20?!0:b===20&&Number(a.substring(0,6))<184467}function Gd(a){var b=a.length;return a[0]==="-"?b<20?!0:b===20&&Number(a.substring(0,7))>-922337:b<19?!0:b===19&&Number(a.substring(0,6))<922337}function Hd(a){A(a<0||!(0<a&&a<ld));A(Number.isInteger(a));if(a<0){Zc(a);var b=bd(N,O);a=Number(b);return id(a)?a:b}b=String(a);if(Fd(b))return b;Zc(a);return ad(N,O)}function Id(a){A(a.indexOf(".")===-1);if(Gd(a))return a;ed(a);return dd()} 
function Ed(a,b){A(qd(a));A(b||!0);a=kd(a);if(!id(a)){A(!id(a));A(Number.isInteger(a));Zc(a);b=N;var c=O;if(a=c&2147483648)b=~b+1>>>0,c=~c>>>0,b==0&&(c=c+1>>>0);b=ad(b,c);a=typeof b==="number"?a?-b:b:a?"-"+b:b}return a}function Ad(a){A(qd(a));A(!0);a=kd(a);if(id(a))a=String(a);else{A(!id(a));A(Number.isInteger(a));var b=String(a);Gd(b)?a=b:(Zc(a),a=dd())}return a} 
function zd(a,b){A(qd(a));A(b||!0);b=kd(Number(a));if(id(b))return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return Id(a)}function Bd(a){var b=kd(Number(a));if(id(b))return Oc(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));return Lb()?Cd(BigInt(a)):Oc(Id(a))}function Dd(a){return id(a)?Oc(Ed(a,!0)):Oc(Ad(a))} 
function Jd(a,b){A(qd(a));A(b||!0);b=kd(Number(a));if(id(b)&&b>=0)return String(b);b=a.indexOf(".");b!==-1&&(a=a.substring(0,b));A(a.indexOf(".")===-1);Fd(a)||(ed(a),a=bd(N,O));return a}function Cd(a){A(typeof a==="bigint");return Oc(gd(64,a))}function Kd(a){var b=typeof a;if(a==null)return a;if(b==="bigint")return Cd(a);if(qd(a)){if(b==="string")return Bd(a);a=lc(a,qc);return Dd(a)}} 
function Ld(a){if(a==null)return a;if(typeof a==="bigint")return a>=0&&Xc(a)?a=Number(a):(a=hd(64,a),a=Xc(a)?Number(a):String(a)),a;if(qd(a)){if(typeof a==="number")return A(qd(a)),A(!0),a=kd(a),a>=0&&id(a)?a:Hd(a);a=lc(a,rc);A(qd(a));A(!0);A(!0);var b=kd(Number(a));0<=b&&b<=ld?a=b:(a=Jd(a,!1),b=Number(a),a=id(b)?b:a);return a}} 
function Md(a){var b=!0;b=b===void 0?!1:b;var c=typeof a;if(a==null)return a;if(c==="bigint")return String(hd(64,a));if(qd(a)){if(c==="string")return Jd(a,b);a=lc(a,qc);A(qd(a));A(b||!0);a=kd(a);a>=0&&id(a)?b=String(a):(b=a,A(b<0||!(0<b&&b<ld)),A(Number.isInteger(b)),a=String(b),Fd(a)?b=a:(Zc(b),b=bd(N,O)));return b}}function Nd(a){if(typeof a!=="string")throw Error("Expected a string but got "+a+" a "+z(a));return a} 
function Od(a){if(a!=null&&typeof a!=="string")throw Error("Expected a string or null or undefined but got "+a+" a "+z(a));return a}function Pd(a){return a==null||typeof a==="string"?a:void 0}function Qd(a,b){if(!(a instanceof b))throw Error("Expected instanceof "+nd(b)+" but got "+(a&&nd(a.constructor)));} 
function Rd(a,b,c,d){if(a!=null&&K(a))return a;if(!Array.isArray(a))return c?d&2?((a=b[tb])||(a=new b,A(K(a)),d=I?a[C(J)]:a.g,Gb(d,34),a=b[tb]=a),b=a):b=new b:b=void 0,b;D(a,"state is only maintained on arrays.");c=a[F]|0;d=c|d&32|d&2;d!==c&&G(a,d);return new b(a)}function Sd(a){if(!Array.isArray(a))throw a="Expected array but got "+z(a)+": "+a,ob(a);};function Td(a){return a}Td[zb]={};function Ud(a){return a};var Vd=function(){throw Error("please construct maps as mutable then call toImmutable");};if(Zb){var Wd=function(){throw Error("Cannot perform instanceof checks on ImmutableMap: please use isImmutableMap or isMutableMap to assert on the mutability of a map. See go/jspb-api-gotchas#immutable-classes for more information");},Xd={};Object.defineProperties(Vd,(Xd[Symbol.hasInstance]={value:Wd,configurable:!1,writable:!1,enumerable:!1},Xd));A(Vd[Symbol.hasInstance]===Wd,"defineProperties did not work: was it monkey-patched?")};var Yd={Fd:!0};function Zd(a,b,c){var d=d===void 0?!1:d;if(Aa(Ab)&&Aa(vb)&&c===Ab){A(K(a));c=I?a[C(J)]:a.g;var e=c[vb];if(!e)return;if(e=e.Gd)try{e(c,b,Yd);return}catch(f){throw Error("unknown binary extension "+b);}}d&&(A(K(a)),A(K(a)),a=I?a[C(J)]:a.g,D(a),(d=Aa(vb))&&d in a&&(a=a[d])&&delete a[b])}function $d(a,b){A(K(a));A(K(a));a=I?a[C(J)]:a.g;D(a);var c=Aa(vb),d;qb&&c&&((d=a[c])==null?void 0:d[b])!=null&&pb(wb,3,"0ub")}function ae(a,b){b<100||pb(xb,1,"0ubs")};function be(a,b,c,d){var e=d!==void 0;d=!!d;var f=Aa(vb),g;!e&&qb&&f&&(g=a[f])&&g.Bd(ae);f=[];var h=a.length;g=4294967295;var k=!1,m=!!(b&64);if(m){A(b&64);var n=b&128?0:-1}else n=void 0;if(!(b&1)){var q=h&&a[h-1];q!=null&&typeof q==="object"&&q.constructor===Object?(h--,g=h):q=void 0;if(m&&!(b&128)&&!e){k=!0;var r;b=(r=ce)!=null?r:Td;g=Pb(b(Tb(g,C(n)),C(n),a,q),C(n))}}r=void 0;for(b=0;b<h;b++){var u=a[b];if(u!=null&&(u=c(u,d))!=null)if(m&&b>=g){de();var w=Tb(b,C(n)),v=void 0;((v=r)!=null?v:r={})[w]= 
u}else f[b]=u}if(q)for(var B in q)h=q[B],h!=null&&(h=c(h,d))!=null&&(b=+B,u=void 0,m&&!Number.isNaN(b)&&(u=Pb(b,C(n)))<g?(de(),f[C(u)]=h):(b=void 0,((b=r)!=null?b:r={})[B]=h));r&&(k?f.push(r):(A(g<4294967295),f[g]=r));e&&Aa(vb)&&(D(f),D(a),A(f[vb]===void 0),Aa(vb)&&D(a));return f} 
function ee(a){C(a);switch(typeof a){case "number":return Number.isFinite(a)?a:""+a;case "bigint":return Xc(a)?Number(a):""+a;case "boolean":return a?1:0;case "object":if(Array.isArray(a)){ac(a);D(a,"state is only maintained on arrays.");var b=a[F]|0;return a.length===0&&b&1?void 0:be(a,b,ee)}if(a!=null&&K(a))return fe(a);A(!(a instanceof Uint8Array));return}return a}var ce;function ge(a){A(!ce);return fe(a)} 
function fe(a){A(K(a));a=I?a[C(J)]:a.g;D(a,"state is only maintained on arrays.");var b=a[F]|0;H(a,b);return be(a,b,ee)}function de(){var a,b=(a=ce)!=null?a:Td;A(b!==Ud)};if(typeof Proxy!=="undefined"){var ie=he;new Proxy({},{getPrototypeOf:ie,setPrototypeOf:ie,isExtensible:ie,preventExtensions:ie,getOwnPropertyDescriptor:ie,defineProperty:ie,has:ie,get:ie,set:ie,deleteProperty:ie,apply:ie,construct:ie})}function he(){throw Error("this array or object is owned by JSPB and should not be reused, did you mean to copy it with copyJspbArray? See go/jspb-api-gotchas#construct_from_array");};function je(a,b,c){var d=d===void 0?0:d;if(a!=null)for(var e=0;e<a.length;e++){var f=a[e];Array.isArray(f)&&ac(f)}if(a==null)e=32,c?(a=[c],e|=128):a=[],b&&(e=Ib(e,b));else{if(!Array.isArray(a))throw Error("data passed to JSPB constructors must be an Array, got '"+JSON.stringify(a)+"' a "+z(a));e=a;D(e,"state is only maintained on arrays.");e=e[F]|0;if(1&e)throw Error("Array passed to JSPB constructor is a repeated field array that belongs to another proto instance.");2048&e&&!(2&e)&&ke();if(Object.isFrozen(a)|| 
!Object.isExtensible(a)||Object.isSealed(a))throw Error("data passed to JSPB constructors must be mutable");if(e&256)throw Error("farr");if(e&64)return d!==0||e&2048||G(a,e|=2048),H(a,e),a;if(c&&(e|=128,c!==a[0]))throw Error('Expected message to have a message id: "'+c+'" in the array, got: '+JSON.stringify(a[0])+" a "+z(a[0])+", are you parsing with the wrong proto?");a:{c=a;e|=64;var g=c.length;if(g){var h=g-1;f=c[h];if(f!=null&&typeof f==="object"&&f.constructor===Object){A(e&64);b=e&128?0:-1; 
g=Tb(h,b);if(g>=1024)throw Error("Found a message with a sparse object at fieldNumber "+g+" is >= the limit 1024");for(var k in f)h=+k,h<g&&(h=Pb(h,b),A(c[h]==null),c[h]=f[k],delete f[k]);e=Ib(e,g);break a}}if(b){A(e&64);k=Math.max(b,Tb(g,e&128?0:-1));if(k>1024)throw Error("a message was constructed with an array of length "+g+" which is longer than 1024, are you using a supported serializer?");e=Ib(e,k)}}}e|=64;d===0&&(e|=2048);G(a,e);return a} 
function ke(){throw Error("Array passed to JSPB constructor already belongs to another JSPB proto instance");};function le(a){A(!(2&a));A(!(2048&a));return!(4096&a)&&!(16&a)}function me(a,b){C(a);if(typeof a!=="object")return a;if(Array.isArray(a)){ac(a);D(a,"state is only maintained on arrays.");var c=a[F]|0;a.length===0&&c&1?a=void 0:c&2||(b&&le(c)?(Gb(a,34),c&4&&Object.freeze(a)):a=ne(a,c,!1,b&&!(c&16)));return a}if(a!=null&&K(a))return A(a!=null&&K(a)),A(K(a)),b=I?a[C(J)]:a.g,D(b,"state is only maintained on arrays."),c=b[F]|0,H(b,c),Ub(a,c)?a:oe(a,b,c)?pe(a,b):ne(b,c);A(!(a instanceof Uint8Array))} 
function pe(a,b,c){a=new a.constructor(b);c&&Xb(a,!0);a.Sc=Wb;return a}function ne(a,b,c,d){var e=b;D(a,"state is only maintained on arrays.");A(e===(a[F]|0));d!=null||(d=!!(34&b));a=be(a,b,me,d);d=32;c&&(d|=2);b=b&8380609|d;G(a,b);return a}function qe(a){A(K(a));var b=I?a[C(J)]:a.g;D(b,"state is only maintained on arrays.");var c=b[F]|0;H(b,c);return Ub(a,c)?oe(a,b,c)?pe(a,b,!0):new a.constructor(ne(b,c,!1)):a} 
function re(a){if(!Vb(a))return!1;var b;A(K(a));var c=b=I?a[C(J)]:a.g;D(c,"state is only maintained on arrays.");var d=c[F]|0;H(c,d);A(d&2);b=ne(b,d);Gb(b,2048);A(K(a));D(b);I?a[C(J)]=b:a.g=b;Xb(a,!1);a.Sc=void 0;return!0}function se(a){if(!re(a)){A(K(a));var b=I?a[C(J)]:a.g;D(b,"state is only maintained on arrays.");var c=b[F]|0;H(b,c);if(Ub(a,c))throw Error("Cannot mutate an immutable Message");}} 
function te(a,b){if(b===void 0)D(a,"state is only maintained on arrays."),b=a[F]|0,H(a,b,!0);else{var c=b;D(a,"state is only maintained on arrays.");var d=a[F]|0;H(a,d,!0);A(c===d)}A(!(b&2));b&32&&!(b&4096)&&G(a,b|4096)}function oe(a,b,c){return Yb&&a[Yb]?!1:c&2?!0:c&32&&!(c&4096)?(G(b,c|2),Xb(a,!0),!0):!1};var ue=Oc(0),we=function(a,b,c,d){A(Object.isExtensible(a));A(K(a));a=I?a[C(J)]:a.g;b=ve(a,b,c,d);(c=b!==null)||(c=void 0);if(c)return b},ve=function(a,b,c,d){dc(a,c);if(b===-1)return null;var e=Rb(b,c);D(a,"state is only maintained on arrays.");var f=a[F]|0;A(f&64);A(e===Pb(b,f&128?0:-1));A(e>=0);f=a.length-1;if(!(f<Rb(1,c))){if(e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object){c=g[b];var h=!0}else if(e===f)c=g;else return}else c=a[e];if(d&&c!=null){d=d(c);if(d==null)return d; 
if(!Object.is(d,c))return h?g[b]=d:a[e]=d,d}return c}},P=function(a,b,c){se(a);A(K(a));var d=I?a[C(J)]:a.g;D(d,"state is only maintained on arrays.");var e=d[F]|0;H(d,e);xe(d,e,b,c);return a}; 
function xe(a,b,c,d){dc(a);var e=Rb(c);D(a,"state is only maintained on arrays.");var f=a[F]|0;A(f&64);A(e===Pb(c,f&128?0:-1));A(e>=0);f=a.length-1;if(f>=Rb(1)&&e>=f){var g=a[f];if(g!=null&&typeof g==="object"&&g.constructor===Object)return g[c]=d,b}if(e<=f)return a[e]=d,b;d!==void 0&&((f=b)==null&&(D(a,"state is only maintained on arrays."),b=a[F]|0,H(a,b),f=b),A(f&64),f=f>>13&1023||536870912,c>=f?(A(f!==536870912),d!=null&&(e={},a[Rb(f)]=(e[c]=d,e))):a[e]=d);return b} 
function ye(a,b){if(!a)return a;A(Hb(b)?Ub(a):!0);return a}function ze(a,b,c){c=c===void 0?!1:c;ac(a,c);D(a,"state is only maintained on arrays.");var d=a[F]|0;A(d&1);c||(A(Object.isFrozen(a)||d&16),A(Hb(b)?Object.isFrozen(a):!0))}var Ae=function(a){return a===cc?2:4}; 
function Be(a,b,c,d){A(K(a));var e=I?a[C(J)]:a.g;var f=e;D(f,"state is only maintained on arrays.");var g=f[F]|0;H(f,g);f=g;c=Ub(a,f)?1:c;d=!!d||c===3;c===2&&re(a)&&(A(K(a)),f=e=I?a[C(J)]:a.g,D(f,"state is only maintained on arrays."),a=f[F]|0,H(f,a),f=a);g=a=Ce(e,b);g===Db?g=7:(D(g,"state is only maintained on arrays."),g=g[F]|0);var h=De(g,f);bc(a);var k=4&h?!1:!0;if(k){4&h&&(a=fd(a),g=0,h=Ee(h,f),f=C(xe(e,f,b,a)));for(var m=0,n=0;m<a.length;m++){var q=Pd(a[m]);q!=null&&(a[n++]=q)}n<m&&(a.length= 
n);h=(h|4)&-513;h&=-1025;h&=-4097}h!==g&&(G(a,h),2&h&&Object.freeze(a));a=Fe(a,h,e,f,b,c,k,d);bc(a);d||ze(a,e);return a}function Fe(a,b,c,d,e,f,g,h){var k=b;f===1||(f!==4?0:2&b||!(16&b)&&32&d)?Ge(b)||(e=!a.length||g&&!(4096&b)||!!(32&d)&&le(b),b|=e?2:256,b!==k&&G(a,b),Object.freeze(a)):(f===2&&Ge(b)&&(a=fd(a),k=0,b=Ee(b,d),d=C(xe(c,d,e,a))),Ge(b)||(h||(b|=16),b!==k&&G(a,b)));2&b||le(b)||te(c,d);return a}function Ce(a,b){a=ve(a,b);return Array.isArray(a)?a:Db} 
function De(a,b){2&b&&(a|=2);return a|1}function Ge(a){return!!(2&a)&&!!(4&a)||!!(256&a)} 
function He(a,b,c){se(a);A(K(a));var d=I?a[C(J)]:a.g;D(d,"state is only maintained on arrays.");var e=d[F]|0;H(d,e);if(c==null)return xe(d,e,b),a;Sd(c);var f=c;f===Db?f=7:(D(f,"state is only maintained on arrays."),f=f[F]|0);var g=f,h=Ge(f),k=h||Object.isFrozen(c);h||(f=0);k||(c=fd(c),g=0,f=Ee(f,e),k=!1);f|=5;for(h=0;h<c.length;h++){var m=c[h],n=ud(m);Object.is(m,n)||(k&&(c=fd(c),g=0,f=Ee(f,e),k=!1),c[h]=n)}f!==g&&(k&&(c=fd(c),f=Ee(f,e)),G(c,f));ac(c);xe(d,e,b,c);return a} 
function Ie(a,b,c,d){se(a);A(K(a));var e=I?a[C(J)]:a.g;D(e,"state is only maintained on arrays.");var f=e[F]|0;H(e,f);xe(e,f,b,(d==="0"?Number(c)===0:c===d)?void 0:c);return a} 
var Le=function(a,b,c,d){se(a);A(K(a));var e=I?a[C(J)]:a.g;D(e,"state is only maintained on arrays.");var f=e[F]|0;H(e,f);if(d==null){var g=Je(e);if(Ke(g,e,f,c)===b)g.set(c,0);else return a}else{A(c.includes(b));g=Je(e);var h=Ke(g,e,f,c);h!==b&&(h&&(f=xe(e,f,h)),g.set(c,b))}xe(e,f,b,d);return a},Ne=function(a,b,c){return Me(a,b)===c?c:-1},Me=function(a,b){A(K(a));a=I?a[C(J)]:a.g;return Ke(Je(a),a,void 0,b)}; 
function Je(a){if(qb){var b;return(b=a[ub])!=null?b:a[ub]=new Map}if(ub in a)return Ka(a[ub],Map);b=new Map;Object.defineProperty(a,ub,{value:b});return b}function Ke(a,b,c,d){dc(b);var e=a.get(d);if(e!=null)return e;for(var f=e=0;f<d.length;f++){var g=d[f];ve(b,g)!=null&&(e!==0&&(c=xe(b,c,e)),e=g)}a.set(d,e);return e} 
var Pe=function(a){var b=Oe;se(a);A(K(a));a=I?a[C(J)]:a.g;D(a,"state is only maintained on arrays.");var c=a[F]|0;H(a,c);var d=ve(a,4);b=qe(Rd(d,b,!0,c));d!==b&&(c=xe(a,c,4,b),te(a,c));return b};function Qe(a,b,c,d,e){var f=!1;d=ve(a,d,e,function(g){var h=Rd(g,c,!1,b);f=h!==g&&h!=null;return h});if(d!=null)return f&&!Ub(d)&&te(a,b),ye(d,a)} 
var Re=function(a){var b=Oe;A(K(a));a=I?a[C(J)]:a.g;D(a,"state is only maintained on arrays.");var c=a[F]|0;H(a,c);(a=Qe(a,c,b,4))||(a=b[tb])||(a=new b,A(K(a)),c=I?a[C(J)]:a.g,Gb(c,34),a=b[tb]=a);return a},Se=function(a,b,c){A(K(a));var d=I?a[C(J)]:a.g;var e=d;D(e,"state is only maintained on arrays.");var f=e[F]|0;H(e,f);b=Qe(d,f,b,c);if(b==null)return b;e=d;D(e,"state is only maintained on arrays.");f=e[F]|0;H(e,f);Ub(a,f)||(e=qe(b),e!==b&&(re(a)&&(A(K(a)),a=d=I?a[C(J)]:a.g,D(a,"state is only maintained on arrays."), 
b=a[F]|0,H(a,b),f=b),b=e,f=xe(d,f,c,b),te(d,f)));return ye(b,d)},Te=function(a,b,c,d){var e;A(K(a));var f=e=I?a[C(J)]:a.g;D(e,"state is only maintained on arrays.");var g=e[F]|0;H(e,g);e=!1;var h=Ub(a,g);d=h?1:d;e=!!e||d===3;h=!h;(d===2||h)&&re(a)&&(A(K(a)),a=f=I?a[C(J)]:a.g,D(a,"state is only maintained on arrays."),g=a[F]|0,H(a,g));a=Ce(f,c);var k=a;k===Db?k=7:(D(k,"state is only maintained on arrays."),k=k[F]|0);var m=k;var n=De(m,g);if(k=!(4&n)){var q=a,r=g,u=!!(2&n);u&&(r|=2);for(var w=!u,v= 
!0,B=0,L=0;B<q.length;B++){var na=Rd(q[B],b,!1,r);if(na instanceof b){if(!u){var oa=Ub(na);w&&(w=!oa);v&&(v=oa)}q[L++]=na}}L<B&&(q.length=L);n|=4;n=v?n&-4097:n|4096;n=w?n|8:n&-9}n!==m&&(G(a,n),2&n&&Object.freeze(a));if(h&&!(8&n||!a.length&&(d===1||(d!==4?0:2&n||!(16&n)&&32&g)))){Ge(n)&&(a=fd(a),n=Ee(n,g),g=C(xe(f,g,c,a)));b=a;h=n;for(m=0;m<b.length;m++)q=b[m],n=qe(q),q!==n&&(b[m]=n);h|=8;n=h=b.length?h|4096:h&-4097;G(a,n)}a=Fe(a,n,f,g,c,d,k,e);if(!e){c=a;d=d===2;d=d===void 0?!1:d;e=Hb(f);g=Hb(c); 
b=Object.isFrozen(c)&&g;ze(c,f,d);if(e||g)d?A(g):A(b);D(c,"state is only maintained on arrays.");A(!!((c[F]|0)&4));if(g&&c.length)for(d=0;d<1;d++)ye(c[d],f)}return a};function Ue(a,b){a!=null?Qd(a,C(b)):a=void 0;return a} 
var Ve=function(a,b,c,d){d=Ue(d,b);P(a,c,d);d&&!Ub(d)&&(A(K(a)),b=I?a[C(J)]:a.g,te(b));return a},We=function(a,b,c,d,e){e=Ue(e,b);Le(a,c,d,e);e&&!Ub(e)&&(A(K(a)),b=I?a[C(J)]:a.g,te(b));return a},Xe=function(a,b,c,d){se(a);A(K(a));var e=I?a[C(J)]:a.g;D(e,"state is only maintained on arrays.");var f=e[F]|0;H(e,f);if(d==null)return xe(e,f,c),a;Sd(d);var g=d;g===Db?g=7:(D(g,"state is only maintained on arrays."),g=g[F]|0);for(var h=g,k=Ge(g),m=k||Object.isFrozen(d),n=!0,q=!0,r=0;r<d.length;r++){var u= 
d[r];Qd(u,C(b));k||(u=Ub(u),n&&(n=!u),q&&(q=u))}k||(g=n?13:5,g=q?g&-4097:g|4096);m&&g===h||(d=fd(d),h=0,g=Ee(g,f));g!==h&&G(d,g);ac(d);f=xe(e,f,c,d);2&g||le(g)||te(e,f);return a};function Ee(a,b){return a=(2&b?a|2:a&-3)&-273}function Ye(a,b){se(a);a=Be(a,4,2,!0);a!==Db&&D(a,"state is only maintained on arrays.");if(Array.isArray(b))for(var c=b.length,d=0;d<c;d++)a.push(Nd(b[d]));else for(b=x(b),c=b.next();!c.done;c=b.next())a.push(Nd(c.value));bc(a)} 
var Ze=function(a,b){a=we(a,b);return a==null||typeof a==="boolean"?a:typeof a==="number"?!!a:void 0},$e=function(a,b){var c=c===void 0?!1:c;var d;return(d=Ze(a,b))!=null?d:c},af=function(a,b){var c=c===void 0?0:c;a=wd(we(a,b));return a!=null?a:c},bf=function(a,b){var c=c===void 0?0:c;a=xd(we(a,b));return a!=null?a:c},cf=function(a,b){var c=c===void 0?ue:c;a=Kd(we(a,b));return a!=null?a:c},df=function(a,b){var c=c===void 0?0:c;a=we(a,b,void 0,md);return a!=null?a:c},ef=function(a,b){var c=c===void 0? 
"":c;a=Pd(we(a,b));return a!=null?a:c},Q=function(a,b){var c=c===void 0?0:c;a=sd(we(a,b));return a!=null?a:c},ff=function(a){var b=b===void 0?"0":b;a=Md(we(a,3));return a!=null?a:b},R=function(a,b,c){return Q(a,Ne(a,c,b))},gf=function(a,b,c){b=Ne(a,c,b);return sd(we(a,b))},hf=function(a,b,c){c=c==null?c:yd(c);return Ie(a,b,c,"0")},jf=function(a,b,c){return Ie(a,b,c==null?c:rd(c),0)},S=function(a,b,c,d){return Le(a,b,c,d==null?d:rd(d))};A(!0);var T=function(a,b,c){this.preventPassingToStructuredClone=$b;Ka(this,T,"The message constructor should only be used by subclasses");A(this.constructor!==T,"Message is an abstract class and cannot be directly constructed");a=je(a,b,c);A(K(this));D(a);I?this[C(J)]=a:this.g=a;A(K(this));a=I?this[C(J)]:this.g;D(a,"state is only maintained on arrays.");b=a[F]|0;H(a,b);A(b&64);A(b&2048)};T.prototype.toJSON=function(){return ge(this)};var kf=function(a){return JSON.stringify(ge(a))}; 
T.prototype.getExtension=function(a){Ka(this,a.Ic);var b=Ka(this,T);$d(b,a.F);Zd(b,a.F,a.Ub);return a.ca?a.ib?a.qa(b,a.ca,a.F,Ae(),a.ea):a.qa(b,a.ca,a.F,a.ea):a.ib?a.qa(b,a.F,Ae(),a.ea):a.qa(b,a.F,a.defaultValue,a.ea)}; 
T.prototype.hasExtension=function(a){A(!a.ib,"repeated extensions don't support hasExtension");var b=Ka(this,T);$d(b,a.F);Zd(b,a.F,a.Ub);if(a.ca){var c=a.ca;var d=a.F;a=a.ea;A(K(b));b=I?b[C(J)]:b.g;D(b,"state is only maintained on arrays.");var e=b[F]|0;H(b,e);c=Qe(b,e,c,d,a)!==void 0}else A(!a.ib,"repeated extensions don't support getExtensionOrUndefined"),Ka(b,a.Ic),c=Ka(b,T),$d(c,a.F),Zd(c,a.F,a.Ub),c=a.ca?a.qa(c,a.ca,a.F,a.ea):a.qa(c,a.F,null,a.ea),c=(c===null?void 0:c)!==void 0;return c}; 
T.prototype.clone=function(){var a=Ka(this,T);A(a!=null&&K(a));A(K(a));var b=I?a[C(J)]:a.g;D(b,"state is only maintained on arrays.");var c=b[F]|0;H(b,c);return oe(a,b,c)?pe(a,b,!0):new a.constructor(ne(b,c,!1))};Mb=T;T.prototype[yb]=Ob;T.prototype.toString=function(){A(K(this));return(I?this[C(J)]:this.g).toString()};function lf(a){if(a instanceof T)return a.constructor.j};(function(){var a=y.jspbGetTypeName;y.jspbGetTypeName=a?function(b){return a(b)||lf(b)}:lf})();var U=T;function mf(a){return function(b){Ha(a);if(b==null||b=="")b=Ka(new a,T);else{Ga(b);b=JSON.parse(b);if(!Array.isArray(b))throw Error("Expected to deserialize an Array but got "+z(b)+": "+b);Gb(b,32);b=new a(b)}return b}};var nf=function(a){U.call(this,a)};t(nf,U);var of=mf(nf);nf.j="contentads.bow.rendering.client.Dv3MetaData";var pf=new function(a,b){this.key=a;this.defaultValue=b===void 0?0:b;this.valueType="number"}("100004",16E3);var qf,rf=64;function sf(){try{return qf!=null||(qf=new Uint32Array(64)),rf>=64&&(crypto.getRandomValues(qf),rf=0),qf[rf++]}catch(a){return Math.floor(Math.random()***********)}};var tf=function(a){var b=!1,c;return function(){b||(c=a(),b=!0);return c}};var uf=function(){return Pa&&Ta?!Ta.mobile&&(E("iPad")||E("Android")||E("Silk")):E("iPad")||E("Android")&&!E("Mobile")||E("Silk")};/* 
 
 Copyright Google LLC 
 SPDX-License-Identifier: Apache-2.0 
*/ 
var vf={};function wf(){if(vf!==vf)throw Error("Bad secret");};var yf=globalThis.trustedTypes,zf;function Af(){var a=null;if(!yf)return a;try{var b=function(c){return c};a=yf.createPolicy("goog#html",{createHTML:b,createScript:b,createScriptURL:b})}catch(c){throw c;}return a}function Bf(){zf===void 0&&(zf=Af());return zf};var Cf=function(a){wf();this.dc=a};Cf.prototype.toString=function(){return this.dc+""};function Df(a){var b=Bf();a=b?b.createScriptURL(a):a;return new Cf(a)}function Ef(a){if(a instanceof Cf)return a.dc;throw Error("Unexpected type when unwrapping TrustedResourceUrl");};var Ff=ra([""]),Gf=qa(["\x00"],["\\0"]),Hf=qa(["\n"],["\\n"]),If=qa(["\x00"],["\\u0000"]),Jf=ra([""]),Kf=qa(["\x00"],["\\0"]),Lf=qa(["\n"],["\\n"]),Mf=qa(["\x00"],["\\u0000"]);function Nf(a){return Object.isFrozen(a)&&Object.isFrozen(a.raw)}function Of(a){return a.toString().indexOf("`")===-1}var Pf=Of(function(a){return a(Ff)})||Of(function(a){return a(Gf)})||Of(function(a){return a(Hf)})||Of(function(a){return a(If)}),Qf=Nf(Jf)&&Nf(Kf)&&Nf(Lf)&&Nf(Mf);var Rf=function(a){wf();this.Wc=a};Rf.prototype.toString=function(){return this.Wc};new Rf("about:blank");new Rf("about:invalid#zClosurez");var Sf=/^\s*(?!javascript:)(?:[\w+.-]+:|[^:/?#]*(?:[/?#]|$))/i,Tf=[],Uf=function(){};Vf(function(a){console.warn("A URL with content '"+a+"' was sanitized away.")});function Vf(a){Tf.indexOf(a)===-1&&Tf.push(a);Uf=function(b){Tf.forEach(function(c){c(b)})}};var Wf=function(a){wf();this.cc=a};Wf.prototype.toString=function(){return this.cc+""};function Xf(a){var b=Bf();a=b?b.createHTML(a):a;return new Wf(a)}function Yf(a){if(a instanceof Wf)return a.cc;throw Error("Unexpected type when unwrapping SafeHtml");};function Zf(a){if(a instanceof Wf)return a;a=$f(String(a));return Xf(a)}function $f(a){return a.replace(/&/g,"&amp;").replace(/</g,"&lt;").replace(/>/g,"&gt;").replace(/"/g,"&quot;").replace(/'/g,"&apos;")}function ag(a){return bg(a)}function bg(a){var b=Zf("");return Xf(a.map(function(c){return Yf(Zf(c))}).join(Yf(b).toString()))} 
var cg=/^[a-z][a-z\d-]*$/i,dg="APPLET BASE EMBED IFRAME LINK MATH META OBJECT SCRIPT STYLE SVG TEMPLATE".split(" "),eg="AREA BR COL COMMAND HR IMG INPUT KEYGEN PARAM SOURCE TRACK WBR".split(" "),fg=["action","formaction","href"];function gg(a){if(!cg.test(a))throw Error("Invalid tag name <"+a+">.");if(dg.indexOf(a.toUpperCase())!==-1)throw Error("Tag name <"+a+"> is not allowed for createHtml.");} 
function hg(a,b){var c={};gg(a);var d="<"+a;c&&(d+=ig(c));Array.isArray(b)||(b=b===void 0?[]:[b]);if(eg.indexOf(a.toUpperCase())!==-1){if(b.length>0)throw Error("Void tag <"+a+"> does not allow content.");d+=">"}else b=ag(b.map(function(e){return e instanceof Wf?e:Zf(String(e))})),d+=">"+b.toString()+"</"+a+">";return Xf(d)} 
function ig(a){for(var b="",c=Object.keys(a),d=0;d<c.length;d++){var e=c[d],f=a[e];if(!cg.test(e))throw Error('Invalid attribute name "'+e+'".');if(f!==void 0&&f!==null){if(/^on./i.test(e))throw Error('Attribute "'+e+" is forbidden. Inline event handlers can lead to XSS. Please use the 'addEventListener' API instead.");if(fg.indexOf(e.toLowerCase())!==-1)if(f instanceof Rf)f=f.toString();else{var g=f=String(f),h=!Sf.test(g);h&&Uf(g);f=h?void 0:f;f=f||"about:invalid#zClosurez"}if(!(f instanceof Rf|| 
f instanceof Wf)&&typeof f!=="string"&&typeof f!=="number")throw Error("String or number value expected, got "+typeof f+" with value '"+f+"' given.");e=e+'="'+Zf(String(f))+'"';b+=" "+e}}return b};"ARTICLE SECTION NAV ASIDE H1 H2 H3 H4 H5 H6 HEADER FOOTER ADDRESS P HR PRE BLOCKQUOTE OL UL LH LI DL DT DD FIGURE FIGCAPTION MAIN DIV EM STRONG SMALL S CITE Q DFN ABBR RUBY RB RT RTC RP DATA TIME CODE VAR SAMP KBD SUB SUP I B U MARK BDI BDO SPAN BR WBR NOBR INS DEL PICTURE PARAM TRACK MAP TABLE CAPTION COLGROUP COL TBODY THEAD TFOOT TR TD TH SELECT DATALIST OPTGROUP OPTION OUTPUT PROGRESS METER FIELDSET LEGEND DETAILS SUMMARY MENU DIALOG SLOT CANVAS FONT CENTER ACRONYM BASEFONT BIG DIR HGROUP STRIKE TT".split(" ").concat(["BUTTON", 
"INPUT"]);function jg(a){var b=sa.apply(1,arguments),c=b.length;if(!Array.isArray(a)||!Array.isArray(a.raw)||a.length!==a.raw.length||!Pf&&a===a.raw||!(Pf&&!Qf||Nf(a))||c+1!==a.length)throw new TypeError("\n    ############################## ERROR ##############################\n\n    It looks like you are trying to call a template tag function (fn`...`)\n    using the normal function syntax (fn(...)), which is not supported.\n\n    The functions in the safevalues library are not designed to be called\n    like normal functions, and doing so invalidates the security guarantees\n    that safevalues provides.\n\n    If you are stuck and not sure how to proceed, please reach out to us\n    instead through:\n     - go/ise-hardening-yaqs (preferred) // LINE-INTERNAL\n     - g/ise-hardening // LINE-INTERNAL\n     - https://github.com/google/safevalues/issues\n\n    ############################## ERROR ##############################"); 
if(b.length===0)return Df(a[0]);c=a[0].toLowerCase();if(/^data:/.test(c))throw Error("Data URLs cannot have expressions in the template literal input.");if(/^https:\/\//.test(c)||/^\/\//.test(c)){var d=c.indexOf("//")+2;var e=c.indexOf("/",d);if(e<=d)throw Error("Can't interpolate data in a url's origin, Please make sure to fully specify the origin, terminated with '/'.");d=c.substring(d,e);if(!/^[0-9a-z.:-]+$/i.test(d))throw Error("The origin contains unsupported characters.");if(!/^[^:]*(:[0-9]+)?$/i.test(d))throw Error("Invalid port number."); 
if(!/(^|\.)[a-z][^.]*$/i.test(d))throw Error("The top-level domain must start with a letter.");d=!0}else d=!1;if(!d)if(/^\//.test(c))if(c==="/"||c.length>1&&c[1]!=="/"&&c[1]!=="\\")d=!0;else throw Error("The path start in the url is invalid.");else d=!1;if(!(d=d||RegExp("^[^:\\s\\\\/]+/").test(c)))if(/^about:blank/.test(c)){if(c!=="about:blank"&&!/^about:blank#/.test(c))throw Error("The about url is invalid.");d=!0}else d=!1;if(!d)throw Error("Trying to interpolate expressions in an unsupported url format."); 
c=a[0];for(d=0;d<b.length;d++)c+=encodeURIComponent(b[d])+a[d+1];return Df(c)}function kg(a,b){a=Ef(a).toString();var c=a.split(/[?#]/),d=/[?]/.test(a)?"?"+c[1]:"";return lg(c[0],d,/[#]/.test(a)?"#"+(d?c[2]:c[1]):"",b)} 
function lg(a,b,c,d){function e(g,h){g!=null&&(Array.isArray(g)?g.forEach(function(k){return e(k,h)}):(b+=f+encodeURIComponent(h)+"="+encodeURIComponent(g),f="&"))}var f=b.length?"&":"?";d.constructor===Object&&(d=Object.entries(d));Array.isArray(d)?d.forEach(function(g){return e(g[1],g[0])}):d.forEach(e);return Df(a+b+c)};var mg=function(a){try{var b;if(b=!!a&&a.location.href!=null)a:{try{fb(a.foo);b=!0;break a}catch(c){}b=!1}return b}catch(c){return!1}},og=function(a,b,c){b=b===void 0?!1:b;var d=d===void 0?y:d;c=(c===void 0?0:c)?ng(d):d;for(d=0;c&&d++<40&&(!b&&!mg(c)||!a(c));)c=ng(c)},ng=function(a){try{var b=a.parent;if(b&&b!=a)return b}catch(c){}return null},pg=function(){if(!globalThis.crypto)return Math.random();try{var a=new Uint32Array(1);globalThis.crypto.getRandomValues(a);return a[0]/65536/65536}catch(b){return Math.random()}}, 
qg=function(a,b){if(a)for(var c in a)Object.prototype.hasOwnProperty.call(a,c)&&b(a[c],c,a)},rg=tf(function(){return(Pa&&Ta?Ta.mobile:!uf()&&(E("iPod")||E("iPhone")||E("Android")||E("IEMobile")))?2:uf()?1:0}),sg=function(a,b){b=b===void 0?document:b;return b.createElement(String(a).toLowerCase())};var tg=[];function ug(){var a=tg;tg=[];a=x(a);for(var b=a.next();!b.done;b=a.next()){b=b.value;try{b()}catch(c){}}};var vg=function(a,b){b=String(b);a.contentType==="application/xhtml+xml"&&(b=b.toLowerCase());return a.createElement(b)};function wg(a){y.document.readyState==="complete"?(tg.push(a),tg.length===1&&(window.Promise?Promise.resolve().then(ug):(a=window.setImmediate,typeof a==="function"?a(ug):setTimeout(ug,0)))):y.addEventListener("load",a)};function xg(a,b,c,d,e){yg(a,b,c===void 0?null:c,d===void 0?!1:d,e===void 0?!1:e)} 
function yg(a,b,c,d,e){e=e===void 0?!1:e;a.google_image_requests||(a.google_image_requests=[]);var f=sg("IMG",a.document);if(c||d){var g=function(h){c&&c(h);if(d){h=a.google_image_requests;var k=cb(h,f);k>=0&&(A(h.length!=null),Array.prototype.splice.call(h,k,1))}typeof f.removeEventListener==="function"&&f.removeEventListener("load",g,!1);typeof f.removeEventListener==="function"&&f.removeEventListener("error",g,!1)};typeof f.addEventListener==="function"&&f.addEventListener("load",g,!1);typeof f.addEventListener=== 
"function"&&f.addEventListener("error",g,!1)}e&&(f.attributionSrc="");f.src=b;a.google_image_requests.push(f)}function zg(a){var b=b===void 0?!1:b;var c;if(c=y.navigator)c=y.navigator.userAgent,c=/Chrome/.test(c)&&!/Edge/.test(c)?!0:!1;c&&typeof y.navigator.sendBeacon==="function"?y.navigator.sendBeacon(a):xg(y,a,void 0,b)} 
function Ag(a){var b=b===void 0?!1:b;var c="https://pagead2.googlesyndication.com/pagead/gen_204?id=rcs_internal";qg(a,function(d,e){if(d||d===0)c+="&"+e+"="+encodeURIComponent(String(d))});Bg(c,b)} 
function Bg(a,b){var c=window;b=b===void 0?!1:b;var d=d===void 0?!1:d;c.fetch?(b={keepalive:!0,credentials:"include",redirect:"follow",method:"get",mode:"no-cors"},d&&(b.mode="cors","setAttributionReporting"in XMLHttpRequest.prototype?b.attributionReporting={eventSourceEligible:"true",triggerEligible:"false"}:b.headers={"Attribution-Reporting-Eligible":"event-source"}),c.fetch(a,b)):xg(c,a,void 0,b,d)};var Cg=function(){this.Za={}},Fg=function(){Dg||(Dg=new Eg);var a=Dg.Za[pf.key];if(pf.valueType==="proto"){try{var b=JSON.parse(a);if(Array.isArray(b))return b}catch(c){}return pf.defaultValue}return typeof a===typeof pf.defaultValue?a:pf.defaultValue};var Eg=function(){this.Za={};var a=document.currentScript;a=(a=(a=a===void 0?null:a)&&a.getAttribute("data-jc")==="0"?a:document.querySelector('[data-jc="0"]'))&&a.getAttribute("data-jc-flags")||"";try{var b=JSON.parse(a)[0];a="";for(var c=0;c<b.length;c++)a+=String.fromCharCode(b.charCodeAt(c)^"\u0003\u0007\u0003\u0007\b\u0004\u0004\u0006\u0005\u0003".charCodeAt(c%10));this.Za=JSON.parse(a)}catch(d){}},Dg;t(Eg,Cg);var Gg=function(a){U.call(this,a)};t(Gg,U);Gg.j="contentads.shared.capture.remote.ClientPingMetadata";var Hg=function(a,b){var c=c===void 0?{}:c;this.error=a;this.meta=c;this.context=b.context;this.msg=b.message||"";this.id=b.id||"jserror"};function Ig(a){var b=a.toString();a.name&&b.indexOf(a.name)==-1&&(b+=": "+a.name);a.message&&b.indexOf(a.message)==-1&&(b+=": "+a.message);if(a.stack)a:{a=a.stack;var c=b;try{a.indexOf(c)==-1&&(a=c+"\n"+a);for(var d;a!=d;)d=a,a=a.replace(RegExp("((https?:/..*/)[^/:]*:\\d+(?:.|\n)*)\\2"),"$1");b=a.replace(RegExp("\n *","g"),"\n");break a}catch(e){b=c;break a}b=void 0}return b};function Jg(a,b){try{var c=function(d){var e={};return[(e[d.fd]=d.Pc,e)]};return JSON.stringify([a.filter(function(d){return d.Wb}).map(c),ge(b),a.filter(function(d){return!d.Wb}).map(c)])}catch(d){return Kg(d,b),""}}function Kg(a,b){try{Ag({m:Ig(a instanceof Error?a:Error(String(a))),b:Q(b,1)||null,v:ef(b,2)||null})}catch(c){}} 
var Lg=function(a,b){var c=new Gg;a=jf(c,1,a);b=Ie(a,2,Od(b),"");A(K(b));a=I?b[C(J)]:b.g;D(a,"state is only maintained on arrays.");c=a[F]|0;H(a,c);this.Va=Ub(b,c)?b:oe(b,a,c)?pe(b,a):new b.constructor(ne(a,c,!0))};var Mg=function(a){U.call(this,a)};t(Mg,U);Mg.prototype.Fa=function(){return Q(this,3)};Mg.prototype.ob=function(a){return P(this,3,a==null?a:rd(a))};Mg.j="tagging.common.experiments.GenotypeDisjointConditionCollisionEvent";var Ng=function(a){U.call(this,a)};t(Ng,U);Ng.prototype.R=function(){return Q(this,1)};Ng.prototype.ha=function(a){return jf(this,1,a)};var Og=function(a,b){return jf(a,2,b)};Ng.j="tagging.common.experiments.GenotypeDiversionEvent.DiversionPointInBinary";var Pg=function(a){U.call(this,a)};t(Pg,U);Pg.prototype.bb=function(){return R(this,2,Qg)};Pg.prototype.qb=function(a){S(this,2,Qg,a)};Pg.j="tagging.common.experiments.GenotypeDiversionEvent.ModAssignment";var Qg=[1,2];var Rg=function(a){U.call(this,a)};t(Rg,U);var Sg=function(a,b){return Ve(a,Ng,1,b)},Tg=function(a,b){return Xe(a,Ng,2,b)},Ug=function(a,b){return He(a,4,b)},Vg=function(a,b){return Xe(a,Pg,5,b)},Wg=function(a,b){return jf(a,6,b)};Rg.j="tagging.common.experiments.GenotypeDiversionEvent";var Xg=function(a){U.call(this,a)};t(Xg,U);l=Xg.prototype;l.Ga=function(){return Me(this,Yg)};l.pa=function(){return R(this,1,Yg)};l.ec=function(a){S(this,1,Yg,a)};l.Ja=function(){return R(this,2,Yg)};l.jc=function(a){S(this,2,Yg,a)};l.Ka=function(){return R(this,3,Yg)};l.mc=function(a){S(this,3,Yg,a)};l.sa=function(){return R(this,4,Yg)};l.nc=function(a){S(this,4,Yg,a)};l.Ia=function(){return R(this,6,Yg)};l.hc=function(a){S(this,6,Yg,a)};l.Rb=function(){return gf(this,6,Yg)!=null};Xg.j="tagging.common.experiments.GenotypeFlagsOverriddenAfterAccessEvent.FlagOverriddenAfterAccess"; 
var Yg=[1,2,3,4,6];var Zg=function(a){U.call(this,a)};t(Zg,U);Zg.prototype.pb=function(a){return Ie(this,1,vd(a),0)};Zg.j="tagging.common.experiments.GenotypeFlagsOverriddenAfterAccessEvent";var $g=function(a){U.call(this,a)};t($g,U);$g.prototype.Ob=function(){return Me(this,ah)};var bh=function(a,b){return P(a,1,b==null?b:rd(b))};l=$g.prototype;l.Jb=function(){return R(this,2,ah)};l.fc=function(a){return S(this,2,ah,a)};l.Nb=function(){return R(this,3,ah)};l.kc=function(a){return S(this,3,ah,a)};l.Qb=function(){return R(this,4,ah)};l.oc=function(a){return S(this,4,ah,a)};$g.j="tagging.common.experiments.GenotypePropertyErrorEvent.PropertyError";var ah=[2,3,4];var ch=function(a){U.call(this,a)};t(ch,U);l=ch.prototype;l.Ob=function(){return Me(this,dh)};l.Jb=function(){return R(this,3,dh)};l.fc=function(a){return S(this,3,dh,a)};l.Nb=function(){return R(this,4,dh)};l.kc=function(a){return S(this,4,dh,a)};l.Qb=function(){return R(this,5,dh)};l.oc=function(a){return S(this,5,dh,a)};ch.j="tagging.common.experiments.Condition";var dh=[3,4,5],eh=[6,7];var fh=function(a){U.call(this,a)};t(fh,U);l=fh.prototype;l.P=function(){return Se(this,ch,1)};l.Oa=function(a){return Ve(this,ch,1,a)};l.R=function(){return Q(this,2)};l.ha=function(a){return P(this,2,a==null?a:rd(a))};l.pb=function(a){return P(this,3,vd(a))};fh.j="tagging.common.experiments.GenotypePropertyErrorEvent";var gh=[4,5];var hh=function(a){U.call(this,a)};t(hh,U);hh.prototype.Pb=function(){return cf(this,6)};hh.prototype.lc=function(a){return hf(this,6,a)};var jh=function(a){var b=new hh;return We(b,Rg,4,ih,a)};hh.j="tagging.common.experiments.GenotypeEvent";var ih=[4,5,7,8,9];var kh=function(){Lg.apply(this,arguments)};t(kh,Lg);var lh=function(){kh.apply(this,arguments)};t(lh,kh);lh.prototype.Ma=function(){this.cd.apply(this,pa(sa.apply(0,arguments).map(function(a){return{Wb:!0,fd:4,Pc:ge(a)}})))};function mh(a,b){if(globalThis.fetch)globalThis.fetch(a,{method:"POST",body:b,keepalive:b.length<65536,credentials:"omit",mode:"no-cors",redirect:"follow"}).catch(function(){});else{var c=new XMLHttpRequest;c.open("POST",a,!0);c.send(b)}};var nh=function(a,b,c,d,e,f,g,h){lh.call(this,a,b);this.Vc=c;this.Uc=d;this.Xc=e;this.Nc=f;this.Tc=g;this.Ab=h;this.T=[];this.xa=null;this.Tb=!1};t(nh,lh);var oh=function(a){a.xa!==null&&(clearTimeout(a.xa),a.xa=null);if(a.T.length){var b=Jg(a.T,a.Va);a.Uc(a.Vc+"?e=1",b);a.T=[]}}; 
nh.prototype.cd=function(){var a=sa.apply(0,arguments),b=this;try{this.Tc&&Jg(this.T.concat(a),this.Va).length>=65536&&oh(this),this.Ab&&!this.Tb&&(this.Tb=!0,this.Ab.wd(function(){oh(b)})),this.T.push.apply(this.T,pa(a)),this.T.length>=this.Nc&&oh(this),this.T.length&&this.xa===null&&(this.xa=setTimeout(function(){oh(b)},this.Xc))}catch(c){Kg(c,this.Va)}}; 
var ph=function(a,b,c,d,e,f){nh.call(this,a,b,"https://pagead2.googlesyndication.com/pagead/ping",mh,c===void 0?1E3:c,d===void 0?100:d,(e===void 0?!1:e)&&!!globalThis.fetch,f)};t(ph,nh);var qh=new function(a,b){b=b===void 0?[]:b;this.Xa=a;this.defaultValue=b}(6001,["A3vKT9yxRPjmXN3DpIiz58f5JykcWHjUo/W7hvmtjgh9jPpQgem9VbADiNovG8NkO6mRmk70Kex8/KUqAYWVWAEAAACLeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiUHJpdmFjeVNhbmRib3hBZHNBUElzIiwiZXhwaXJ5IjoxNjk1MTY3OTk5LCJpc1N1YmRvbWFpbiI6dHJ1ZSwiaXNUaGlyZFBhcnR5Ijp0cnVlfQ=="]),rh=new function(a,b){this.Xa=a;this.defaultValue=b===void 0?!1:b}(6E3,!0);function sh(a){A(a==null||Rc(a));if(a!=null)return A(Rc(a),"Expected a gbigint, got "+a+" of type "+typeof a),A(Xc(a),"Expected a safe int52, got "+a),Xc(a)?Number(a):String(a)};var th=["FRAME","IMG","IFRAME"],uh=/^[01](px)?$/,vh=function(){this.Vb=this.Ua=this.bc=this.Fb=!1},wh=function(){var a=new vh;a.Fb=!1;a.bc=!0;a.Ua=!0;a.Vb=!0;return a};function xh(a){return typeof a==="string"?document.getElementById(a):a} 
function yh(a,b){b=b===void 0?!1:b;if(a.tagName==="IMG"){if(a.complete&&(!a.naturalWidth||!a.naturalHeight))return!0;var c;if(b&&((c=a.style)==null?void 0:c.display)==="none")return!0}var d,e;return uh.test((d=a.getAttribute("width"))!=null?d:"")&&uh.test((e=a.getAttribute("height"))!=null?e:"")} 
function zh(a,b){if(a.tagName==="IMG")return a.naturalWidth&&a.naturalHeight?!0:!1;try{if(a.readyState)var c=a.readyState;else{var d,e;c=(d=a.contentWindow)==null?void 0:(e=d.document)==null?void 0:e.readyState}return c==="complete"}catch(f){return b===void 0?!1:b}}function Ah(a){a||(a=function(b,c,d){b.addEventListener(c,d)});return a} 
function Bh(a,b,c){c=c===void 0?new vh:c;if(a=xh(a)){var d=Ah(d);for(var e=!1,f=function(B){e||(e=!0,b(B))},g,h=2,k=0;k<th.length;++k)if(th[k]===a.tagName){h=3;g=[a];break}g||(g=a.querySelectorAll(th.join(",")));var m=0,n=0,q=!c.Ua,r=a=!1;k={};for(var u=0;u<g.length;k={La:void 0},u++){var w=g[u];if(!yh(w,c.Ua))if(k.La=w.tagName==="IMG",zh(w,c.Fb))a=!0,k.La&&(q=!0);else{m++;var v=function(B){return function(L){m--;!m&&q&&f(h);B.La&&(L=L&&L.type==="error",n--,L||(q=!0),!n&&r&&q&&f(h))}}(k);d(w,"load", 
v);k.La&&(n++,d(w,"error",v))}}n===0&&(q=!0);g=null;g=y.document.readyState==="complete";if(c.Vb&&g){if(n>0){r=!0;return}h=5}else if(m===0&&!a&&g)h=5;else if(m||!a){d(y,"load",function(){!c.bc||!n&&q?f(4):r=!0});return}f(h)}};var Ch=RegExp("^https?://(\\w|-)+\\.cdn\\.ampproject\\.(net|org)(\\?|/|$)"),Fh=function(){if(!b){var a=y;var b=[];var c=null;do{var d=a;if(mg(d)){var e=d.location.href;c=d.document&&d.document.referrer||null}else e=c,c=null;b.push(new Dh(e||""));try{a=d.parent}catch(g){a=null}}while(a&&d!==a);d=0;for(a=b.length-1;d<=a;++d)b[d].depth=a-d;d=y;if(d.location&&d.location.ancestorOrigins&&d.location.ancestorOrigins.length===b.length-1)for(a=1;a<b.length;++a)e=b[a],e.url||(e.url=d.location.ancestorOrigins[a- 
1]||"",e.Sb=!0)}d=new Dh(y.location.href,!1);e=null;for(c=a=b.length-1;c>=0;--c){var f=b[c];!e&&Ch.test(f.url)&&(e=f);if(f.url&&!f.Sb){d=f;break}}e=null;c=b.length&&b[a].url;d.depth!==0&&c&&(e=b[a]);return new Eh(d,e)},Eh=function(a,b){this.za=a;this.Ra=b},Dh=function(a,b){this.url=a;this.Sb=!!b;this.depth=null};var Gh=null,Hh=function(){var a=a===void 0?window:a;if(Gh===null){Gh="";try{var b="";try{b=a.top.location.hash}catch(d){b=a.location.hash}if(b){var c=b.match(/\bdeid=([\d,]+)/);Gh=c?c[1]:""}}catch(d){}}return Gh};var Ih=y.performance,Jh=!!(Ih&&Ih.mark&&Ih.measure&&Ih.clearMarks),Kh=tf(function(){var a;if(a=Jh)a=Hh(),a=!!a.indexOf&&a.indexOf("1337")>=0;return a});var Lh=function(a){this.Oc=a===void 0?4E3:a;this.kb="&";this.ba={};this.Rc=0;this.ka=[]},Mh=function(a,b){var c={};c[a]=b;return[c]},Oh=function(a,b,c,d,e){var f=[];qg(a,function(g,h){(g=Nh(g,b,c,d,e))&&f.push(h+"="+g)});return f.join(b)},Nh=function(a,b,c,d,e){if(a==null)return"";b=b||"&";c=c||",$";typeof c==="string"&&(c=c.split(""));if(a instanceof Array){if(d||(d=0),d<c.length){for(var f=[],g=0;g<a.length;g++)f.push(Nh(a[g],b,c,d+1,e));return f.join(c[d])}}else if(typeof a==="object")return e|| 
(e=0),e<2?encodeURIComponent(Oh(a,b,c,d,e+1)):"...";return encodeURIComponent(String(a))},Ph=function(a,b,c){A(!a.ba[b]);a.ka.push(b);a.ba[b]=c},Qh=function(a,b,c,d){A(!a.ba[b]);a.ka.push(b);a.ba[b]=Mh(c,d)},Rh=function(a,b,c){var d=a.Rc++;Ph(a,d,Mh(b,c))},Th=function(a,b,c,d){b=b+"//"+c+d;var e=Sh(a)-d.length;if(e<0)return"";a.ka.sort(function(n,q){return n-q});d=null;c="";for(var f=0;f<a.ka.length;f++)for(var g=a.ka[f],h=a.ba[g],k=0;k<h.length;k++){if(!e){d=d==null?g:d;break}var m=Oh(h[k],a.kb, 
",$");if(m){m=c+m;if(e>=m.length){e-=m.length;b+=m;c=a.kb;break}d=d==null?g:d}}a="";d!=null&&(a=""+c+"trn="+d);return b+a},Sh=function(a){var b=1,c;for(c in a.ba)c.length>b&&(b=c.length);return a.Oc-3-b-a.kb.length-1};var Uh=RegExp("^(?:([^:/?#.]+):)?(?://(?:([^\\\\/?#]*)@)?([^\\\\/?#]*?)(?::([0-9]+))?(?=[\\\\/?#]|$))?([^?#]+)?(?:\\?([^#]*))?(?:#([\\s\\S]*))?$"),Vh=function(a,b){if(a){a=a.split("&");for(var c=0;c<a.length;c++){var d=a[c].indexOf("="),e=null;if(d>=0){var f=a[c].substring(0,d);e=a[c].substring(d+1)}else f=a[c];b(f,e?decodeURIComponent(e.replace(/\+/g," ")):"")}}};var Xh=function(){this.Z=Wh;this.Gc="dv3-err";this.qc=!0;this.zb=this.Qa=null;this.sc=!1;this.Fc=this.Xb},Zh=function(a,b){var c=Yh;try{if(c.Qa&&c.Qa.zd){var d=c.Qa.start((66).toString(),3);var e=a();c.Qa.end(d)}else e=a()}catch(h){a=c.qc;try{d&&Ih&&Kh()&&(Ih.clearMarks("goog_"+d.label+"_"+d.uniqueId+"_start"),Ih.clearMarks("goog_"+d.label+"_"+d.uniqueId+"_end")),a=c.Fc(66,new Hg(h,{message:Ig(h)}),void 0,b)}catch(k){c.Xb(217,k)}if(a){var f,g;(f=window.console)==null||(g=f.error)==null||g.call(f, 
h)}else throw h;}return e},ai=function(a){var b=$h;return function(){var c=sa.apply(0,arguments);return Zh(function(){return a.apply(void 0,c)},b)}}; 
Xh.prototype.Xb=function(a,b,c,d,e){e=e||this.Gc;var f=void 0;try{var g=new Lh;Qh(g,1,"context",a);b.error&&b.meta&&b.id||(b=new Hg(b,{message:Ig(b)}));b.msg&&Qh(g,2,"msg",b.msg.substring(0,512));var h=b.meta||{};if(this.zb)try{this.zb(h)}catch(B){}if(d)try{d(h)}catch(B){}Ph(g,3,[h]);f=Fh();f.Ra&&Qh(g,4,"top",f.Ra.url||"");var k={url:f.za.url||""};if(f.za.url){var m=f.za.url.match(Uh),n=m[1],q=m[3],r=m[4];d="";n&&(d+=n+":");q&&(d+="//",d+=q,r&&(d+=":"+r));var u=d}else u="";Ph(g,5,[k,{url:u}]);bi(this.Z, 
e,g,this.sc,c)}catch(B){try{var w,v;bi(this.Z,e,{context:"ecmserr",rctx:a,msg:Ig(B),url:(v=(w=f)==null?void 0:w.za.url)!=null?v:""},this.sc,c)}catch(L){}}return this.qc};function ci(a,b){var c=Te(a,ch,2,Ae());if(!c.length)return di(a,b);a=Q(a,1);if(a===1){var d=ci(c[0],b);return d.success?{success:!0,value:!d.value}:d}c=db(c,function(h){return ci(h,b)});switch(a){case 2:var e;return(e=(d=c.find(function(h){return h.success&&!h.value}))!=null?d:c.find(function(h){return!h.success}))!=null?e:{success:!0,value:!0};case 3:var f,g;return(g=(f=c.find(function(h){return h.success&&h.value}))!=null?f:c.find(function(h){return!h.success}))!=null?g:{success:!0,value:!1};default:return{success:!1, 
J:3}}} 
function di(a,b){var c=a.Ob();a:{switch(c){case 3:var d=a.Jb();break a;case 4:d=a.Nb();break a;case 5:d=a.Qb();break a}d=void 0}if(!d)return{success:!1,J:2};b=(b=b[c])&&b[d];if(!b)return{success:!1,property:d,wa:c,J:1};try{var e=b.apply;var f=Be(a,8,Ae());var g=e.call(b,null,pa(f))}catch(h){return{success:!1,property:d,wa:c,J:2}}e=Q(a,1);if(e===4)return{success:!0,value:!!g};if(e===5)return{success:!0,value:g!=null};if(e===12)a=ef(a,Ne(a,eh,7));else a:{switch(c){case 4:a=df(a,Ne(a,eh,6));break a;case 5:a= 
ef(a,Ne(a,eh,7));break a}a=void 0}if(a==null)return{success:!1,property:d,wa:c,J:3};if(e===6)return{success:!0,value:g===a};if(e===9)return{success:!0,value:g!=null&&Na(String(g),a)===0};if(g==null)return{success:!1,property:d,wa:c,J:4};switch(e){case 7:c=g<a;break;case 8:c=g>a;break;case 12:c=rc(a)&&rc(g)&&(new RegExp(a)).test(g);break;case 10:c=g!=null&&Na(String(g),a)===-1;break;case 11:c=g!=null&&Na(String(g),a)===1;break;default:return{success:!1,J:3}}return{success:!0,value:c}} 
function ei(a,b){return a?b?ci(a,b):{success:!1,J:1}:{success:!0,value:!0}};var Oe=function(a){U.call(this,a)};t(Oe,U);Oe.prototype.getStringValue=function(){return ef(this,3)};Oe.prototype.setStringValue=function(a){return Ie(this,3,Od(a),"")};var fi=function(a){return Be(a,4,Ae())};Oe.j="tagging.common.experiments.FlagValue";var gi=function(a){U.call(this,a)};t(gi,U);gi.prototype.P=function(){return Se(this,ch,1)};gi.prototype.Oa=function(a){return Ve(this,ch,1,a)};gi.prototype.getValue=function(){return Se(this,Oe,2)};gi.j="tagging.common.experiments.ConditionalValue";var hi=function(a){U.call(this,a)};t(hi,U);l=hi.prototype;l.Ga=function(){return Me(this,ii)};l.pa=function(){return R(this,1,ii)};l.ec=function(a){S(this,1,ii,a)};l.Ja=function(){return R(this,2,ii)};l.jc=function(a){S(this,2,ii,a)};l.Ka=function(){return R(this,3,ii)};l.mc=function(a){S(this,3,ii,a)};l.sa=function(){return R(this,6,ii)};l.nc=function(a){S(this,6,ii,a)};l.Ia=function(){return R(this,8,ii)};l.hc=function(a){S(this,8,ii,a)};l.Rb=function(){return gf(this,8,ii)!=null};var ji=mf(hi); 
hi.j="tagging.common.experiments.FlagDefinition";var ii=[1,2,3,6,7,8];var ki=function(a,b,c){var d=d===void 0?new ph(6,"unknown",b):d;this.Zc=a;this.Yc=c;this.Z=d;this.va=[];this.Na=a>0&&pg()<1/a},mi=function(a,b,c,d,e,f){if(a.Na){var g=Og((new Ng).ha(b),c);b=Wg(Tg(Sg(Vg(Ug(new Rg,d),e),g),a.va.slice()),f);b=jh(b);a.Z.Ma(li(a,b));if(f===1||f===3||f===4&&!a.va.some(function(h){return h.R()===g.R()&&Q(h,2)===c}))a.va.push(g),a.va.length>100&&a.va.shift()}},ni=function(a,b,c,d){if(a.Na){var e=new Mg;b=P(e,1,vd(b));c=P(b,2,vd(c)).ob(d);d=new hh;c=We(d,Mg,8,ih,c);a.Z.Ma(li(a, 
c))}},oi=function(a,b,c,d,e){if(a.Na){b=(new fh).Oa(b).ha(c).pb(d);if(e.wa===void 0)S(b,4,gh,e.J);else switch(e.wa){case 3:e=bh((new $g).fc(e.property),e.J);We(b,$g,5,gh,e);break;case 4:e=bh((new $g).kc(e.property),e.J);We(b,$g,5,gh,e);break;case 5:e=bh((new $g).oc(e.property),e.J),We(b,$g,5,gh,e)}e=new hh;b=We(e,fh,9,ih,b);a.Z.Ma(li(a,b))}},li=function(a,b){var c=Date.now();c=Number.isFinite(c)?Math.round(c):0;b=hf(b,1,c);c=window;if(!qc(c.goog_pvsid))try{var d=sf()+(sf()&2097151)***********;Object.defineProperty(c, 
"goog_pvsid",{value:d,configurable:!0})}catch(e){}return hf(b,2,Number(c.goog_pvsid)||-1).lc(a.Zc)};var pi=[],V=function(a){A(!Object.isSealed(a),"Cannot use getInstance() with a sealed constructor.");var b="hb";if(a.hb&&a.hasOwnProperty(b))return a.hb;pi.push(a);var c=new a;a.hb=c;A(a.hasOwnProperty(b),"Could not instantiate singleton.");return c};var qi=function(){var a={};this.H=(a[3]={},a[4]={},a[5]={},a)};l=qi.prototype;l.tb=function(a){this.H=a};l.Ba=function(a){for(var b=x([3,4,5]),c=b.next();!c.done;c=b.next())c=c.value,Object.assign(lc(this.H[c],pc),a[c])};l.nb=function(a,b){this.H[3][a]==null&&(this.H[3][a]=b)};l.sb=function(a,b){this.H[4][a]==null&&(this.H[4][a]=b)};l.ub=function(a,b){this.H[5][a]==null&&(this.H[5][a]=b)};var ri=/^true$/.test("false");function si(a,b){switch(b){case 1:return a.pa();case 2:return a.Ja();case 3:return a.Ka();case 6:return a.sa();case 8:return a.Ia();default:return null}}function ti(a,b){if(!a)return null;switch(b){case 1:return $e(a,1);case 7:return a.getStringValue();case 2:return df(a,2);case 3:return a.getStringValue();case 6:return fi(a);case 8:return fi(a);default:return null}} 
var ui=tf(function(){if(!ri)return{};try{var a=a===void 0?window:a;try{var b=a.sessionStorage.getItem("GGDFSSK")}catch(c){b=null}if(b)return JSON.parse(b)}catch(c){}return{}});function vi(a,b,c,d){var e=d=d===void 0?0:d,f,g;V(wi).ab[e]=(g=(f=V(wi).ab[e])==null?void 0:f.add(b))!=null?g:(new Set).add(b);e=ui();if(e[b]!=null)return e[b];b=xi(d)[b];if(!b)return c;b=ji(JSON.stringify(b));b=yi(b);a=ti(b,a);return a!=null?a:c} 
function yi(a){var b=V(qi).H;if(b&&a.Ga()!==8){var c=eb(Te(a,gi,5,Ae()),function(f){f=ei(f.P(),b);return f.success&&f.value});if(c){var d;return(d=c.getValue())!=null?d:null}}var e;return(e=Se(a,Oe,4))!=null?e:null}var wi=function(){this.Ya={};this.zc=[];this.ab={};this.W=new Map};function zi(a,b,c){return!!vi(1,a,b===void 0?!1:b,c)}function Ai(a,b,c){b=b===void 0?0:b;a=Number(vi(2,a,b,c));return isNaN(a)?b:a}function Bi(a,b,c){b=b===void 0?"":b;a=vi(3,a,b,c);return typeof a==="string"?a:b} 
function Ci(a,b,c){b=b===void 0?[]:b;a=vi(6,a,b,c);return Array.isArray(a)?a:b}function Di(a,b,c){b=b===void 0?[]:b;a=vi(8,a,b,c);return Array.isArray(a)?a:b}function xi(a){return V(wi).Ya[a]||(V(wi).Ya[a]={})}function Ei(a,b){var c=xi(b);qg(a,function(d,e){if(c[e]){d=ji(JSON.stringify(d));if(d.Rb()){var f=ji(JSON.stringify(c[e])),g=Pe(d);f=fi(Re(f));Ye(g,f)}c[e]=ge(d)}else c[e]=d})} 
function Fi(a,b,c,d,e){e=e===void 0?!1:e;var f=[],g=[];b=x(b);for(var h=b.next();!h.done;h=b.next()){h=h.value;for(var k=xi(h),m=x(a),n=m.next();!n.done;n=m.next()){n=n.value;var q=n.Ga(),r=si(n,q);if(r){var u=void 0,w=void 0,v=void 0;var B=(u=(v=V(wi).W.get(h))==null?void 0:(w=v.get(r))==null?void 0:w.slice(0))!=null?u:[];a:{u=r;w=q;v=new Xg;switch(w){case 1:v.ec(u);break;case 2:v.jc(u);break;case 3:v.mc(u);break;case 6:v.nc(u);break;case 8:v.hc(u);break;default:B=void 0;break a}He(v,5,B);B=v}if(u= 
B)w=void 0,u=!((w=V(wi).ab[h])==null||!w.has(r));u&&f.push(B);if(q===8&&k[r])B=ji(JSON.stringify(k[r])),q=Pe(n),B=fi(Re(B)),Ye(q,B);else{if(q=B)u=void 0,q=!((u=V(wi).W.get(h))==null||!u.has(r));q&&g.push(B)}e||(q=r,B=h,u=d,w=V(wi),w.W.has(B)||w.W.set(B,new Map),w.W.get(B).has(q)||w.W.get(B).set(q,[]),u&&w.W.get(B).get(q).push(u));k[r]=ge(n)}}}if(f.length||g.length)a=d!=null?d:void 0,c.Na&&c.Yc&&(d=new Zg,f=Xe(d,Xg,2,f),g=Xe(f,Xg,3,g),a&&g.pb(a),f=new hh,g=We(f,Zg,7,ih,g),c.Z.Ma(li(c,g)))} 
function Gi(a,b){b=xi(b);a=x(a);for(var c=a.next();!c.done;c=a.next()){c=c.value;var d=ji(JSON.stringify(c)),e=d.Ga();(d=si(d,e))&&(b[d]||(b[d]=c))}}function Hi(){return Object.keys(V(wi).Ya).map(function(a){return Number(a)})}function Ii(a){V(wi).zc.includes(a)||Ei(xi(4),a)};function W(a,b,c){c.hasOwnProperty(a)||Object.defineProperty(c,String(a),{value:b})}function X(a,b,c){return b[a]||c}function Ji(a){W(5,zi,a);W(6,Ai,a);W(7,Bi,a);W(8,Ci,a);W(17,Di,a);W(13,Gi,a);W(15,Ii,a)}function Ki(a){W(4,function(b){return void V(qi).tb(b)},a);W(9,function(b,c){return void V(qi).nb(b,c)},a);W(10,function(b,c){return void V(qi).sb(b,c)},a);W(11,function(b,c){return void V(qi).ub(b,c)},a);W(14,function(b){return void V(qi).Ba(b)},a)} 
function Li(a){a.hasOwnProperty("init-done")||Object.defineProperty(a,"init-done",{value:!0})};var Mi=function(){};l=Mi.prototype;l.la=function(){};l.ja=function(){};l.Da=function(){return[]};l.da=function(){return[]};l.gb=function(a,b){this.la=X(1,a,function(){});this.Da=function(c,d){return X(2,a,function(){return[]})(c,b,d)};this.da=function(){return X(3,a,function(){return[]})(b)};this.ja=function(c){X(16,a,function(){})(c,b)}};function Ni(a){V(Mi).ja(a)}function Oi(a){V(Mi).Da(22,a)};function Pi(a,b){b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.allowedFeatures().includes(a))};function Qi(a,b){try{var c=a.split(".");a=y;for(var d=0,e;a!=null&&d<c.length;d++)e=a,a=a[c[d]],typeof a==="function"&&(a=e[c[d]]());var f=a;if(typeof f===b)return f}catch(g){}} 
var Ri={},Si={},Ti={},Ui={},Vi=(Ui[3]=(Ri[8]=function(a){try{return xa(a)!=null}catch(b){}},Ri[9]=function(a){try{var b=xa(a)}catch(c){return}if(a=typeof b==="function")b=b&&b.toString&&b.toString(),a=typeof b==="string"&&b.indexOf("[native code]")!=-1;return a},Ri[10]=function(){return window===window.top},Ri[6]=function(a){var b=V(Mi).da();return cb(b,Number(a))>=0},Ri[27]=function(a){a=Qi(a,"boolean");return a!==void 0?a:void 0},Ri[60]=function(a){try{return!!y.document.querySelector(a)}catch(b){}}, 
Ri[80]=function(a){try{return!!y.matchMedia(a).matches}catch(b){}},Ri[69]=function(a){var b=y.document;b=b===void 0?document:b;var c;return!((c=b.featurePolicy)==null||!c.features().includes(a))},Ri[70]=function(a){return Pi(a,y.document)},Ri[79]=function(a){var b=y.navigator;b=b===void 0?navigator:b;try{var c,d;var e=!!((c=b.protectedAudience)==null?0:(d=c.queryFeatureSupport)==null?0:d.call(c,a))}catch(f){e=!1}return e},Ri),Ui[4]=(Si[3]=function(){return rg()},Si[6]=function(a){a=Qi(a,"number"); 
return a!==void 0?a:void 0},Si),Ui[5]=(Ti[2]=function(){return window.location.href},Ti[3]=function(){try{return window.top.location.hash}catch(a){return""}},Ti[4]=function(a){a=Qi(a,"string");return a!==void 0?a:void 0},Ti[12]=function(a){try{var b=Qi(a,"string");if(b!==void 0)return atob(b)}catch(c){}},Ti),Ui);function Wi(){var a=a===void 0?y:a;return a.ggeac||(a.ggeac={})};var Xi=function(a){U.call(this,a)};t(Xi,U);l=Xi.prototype;l.Ha=function(a){return Te(this,hi,2,Ae(a))};l.P=function(){return Se(this,ch,3)};l.Oa=function(a){return Ve(this,ch,3,a)};l.eb=function(){return af(this,4)};l.ra=function(){return Q(this,6)};l.R=function(){return Q(this,7)};l.ha=function(a){return jf(this,7,a)};l.bb=function(){return Q(this,13)};l.qb=function(a){jf(this,13,a)};l.Mb=function(){return Q(this,14)};l.Lb=function(){return bf(this,11)};l.Fa=function(){return Q(this,17)}; 
l.ob=function(a){return jf(this,17,a)};l.Kb=function(){return Q(this,18)};Xi.j="tagging.common.experiments.Experiment";var Yi=function(a){U.call(this,a)};t(Yi,U);Yi.prototype.eb=function(){return bf(this,1)};var Zi=function(a){return Te(a,Xi,2,Ae())};l=Yi.prototype;l.P=function(){return Se(this,ch,3)};l.Oa=function(a){return Ve(this,ch,3,a)};l.ra=function(){return Q(this,4)};l.R=function(){return Q(this,5)};l.ha=function(a){return jf(this,5,a)};l.bb=function(){return Q(this,10)};l.qb=function(a){jf(this,10,a)};l.Mb=function(){return Q(this,11)};l.Lb=function(){return bf(this,8)};l.Fa=function(){return Q(this,13)}; 
l.ob=function(a){return jf(this,13,a)};l.Kb=function(){return Q(this,14)};Yi.j="tagging.common.experiments.Study";var $i=function(a){U.call(this,a)};t($i,U);$i.prototype.R=function(){return Q(this,1)};$i.prototype.ha=function(a){return jf(this,1,a)};$i.j="tagging.common.experiments.ExperimentState.DiversionPointToStudy";var aj=function(a){U.call(this,a)};t(aj,U);aj.prototype.Pb=function(){return cf(this,2)};aj.prototype.lc=function(a){return hf(this,2,a)};aj.j="tagging.common.experiments.ExperimentState.ObservabilityConfig";var bj=function(a){U.call(this,a)};t(bj,U);bj.prototype.Ha=function(a){return Te(this,hi,1,Ae(a))};bj.j="tagging.common.experiments.ExperimentState";function cj(a){var b={};return dj((b[0]=new Map,b[1]=new Map,b[2]=new Map,b),a)} 
function dj(a,b){for(var c=new Map,d=x(a[1].entries()),e=d.next();!e.done;e=d.next()){var f=x(e.value);e=f.next().value;f=f.next().value;f=f[f.length-1];c.set(e,f.rc+f.Yb*f.Zb)}b=x(b);for(d=b.next();!d.done;d=b.next())for(d=d.value,e=Te(d,Yi,2,Ae()),e=x(e),f=e.next();!f.done;f=e.next())if(f=f.value,Zi(f).length!==0){var g=f.Lb();if(f.ra()&&!f.Fa()&&!f.Kb()){var h=void 0;g=(h=c.get(f.ra()))!=null?h:0;h=f.eb()*Zi(f).length;c.set(f.ra(),g+h)}h=[];for(var k=0;k<Zi(f).length;k++){var m={rc:g,Yb:f.eb(), 
Zb:Zi(f).length,Qc:k,Ca:d.R(),Pa:f,experiment:Zi(f)[k]};h.push(m)}ej(a[2],f.bb(),h)||ej(a[1],f.ra(),h)||(g=a[0],f=Zi(f)[0],f=af(f,1),ej(g,f,h))}return a}function ej(a,b,c){if(!b)return!1;a.has(b)||a.set(b,[]);a=a.get(b);a=lc(a,pc);a.push.apply(a,pa(c));return!0};function fj(a){var b=a.length;if(b===0)return 0;for(var c=305419896,d=0;d<b;d++)c^=(c<<5)+(c>>2)+a.charCodeAt(d)&4294967295;return c>0?c:**********+c};function gj(){var a=a===void 0?pg():a;return function(b){return fj(b+" + "+a)%1E3}};var hj=[12,13,20],ij=function(a,b,c,d){d=d===void 0?{}:d;var e=d.Wa===void 0?!1:d.Wa;d=d.bd===void 0?[]:d.bd;this.ua=a;this.S=c;this.Gb={};this.Wa=e;a={};this.ya=(a[b]=[],a[4]=[],a);this.Db={};this.mb={};if(b=Hh())for(b=x(b.split(",")||[]),a=b.next();!a.done;a=b.next())(a=Number(a.value))&&this.la(a);d=x(d);for(b=d.next();!b.done;b=d.next())this.la(b.value)};ij.prototype.da=function(a){return(this.ya[a]||[]).concat(this.ya[4])};ij.prototype.la=function(a){this.Db[a]=!0}; 
ij.prototype.Da=function(a,b,c){var d=this,e=[],f;if(f=a!==9)this.Gb[a]?f=!0:(this.Gb[a]=!0,f=!1);if(f)return mi(this.S,a,b,e,[],4),e;f=hj.includes(a);for(var g=[],h=[],k=x([0,1,2]),m=k.next();!m.done;m=k.next()){m=m.value;for(var n=x(this.ua[m].entries()),q=n.next();!q.done;q=n.next()){var r=x(q.value);q=r.next().value;r=r.next().value;var u=q,w=r;q=new Pg;r=w.filter(function(oa){return oa.Ca===a&&d.Db[af(oa.experiment,1)]&&jj(d,oa)});if(r.length)for(q=x(r),r=q.next();!r.done;r=q.next())h.push(r.value.experiment); 
else if(!this.Wa){r=void 0;m===2?(r=c[1],q.qb(u)):r=c[0];var v=void 0,B=void 0;r=(B=(v=r)==null?void 0:v(String(u)))!=null?B:m===2&&w[0].Pa.Mb()===1?void 0:c[0](String(u));if(r!==void 0){u=x(w);for(w=u.next();!w.done;w=u.next())if(w=w.value,w.Ca===a){v=r-w.rc;var L=w;B=L.Yb;var na=L.Zb;L=L.Qc;v<0||v>=B*na||v%na!==L||!jj(this,w)||(v=w.Pa.Fa(),v!==0&&v!==void 0&&(B=this.mb[String(v)],B!==void 0&&B!==af(w.experiment,1)?ni(this.S,this.mb[String(v)],af(w.experiment,1),v):this.mb[String(v)]=af(w.experiment, 
1)),h.push(w.experiment))}Me(q,Qg)!==0&&(Ie(q,3,vd(r),0),g.push(q))}}}}c=x(h);for(h=c.next();!h.done;h=c.next())h=h.value,k=af(h,1),e.push(k),this.ja(k,f?4:b),Fi(h.Ha(),f?Hi():[b],this.S,k);mi(this.S,a,b,e,g,1);return e};ij.prototype.ja=function(a,b){this.ya[b]||(this.ya[b]=[]);b=this.ya[b];b.includes(a)||b.push(a)}; 
var jj=function(a,b){var c=V(qi).H,d=ei(b.Pa.P(),c);if(!d.success)return oi(a.S,b.Pa.P(),b.Ca,af(b.experiment,1),d),!1;if(!d.value)return!1;c=ei(b.experiment.P(),c);return c.success?c.value?!0:!1:(oi(a.S,b.experiment.P(),b.Ca,af(b.experiment,1),c),!1)},kj=function(a,b){b=b.map(function(c){return new $i(c)}).filter(function(c){return!hj.includes(c.R())});a.ua=dj(a.ua,b)},lj=function(a,b){W(1,function(c){return void a.la(c)},b);W(2,function(c,d,e){return a.Da(c,d,e)},b);W(3,function(c){return a.da(c)}, 
b);W(12,function(c){return void kj(a,c)},b);W(16,function(c,d){return void a.ja(c,d)},b)};var mj=function(){var a={};this.pa=function(b,c){return a[b]!=null?Ia(a[b]):c};this.Ja=function(){return a[void 0]!=null?Fa(a[void 0]):void 0};this.Ka=function(){return a[void 0]!=null?Ga(a[void 0]):void 0};this.sa=function(b,c){return a[b]!=null?D(a[b]):c};this.Ia=function(){return a[void 0]!=null?(void 0).concat(D(a[void 0])):void 0};this.Eb=function(){}};var nj=function(){this.nb=function(){};this.sb=function(){};this.ub=function(){};this.tb=function(){};this.Ba=function(){}};nj.prototype.gb=function(a){this.nb=X(9,a,function(){});this.sb=X(10,a,function(){});this.ub=X(11,a,function(){});this.tb=X(4,a,function(){});this.Ba=X(14,a,function(){})};function oj(a){V(nj).Ba(a)};var pj,qj,rj,sj,tj,uj; 
function vj(a){var b=a.Hc;var c=a.H;var d=a.config;var e=a.Ac===void 0?Wi():a.Ac;var f=a.Cb===void 0?0:a.Cb;var g=a.S===void 0?new ki((sj=sh((pj=Se(b,aj,5))==null?void 0:pj.Pb()))!=null?sj:0,(tj=sh((qj=Se(b,aj,5))==null?void 0:cf(qj,4)))!=null?tj:0,(uj=(rj=Se(b,aj,5))==null?void 0:$e(rj,3))!=null?uj:!1):a.S;a=a.ua===void 0?cj(Te(b,$i,2,Ae(cc))):a.ua;e.hasOwnProperty("init-done")?(X(12,e,function(){})(Te(b,$i,2,Ae()).map(function(h){return ge(h)})),X(13,e,function(){})(b.Ha().map(function(h){return ge(h)}),f), 
c&&X(14,e,function(){})(c),wj(f,e)):(lj(new ij(a,f,g,d),e),Ji(e),Ki(e),Li(e),wj(f,e),Fi(b.Ha(cc),[f],g,void 0,!0),ri=ri||!(!d||!d.Cd),oj(Vi),c&&oj(c))}function wj(a,b){var c=b=b===void 0?Wi():b;V(Mi).gb(c,a);xj(b,a);a=b;V(nj).gb(a);V(mj).Eb()} 
function xj(a,b){var c=V(mj);c.pa=function(d,e){return X(5,a,function(){return!1})(d,e,b)};c.Ja=function(){return X(6,a,function(){return 0})(void 0,void 0,b)};c.Ka=function(){return X(7,a,function(){return""})(void 0,void 0,b)};c.sa=function(d,e){return X(8,a,function(){return[]})(d,e,b)};c.Ia=function(){return X(17,a,function(){return[]})(void 0,void 0,b)};c.Eb=function(){X(15,a,function(){})(b)}};var bi=function(a,b,c,d,e){if(((d===void 0?0:d)?a.dd:Math.random())<(e||a.Ec))try{if(c instanceof Lh)var f=c;else f=new Lh,qg(c,function(h,k){Rh(f,k,h)});var g=Th(f,a.protocol,a.domain,a.path+b+"&");g&&(a.yc?zg(g):xg(y,g))}catch(h){}};var yj=function(a){U.call(this,a)};t(yj,U);yj.j="wireless.mdl.UserAgentClientHints.BrandAndVersion";var zj=function(a){U.call(this,a)};t(zj,U);var Aj=function(a,b){return P(a,2,Od(b))},Bj=function(a,b){return P(a,3,Od(b))},Cj=function(a,b){return P(a,4,Od(b))},Dj=function(a,b){return P(a,5,Od(b))},Ej=function(a,b){return P(a,9,Od(b))},Fj=function(a,b){return Xe(a,yj,10,b)},Gj=function(a,b){return P(a,11,b==null?b:od(b))},Hj=function(a,b){return P(a,1,Od(b))},Ij=function(a,b){return P(a,7,b==null?b:od(b))};zj.j="wireless.mdl.UserAgentClientHints";var Jj="platform platformVersion architecture model uaFullVersion bitness fullVersionList wow64".split(" ");function Kj(){var a;return(a=y.google_tag_data)!=null?a:y.google_tag_data={}}function Lj(){var a,b;return typeof((a=y.navigator)==null?void 0:(b=a.userAgentData)==null?void 0:b.getHighEntropyValues)==="function"} 
function Mj(){if(!Lj())return null;var a=Kj();if(a.uach_promise)return a.uach_promise;var b=y.navigator.userAgentData.getHighEntropyValues(Jj).then(function(c){a.uach!=null||(a.uach=c);return c});return a.uach_promise=b} 
function Nj(a){var b;return Gj(Fj(Dj(Aj(Hj(Cj(Ij(Ej(Bj(new zj,a.architecture||""),a.bitness||""),a.mobile||!1),a.model||""),a.platform||""),a.platformVersion||""),a.uaFullVersion||""),((b=a.fullVersionList)==null?void 0:b.map(function(c){var d=new yj;d=P(d,1,Od(c.brand));return P(d,2,Od(c.version))}))||[]),a.wow64||!1)};var Oj=function(a){this.ga=a},Pj=function(a,b){b&&(a.ga.aacm=b);return a.ga.aacm},Qj=function(a,b){b&&(a.ga.corr=b);return a.ga.corr};Oj.prototype.timestamp=function(a){a&&(this.ga.ts=a);return this.ga.ts};var Sj=function(a){return(a=Rj(a))?new Oj(a):null};function Tj(a,b){qg(a,function(c,d){b[d]=c})}function Uj(a,b){Qj(b,Qj(a));b.timestamp(a.timestamp());Pj(b,{});Pj(a)&&Tj(Pj(a),Pj(b))}function Rj(a){return za(a)?typeof a.corr==="string"&&typeof a.ts==="number"&&za(a.aacm)?a:null:null};function Vj(a,b,c,d,e){try{for(var f=[],g,h=0;(g=c(a))&&a!==b&&++h<100;){for(var k=null,m=d(g),n=0,q=0;q<m.length;++q){if(m[q]===a){k=q-n;break}e(m[q])&&++n}if(k===null)return null;f.unshift(k);a=g}return f}catch(r){return null}}function Wj(){return Vj(y,y.top||y,function(a){try{return a.parent}catch(b){return null}},function(a){return a.frames},function(){return!1})} 
function Xj(a){return Vj(a,document.documentElement,function(b){return b.parentElement},function(b){return b.children},function(b){return b.nodeType===8})};var Yj=function(a){if(a.Y&&typeof a.Y=="function")return a.Y();if(typeof Map!=="undefined"&&a instanceof Map||typeof Set!=="undefined"&&a instanceof Set)return Array.from(a.values());if(typeof a==="string")return a.split("");if(ya(a)){for(var b=[],c=a.length,d=0;d<c;d++)b.push(a[d]);return b}b=[];c=0;for(d in a)b[c++]=a[d];return b},Zj=function(a){if(a.cb&&typeof a.cb=="function")return a.cb();if(!a.Y||typeof a.Y!="function"){if(typeof Map!=="undefined"&&a instanceof Map)return Array.from(a.keys()); 
if(!(typeof Set!=="undefined"&&a instanceof Set)){if(ya(a)||typeof a==="string"){var b=[];a=a.length;for(var c=0;c<a;c++)b.push(c);return b}b=[];c=0;for(var d in a)b[c++]=d;return b}}},ak=function(a,b,c){if(a.forEach&&typeof a.forEach=="function")a.forEach(b,c);else if(ya(a)||typeof a==="string")Array.prototype.forEach.call(a,b,c);else for(var d=Zj(a),e=Yj(a),f=e.length,g=0;g<f;g++)b.call(c,e[g],d&&d[g],a)};var bk=function(a){this.I=this.aa=this.U="";this.fa=null;this.X=this.M="";this.G=this.Lc=!1;if(a instanceof bk){this.G=a.G;ck(this,a.U);var b=a.aa;Y(this);this.aa=b;b=a.I;Y(this);this.I=b;dk(this,a.fa);b=a.M;Y(this);this.M=b;ek(this,a.D.clone());a=a.X;Y(this);this.X=a}else a&&(b=String(a).match(Uh))?(this.G=!1,ck(this,b[1]||"",!0),a=b[2]||"",Y(this),this.aa=fk(a),a=b[3]||"",Y(this),this.I=fk(a,!0),dk(this,b[4]),a=b[5]||"",Y(this),this.M=fk(a,!0),ek(this,b[6]||"",!0),a=b[7]||"",Y(this),this.X=fk(a)): 
(this.G=!1,this.D=new gk(null,this.G))};bk.prototype.toString=function(){var a=[],b=this.U;b&&a.push(hk(b,ik,!0),":");var c=this.I;if(c||b=="file")a.push("//"),(b=this.aa)&&a.push(hk(b,ik,!0),"@"),a.push(encodeURIComponent(String(c)).replace(/%25([0-9a-fA-F]{2})/g,"%$1")),c=this.fa,c!=null&&a.push(":",String(c));if(c=this.M)this.I&&c.charAt(0)!="/"&&a.push("/"),a.push(hk(c,c.charAt(0)=="/"?jk:kk,!0));(c=this.D.toString())&&a.push("?",c);(c=this.X)&&a.push("#",hk(c,lk));return a.join("")}; 
bk.prototype.resolve=function(a){var b=this.clone(),c=!!a.U;c?ck(b,a.U):c=!!a.aa;if(c){var d=a.aa;Y(b);b.aa=d}else c=!!a.I;c?(d=a.I,Y(b),b.I=d):c=a.fa!=null;d=a.M;if(c)dk(b,a.fa);else if(c=!!a.M){if(d.charAt(0)!="/")if(this.I&&!this.M)d="/"+d;else{var e=b.M.lastIndexOf("/");e!=-1&&(d=b.M.slice(0,e+1)+d)}e=d;if(e==".."||e==".")d="";else if(e.indexOf("./")!=-1||e.indexOf("/.")!=-1){d=e.lastIndexOf("/",0)==0;e=e.split("/");for(var f=[],g=0;g<e.length;){var h=e[g++];h=="."?d&&g==e.length&&f.push(""): 
h==".."?((f.length>1||f.length==1&&f[0]!="")&&f.pop(),d&&g==e.length&&f.push("")):(f.push(h),d=!0)}d=f.join("/")}else d=e}c?(Y(b),b.M=d):c=a.D.toString()!=="";c?ek(b,a.D.clone()):c=!!a.X;c&&(a=a.X,Y(b),b.X=a);return b};bk.prototype.clone=function(){return new bk(this)}; 
var ck=function(a,b,c){Y(a);a.U=c?fk(b,!0):b;a.U&&(a.U=a.U.replace(/:$/,""))},dk=function(a,b){Y(a);if(b){b=Number(b);if(isNaN(b)||b<0)throw Error("Bad port number "+b);a.fa=b}else a.fa=null},ek=function(a,b,c){Y(a);b instanceof gk?(a.D=b,a.D.rb(a.G)):(c||(b=hk(b,mk)),a.D=new gk(b,a.G))};bk.prototype.getQuery=function(){return this.D.toString()};bk.prototype.removeParameter=function(a){Y(this);this.D.remove(a);return this};var Y=function(a){if(a.Lc)throw Error("Tried to modify a read-only Uri");}; 
bk.prototype.rb=function(a){this.G=a;this.D&&this.D.rb(a)}; 
var fk=function(a,b){return a?b?decodeURI(a.replace(/%25/g,"%2525")):decodeURIComponent(a):""},hk=function(a,b,c){return typeof a==="string"?(a=encodeURI(a).replace(b,nk),c&&(a=a.replace(/%25([0-9a-fA-F]{2})/g,"%$1")),a):null},nk=function(a){a=a.charCodeAt(0);return"%"+(a>>4&15).toString(16)+(a&15).toString(16)},ik=/[#\/\?@]/g,kk=/[#\?:]/g,jk=/[#\?]/g,mk=/[#\?@]/g,lk=/#/g,gk=function(a,b){this.A=this.l=null;this.C=a||null;this.G=!!b},ok=function(a){a.l||(a.l=new Map,a.A=0,a.C&&Vh(a.C,function(b,c){a.add(decodeURIComponent(b.replace(/\+/g, 
" ")),c)}))};gk.prototype.add=function(a,b){ok(this);this.C=null;a=pk(this,a);var c=this.l.get(a);c||this.l.set(a,c=[]);c.push(b);this.A=Fa(this.A)+1;return this};gk.prototype.remove=function(a){ok(this);a=pk(this,a);return this.l.has(a)?(this.C=null,this.A=Fa(this.A)-this.l.get(a).length,this.l.delete(a)):!1};gk.prototype.clear=function(){this.l=this.C=null;this.A=0};gk.prototype.isEmpty=function(){ok(this);return this.A==0};var qk=function(a,b){ok(a);b=pk(a,b);return a.l.has(b)};l=gk.prototype; 
l.forEach=function(a,b){ok(this);this.l.forEach(function(c,d){c.forEach(function(e){a.call(b,e,d,this)},this)},this)};l.cb=function(){ok(this);for(var a=Array.from(this.l.values()),b=Array.from(this.l.keys()),c=[],d=0;d<b.length;d++)for(var e=a[d],f=0;f<e.length;f++)c.push(b[d]);return c};l.Y=function(a){ok(this);var b=[];if(typeof a==="string")qk(this,a)&&(b=b.concat(this.l.get(pk(this,a))));else{a=Array.from(this.l.values());for(var c=0;c<a.length;c++)b=b.concat(a[c])}return b}; 
l.set=function(a,b){ok(this);this.C=null;a=pk(this,a);qk(this,a)&&(this.A=Fa(this.A)-this.l.get(a).length);this.l.set(a,[b]);this.A=Fa(this.A)+1;return this};l.get=function(a,b){if(!a)return b;a=this.Y(a);return a.length>0?String(a[0]):b}; 
l.toString=function(){if(this.C)return this.C;if(!this.l)return"";for(var a=[],b=Array.from(this.l.keys()),c=0;c<b.length;c++){var d=b[c],e=encodeURIComponent(String(d));d=this.Y(d);for(var f=0;f<d.length;f++){var g=e;d[f]!==""&&(g+="="+encodeURIComponent(String(d[f])));a.push(g)}}return this.C=a.join("&")};l.clone=function(){var a=new gk;a.C=this.C;this.l&&(a.l=new Map(this.l),a.A=this.A);return a};var pk=function(a,b){b=String(b);a.G&&(b=b.toLowerCase());return b}; 
gk.prototype.rb=function(a){a&&!this.G&&(ok(this),this.C=null,this.l.forEach(function(b,c){var d=c.toLowerCase();if(c!=d&&(this.remove(c),this.remove(d),b.length>0)){this.C=null;c=this.l;var e=c.set;d=pk(this,d);var f=b.length;if(f>0){for(var g=Array(f),h=0;h<f;h++)g[h]=b[h];f=g}else f=[];e.call(c,d,f);this.A=Fa(this.A)+b.length}},this));this.G=a};gk.prototype.extend=function(a){for(var b=0;b<arguments.length;b++)ak(arguments[b],function(c,d){this.add(d,c)},this)};var rk=ra(["https://pagead2.googlesyndication.com/","/",""]),sk=ra(["https://pagead2.googlesyndication-cn.com/","/",""]),tk=ra(["https://ad.doubleclick.net/","/",""]),uk=ra(["https://googleads.g.doubleclick.net/","/",""]),vk=function(a){this.ac=!1;this.na="dbm";this.oa="ad";this.ma=new bk(a)},Z=function(a,b,c,d){d=d===void 0?!1:d;(b.length+encodeURIComponent(c).length+1+a.ma.toString().length<(Number(Fg())||16384)||d)&&a.ma.D.set(b,c)},xk=function(a){var b=wk(),c,d;b&&(b.responseEnd&&b.startTime&& 
(c=b.responseEnd-b.startTime),b.transferSize!==void 0&&(d=b.transferSize===0));var e;if(b=(e=window.performance)==null?0:e.getEntriesByName){var f;(f=window.performance)!=null&&f.measure?(window.performance.measure("dm","db"),b=!0):b=!1}if(b&&(e=window.performance.getEntriesByName("dm"),e.length>0&&e[0].entryType==="measure"))var g=e[0].duration;c&&Z(a,"idt",Math.round(c).toString());d!==void 0&&Z(a,"cac",d?"1":"0");g&&Z(a,"dtd",Math.round(g).toString())},yk=function(a){var b=new Map;a.ma.D.forEach(function(c, 
d){return b.set(d,c)});return a.ma.I==="pagead2.googlesyndication.com"?kg(jg(rk,a.na,a.oa),b):a.ma.I==="pagead2.googlesyndication-cn.com"?kg(jg(sk,a.na,a.oa),b):a.ac?kg(jg(tk,a.na,a.oa),b):kg(jg(uk,a.na,a.oa),b)};var zk=RegExp("((.*\\.doubleclick\\.net/(dbm/ad|pagead/adfetch))|(pagead2\\.googlesyndication(-cn)?\\.com/(dbm/(ad|clk|vast)|pagead/adfetch))).*dv3_ver=.*");function wk(){var a;if((a=window.performance)!=null&&a.getEntriesByType&&(a=window.performance.getEntriesByType("resource").filter(function(b){return b.name.indexOf("pagead/js/dv3.js")>=0}),a.length!==0))return a[0]}function Ak(){var a;(a=window.performance)==null||a.mark("db")} 
function Bk(){var a;(a=window.performance)==null||a.clearMarks()}function Ck(){var a;return(a=window.performance)!=null&&a.getEntriesByType?window.performance.getEntriesByType("resource").filter(function(b){return zk.test(b.name)}).length>0:!0};var Dk=!1,Ek=function(a){this.Ea=a;this.V=null;this.Bb=!1;this.jb=a.getAttribute("data-dv3-creative-fetch");this.u=new vk(this.jb);this.lb=a.getAttribute("data-dv3-render-mode").toLowerCase();this.Ib=a.getAttribute("data-dv3-width");this.Hb=a.getAttribute("data-dv3-height");var b=a.getAttribute("data-dv3-meta-data");this.O=of(b);b=Wj()||["?"];a=Xj(a)||["?"];this.slotId=fj(b.join(",")+":"+a.join(","));Dk=!1},Jk=function(a){if(a.V)Fk(a),y.setTimeout(Gk(a,"f"),1E3);else{var b=yk(a.u);if(a.lb==="script")try{var c= 
document,d=Hk(a,b);c.write(Yf(d))}catch(f){Ik(a),Dk=!0}else{d=vg(document,"IFRAME");d.src=Ef(b);b=d.ownerDocument;b=b===void 0?document:b;var e;b=(e=(c=b).querySelector)==null?void 0:e.call(c,"script[nonce]");(c=b==null?"":b.nonce||b.getAttribute("nonce")||"")&&d.setAttribute("nonce",c);d.allow="attribution-reporting";d.width=a.Ib;d.height=a.Hb;d.title="Advertisement";d.style="border: 0";d.scrolling="no";document.body.appendChild(d)}}},Hk=function(a,b){a={attributionSrc:a.Jc};b=Ef(b).toString();b= 
'<script src="'+$f(b)+'"';if(a==null?0:a.async)b+=" async";(a==null?void 0:a.attributionSrc)!==void 0&&(b+=' attributionsrc="'+$f(a.attributionSrc)+'"');if(a==null?0:a.Dc)b+=' custom-element="'+$f(a.Dc)+'"';if(a==null?0:a.defer)b+=" defer";if(a==null?0:a.id)b+=' id="'+$f(a.id)+'"';if(a==null?0:a.nonce)b+=' nonce="'+$f(a.nonce)+'"';if(a==null?0:a.type)b+=' type="'+$f(a.type)+'"';if(a==null?0:a.Cc)b+=' crossorigin="'+$f(a.Cc)+'"';a=Xf(b+">\x3c/script>").toString().replace(/&amp;/g,"&");return Xf(a)}, 
Gk=function(a,b){return function(c){if(!a.Bb){a.Bb=!0;var d=a.u;xk(d);Z(d,"cbvp","2",!0);Z(d,"dett",c||0);Z(d,"clm",b);c=yk(d).toString();window.fetch(c,{method:"GET",keepalive:!0,credentials:"include",mode:"no-cors",xd:{eventSourceEligible:!0,triggerEligible:!1}});Bk()}}},Fk=function(a){var b=a.Ea.parentElement||document.body,c=wh();delete y.dv3Utw;Bh(b,Gk(a,"i"),c);document.addEventListener("DOMContentLoaded",function(){Bh(b,Gk(a,"d"),c)});var d=new MutationObserver(function(){Bh(b,Gk(a,"m"),c); 
d.disconnect()});d.observe(b,{childList:!0})},Kk=function(){var a=V(mj).sa(qh.Xa,qh.defaultValue);og(function(b){b=b.document;if(a.length&&b.head)for(var c=x(a),d=c.next();!d.done;d=c.next())if((d=d.value)&&b.head){var e=sg("META");b.head.appendChild(e);e.httpEquiv="origin-trial";e.content=d}return!1},!1,!1)},Ik=function(a){if(!Dk){Dk=!0;Z(a.u,"fbi","1");var b=yk(a.u),c=a.Ib,d=a.Hb,e=vg(document,"IFRAME");e.setAttribute("frameborder","0");e.setAttribute("scrolling","no");e.setAttribute("marginheight", 
"0");e.setAttribute("marginwidth","0");e.setAttribute("topmargin","0");e.setAttribute("leftmargin","0");e.setAttribute("allowtransparency","true");e.setAttribute("width",c);e.setAttribute("height",d);e.title="Advertisement";af(a.O,2)===77&&(e.style.display="none");a.Ea.parentNode?a.Ea.parentNode.insertBefore(e,a.Ea.nextSibling):document.body.appendChild(e);a=hg("body",Hk(a,b));a=ag([Xf("<!DOCTYPE html>"),hg("html",a)]);e=e.contentWindow?e.contentWindow.document:e.contentDocument;if(!e)throw Error("Can not create iFrame."); 
e.open("text/html","replace");e.write(Yf(a));e.close()}},Lk=function(a){var b;og(function(d){d=d.google_tag_data;if(d!=null&&d.uach){d=d.uach;var e=Object.assign({},d);d.fullVersionList&&(e.fullVersionList=d.fullVersionList.slice(0));d=e}else d=null;d&&(b=d);return!!b});if(b)Z(a.u,"uach",lb(kf(Nj(b))));else{var c=Mj();c&&c.then(function(d){Z(a.u,"uach",lb(kf(Nj(d))))})}};var Wh=new function(){this.domain="pagead2.googlesyndication.com";this.path="/pagead/gen_204?id=";this.Ec=.1;this.yc=!0;this.protocol="https:";this.dd=Math.random()},Yh=new Xh;var Mk=function(a){U.call(this,a)};t(Mk,U);var Nk=mf(Mk);Mk.j="tagging.dv3.proto.Dv3ServeTimeConfig";var Ok=typeof stc==="undefined"?void 0:stc;function Pk(){var a=11;a=a===void 0?0:a;hc(Ok,rc);var b=Nk(Ok);var c=Se(b,bj,1);if(c){var d={},e={};vj({Hc:c,H:(e[3]={},e[4]={},e[5]=(d[9]=function(){return ef(b,2)},d),e),Cb:a});(a=ef(b,2))&&Ni(Number(a));a={};Oi((a[0]=gj(),a))}};var Qk=String(Math.floor(Math.random()*1E13));function Rk(a,b,c){var d=d===void 0?1:d;var e=new Lh(15E3);Rh(e,"msg",a);c&&Rh(e,"fet",c);Rh(e,"ord",Qk);Rh(e,"version","m202505270101");b&&(a=b.O,Rh(e,"ct",af(a,2)),Rh(e,"x",af(a,1)),Ld(we(a,3))!=null&&Rh(e,"cor",ff(a)));bi(Wh,"dv3-render",e,!0,d)}function Sk(a){a.lb==="script"&&(Rk("tler",a),a.V||Ck()||(Rk("tlmf",a,yk(a.u).toString()),Ik(a)))} 
function Tk(){a:{var a=x(document.getElementsByTagName("SCRIPT"));for(var b=a.next();!b.done;b=a.next())if((b=b.value)&&b.getAttribute("data-dv3-creative-fetch")&&b.getAttribute("data-dv3-render-mode")&&b.getAttribute("data-dv3-width")&&b.getAttribute("data-dv3-height")&&b.getAttribute("data-dv3-done")!=="true"){a=b;break a}a=null}if(!a){a=Error;b=document.currentScript||document.scripts[document.scripts.length-1];A(b!==null,"goog.dom.getOuterHtml expects a non-null value for element");if("outerHTML"in 
b)b=b.outerHTML;else{A(b,"Node cannot be null or undefined.");var c=vg(b.nodeType==9?b:b.ownerDocument||b.document,"DIV");c.appendChild(b.cloneNode(!0));b=c.innerHTML}throw a("NoDv3Node:"+b);}b=new Ek(a);Rk("tlbr",b);Lk(b);Ze(b.O,4)!=null&&af(b.O,2)===77&&(b.V=$e(b.O,4));var d=[];b.V?d.push(95351359):b.V===!1&&d.push(95351358);c=b.u;var e=V(Mi).da();d=d===void 0?[]:d;if(e.length>0||d.length>0)d=[].concat(pa(e),pa(d)),Z(c,"dc_eid",d.join(","));Z(b.u,"dv3_ver","m202505270101");V(mj).pa(rh.Xa,rh.defaultValue)&& 
(Kk(),Pi("attribution-reporting")&&Z(b.u,"nel","1"),b.jb.startsWith("https://ad.doubleclick.net")&&b.lb==="script"&&(b.Jc="",b.u.ac=!0));RegExp(".*.doubleclick.net/pagead/adfetch").test(b.jb)&&(c=b.u,c.na="pagead",c.oa="adfetch");c=b.u;d=Fh();d=d.Ra?d.Ra.url:d.za.url;e=d.indexOf("?");(d=e>=0?d.substring(0,e):d)&&Z(c,"rfl",d);typeof document.readyState==="string"&&Z(b.u,"ds",document.readyState.charAt(0));c=b.u;d=!mg(y.top);Z(c,"xdt",d?"1":"0");c=b.u;d=af(b.O,2);Z(c,"ct",d);Z(b.u,"iif",y!==y.parent? 
"1":"0");Ld(we(b.O,3))!=null&&(c=b.u,d=ff(b.O),Z(c,"cor",d));Z(b.u,"adk",b.slotId);e=Rj(y.googDdmPs);c=null;var f=!0;f=f===void 0?!1:f;var g=[y.top];d=[];for(var h=0,k;k=g[h++];){f&&!mg(k)||d.push(k);try{if(k.frames)for(var m=0;m<k.frames.length&&g.length<1024;++m)g.push(k.frames[m])}catch(n){}}f=x(d);for(g=f.next();!g.done&&(g=g.value,g===y||!(c=Rj(g.googDdmPs)));g=f.next());if(e)c&&(f=new Oj(e),g=new Oj(c),g.timestamp()>f.timestamp()?Uj(g,f):Uj(f,g));else if(c)y.googDdmPs=e={},Uj(new Oj(c),new Oj(e)); 
else{e="";for(f=0;f<10;++f)e+="0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ-_.!*'()"[Math.floor(Math.random()*70)];y.googDdmPs=e={corr:e,ts:Date.now(),aacm:{}}}c||((c=d[0])&&c!==y?(c.googDdmPs=c={},Uj(new Oj(e),new Oj(c))):c=e);d=c;c=b.slotId;(e=Sj(e))?(e=Pj(e),f=e[c]||0,e[c]=f+1,(d=Sj(d))&&(Pj(d)[c]=e[c]),c=f):c=0;c>0&&Z(b.u,"rc",c);a.setAttribute("data-dv3-done","true");return b}function $h(a){a.version="m202505270101";a.sample="0.1";a.experiments=V(Mi).da().join(",")};ai(function(){y.dv3_render_state=y.dv3_render_state?y.dv3_render_state+1:1;Rk("running");Ak();Pk();var a=Tk();wg(function(){y.setTimeout(function(){ai(Sk)(a)},1E3)});a.V||xk(a.u);Jk(a);a.V||Bk()})(); 
}).call(this,"[[[[null,null,null,[null,null,null,[\"A3vKT9yxRPjmXN3DpIiz58f5JykcWHjUo\/W7hvmtjgh9jPpQgem9VbADiNovG8NkO6mRmk70Kex8\/KUqAYWVWAEAAACLeyJvcmlnaW4iOiJodHRwczovL2dvb2dsZXN5bmRpY2F0aW9uLmNvbTo0NDMiLCJmZWF0dXJlIjoiUHJpdmFjeVNhbmRib3hBZHNBUElzIiwiZXhwaXJ5IjoxNjk1MTY3OTk5LCJpc1N1YmRvbWFpbiI6dHJ1ZSwiaXNUaGlyZFBhcnR5Ijp0cnVlfQ==\"]],null,6001],[6000,null,null,[1]]],null,null,null,[null,1000,1,1000]]]");
