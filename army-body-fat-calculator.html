<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Army Body Fat Calculator</title>
    <meta name="description" content="Calculate body fat percentage using US Army standards and regulations. Official Army body fat calculator with height/weight standards and tape test requirements.">
    <link rel="stylesheet" href="./Carbohydrate Calculator_files/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Mobile responsive styles */
        @media screen and (max-width: 768px) {
            #contentout { width: 100% !important; padding: 0 10px; }
            #content { width: 100% !important; margin: 0; }
            #right { display: none; }
            .panel2 { width: 100% !important; padding: 5px !important; }
            table { width: 100% !important; font-size: 14px; }
            td { padding: 8px 4px !important; word-wrap: break-word; }
            input, select { width: 100% !important; max-width: 100% !important; box-sizing: border-box; font-size: 16px; }
            .inlongest, .innormal, .inlongesthalf { width: 100% !important; }
            h1 { font-size: 24px; }
            .cinfoT { font-size: 12px; overflow-x: auto; }
            .cinfoT td { padding: 4px 2px !important; }
            .cbcontainer { font-size: 12px; margin: 2px 0; }
            #topmenu ul li { display: inline-block; margin: 2px; font-size: 12px; }
        }
        @media screen and (max-width: 480px) {
            table td:first-child { width: 30% !important; }
            table td:last-child { width: 70% !important; }
            input[type="submit"], input[type="button"] {
                width: 100% !important; margin: 5px 0 !important; padding: 12px !important; font-size: 14px;
            }
            .inuifoot, .inuiinch { width: 45% !important; display: inline-block; }
            .cbcontainer { display: block; margin: 3px 0; }
        }
    </style>
    <link rel="apple-touch-icon" sizes="180x180" href="https://d26tpo4cm8sb6k.cloudfront.net/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-16x16.png">
    <link rel="manifest" href="https://www.calculator.net/manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="https://www.calculator.net/"><img src="./Carbohydrate Calculator_files/calculator-white.svg" width="208" height="22" alt="Calculator.net"></a></div>
        <div id="login"><a href="https://www.calculator.net/my-account/sign-in.php">sign in</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/" itemprop="item"><span itemprop="name">home</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/fitness-and-health-calculator.html" itemprop="item"><span itemprop="name">fitness &amp; health</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="#" itemprop="item"><span itemprop="name">army body fat calculator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="window.print();return false;">Print</a></div>
        
        <h1>Army Body Fat Calculator</h1>
        <p>The <i>Army Body Fat Calculator</i> is based on the U.S. Army Standards of Medical Fitness, published on June 27th, 2019. This calculator will determine body fat percentage and whether the calculated body fat percentage is in compliance with the army recruitment standard, the standard after entry into the army, or the most stringent standard of being in the army.</p>
        
        <div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
        <span id="ucframe"></span>
        
        <div id="topmenu" class="topmenucenter">
            <ul>
                <li><a href="#" onclick="return showUnits('standard');">US Units</a></li>
                <li id="menuon"><a href="#" onclick="return showUnits('metric');">Metric Units</a></li>
                <li><a href="#" onclick="return showUnits('other');">Other Units</a></li>
            </ul>
        </div>

        <div class="panel2" style="padding:10px;">
            <form name="armyBodyFatForm" id="army-bodyfat-calculator-form">
                <table id="army-bodyfat-input-table" width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">Age</td>
                            <td width="550"><input type="number" name="age" id="age" value="25" class="inlongesthalf" min="17" max="65"> ages 17 - 65</td>
                        </tr>
                        <tr>
                            <td>Gender</td>
                            <td>
                                <label for="male" class="cbcontainer"><input type="radio" name="gender" id="male" value="male" checked=""><span class="rbmark"></span>male</label> &nbsp; 
                                <label for="female" class="cbcontainer"><input type="radio" name="gender" id="female" value="female"><span class="rbmark"></span>female</label>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- US Units -->
                <table width="620" id="standard-units" bgcolor="#eeeeee" style="display: none;">
                    <tbody>
                        <tr>
                            <td width="70">Height</td>
                            <td width="550">
                                <input type="number" name="feet" id="feet" value="5" class="inlongesthalf inuifoot" min="0" max="8"><span class="inuifootspan">feet</span>
                                &nbsp;&nbsp;
                                <input type="number" name="inches" id="inches" value="10" class="inlongesthalf inuiinch" min="0" max="11"><span class="inuiinchspan">inches</span>
                            </td>
                        </tr>
                        <tr>
                            <td>Weight</td>
                            <td><input type="number" name="pounds" id="pounds" value="160" class="inlongest inuipound" min="50" max="1000"><span class="inuipoundspan">pounds</span></td>
                        </tr>
                        <tr>
                            <td>Neck</td>
                            <td><input type="number" name="neck_inches" id="neck_inches" value="15" class="inlongest" min="8" max="24" step="0.1"> inches</td>
                        </tr>
                        <tr>
                            <td>Waist</td>
                            <td><input type="number" name="waist_inches" id="waist_inches" value="32" class="inlongest" min="20" max="60" step="0.1"> inches</td>
                        </tr>
                        <tr id="hip-row-us" style="display: none;">
                            <td>Hip</td>
                            <td><input type="number" name="hip_inches" id="hip_inches" value="36" class="inlongest" min="24" max="60" step="0.1"> inches</td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- Metric Units -->
                <table width="620" id="metric-units" bgcolor="#eeeeee" style="display: block;">
                    <tbody>
                        <tr>
                            <td width="70">Height</td>
                            <td width="550"><input type="number" name="height_cm" id="height_cm" value="180" class="inlongest inuick" min="100" max="250" step="0.1"><span class="inuickspan">cm</span></td>
                        </tr>
                        <tr>
                            <td>Weight</td>
                            <td><input type="number" name="weight_kg" id="weight_kg" value="70" class="inlongest inuick" min="20" max="300" step="0.1"><span class="inuickspan">kg</span></td>
                        </tr>
                        <tr>
                            <td>Neck</td>
                            <td><input type="number" name="neck_cm" id="neck_cm" value="37" class="inlongest inuick" min="20" max="60" step="0.1"><span class="inuickspan">cm</span></td>
                        </tr>
                        <tr>
                            <td>Waist</td>
                            <td><input type="number" name="waist_cm" id="waist_cm" value="85" class="inlongest inuick" min="50" max="150" step="0.1"><span class="inuickspan">cm</span></td>
                        </tr>
                        <tr id="hip-row-metric" style="display: none;">
                            <td>Hip</td>
                            <td><input type="number" name="hip_cm" id="hip_cm" value="95" class="inlongest inuick" min="60" max="150" step="0.1"><span class="inuickspan">cm</span></td>
                        </tr>
                    </tbody>
                </table>
                
                <table width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">&nbsp;</td>
                            <td width="550">
                                <input type="submit" name="x" value="Calculate Army Body Fat" id="calculate-btn">
                                <input type="button" value="Clear" id="clear-btn" onclick="clearArmyBodyFatForm();">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </form>
        </div>

        <ul style="margin-top:0px;margin-left:-20px;">
            <li class="smalltext"><b>Army Standards:</b> Based on AR 600-9 Army Body Composition Program.</li>
            <li class="smalltext"><b>Tape Test:</b> Uses circumference measurements for body fat estimation.</li>
            <li class="smalltext"><b>Age Groups:</b> Different standards apply to different age ranges.</li>
        </ul>

        <!-- Results Display Area -->
        <div id="army-bodyfat-results" style="display: none; margin-top: 20px;">
            <div class="h2result" id="army-bodyfat-results-content"></div>
        </div>

        <br>
        <fieldset>
            <legend>Related</legend>
            <a href="#">Body Fat Calculator</a> | 
            <a href="#">BMI Calculator</a> | 
            <a href="#">Ideal Weight Calculator</a>
        </fieldset>

        <br>
        <h3>Army Body Fat Standards</h3>
        <p>The U.S. Army uses body fat percentage standards to ensure soldiers maintain appropriate fitness levels. These standards vary by age and gender, with different requirements for initial entry, retention, and promotion.</p>

        <h3>Maximum Allowable Body Fat Percentages</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Age Group</td>
                    <td class="cinfoHdL">Male</td>
                    <td class="cinfoHdL">Female</td>
                    <td class="cinfoHdL">Standard</td>
                </tr>
                <tr><td><strong>17-20</strong></td><td>20%</td><td>30%</td><td>Entry/Retention</td></tr>
                <tr><td><strong>21-27</strong></td><td>22%</td><td>32%</td><td>Entry/Retention</td></tr>
                <tr><td><strong>28-39</strong></td><td>24%</td><td>34%</td><td>Entry/Retention</td></tr>
                <tr><td><strong>40+</strong></td><td>26%</td><td>36%</td><td>Entry/Retention</td></tr>
            </tbody>
        </table>

        <h3>Army Height and Weight Standards</h3>
        <p>Before the tape test is administered, soldiers must first meet height and weight standards. If a soldier exceeds the maximum weight for their height, they must pass the body fat assessment.</p>

        <h3>Measurement Procedures</h3>
        <ul>
            <li><strong>Height:</strong> Measured without shoes, standing straight against a wall</li>
            <li><strong>Weight:</strong> Measured in minimal clothing (PT uniform)</li>
            <li><strong>Neck:</strong> Measured just below the larynx (Adam's apple)</li>
            <li><strong>Waist (Male):</strong> Measured at the navel level</li>
            <li><strong>Waist (Female):</strong> Measured at the narrowest point of the torso</li>
            <li><strong>Hip (Female only):</strong> Measured at the largest protrusion of the buttocks</li>
        </ul>

        <h3>Army Body Composition Program (AR 600-9)</h3>
        <p>The Army Body Composition Program is designed to:</p>
        <ul>
            <li>Ensure all soldiers maintain optimal health and fitness</li>
            <li>Establish body fat standards for military appearance</li>
            <li>Provide a fair and accurate assessment method</li>
            <li>Support readiness and performance standards</li>
            <li>Promote long-term health and wellness</li>
        </ul>

        <h3>Consequences of Non-Compliance</h3>
        <p>Soldiers who exceed body fat standards may face:</p>
        <ul>
            <li>Enrollment in the Army Body Composition Program (ABCP)</li>
            <li>Mandatory nutrition counseling and fitness training</li>
            <li>Restrictions on promotions and assignments</li>
            <li>Potential separation from military service</li>
            <li>Regular progress assessments and monitoring</li>
        </ul>

        <h3>Tips for Meeting Army Standards</h3>
        <ul>
            <li>Maintain regular cardiovascular and strength training</li>
            <li>Follow a balanced, nutritious diet</li>
            <li>Stay hydrated and get adequate sleep</li>
            <li>Monitor body composition regularly</li>
            <li>Seek guidance from military fitness professionals</li>
            <li>Focus on building lean muscle mass</li>
        </ul>
    </div>
    
    <div id="right">
        <div style="padding-top:10px; min-height:280px; text-align:center;">
            <div style="width: 300px; height: 250px; background: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; color: #666;">
                Advertisement Space
            </div>
        </div>
        
        <form name="calcSearchForm" onsubmit="return false;" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td><input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators..."></td>
                        <td><span id="bluebtn" onclick="performSearch();">Search</span></td>
                    </tr>
                    <tr>
                        <td colspan="2"><div id="calcSearchOut"></div></td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="#">Fitness and Health Calculators</a>
            </div>
            <div id="occontent">
                <a href="#">BMI Calculator</a>
                <a href="#">Body Fat Calculator</a>
                <a href="#">Calorie Calculator</a>
                <a href="#">BMR Calculator</a>
                <a href="#">Ideal Weight Calculator</a>
                <a href="#">Macro Calculator</a>
                <a href="#">Pace Calculator</a>
                <a href="#">Pregnancy Calculator</a>
                <a href="#">Heart Rate Calculator</a>
                <a href="#">More Fitness Calculators</a>
            </div>
            <div id="ocother">
                <a href="#">Financial</a> | 
                <a href="#">Fitness and Health</a> | 
                <a href="#">Math</a> | 
                <a href="#">Other</a>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="#">about us</a> | 
            <a href="#">sitemap</a> | 
            <a href="#">terms of use</a> | 
            <a href="#">privacy policy</a> &nbsp;  © 2008 - 2025 <a href="#">calculator.net</a>
        </div>
    </div>
</div>

<div class="topNavAbs">
    <a href="#">Financial</a>
    <a href="#" class="topNavOn">Fitness &amp; Health</a>
    <a href="#">Math</a>
    <a href="#">Other</a>
</div>

<script>
let currentUnits = 'metric';

function showUnits(unitType) {
    document.getElementById('standard-units').style.display = 'none';
    document.getElementById('metric-units').style.display = 'none';
    
    if (unitType === 'standard') {
        document.getElementById('standard-units').style.display = 'block';
    } else if (unitType === 'metric') {
        document.getElementById('metric-units').style.display = 'block';
    }
    
    const menuItems = document.querySelectorAll('#topmenu ul li');
    menuItems.forEach(item => {
        item.id = '';
        const link = item.querySelector('a');
        if (link && link.textContent.toLowerCase().includes(unitType === 'standard' ? 'us' : unitType)) {
            item.id = 'menuon';
        }
    });
    
    currentUnits = unitType;
    updateGenderFields();
    return false;
}

function updateGenderFields() {
    const gender = document.querySelector('input[name="gender"]:checked').value;
    const hipRowMetric = document.getElementById('hip-row-metric');
    const hipRowUS = document.getElementById('hip-row-us');
    
    if (gender === 'female') {
        hipRowMetric.style.display = '';
        hipRowUS.style.display = '';
    } else {
        hipRowMetric.style.display = 'none';
        hipRowUS.style.display = 'none';
    }
}

// Add event listeners for gender change
document.getElementById('male').addEventListener('change', updateGenderFields);
document.getElementById('female').addEventListener('change', updateGenderFields);

document.getElementById('calculate-btn').addEventListener('click', function(e) {
    e.preventDefault();
    calculateArmyBodyFat();
});

function calculateArmyBodyFat() {
    const age = parseInt(document.getElementById('age').value);
    const gender = document.querySelector('input[name="gender"]:checked').value;
    
    let height, weight, neck, waist, hip = 0;
    
    if (currentUnits === 'metric') {
        height = parseFloat(document.getElementById('height_cm').value);
        weight = parseFloat(document.getElementById('weight_kg').value);
        neck = parseFloat(document.getElementById('neck_cm').value);
        waist = parseFloat(document.getElementById('waist_cm').value);
        if (gender === 'female') {
            hip = parseFloat(document.getElementById('hip_cm').value);
        }
    } else {
        const feet = parseFloat(document.getElementById('feet').value) || 0;
        const inches = parseFloat(document.getElementById('inches').value) || 0;
        height = ((feet * 12) + inches) * 2.54; // Convert to cm
        weight = parseFloat(document.getElementById('pounds').value) * 0.453592; // Convert to kg
        neck = parseFloat(document.getElementById('neck_inches').value) * 2.54; // Convert to cm
        waist = parseFloat(document.getElementById('waist_inches').value) * 2.54; // Convert to cm
        if (gender === 'female') {
            hip = parseFloat(document.getElementById('hip_inches').value) * 2.54; // Convert to cm
        }
    }
    
    if (!height || !weight || !neck || !waist || (gender === 'female' && !hip)) {
        alert('Please enter valid measurements for all required fields');
        return;
    }
    
    // Calculate body fat using Army formula
    let bodyFat;
    if (gender === 'male') {
        bodyFat = 495 / (1.0324 - 0.19077 * Math.log10(waist - neck) + 0.15456 * Math.log10(height)) - 450;
    } else {
        bodyFat = 495 / (1.29579 - 0.35004 * Math.log10(waist + hip - neck) + 0.22100 * Math.log10(height)) - 450;
    }
    
    // Get Army standards
    const standard = getArmyStandard(age, gender);
    const compliance = bodyFat <= standard.maxBodyFat;
    
    // Calculate BMI for additional info
    const heightM = height / 100;
    const bmi = weight / (heightM * heightM);
    
    const resultsDiv = document.getElementById('army-bodyfat-results-content');
    resultsDiv.innerHTML = `
        <strong>Army Body Fat Assessment Results:</strong><br>
        Body Fat Percentage: ${bodyFat.toFixed(1)}%<br>
        Army Standard (Age ${age}): ${standard.maxBodyFat}% maximum<br>
        Status: ${compliance ? '<span style="color: green;">PASS</span>' : '<span style="color: red;">FAIL</span>'}<br>
        BMI: ${bmi.toFixed(1)} kg/m²<br>
        Age Group: ${standard.ageGroup}<br>
        ${compliance ? 'Meets Army body composition standards.' : 'Exceeds Army body composition standards - requires ABCP enrollment.'}
    `;
    document.getElementById('army-bodyfat-results').style.display = 'block';
}

function getArmyStandard(age, gender) {
    let ageGroup, maxBodyFat;
    
    if (age >= 17 && age <= 20) {
        ageGroup = '17-20';
        maxBodyFat = gender === 'male' ? 20 : 30;
    } else if (age >= 21 && age <= 27) {
        ageGroup = '21-27';
        maxBodyFat = gender === 'male' ? 22 : 32;
    } else if (age >= 28 && age <= 39) {
        ageGroup = '28-39';
        maxBodyFat = gender === 'male' ? 24 : 34;
    } else {
        ageGroup = '40+';
        maxBodyFat = gender === 'male' ? 26 : 36;
    }
    
    return { ageGroup, maxBodyFat };
}

function clearArmyBodyFatForm() {
    document.getElementById('age').value = '25';
    document.getElementById('male').checked = true;
    
    if (currentUnits === 'metric') {
        document.getElementById('height_cm').value = '180';
        document.getElementById('weight_kg').value = '70';
        document.getElementById('neck_cm').value = '37';
        document.getElementById('waist_cm').value = '85';
        document.getElementById('hip_cm').value = '95';
    } else {
        document.getElementById('feet').value = '5';
        document.getElementById('inches').value = '10';
        document.getElementById('pounds').value = '160';
        document.getElementById('neck_inches').value = '15';
        document.getElementById('waist_inches').value = '32';
        document.getElementById('hip_inches').value = '36';
    }
    
    document.getElementById('army-bodyfat-results').style.display = 'none';
    updateGenderFields();
}

function performSearch() {
    const searchTerm = document.getElementById('calcSearchTerm').value;
    const searchOut = document.getElementById('calcSearchOut');
    
    if (searchTerm.trim() === '') {
        searchOut.innerHTML = '';
        return;
    }
    
    const calculators = [
        'BMI Calculator', 'Body Fat Calculator', 'Calorie Calculator', 
        'BMR Calculator', 'Ideal Weight Calculator', 'Army Body Fat Calculator'
    ];
    
    const results = calculators.filter(calc => 
        calc.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    if (results.length > 0) {
        searchOut.innerHTML = '<div>' + results.map(result => 
            `<a href="#" style="display: block; padding: 2px 0;">${result}</a>`
        ).join('') + '</div>';
    } else {
        searchOut.innerHTML = '<div>No calculators found</div>';
    }
}

// Initialize
showUnits('metric');
</script>

</body>
</html>
