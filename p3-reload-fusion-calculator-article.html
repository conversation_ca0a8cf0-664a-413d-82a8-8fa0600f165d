<style>
#insmdc{background-color:#336699;margin-bottom:3px;text-align:center;}
.persona-interface {
    background: linear-gradient(135deg, #1565c0 0%, #0d47a1 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    box-shadow: 0 8px 16px rgba(0,0,0,0.3);
}
.arcana-selector {
    background-color: #e3f2fd;
    border: 2px solid #1565c0;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
.persona-card {
    background: white;
    border: 2px solid #e0e0e0;
    border-radius: 10px;
    padding: 15px;
    margin: 10px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-block;
    width: 140px;
}
.persona-card:hover {
    border-color: #1565c0;
    transform: translateY(-3px);
}
.persona-card.selected {
    border-color: #1565c0;
    background-color: #e3f2fd;
}
.fusion-result {
    background: linear-gradient(135deg, #ff6b35 0%, #f44336 100%);
    border-radius: 15px;
    padding: 25px;
    margin: 20px 0;
    color: white;
    text-align: center;
    box-shadow: 0 6px 12px rgba(0,0,0,0.15);
}
.arcana-badge {
    display: inline-block;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: bold;
    margin: 3px;
    color: white;
}
.fool { background-color: #ffeb3b; color: #333; }
.magician { background-color: #f44336; }
.priestess { background-color: #2196f3; }
.empress { background-color: #4caf50; }
.emperor { background-color: #ff9800; }
.hierophant { background-color: #795548; }
.lovers { background-color: #e91e63; }
.chariot { background-color: #9c27b0; }
.justice { background-color: #607d8b; }
.hermit { background-color: #8bc34a; }
.fortune { background-color: #ff5722; }
.strength { background-color: #ffc107; color: #333; }
.hanged { background-color: #3f51b5; }
.death { background-color: #424242; }
.temperance { background-color: #00bcd4; }
.devil { background-color: #9e9e9e; }
.tower { background-color: #f44336; }
.star { background-color: #673ab7; }
.moon { background-color: #1976d2; }
.sun { background-color: #ff9800; }
.judgement { background-color: #4caf50; }
.aeon { background-color: #e91e63; }
.skill-list {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin: 20px 0;
    color: #2c3e50;
}
.level-requirement {
    background-color: #f1f8e9;
    border: 2px solid #4caf50;
    border-radius: 10px;
    padding: 20px;
    margin: 15px 0;
}
</style>

<h1>Persona 3 Reload Fusion Calculator</h1>
<p>Calculate Persona fusion combinations in Persona 3 Reload with our comprehensive fusion calculator. Discover optimal fusion paths, plan your Persona team compositions, and master the Velvet Room's fusion mechanics with detailed skill inheritance and level requirements.</p>

<div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
<div>
    <form name="p3form" id="p3-calculator-form">
        <div id="topmenu">
            <ul>
                <li id="menuon"><a href="#" data-tab="calculator">Fusion Calculator</a></li>
                <li><a href="#" data-tab="personas">Persona Database</a></li>
                <li><a href="#" data-tab="skills">Skill Guide</a></li>
            </ul>
        </div>
        
        <div class="panel2" style="padding: 20px;">
            <div class="persona-interface">
                <h3 style="margin-bottom: 20px; text-align: center;">🎭 Persona 3 Reload Fusion Calculator</h3>
                
                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 20px;">
                    <div class="arcana-selector">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🎭 First Persona</h4>
                        <div>
                            <label for="persona1-arcana"><strong>Arcana:</strong></label>
                            <select id="persona1-arcana" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Select Arcana</option>
                                <option value="fool">Fool</option>
                                <option value="magician">Magician</option>
                                <option value="priestess">High Priestess</option>
                                <option value="empress">Empress</option>
                                <option value="emperor">Emperor</option>
                                <option value="hierophant">Hierophant</option>
                                <option value="lovers">Lovers</option>
                                <option value="chariot">Chariot</option>
                                <option value="justice">Justice</option>
                                <option value="hermit">Hermit</option>
                                <option value="fortune">Wheel of Fortune</option>
                                <option value="strength">Strength</option>
                                <option value="hanged">Hanged Man</option>
                                <option value="death">Death</option>
                                <option value="temperance">Temperance</option>
                                <option value="devil">Devil</option>
                                <option value="tower">Tower</option>
                                <option value="star">Star</option>
                                <option value="moon">Moon</option>
                                <option value="sun">Sun</option>
                                <option value="judgement">Judgement</option>
                                <option value="aeon">Aeon</option>
                            </select>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="persona1-name"><strong>Persona:</strong></label>
                            <select id="persona1-name" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Select Persona</option>
                            </select>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="persona1-level"><strong>Level:</strong></label>
                            <input type="number" id="persona1-level" value="1" min="1" max="99" class="innormal" style="width: 80px; margin-left: 10px;">
                        </div>
                    </div>
                    
                    <div class="arcana-selector">
                        <h4 style="color: #2c3e50; margin-bottom: 15px;">🎭 Second Persona</h4>
                        <div>
                            <label for="persona2-arcana"><strong>Arcana:</strong></label>
                            <select id="persona2-arcana" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Select Arcana</option>
                                <option value="fool">Fool</option>
                                <option value="magician">Magician</option>
                                <option value="priestess">High Priestess</option>
                                <option value="empress">Empress</option>
                                <option value="emperor">Emperor</option>
                                <option value="hierophant">Hierophant</option>
                                <option value="lovers">Lovers</option>
                                <option value="chariot">Chariot</option>
                                <option value="justice">Justice</option>
                                <option value="hermit">Hermit</option>
                                <option value="fortune">Wheel of Fortune</option>
                                <option value="strength">Strength</option>
                                <option value="hanged">Hanged Man</option>
                                <option value="death">Death</option>
                                <option value="temperance">Temperance</option>
                                <option value="devil">Devil</option>
                                <option value="tower">Tower</option>
                                <option value="star">Star</option>
                                <option value="moon">Moon</option>
                                <option value="sun">Sun</option>
                                <option value="judgement">Judgement</option>
                                <option value="aeon">Aeon</option>
                            </select>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="persona2-name"><strong>Persona:</strong></label>
                            <select id="persona2-name" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="">Select Persona</option>
                            </select>
                        </div>
                        
                        <div style="margin-top: 15px;">
                            <label for="persona2-level"><strong>Level:</strong></label>
                            <input type="number" id="persona2-level" value="1" min="1" max="99" class="innormal" style="width: 80px; margin-left: 10px;">
                        </div>
                    </div>
                </div>
                
                <div class="level-requirement">
                    <h4 style="color: #2c3e50; margin-bottom: 15px;">🔮 Fusion Settings</h4>
                    <div style="display: grid; grid-template-columns: 1fr 1fr 1fr; gap: 20px;">
                        <div>
                            <label for="protagonist-level"><strong>Protagonist Level:</strong></label>
                            <input type="number" id="protagonist-level" value="10" min="1" max="99" class="innormal" style="width: 100%; margin-top: 5px;">
                        </div>
                        
                        <div>
                            <label for="fusion-type"><strong>Fusion Type:</strong></label>
                            <select id="fusion-type" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="normal">Normal Fusion</option>
                                <option value="special">Special Fusion</option>
                                <option value="triangle">Triangle Fusion</option>
                            </select>
                        </div>
                        
                        <div>
                            <label for="social-link"><strong>Social Link Bonus:</strong></label>
                            <select id="social-link" class="innormal" style="width: 100%; margin-top: 5px;">
                                <option value="0">No Bonus</option>
                                <option value="1">Rank 1-3 (+1 Level)</option>
                                <option value="2">Rank 4-6 (+2 Levels)</option>
                                <option value="3">Rank 7-9 (+3 Levels)</option>
                                <option value="4">Rank 10 (+5 Levels)</option>
                            </select>
                        </div>
                    </div>
                </div>
            </div>
            
            <div style="text-align: center; margin: 25px 0;">
                <input type="button" value="Calculate Fusion" id="calculate-p3-btn" style="padding: 15px 30px; font-size: 18px; background-color: #1565c0; color: white; border: none; border-radius: 8px; cursor: pointer; margin-right: 15px;">
                <input type="button" value="Clear Selection" id="clear-p3-btn" style="padding: 15px 30px; font-size: 18px; background-color: #6c757d; color: white; border: none; border-radius: 8px; cursor: pointer;">
            </div>
        </div>
        
        <div class="smalltext" style="text-align: center; color: #666; font-style: italic; margin: 20px 0;">
            <strong>Note:</strong> Fusion results are based on Persona 3 Reload mechanics. Some special fusions may require specific conditions.
        </div>
        
        <div id="p3-results" style="display: none; margin-top: 30px;">
            <div class="fusion-result">
                <h3 style="margin-bottom: 20px;">✨ Fusion Results</h3>
                <div id="p3-results-content"></div>
            </div>
        </div>
    </form>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const calculateBtn = document.getElementById('calculate-p3-btn');
    const clearBtn = document.getElementById('clear-p3-btn');
    const resultsDiv = document.getElementById('p3-results');
    const resultsContent = document.getElementById('p3-results-content');
    
    // Sample Persona data for P3 Reload
    const personaData = {
        fool: [
            { name: 'Orpheus', level: 1, skills: ['Cadenza', 'Agi', 'Bash'] },
            { name: 'Slime', level: 12, skills: ['Acid Breath', 'Poison Mist'] },
            { name: 'Legion', level: 22, skills: ['Evil Touch', 'Poison Skewer'] }
        ],
        magician: [
            { name: 'Nekomata', level: 5, skills: ['Agi', 'Cleave', 'Sukukaja'] },
            { name: 'Jack Frost', level: 8, skills: ['Bufu', 'Ice Break', 'Mabufu'] },
            { name: 'Pyro Jack', level: 13, skills: ['Agi', 'Maragi', 'Fire Break'] }
        ],
        priestess: [
            { name: 'Pixie', level: 2, skills: ['Zio', 'Dia', 'Patra'] },
            { name: 'Angel', level: 4, skills: ['Hama', 'Dia', 'Media'] },
            { name: 'Archangel', level: 10, skills: ['Hama', 'Media', 'Mahama'] }
        ],
        empress: [
            { name: 'Leanan Sidhe', level: 6, skills: ['Agi', 'Maragi', 'Media'] },
            { name: 'Hariti', level: 40, skills: ['Mediarama', 'Samarecarm', 'Mudoon'] }
        ],
        emperor: [
            { name: 'King Frost', level: 20, skills: ['Bufula', 'Ice Break', 'Mabufu'] },
            { name: 'Raja Naga', level: 55, skills: ['Agidyne', 'Fire Amp', 'Maragidyne'] }
        ],
        death: [
            { name: 'Ghoul', level: 18, skills: ['Cleave', 'Poison Skewer', 'Evil Touch'] },
            { name: 'Pale Rider', level: 54, skills: ['Mudoon', 'Mamudoon', 'Curse Boost'] },
            { name: 'Alice', level: 56, skills: ['Die For Me!', 'Mudoon', 'Mamudoon'] }
        ]
    };
    
    // Fusion chart for P3 Reload
    const fusionChart = {
        'fool+magician': 'death',
        'fool+priestess': 'magician',
        'magician+priestess': 'lovers',
        'magician+empress': 'emperor',
        'priestess+empress': 'emperor',
        'empress+emperor': 'justice',
        'death+fool': 'moon'
    };
    
    // Populate persona dropdowns when arcana is selected
    document.getElementById('persona1-arcana').addEventListener('change', function() {
        populatePersonaDropdown('persona1-name', this.value);
    });
    
    document.getElementById('persona2-arcana').addEventListener('change', function() {
        populatePersonaDropdown('persona2-name', this.value);
    });
    
    function populatePersonaDropdown(dropdownId, arcana) {
        const dropdown = document.getElementById(dropdownId);
        dropdown.innerHTML = '<option value="">Select Persona</option>';
        
        if (arcana && personaData[arcana]) {
            personaData[arcana].forEach(persona => {
                const option = document.createElement('option');
                option.value = persona.name.toLowerCase().replace(/\s+/g, '_');
                option.textContent = `${persona.name} (Lv.${persona.level})`;
                dropdown.appendChild(option);
            });
        }
    }
    
    calculateBtn.addEventListener('click', function() {
        const arcana1 = document.getElementById('persona1-arcana').value;
        const arcana2 = document.getElementById('persona2-arcana').value;
        const persona1 = document.getElementById('persona1-name').value;
        const persona2 = document.getElementById('persona2-name').value;
        const level1 = parseInt(document.getElementById('persona1-level').value) || 1;
        const level2 = parseInt(document.getElementById('persona2-level').value) || 1;
        const protagonistLevel = parseInt(document.getElementById('protagonist-level').value) || 10;
        const fusionType = document.getElementById('fusion-type').value;
        const socialLinkBonus = parseInt(document.getElementById('social-link').value) || 0;
        
        if (!arcana1 || !arcana2) {
            alert('Please select both Persona arcanas.');
            return;
        }
        
        if (!persona1 || !persona2) {
            alert('Please select both Personas.');
            return;
        }
        
        const fusionResult = calculateP3Fusion(arcana1, arcana2, persona1, persona2, 
            level1, level2, protagonistLevel, fusionType, socialLinkBonus);
        displayP3Results(fusionResult);
        
        resultsDiv.style.display = 'block';
        resultsDiv.scrollIntoView({ behavior: 'smooth' });
    });
    
    clearBtn.addEventListener('click', function() {
        document.getElementById('p3-calculator-form').reset();
        document.getElementById('persona1-name').innerHTML = '<option value="">Select Persona</option>';
        document.getElementById('persona2-name').innerHTML = '<option value="">Select Persona</option>';
        resultsDiv.style.display = 'none';
    });
    
    function calculateP3Fusion(arcana1, arcana2, persona1, persona2, level1, level2, 
                              protagonistLevel, fusionType, socialLinkBonus) {
        
        // Create fusion key
        const fusionKey1 = `${arcana1}+${arcana2}`;
        const fusionKey2 = `${arcana2}+${arcana1}`;
        
        let resultArcana = fusionChart[fusionKey1] || fusionChart[fusionKey2];
        
        if (!resultArcana) {
            // Default fusion logic
            const arcanaList = ['fool', 'magician', 'priestess', 'empress', 'emperor', 'death'];
            const index1 = arcanaList.indexOf(arcana1);
            const index2 = arcanaList.indexOf(arcana2);
            
            if (index1 !== -1 && index2 !== -1) {
                const resultIndex = (index1 + index2) % arcanaList.length;
                resultArcana = arcanaList[resultIndex];
            } else {
                resultArcana = 'fool';
            }
        }
        
        // Calculate result level
        const averageLevel = Math.floor((level1 + level2) / 2) + 1;
        let resultLevel = averageLevel + socialLinkBonus;
        
        // Check protagonist level requirement
        if (resultLevel > protagonistLevel) {
            resultLevel = protagonistLevel;
        }
        
        resultLevel = Math.max(1, Math.min(99, resultLevel));
        
        // Get available personas from result arcana
        const availablePersonas = personaData[resultArcana] || personaData.fool;
        
        // Find persona closest to result level
        let resultPersona = availablePersonas[0];
        let levelDifference = Math.abs(resultPersona.level - resultLevel);
        
        availablePersonas.forEach(persona => {
            const diff = Math.abs(persona.level - resultLevel);
            if (diff < levelDifference && persona.level <= protagonistLevel) {
                levelDifference = diff;
                resultPersona = persona;
            }
        });
        
        // Calculate inherited skills
        const persona1Data = findPersonaData(arcana1, persona1);
        const persona2Data = findPersonaData(arcana2, persona2);
        const inheritedSkills = calculateP3SkillInheritance(persona1Data, persona2Data, resultPersona);
        
        // Calculate fusion cost
        const fusionCost = calculateFusionCost(resultPersona.level);
        
        return {
            arcana: resultArcana,
            persona: resultPersona,
            level: resultLevel,
            fusionType: fusionType,
            socialLinkBonus: socialLinkBonus,
            materials: [
                { arcana: arcana1, name: persona1, level: level1 },
                { arcana: arcana2, name: persona2, level: level2 }
            ],
            inheritedSkills: inheritedSkills,
            fusionCost: fusionCost,
            protagonistLevel: protagonistLevel
        };
    }
    
    function findPersonaData(arcana, personaName) {
        if (!personaData[arcana]) return null;
        return personaData[arcana].find(persona => 
            persona.name.toLowerCase().replace(/\s+/g, '_') === personaName
        );
    }
    
    function calculateP3SkillInheritance(persona1, persona2, resultPersona) {
        const skills = [];
        
        // Add result persona's natural skills
        skills.push(...resultPersona.skills);
        
        // Add skills from parent personas
        if (persona1 && persona1.skills) {
            skills.push(...persona1.skills.slice(0, 2));
        }
        
        if (persona2 && persona2.skills) {
            skills.push(...persona2.skills.slice(0, 2));
        }
        
        // Remove duplicates and limit to 8 skills
        return [...new Set(skills)].slice(0, 8);
    }
    
    function calculateFusionCost(level) {
        return level * level * 10 + 2000;
    }
    
    function displayP3Results(result) {
        const arcanaClass = result.arcana;
        const capitalizedArcana = result.arcana.charAt(0).toUpperCase() + result.arcana.slice(1);
        
        let html = `
            <div style="display: flex; align-items: center; justify-content: center; margin-bottom: 25px;">
                <div style="text-align: center; margin: 0 15px;">
                    <h4>${result.materials[0].name}</h4>
                    <div class="arcana-badge ${result.materials[0].arcana}">${result.materials[0].arcana.toUpperCase()}</div>
                    <div>Lv.${result.materials[0].level}</div>
                </div>
                
                <div style="font-size: 24px; margin: 0 20px;">+</div>
                
                <div style="text-align: center; margin: 0 15px;">
                    <h4>${result.materials[1].name}</h4>
                    <div class="arcana-badge ${result.materials[1].arcana}">${result.materials[1].arcana.toUpperCase()}</div>
                    <div>Lv.${result.materials[1].level}</div>
                </div>
                
                <div style="font-size: 24px; margin: 0 20px;">=</div>
                
                <div style="text-align: center; margin: 0 15px; padding: 20px; background-color: rgba(255,255,255,0.2); border-radius: 10px;">
                    <h3 style="margin: 0;">${result.persona.name}</h3>
                    <div class="arcana-badge ${arcanaClass}">${capitalizedArcana}</div>
                    <div style="margin-top: 10px;">Level ${result.level}</div>
                </div>
            </div>
            
            <div class="skill-list">
                <h4 style="color: #1565c0; margin-bottom: 15px;">🎯 Inherited Skills</h4>
                <div style="display: flex; flex-wrap: wrap; gap: 10px; margin-top: 15px;">
        `;
        
        result.inheritedSkills.forEach(skill => {
            html += `<span style="background-color: #1565c0; color: white; padding: 6px 12px; border-radius: 15px; font-size: 14px;">${skill}</span>`;
        });
        
        html += `
                </div>
                
                <div style="margin-top: 20px; padding: 15px; background-color: #f5f5f5; border-radius: 8px;">
                    <h5 style="color: #2c3e50;">Fusion Details:</h5>
                    <ul style="color: #2c3e50; margin: 10px 0;">
                        <li><strong>Fusion Type:</strong> ${result.fusionType.charAt(0).toUpperCase() + result.fusionType.slice(1)} Fusion</li>
                        <li><strong>Social Link Bonus:</strong> +${result.socialLinkBonus} levels</li>
                        <li><strong>Fusion Cost:</strong> ¥${result.fusionCost.toLocaleString()}</li>
                        <li><strong>Protagonist Level:</strong> ${result.protagonistLevel}</li>
                    </ul>
                </div>
            </div>
            
            <div style="background-color: rgba(255,255,255,0.2); border-radius: 10px; padding: 20px; margin-top: 20px;">
                <h4>💡 P3 Reload Fusion Tips:</h4>
                <ul style="text-align: left; margin: 10px 0;">
                    <li><strong>Social Links:</strong> Higher ranks give level bonuses to fused Personas</li>
                    <li><strong>Skill Cards:</strong> Use skill cards to teach specific skills</li>
                    <li><strong>Level Requirements:</strong> You can only fuse Personas at or below your level</li>
                    <li><strong>Special Fusions:</strong> Some Personas require specific fusion recipes</li>
                    <li><strong>Inheritance:</strong> Plan skill inheritance for optimal builds</li>
                    <li><strong>Compendium:</strong> Register strong Personas for future use</li>
                </ul>
            </div>
        `;
        
        resultsContent.innerHTML = html;
    }
});
</script>
