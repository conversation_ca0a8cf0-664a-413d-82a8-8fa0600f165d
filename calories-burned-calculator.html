<!DOCTYPE html>
<html lang="en">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
    <title>Calories Burned Calculator</title>
    <meta name="description" content="Calculate calories burned during exercise and daily activities. Free calories burned calculator with MET values for running, walking, cycling, and more.">
    <link rel="stylesheet" href="./Carbohydrate Calculator_files/style.css">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style>
        /* Mobile responsive styles */
        @media screen and (max-width: 768px) {
            #contentout { width: 100% !important; padding: 0 10px; }
            #content { width: 100% !important; margin: 0; }
            #right { display: none; }
            .panel2 { width: 100% !important; padding: 5px !important; }
            table { width: 100% !important; font-size: 14px; }
            td { padding: 8px 4px !important; word-wrap: break-word; }
            input, select { width: 100% !important; max-width: 100% !important; box-sizing: border-box; font-size: 16px; }
            .inlongest, .innormal, .inlongesthalf, .in2char { width: 100% !important; }
            h1 { font-size: 24px; }
            .cinfoT { font-size: 12px; overflow-x: auto; }
            .cinfoT td { padding: 4px 2px !important; }
            .cbcontainer { font-size: 12px; margin: 2px 0; }
            #topmenu ul li { display: inline-block; margin: 2px; font-size: 12px; }
        }
        @media screen and (max-width: 480px) {
            table td:first-child { width: 30% !important; }
            table td:last-child { width: 70% !important; }
            input[type="submit"], input[type="button"] {
                width: 100% !important; margin: 5px 0 !important; padding: 12px !important; font-size: 14px;
            }
            .in2char { width: 30% !important; display: inline-block; margin: 0 2px; }
            .cbcontainer { display: block; margin: 3px 0; }
        }
    </style>
    <link rel="apple-touch-icon" sizes="180x180" href="https://d26tpo4cm8sb6k.cloudfront.net/apple-touch-icon.png">
    <link rel="icon" type="image/png" sizes="32x32" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-32x32.png">
    <link rel="icon" type="image/png" sizes="16x16" href="https://d26tpo4cm8sb6k.cloudfront.net/icon-16x16.png">
    <link rel="manifest" href="https://www.calculator.net/manifest.json">
</head>
<body>
<div id="headerout">
    <div id="header">
        <div id="logo"><a href="https://www.calculator.net/"><img src="./Carbohydrate Calculator_files/calculator-white.svg" width="208" height="22" alt="Calculator.net"></a></div>
        <div id="login"><a href="https://www.calculator.net/my-account/sign-in.php">sign in</a></div>
    </div>
</div>
<div id="clear"></div>
<div id="contentout">
    <div id="content">
        <div id="breadcrumbs" itemscope="" itemtype="https://schema.org/BreadcrumbList">
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/" itemprop="item"><span itemprop="name">home</span></a>
                <meta itemprop="position" content="1">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="https://www.calculator.net/fitness-and-health-calculator.html" itemprop="item"><span itemprop="name">fitness &amp; health</span></a>
                <meta itemprop="position" content="2">
            </span> / 
            <span itemprop="itemListElement" itemscope="" itemtype="https://schema.org/ListItem">
                <a href="#" itemprop="item"><span itemprop="name">calories burned calculator</span></a>
                <meta itemprop="position" content="3">
            </span>
        </div>
        <div id="printit"><a href="#" onclick="window.print();return false;">Print</a></div>
        
        <h1>Calories Burned Calculator</h1>
        <p>The <i>Calories Burned Calculator</i> calculates the number of calories burned during various physical activities. The calculator uses the concept of METs (Metabolic Equivalent of Task) to estimate energy expenditure for different activities based on your body weight and duration of activity.</p>
        
        <div id="insmdc"><img src="https://calculator.net/img/svg/insm.svg" width="630" height="35" alt="Modify the values and click the calculate button to use"></div>
        <span id="ucframe"></span>
        
        <div id="topmenu" class="topmenucenter">
            <ul>
                <li><a href="#" onclick="return showUnits('standard');">US Units</a></li>
                <li id="menuon"><a href="#" onclick="return showUnits('metric');">Metric Units</a></li>
                <li><a href="#" onclick="return showUnits('other');">Other Units</a></li>
            </ul>
        </div>

        <div class="panel2" style="padding:10px;">
            <form name="caloriesBurnedForm" id="calories-burned-calculator-form">
                <table id="calories-burned-input-table" width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">Age</td>
                            <td width="550"><input type="number" name="age" id="age" value="25" class="inlongesthalf" min="15" max="80"> ages 15 - 80</td>
                        </tr>
                        <tr>
                            <td>Gender</td>
                            <td>
                                <label for="male" class="cbcontainer"><input type="radio" name="gender" id="male" value="male" checked=""><span class="rbmark"></span>male</label> &nbsp; 
                                <label for="female" class="cbcontainer"><input type="radio" name="gender" id="female" value="female"><span class="rbmark"></span>female</label>
                            </td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- US Units -->
                <table width="620" id="standard-units" bgcolor="#eeeeee" style="display: none;">
                    <tbody>
                        <tr>
                            <td width="70">Weight</td>
                            <td width="550"><input type="number" name="pounds" id="pounds" value="160" class="inlongest inuipound" min="50" max="1000"><span class="inuipoundspan">pounds</span></td>
                        </tr>
                    </tbody>
                </table>
                
                <!-- Metric Units -->
                <table width="620" id="metric-units" bgcolor="#eeeeee" style="display: block;">
                    <tbody>
                        <tr>
                            <td width="70">Weight</td>
                            <td width="550"><input type="number" name="weight_kg" id="weight_kg" value="70" class="inlongest inuick" min="20" max="300" step="0.1"><span class="inuickspan">kg</span></td>
                        </tr>
                    </tbody>
                </table>
                
                <table width="620" bgcolor="#eeeeee">
                    <tbody>
                        <tr>
                            <td width="70">Activity</td>
                            <td width="550">
                                <select id="activity" name="activity" class="inlongest" onchange="updateMETValue()">
                                    <optgroup label="Running">
                                        <option value="6.0">Running, 5 mph (12 min/mile)</option>
                                        <option value="8.3">Running, 6 mph (10 min/mile)</option>
                                        <option value="9.8">Running, 7 mph (8.5 min/mile)</option>
                                        <option value="11.0">Running, 8 mph (7.5 min/mile)</option>
                                        <option value="12.3">Running, 9 mph (6.5 min/mile)</option>
                                        <option value="14.5">Running, 10 mph (6 min/mile)</option>
                                    </optgroup>
                                    <optgroup label="Walking">
                                        <option value="2.0">Walking, slow pace (2 mph)</option>
                                        <option value="2.8">Walking, 2.5 mph</option>
                                        <option value="3.5" selected="">Walking, 3.5 mph, brisk pace</option>
                                        <option value="4.3">Walking, 4.0 mph, very brisk</option>
                                        <option value="5.0">Walking, 4.5 mph</option>
                                    </optgroup>
                                    <optgroup label="Cycling">
                                        <option value="4.0">Cycling, leisure, 5.5 mph</option>
                                        <option value="6.8">Cycling, 12-13.9 mph, moderate</option>
                                        <option value="8.0">Cycling, 14-15.9 mph, vigorous</option>
                                        <option value="10.0">Cycling, 16-19 mph, very fast</option>
                                        <option value="12.0">Cycling, >20 mph, racing</option>
                                    </optgroup>
                                    <optgroup label="Swimming">
                                        <option value="6.0">Swimming, leisure</option>
                                        <option value="8.3">Swimming, moderate pace</option>
                                        <option value="10.0">Swimming, vigorous pace</option>
                                        <option value="13.8">Swimming, butterfly</option>
                                    </optgroup>
                                    <optgroup label="Sports">
                                        <option value="7.0">Basketball, general</option>
                                        <option value="8.0">Football, general</option>
                                        <option value="10.0">Soccer, general</option>
                                        <option value="4.0">Tennis, general</option>
                                        <option value="6.0">Volleyball, general</option>
                                    </optgroup>
                                    <optgroup label="Gym Activities">
                                        <option value="3.0">Weight lifting, light</option>
                                        <option value="6.0">Weight lifting, vigorous</option>
                                        <option value="7.0">Aerobics, general</option>
                                        <option value="8.5">Aerobics, high impact</option>
                                        <option value="2.5">Yoga, general</option>
                                    </optgroup>
                                    <optgroup label="Daily Activities">
                                        <option value="1.5">Sleeping</option>
                                        <option value="1.8">Watching TV</option>
                                        <option value="2.0">Office work</option>
                                        <option value="2.5">Cooking</option>
                                        <option value="3.0">Cleaning house</option>
                                        <option value="4.0">Gardening</option>
                                    </optgroup>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>Duration</td>
                            <td>
                                <input type="number" name="hours" id="hours" value="0" class="in2char" min="0" max="23"> hours
                                <input type="number" name="minutes" id="minutes" value="30" class="in2char" min="0" max="59"> minutes
                            </td>
                        </tr>
                        <tr>
                            <td>MET Value</td>
                            <td>
                                <input type="number" name="met" id="met" value="3.5" class="innormal" min="1" max="20" step="0.1" readonly>
                                <span class="smalltext">(Metabolic Equivalent of Task)</span>
                            </td>
                        </tr>
                        <tr>
                            <td width="70">&nbsp;</td>
                            <td width="550">
                                <input type="submit" name="x" value="Calculate Calories Burned" id="calculate-btn">
                                <input type="button" value="Clear" id="clear-btn" onclick="clearCaloriesBurnedForm();">
                            </td>
                        </tr>
                    </tbody>
                </table>
            </form>
        </div>

        <ul style="margin-top:0px;margin-left:-20px;">
            <li class="smalltext"><b>MET:</b> Metabolic Equivalent of Task - energy cost of activities.</li>
            <li class="smalltext"><b>Formula:</b> Calories = MET × weight (kg) × time (hours).</li>
            <li class="smalltext"><b>Estimates:</b> Individual results may vary based on fitness level and body composition.</li>
        </ul>

        <!-- Results Display Area -->
        <div id="calories-burned-results" style="display: none; margin-top: 20px;">
            <div class="h2result" id="calories-burned-results-content"></div>
        </div>

        <br>
        <fieldset>
            <legend>Related</legend>
            <a href="#">Calorie Calculator</a> | 
            <a href="#">BMR Calculator</a> | 
            <a href="#">Pace Calculator</a>
        </fieldset>

        <br>
        <h3>What are METs?</h3>
        <p>MET stands for Metabolic Equivalent of Task. One MET is defined as the energy cost of sitting quietly and is equivalent to a caloric consumption of 1 kcal/kg/hour. It is estimated that compared to sitting quietly, a person's caloric expenditure increases by 30-40% by standing, 100% by walking slowly, and so on.</p>

        <h3>MET Values for Common Activities</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Activity</td>
                    <td class="cinfoHdL">MET Value</td>
                    <td class="cinfoHdL">Intensity</td>
                    <td class="cinfoHdL">Calories/Hour (70kg person)</td>
                </tr>
                <tr><td><strong>Sleeping</strong></td><td>1.0</td><td>Very Light</td><td>70</td></tr>
                <tr><td><strong>Watching TV</strong></td><td>1.8</td><td>Light</td><td>126</td></tr>
                <tr><td><strong>Walking (3.5 mph)</strong></td><td>3.5</td><td>Moderate</td><td>245</td></tr>
                <tr><td><strong>Cycling (moderate)</strong></td><td>6.8</td><td>Vigorous</td><td>476</td></tr>
                <tr><td><strong>Running (6 mph)</strong></td><td>8.3</td><td>Vigorous</td><td>581</td></tr>
                <tr><td><strong>Swimming (vigorous)</strong></td><td>10.0</td><td>Very Vigorous</td><td>700</td></tr>
            </tbody>
        </table>

        <h3>Factors Affecting Calorie Burn</h3>
        <ul>
            <li><strong>Body Weight:</strong> Heavier individuals burn more calories during the same activity</li>
            <li><strong>Intensity:</strong> Higher intensity activities have higher MET values</li>
            <li><strong>Duration:</strong> Longer activities burn more total calories</li>
            <li><strong>Fitness Level:</strong> Fitter individuals may be more efficient and burn fewer calories</li>
            <li><strong>Age:</strong> Metabolism generally decreases with age</li>
            <li><strong>Gender:</strong> Men typically burn more calories due to higher muscle mass</li>
            <li><strong>Environmental Conditions:</strong> Heat, cold, and altitude can affect calorie burn</li>
        </ul>

        <h3>Activity Intensity Guidelines</h3>
        <table class="cinfoT">
            <tbody>
                <tr>
                    <td class="cinfoHd">Intensity Level</td>
                    <td class="cinfoHdL">MET Range</td>
                    <td class="cinfoHdL">Examples</td>
                    <td class="cinfoHdL">Benefits</td>
                </tr>
                <tr><td><strong>Light</strong></td><td>1.6 - 2.9</td><td>Slow walking, light housework</td><td>Daily living activities</td></tr>
                <tr><td><strong>Moderate</strong></td><td>3.0 - 5.9</td><td>Brisk walking, dancing</td><td>Health benefits, weight maintenance</td></tr>
                <tr><td><strong>Vigorous</strong></td><td>6.0 - 8.9</td><td>Running, cycling, sports</td><td>Fitness improvement, weight loss</td></tr>
                <tr><td><strong>Very Vigorous</strong></td><td>9.0+</td><td>Fast running, competitive sports</td><td>Athletic performance</td></tr>
            </tbody>
        </table>

        <h3>Tips for Maximizing Calorie Burn</h3>
        <ul>
            <li>Increase exercise intensity gradually to burn more calories</li>
            <li>Combine cardiovascular and strength training exercises</li>
            <li>Try interval training for higher calorie burn in less time</li>
            <li>Stay consistent with your exercise routine</li>
            <li>Choose activities you enjoy to maintain long-term adherence</li>
            <li>Consider adding incline or resistance to increase intensity</li>
            <li>Stay hydrated and fuel your body properly for optimal performance</li>
        </ul>

        <h3>Using Calorie Burn for Weight Management</h3>
        <p>To lose 1 pound (0.45 kg) of body weight, you need to create a calorie deficit of approximately 3,500 calories. This can be achieved through a combination of diet and exercise. For example:</p>
        <ul>
            <li>Reduce daily calorie intake by 250 calories</li>
            <li>Burn an additional 250 calories through exercise</li>
            <li>This creates a 500-calorie daily deficit</li>
            <li>Results in approximately 1 pound of weight loss per week</li>
        </ul>
    </div>
    
    <div id="right">
        <div style="padding-top:10px; min-height:280px; text-align:center;">
            <div style="width: 300px; height: 250px; background: #f0f0f0; border: 1px solid #ccc; display: flex; align-items: center; justify-content: center; color: #666;">
                Advertisement Space
            </div>
        </div>
        
        <form name="calcSearchForm" onsubmit="return false;" autocomplete="off">
            <table align="center" id="searchbox">
                <tbody>
                    <tr>
                        <td><input type="text" name="calcSearchTerm" id="calcSearchTerm" class="inlongest" placeholder="Search calculators..."></td>
                        <td><span id="bluebtn" onclick="performSearch();">Search</span></td>
                    </tr>
                    <tr>
                        <td colspan="2"><div id="calcSearchOut"></div></td>
                    </tr>
                </tbody>
            </table>
        </form>
        
        <div id="othercalc">
            <div id="octitle">
                <a href="#">Fitness and Health Calculators</a>
            </div>
            <div id="occontent">
                <a href="#">BMI Calculator</a>
                <a href="#">Body Fat Calculator</a>
                <a href="#">Calorie Calculator</a>
                <a href="#">BMR Calculator</a>
                <a href="#">Ideal Weight Calculator</a>
                <a href="#">Macro Calculator</a>
                <a href="#">Pace Calculator</a>
                <a href="#">Pregnancy Calculator</a>
                <a href="#">Heart Rate Calculator</a>
                <a href="#">More Fitness Calculators</a>
            </div>
            <div id="ocother">
                <a href="#">Financial</a> | 
                <a href="#">Fitness and Health</a> | 
                <a href="#">Math</a> | 
                <a href="#">Other</a>
            </div>
        </div>
    </div>
</div>

<div id="clear"></div>

<div id="footer">
    <div id="footerin">
        <div id="footernav">
            <a href="#">about us</a> | 
            <a href="#">sitemap</a> | 
            <a href="#">terms of use</a> | 
            <a href="#">privacy policy</a> &nbsp;  © 2008 - 2025 <a href="#">calculator.net</a>
        </div>
    </div>
</div>

<div class="topNavAbs">
    <a href="#">Financial</a>
    <a href="#" class="topNavOn">Fitness &amp; Health</a>
    <a href="#">Math</a>
    <a href="#">Other</a>
</div>

<script>
let currentUnits = 'metric';

function showUnits(unitType) {
    document.getElementById('standard-units').style.display = 'none';
    document.getElementById('metric-units').style.display = 'none';
    
    if (unitType === 'standard') {
        document.getElementById('standard-units').style.display = 'block';
    } else if (unitType === 'metric') {
        document.getElementById('metric-units').style.display = 'block';
    }
    
    const menuItems = document.querySelectorAll('#topmenu ul li');
    menuItems.forEach(item => {
        item.id = '';
        const link = item.querySelector('a');
        if (link && link.textContent.toLowerCase().includes(unitType === 'standard' ? 'us' : unitType)) {
            item.id = 'menuon';
        }
    });
    
    currentUnits = unitType;
    return false;
}

function updateMETValue() {
    const activitySelect = document.getElementById('activity');
    const metInput = document.getElementById('met');
    metInput.value = activitySelect.value;
}

document.getElementById('calculate-btn').addEventListener('click', function(e) {
    e.preventDefault();
    calculateCaloriesBurned();
});

function calculateCaloriesBurned() {
    const age = parseInt(document.getElementById('age').value);
    const gender = document.querySelector('input[name="gender"]:checked').value;
    const hours = parseInt(document.getElementById('hours').value) || 0;
    const minutes = parseInt(document.getElementById('minutes').value) || 0;
    const met = parseFloat(document.getElementById('met').value);
    
    let weight;
    
    if (currentUnits === 'metric') {
        weight = parseFloat(document.getElementById('weight_kg').value);
    } else {
        const pounds = parseFloat(document.getElementById('pounds').value) || 0;
        weight = pounds * 0.453592; // Convert to kg
    }
    
    if (weight <= 0) {
        alert('Please enter a valid weight');
        return;
    }
    
    if (hours === 0 && minutes === 0) {
        alert('Please enter a valid duration');
        return;
    }
    
    const totalHours = hours + (minutes / 60);
    
    // Calculate calories burned using MET formula
    // Calories = MET × weight (kg) × time (hours)
    const caloriesBurned = met * weight * totalHours;
    
    // Calculate calories per minute and per hour
    const caloriesPerMinute = caloriesBurned / (totalHours * 60);
    const caloriesPerHour = met * weight;
    
    // Get activity name
    const activitySelect = document.getElementById('activity');
    const activityName = activitySelect.options[activitySelect.selectedIndex].text;
    
    // Calculate equivalent activities
    const equivalents = calculateEquivalentActivities(caloriesBurned, weight);
    
    const resultsDiv = document.getElementById('calories-burned-results-content');
    resultsDiv.innerHTML = `
        <strong>Calories Burned Results:</strong><br>
        Activity: ${activityName}<br>
        Duration: ${hours}h ${minutes}m<br>
        Total Calories Burned: ${Math.round(caloriesBurned)} calories<br>
        Calories per Hour: ${Math.round(caloriesPerHour)} calories<br>
        Calories per Minute: ${caloriesPerMinute.toFixed(1)} calories<br><br>
        
        <strong>Equivalent Activities (same calories):</strong><br>
        ${equivalents}
    `;
    document.getElementById('calories-burned-results').style.display = 'block';
}

function calculateEquivalentActivities(targetCalories, weight) {
    const activities = [
        { name: 'Walking (3.5 mph)', met: 3.5 },
        { name: 'Running (6 mph)', met: 8.3 },
        { name: 'Cycling (moderate)', met: 6.8 },
        { name: 'Swimming (moderate)', met: 8.3 },
        { name: 'Basketball', met: 7.0 }
    ];
    
    let equivalents = '';
    activities.forEach(activity => {
        const timeNeeded = targetCalories / (activity.met * weight);
        const hours = Math.floor(timeNeeded);
        const minutes = Math.round((timeNeeded - hours) * 60);
        
        if (hours > 0) {
            equivalents += `• ${activity.name}: ${hours}h ${minutes}m<br>`;
        } else {
            equivalents += `• ${activity.name}: ${minutes}m<br>`;
        }
    });
    
    return equivalents;
}

function clearCaloriesBurnedForm() {
    document.getElementById('age').value = '25';
    document.getElementById('male').checked = true;
    document.getElementById('activity').selectedIndex = 2; // Walking 3.5 mph
    document.getElementById('hours').value = '0';
    document.getElementById('minutes').value = '30';
    document.getElementById('met').value = '3.5';
    
    if (currentUnits === 'metric') {
        document.getElementById('weight_kg').value = '70';
    } else {
        document.getElementById('pounds').value = '160';
    }
    
    document.getElementById('calories-burned-results').style.display = 'none';
}

function performSearch() {
    const searchTerm = document.getElementById('calcSearchTerm').value;
    const searchOut = document.getElementById('calcSearchOut');
    
    if (searchTerm.trim() === '') {
        searchOut.innerHTML = '';
        return;
    }
    
    const calculators = [
        'BMI Calculator', 'Body Fat Calculator', 'Calorie Calculator', 
        'BMR Calculator', 'Calories Burned Calculator', 'Pace Calculator'
    ];
    
    const results = calculators.filter(calc => 
        calc.toLowerCase().includes(searchTerm.toLowerCase())
    );
    
    if (results.length > 0) {
        searchOut.innerHTML = '<div>' + results.map(result => 
            `<a href="#" style="display: block; padding: 2px 0;">${result}</a>`
        ).join('') + '</div>';
    } else {
        searchOut.innerHTML = '<div>No calculators found</div>';
    }
}

// Initialize
showUnits('metric');
updateMETValue();
</script>

</body>
</html>
